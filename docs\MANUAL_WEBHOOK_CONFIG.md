# 🔧 **Configuração Manual do Webhook - Evolution API 2.2.3**

## ⚠️ **IMPORTANTE: Configuração Automática Não Disponível**

A Evolution API versão 2.2.3 **NÃO possui endpoints públicos** para configuração automática de webhooks via script. A configuração deve ser feita **manualmente** através da interface web.

## 📋 **ETAPAS PARA CONFIGURAÇÃO MANUAL**

### **1. 🌐 Acessar Interface Manager**
```
http://localhost:8080/manager
```

### **2. 🔑 Configurar Autenticação**
- **API Key:** `YD3%!nkTpsXueWAO`
- **Instância Criada:** `./`

### **3. 🔗 Configurar Webhook N8N**

#### **No N8N (http://localhost:5678):**
1. Criar novo workflow
2. Adicionar nó **Webhook**
3. Configurar:
   - **Método:** POST
   - **Path:** `/api/n8n-evolution`
   - **URL Completa:** `http://host.docker.internal:5678/api/n8n-evolution`
4. Ativar o workflow

#### **Na Evolution Manager:**
1. Selecionar instância `agente_A`
2. Ir para seção **Webhooks**
3. Configurar:
   - **URL:** `http://host.docker.internal:5678/api/n8n-evolution`
   - **Eventos:** `MESSAGES_UPSERT`
   - **Base64:** Habilitado
   - **Status:** Ativo

### **4. ✅ Validar Configuração**
```bash
# Testar webhook N8N
curl -X POST http://localhost:5678/api/n8n-evolution \
  -H "Content-Type: application/json" \
  -d '{"test": "webhook_validation"}'
```

## 🚨 **TROUBLESHOOTING**

### **Interface Manager Não Carrega:**
1. Verificar se Evolution API está healthy:
   ```bash
   docker ps | findstr evolution
   ```
2. Verificar logs:
   ```bash
   docker logs evolution_aula --tail 20
   ```
3. Reiniciar container se necessário:
   ```bash
   docker restart evolution_aula
   ```

### **Webhook Não Funciona:**
1. Verificar conectividade:
   ```bash
   docker exec evolution_aula ping host.docker.internal
   ```
2. Testar N8N diretamente:
   ```bash
   curl http://localhost:5678/api/n8n-evolution
   ```

## 📚 **DOCUMENTAÇÃO OFICIAL**
- Evolution API: https://doc.evolution-api.com
- N8N Webhooks: https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.webhook/

## 🔄 **PRÓXIMOS PASSOS**
Após configuração manual:
1. Executar `.\Onboard-Workflow.ps1` para importar workflows
2. Testar integração completa
3. Monitorar logs para validação 