# Onboard-Workflow.ps1 - Sistema Modular de Onboarding de Workflows
# Versão: 2.1 - Versão Corrigida com Implementação Adequada das Melhorias de Robustez
# =====================================================
# PROPÓSITO: Este script configura workflows específicos SOBRE uma infraestrutura
# já provisionada, mantendo total separação entre infraestrutura e aplicação.
# =====================================================

[CmdletBinding()]
param(
    [Parameter(Mandatory=$true, HelpMessage="Nome do diretório do workflow dentro de 'workflows/'")]
    [string]$WorkflowName,
    
    [Parameter(HelpMessage="Nome do banco de dados alvo (padrão: n8n_fila)")]
    [string]$TargetDatabase = "n8n_fila",
    
    [Parameter(HelpMessage="Nome do contêiner PostgreSQL (padrão: postgres_aula)")]
    [string]$PostgresContainer = "postgres_aula"
)

# =====================================================
# IMPORTAR MÓDULO DE UTILITÁRIOS
# =====================================================

$utilityModuleName = "AutomationUtils"
$modulePath = Join-Path $PSScriptRoot ".\PowerShellModules\$utilityModuleName.psm1"

if (Test-Path $modulePath) {
    try {
        Import-Module $modulePath -Force -ErrorAction Stop
        Log-Event "Módulo '$utilityModuleName' carregado com sucesso." "WorkflowOnboarding" "SUCCESS"
    } catch {
        Write-Error "ERRO FATAL: Falha ao importar módulo '$utilityModuleName': $_"
        exit 1
    }
} else {
    Write-Error "ERRO FATAL: Módulo '$utilityModuleName' não encontrado em '$modulePath'"
    exit 1
}

# =====================================================
# FUNÇÕES AUXILIARES PURAS (SEM EFEITOS COLATERAIS)
# =====================================================

function Find-EnvironmentVariables {
    <#
    .SYNOPSIS
    Encontra placeholders de variáveis de ambiente em um objeto JSON de forma recursiva.
    
    .DESCRIPTION
    Esta função percorre recursivamente um objeto JSON e extrai todas as variáveis
    de ambiente no formato ${VARIABLE_NAME}, retornando uma lista única e ordenada.
    
    .PARAMETER Object
    O objeto a ser analisado (string, PSCustomObject, array)
    
    .PARAMETER Path
    Caminho atual na estrutura (usado para recursão)
    
    .RETURNS
    Array de strings contendo os nomes das variáveis encontradas
    #>
    param(
        [Parameter(Mandatory=$true)]
        $Object,
        
        [Parameter()]
        [string]$Path = ""
    )
    
    $foundVars = @()
    
    if ($Object -is [string] -and $Object -match '\$\{([^}]+)\}') {
        # Extrair todas as variáveis encontradas na string
        $matches = [regex]::Matches($Object, '\$\{([^}]+)\}')
        foreach ($match in $matches) {
            $foundVars += $match.Groups[1].Value
        }
    } elseif ($Object -is [System.Management.Automation.PSCustomObject]) {
        # Recursão para propriedades de objetos
        $Object.PSObject.Properties | ForEach-Object {
            $childVars = Find-EnvironmentVariables -Object $_.Value -Path "$Path.$($_.Name)"
            $foundVars += $childVars
        }
    } elseif ($Object -is [array]) {
        # Recursão para arrays
        $Object | ForEach-Object {
            $childVars = Find-EnvironmentVariables -Object $_ -Path $Path
            $foundVars += $childVars
        }
    }
    
    return $foundVars
}

function Invoke-DockerCommand {
    <#
    .SYNOPSIS
    Executa comandos Docker de forma segura sem usar Invoke-Expression.
    
    .DESCRIPTION
    Esta função executa comandos Docker passando argumentos de forma segura,
    usando & (call operator) para execução direta e confiável.
    
    .PARAMETER Arguments
    Array de argumentos para o comando docker
    
    .PARAMETER ErrorMessage
    Mensagem de erro personalizada em caso de falha
    
    .RETURNS
    Objeto com propriedades: Success (bool), Output (array), ExitCode (int)
    #>
    param(
        [Parameter(Mandatory=$true)]
        [string[]]$Arguments,
        
        [Parameter()]
        [string]$ErrorMessage = "Falha ao executar comando Docker"
    )
    
    try {
        # Usar o operador de chamada (&) para execução segura e direta
        $output = & docker $Arguments 2>&1
        $exitCode = $LASTEXITCODE
        
        # Separar stdout e stderr baseado no tipo de objeto
        $stdout = @()
        $stderr = @()
        
        foreach ($line in $output) {
            if ($line -is [System.Management.Automation.ErrorRecord]) {
                $stderr += $line.ToString()
            } else {
                $stdout += $line.ToString()
            }
        }
        
        return @{
            Success = ($exitCode -eq 0)
            Output = $output
            ExitCode = $exitCode
            StdOut = $stdout
            StdErr = $stderr
        }
    } catch {
        return @{
            Success = $false
            Output = @("Exceção ao executar docker: $($_.Exception.Message)")
            ExitCode = -1
            StdOut = @()
            StdErr = @("Exceção: $($_.Exception.Message)")
        }
    }
}

# =====================================================
# FUNÇÕES AUXILIARES PARA PROCESSAMENTO SQL ROBUSTO - VERSÃO MELHORADA
# =====================================================

function Read-SqlFileRobust-Enhanced {
    <#
    .SYNOPSIS
    Versão melhorada da leitura robusta de arquivos SQL com detecção avançada de encoding.
    
    .DESCRIPTION
    Esta função implementa uma estratégia mais agressiva para detectar o encoding correto,
    incluindo análise de BOM, validação de caracteres e fallback inteligente.
    
    .PARAMETER FilePath
    Caminho para o arquivo SQL a ser lido
    
    .RETURNS
    Hashtable com: Success (bool), Content (array), Encoding (string), ErrorMessage (string)
    #>
    param(
        [Parameter(Mandatory=$true)]
        [string]$FilePath
    )
    
    Log-Event "Iniciando leitura robusta melhorada do arquivo: $FilePath" "SQLProcessor" "DEBUG"
    
    # Verificar se o arquivo existe e é legível
    try {
        $fileInfo = Get-Item -Path $FilePath -ErrorAction Stop
        if ($fileInfo.Length -eq 0) {
            return @{
                Success = $false
                Content = @()
                Encoding = $null
                ErrorMessage = "Arquivo está vazio"
            }
        }
        Log-Event "Arquivo encontrado. Tamanho: $($fileInfo.Length) bytes" "SQLProcessor" "DEBUG"
    } catch {
        return @{
            Success = $false
            Content = @()
            Encoding = $null
            ErrorMessage = "Não foi possível acessar o arquivo: $($_.Exception.Message)"
        }
    }
    
    # Detectar BOM primeiro para otimizar tentativas de encoding
    $bomDetected = $null
    try {
        $firstBytes = [System.IO.File]::ReadAllBytes($FilePath) | Select-Object -First 4
        
        if ($firstBytes.Count -ge 3) {
            # UTF-8 BOM: EF BB BF
            if ($firstBytes[0] -eq 0xEF -and $firstBytes[1] -eq 0xBB -and $firstBytes[2] -eq 0xBF) {
                $bomDetected = "UTF8BOM"
            }
            # UTF-16 LE BOM: FF FE
            elseif ($firstBytes[0] -eq 0xFF -and $firstBytes[1] -eq 0xFE) {
                $bomDetected = "Unicode"
            }
            # UTF-16 BE BOM: FE FF
            elseif ($firstBytes[0] -eq 0xFE -and $firstBytes[1] -eq 0xFF) {
                $bomDetected = "BigEndianUnicode"
            }
        }
        
        if ($bomDetected) {
            Log-Event "BOM detectado: $bomDetected" "SQLProcessor" "DEBUG"
        }
    } catch {
        Log-Event "Falha na detecção de BOM: $($_.Exception.Message)" "SQLProcessor" "DEBUG"
    }
    
    # Lista otimizada de encodings baseada na detecção de BOM
    $encodingsToTry = if ($bomDetected) {
        @($bomDetected, 'UTF8', 'UTF8NoBOM', 'ASCII', 'Default', 'UTF32')
    } else {
        @('UTF8', 'UTF8NoBOM', 'UTF8BOM', 'Unicode', 'BigEndianUnicode', 'ASCII', 'Default', 'UTF32')
    }
    
    foreach ($encoding in $encodingsToTry) {
        try {
            Log-Event "Tentando encoding: $encoding" "SQLProcessor" "DEBUG"
            
            $content = Get-Content -Path $FilePath -Encoding $encoding -ErrorAction Stop
            
            if (-not $content -or $content.Count -eq 0) {
                Log-Event "Encoding $encoding resultou em conteúdo vazio" "SQLProcessor" "DEBUG"
                continue
            }
            
            # Validação avançada de caracteres para detectar encoding incorreto
            $isValidEncoding = $true
            $sampleLines = $content | Select-Object -First 10
            
            foreach ($line in $sampleLines) {
                if ([string]::IsNullOrEmpty($line)) { continue }
                
                # Verificar caracteres de controle inválidos
                if ($line -match '[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]') {
                    $isValidEncoding = $false
                    break
                }
                
                # Verificar caracteres de substituição Unicode (indicam encoding incorreto)
                if ($line -match '\uFFFD') {
                    $isValidEncoding = $false
                    break
                }
                
                # Verificar sequências suspeitas que indicam encoding incorreto
                if ($line -match '[\x80-\x9F]{2,}' -or $line -match 'Ã[\x80-\xBF]') {
                    $isValidEncoding = $false
                    break
                }
            }
            
            if ($isValidEncoding) {
                Log-Event "Arquivo lido com sucesso usando encoding: $encoding" "SQLProcessor" "SUCCESS"
                Log-Event "Total de linhas lidas: $($content.Count)" "SQLProcessor" "DEBUG"
                
                # Limpeza de BOM se presente
                if ($content.Count -gt 0 -and $content[0].Length -gt 0) {
                    $firstChar = $content[0][0]
                    $bomChars = @(65279, 65534, 65535, 8203, 8204, 8205) # Vários tipos de BOM e caracteres invisíveis
                    
                    if ($bomChars -contains [int]$firstChar) {
                        Log-Event "BOM/caractere invisível detectado e removido da primeira linha" "SQLProcessor" "DEBUG"
                        $content[0] = $content[0].Substring(1)
                    }
                }
                
                return @{
                    Success = $true
                    Content = $content
                    Encoding = $encoding
                    ErrorMessage = $null
                }
            }
            
            Log-Event "Encoding $encoding resultou em caracteres inválidos" "SQLProcessor" "DEBUG"
            
        } catch {
            Log-Event "Falha ao ler arquivo com encoding $encoding`: $($_.Exception.Message)" "SQLProcessor" "DEBUG"
            continue
        }
    }
    
    return @{
        Success = $false
        Content = @()
        Encoding = $null
        ErrorMessage = "Não foi possível ler o arquivo com nenhum encoding suportado"
    }
}

function Find-ConnectCommand-Enhanced {
    <#
    .SYNOPSIS
    Versão melhorada da detecção de comandos \connect com suporte a casos edge complexos.
    
    .DESCRIPTION
    Esta função usa múltiplas estratégias de regex e normalização para detectar
    comandos \connect em todas as suas variações possíveis.
    
    .PARAMETER Line
    Linha de texto para analisar
    
    .RETURNS
    Hashtable com: Found (bool), DatabaseName (string), CleanedLine (string), OriginalCommand (string)
    #>
    param(
        [Parameter(Mandatory=$true)]
        [AllowEmptyString()]
        [string]$Line
    )
    
    # Validação de entrada - retornar imediatamente se linha for vazia
    if ([string]::IsNullOrEmpty($Line)) {
        return @{
            Found = $false
            DatabaseName = $null
            CleanedLine = $Line
            OriginalCommand = $null
        }
    }
    
    # Normalizar linha removendo caracteres invisíveis e espaços especiais
    $normalizedLine = $Line -replace '[\u00A0\u2000-\u200F\u2028-\u202F\u205F\u3000]', ' '  # Normalizar espaços especiais
    $normalizedLine = $normalizedLine -replace '[\u200B-\u200D\uFEFF]', ''  # Remover caracteres de largura zero
    $normalizedLine = $normalizedLine.Trim()
    
    Log-Event "Analisando linha: '$normalizedLine'" "SQLProcessor" "DEBUG"
    
    # Múltiplos padrões regex para cobrir diferentes casos
    $patterns = @(
        # Padrão 1: \connect ou \c com aspas simples/duplas e suporte a caracteres especiais
        '^\s*\\(connect|c)\s+([''"]?)([^''";\s\r\n]+)\2\s*(?:--|#|/\*|$|;)',
        
        # Padrão 2: \connect com nome entre aspas contendo espaços ou caracteres especiais
        '^\s*\\(connect|c)\s+([''"])([^''"]+)\2\s*(?:--|#|/\*|$|;)',
        
        # Padrão 3: \connect sem aspas mas com underscores, hífens, pontos
        '^\s*\\(connect|c)\s+([a-zA-Z0-9_\-\.]+)\s*(?:--|#|/\*|$|;)',
        
        # Padrão 4: \connect com identificadores delimitados (PostgreSQL style)
        '^\s*\\(connect|c)\s+"([^"]+)"\s*(?:--|#|/\*|$|;)',
        
        # Padrão 5: Fallback para qualquer coisa após \connect até espaço ou fim de linha
        '^\s*\\(connect|c)\s+(\S+)'
    )
    
    foreach ($pattern in $patterns) {
        if ($normalizedLine -match $pattern) {
            $commandType = $Matches[1]  # 'connect' ou 'c'
            
            # Extrair nome do banco baseado no padrão que funcionou
            $dbName = switch ($pattern) {
                {$_ -match 'connect|c.*\([^''"][^''"]+\)'} { $Matches[3] }  # Padrões 1 e 2
                {$_ -match 'connect|c.*\([a-zA-Z0-9_\-\.]+'} { $Matches[2] }  # Padrão 3
                {$_ -match 'connect|c.*"[^"]+"'} { $Matches[2] }  # Padrão 4
                default { $Matches[2] }  # Padrão 5 (fallback)
            }
            
            # Limpar nome do banco removendo aspas e espaços
            $dbName = $dbName.Trim().Trim('"').Trim("'")
            
            # Validar nome do banco
            if ([string]::IsNullOrWhiteSpace($dbName)) {
                Log-Event "Nome de banco vazio detectado na linha: '$normalizedLine'" "SQLProcessor" "WARN"
                continue
            }
            
            # Validar caracteres permitidos em nomes de banco PostgreSQL
            if ($dbName -match '^[a-zA-Z_][a-zA-Z0-9_\-\.]*$' -or $dbName.Length -le 63) {
                $originalCommand = "\$commandType $dbName"
                Log-Event "Comando \connect detectado - Tipo: '$commandType', Banco: '$dbName'" "SQLProcessor" "INFO"
                
                return @{
                    Found = $true
                    DatabaseName = $dbName
                    CleanedLine = ""  # Linha será removida completamente
                    OriginalCommand = $originalCommand
                }
            } else {
                Log-Event "Nome de banco inválido detectado: '$dbName'" "SQLProcessor" "WARN"
            }
        }
    }
    
    # Nenhum comando \connect encontrado
    return @{
        Found = $false
        DatabaseName = $null
        CleanedLine = $Line
        OriginalCommand = $null
    }
}

function New-SecureTemporaryFile {
    <#
    .SYNOPSIS
    Cria um arquivo temporário com nome verdadeiramente único e verificação de existência.
    
    .DESCRIPTION
    Gera nomes únicos usando múltiplas fontes de entropia e verifica disponibilidade.
    
    .PARAMETER OriginalFileName
    Nome do arquivo original (usado como base)
    
    .PARAMETER MaxAttempts
    Número máximo de tentativas para gerar nome único
    
    .RETURNS
    Hashtable com: Success (bool), TempPath (string), ErrorMessage (string)
    #>
    param(
        [Parameter(Mandatory=$true)]
        [string]$OriginalFileName,
        
        [Parameter()]
        [int]$MaxAttempts = 10
    )
    
    $baseName = [System.IO.Path]::GetFileNameWithoutExtension($OriginalFileName)
    $extension = [System.IO.Path]::GetExtension($OriginalFileName)
    
    for ($attempt = 1; $attempt -le $MaxAttempts; $attempt++) {
        try {
            # Múltiplas fontes de entropia para garantir unicidade
            $timestamp = Get-Date -Format "yyyyMMdd_HHmmss_fff"
            $processId = $PID
            $randomGuid = [System.Guid]::NewGuid().ToString("N").Substring(0, 12)
            $rng = [System.Security.Cryptography.RandomNumberGenerator]::Create()
            $randomByteArray = New-Object byte[] 6
            $rng.GetBytes($randomByteArray)
            $randomBytes = [System.Convert]::ToBase64String($randomByteArray).Replace('/', '_').Replace('+', '-')
            $rng.Dispose()
            
            $uniqueName = "$($baseName)_$($timestamp)_$($processId)_$($randomGuid)_$($randomBytes)$($extension)"
            $tempPath = "/tmp/$uniqueName"
            
            # Verificar se o arquivo já existe no contêiner (improvável, mas seguro)
            $checkArgs = @("exec", $PostgresContainer, "test", "-f", $tempPath)
            $checkResult = Invoke-DockerCommand -Arguments $checkArgs -ErrorMessage "Falha ao verificar arquivo temporário"
            
            if (-not $checkResult.Success) {
                # Arquivo não existe - perfeito!
                Log-Event "Arquivo temporário único gerado (tentativa $attempt): $tempPath" "SQLProcessor" "DEBUG"
                return @{
                    Success = $true
                    TempPath = $tempPath
                    ErrorMessage = $null
                }
            }
            
            Log-Event "Arquivo temporário já existe (tentativa $attempt): $tempPath" "SQLProcessor" "DEBUG"
        } catch {
            Log-Event "Erro na tentativa $attempt de gerar arquivo temporário: $($_.Exception.Message)" "SQLProcessor" "DEBUG"
        }
    }
    
    return @{
        Success = $false
        TempPath = $null
        ErrorMessage = "Não foi possível gerar nome de arquivo temporário único após $MaxAttempts tentativas"
    }
}

function Invoke-CleanupManager {
    <#
    .SYNOPSIS
    Gerenciador centralizado de limpeza de recursos temporários.
    
    .DESCRIPTION
    Garante limpeza completa de todos os recursos temporários, incluindo arquivos locais e remotos.
    
    .PARAMETER LocalFiles
    Array de caminhos de arquivos locais para limpar
    
    .PARAMETER RemoteFiles
    Array de caminhos de arquivos remotos (no contêiner) para limpar
    
    .PARAMETER PostgresContainer
    Nome do contêiner PostgreSQL
    #>
    param(
        [Parameter()]
        [string[]]$LocalFiles = @(),
        
        [Parameter()]
        [string[]]$RemoteFiles = @(),
        
        [Parameter()]
        [string]$PostgresContainer
    )
    
    $cleanupErrors = @()
    
    # Limpar arquivos locais
    foreach ($localFile in $LocalFiles) {
        if ([string]::IsNullOrWhiteSpace($localFile)) { continue }
        
        try {
            if (Test-Path $localFile) {
                Remove-Item $localFile -Force -ErrorAction Stop
                Log-Event "Arquivo local removido: $localFile" "CleanupManager" "DEBUG"
            }
        } catch {
            $errorMsg = "Falha ao remover arquivo local '$localFile': $($_.Exception.Message)"
            $cleanupErrors += $errorMsg
            Log-Event $errorMsg "CleanupManager" "WARN"
        }
    }
    
    # Limpar arquivos remotos (no contêiner)
    if ($PostgresContainer -and $RemoteFiles.Count -gt 0) {
        foreach ($remoteFile in $RemoteFiles) {
            if ([string]::IsNullOrWhiteSpace($remoteFile)) { continue }
            
            try {
                $cleanArgs = @("exec", $PostgresContainer, "rm", "-f", $remoteFile)
                $cleanResult = Invoke-DockerCommand -Arguments $cleanArgs -ErrorMessage "Falha ao limpar arquivo remoto"
                
                if ($cleanResult.Success) {
                    Log-Event "Arquivo remoto removido: $remoteFile" "CleanupManager" "DEBUG"
                } else {
                    $errorMsg = "Falha ao remover arquivo remoto '$remoteFile': $($cleanResult.StdErr -join '; ')"
                    $cleanupErrors += $errorMsg
                    Log-Event $errorMsg "CleanupManager" "WARN"
                }
            } catch {
                $errorMsg = "Exceção ao remover arquivo remoto '$remoteFile': $($_.Exception.Message)"
                $cleanupErrors += $errorMsg
                Log-Event $errorMsg "CleanupManager" "WARN"
            }
        }
    }
    
    if ($cleanupErrors.Count -gt 0) {
        Log-Event "Limpeza concluída com $($cleanupErrors.Count) avisos" "CleanupManager" "WARN"
    } else {
        Log-Event "Limpeza concluída com sucesso" "CleanupManager" "SUCCESS"
    }
}

# =====================================================
# FUNÇÕES AUXILIARES PARA PROCESSAMENTO SQL ROBUSTO
# =====================================================

function Read-SqlFileRobust {
    <#
    .SYNOPSIS
    Lê um arquivo SQL de forma robusta, lidando com diferentes encodings e BOMs.
    
    .DESCRIPTION
    Esta função tenta múltiplos encodings para garantir leitura correta do arquivo,
    removendo BOMs automaticamente e fornecendo logs detalhados.
    
    .PARAMETER FilePath
    Caminho para o arquivo SQL a ser lido
    
    .RETURNS
    Array de strings contendo as linhas do arquivo, ou $null em caso de erro
    #>
    param(
        [Parameter(Mandatory=$true)]
        [string]$FilePath
    )
    
    # Lista de encodings para tentar em ordem de prioridade
    $encodingsToTry = @(
        'UTF8',           # Mais comum em arquivos modernos
        'UTF8BOM',        # UTF-8 com BOM
        'UTF8NoBOM',      # UTF-8 sem BOM (PowerShell 6+)
        'Unicode',        # UTF-16 LE
        'BigEndianUnicode', # UTF-16 BE
        'UTF32',          # UTF-32
        'ASCII',          # ASCII puro
        'Default'         # Encoding padrão do sistema (ANSI)
    )
    
    Log-Event "Iniciando leitura robusta do arquivo: $FilePath" "SQLProcessor" "DEBUG"
    
    foreach ($encoding in $encodingsToTry) {
        try {
            Log-Event "Tentando encoding: $encoding" "SQLProcessor" "DEBUG"
            
            # Tentar ler com o encoding atual
            $content = Get-Content -Path $FilePath -Encoding $encoding -ErrorAction Stop
            
            # Verificar se o conteúdo foi lido corretamente
            if ($content -and $content.Count -gt 0) {
                # Verificar se há caracteres estranhos que indicam encoding incorreto
                $hasStrangeChars = $false
                foreach ($line in ($content | Select-Object -First 5)) {
                    if ($line -match '[\x00-\x08\x0B\x0C\x0E-\x1F\x7F-\x9F]' -or 
                        $line -match '' -or 
                        $line -match '\uFFFD') {
                        $hasStrangeChars = $true
                        break
                    }
                }
                
                if (-not $hasStrangeChars) {
                    Log-Event "Arquivo lido com sucesso usando encoding: $encoding" "SQLProcessor" "SUCCESS"
                    Log-Event "Total de linhas lidas: $($content.Count)" "SQLProcessor" "DEBUG"
                    
                    # Remover BOM se presente (caracteres invisíveis no início)
                    if ($content.Count -gt 0 -and $content[0].Length -gt 0) {
                        $firstChar = $content[0][0]
                        if ([int]$firstChar -eq 65279 -or [int]$firstChar -eq 65534 -or [int]$firstChar -eq 65535) {
                            Log-Event "BOM detectado e removido da primeira linha" "SQLProcessor" "DEBUG"
                            $content[0] = $content[0].Substring(1)
                        }
                    }
                    
                    return $content
                }
            }
            
            Log-Event "Encoding $encoding resultou em conteúdo inválido ou vazio" "SQLProcessor" "DEBUG"
            
        } catch {
                                Log-Event "Falha ao ler arquivo com encoding $encoding`: $($_.Exception.Message)" "SQLProcessor" "DEBUG"
            continue
        }
    }
    
    Log-Event "ERRO: Não foi possível ler o arquivo $FilePath com nenhum encoding testado" "SQLProcessor" "ERROR"
    return $null
}

function Find-ConnectCommand {
    <#
    .SYNOPSIS
    Detecta comandos \connect ou \c em uma linha SQL de forma robusta.
    
    .DESCRIPTION
    Esta função usa regex para detectar variações do comando \connect,
    incluindo espaços, tabulações, aspas e comentários.
    
    .PARAMETER Line
    Linha de texto para analisar
    
    .RETURNS
    Hashtable com: Found (bool), DatabaseName (string), CleanedLine (string)
    #>
    param(
        [Parameter(Mandatory=$true)]
        [string]$Line
    )
    
    # Remover espaços em branco no início e fim
    $trimmedLine = $Line.Trim()
    
    # Regex robusta para detectar \connect ou \c com variações
    # Padrões suportados:
    # \connect dbname
    # \c dbname  
    # \connect "db-name"
    # \c 'db name'
    # \connect dbname -- comentário
    # Com espaços/tabs variáveis
    $connectPattern = '^\s*\\(connect|c)\s+([''"]?)([^''";\s]+)\2\s*(?:--|$|;)'
    
    if ($trimmedLine -match $connectPattern) {
        $dbName = $Matches[3]
        
        Log-Event "Comando \connect detectado - Banco: '$dbName'" "SQLProcessor" "INFO"
        
        return @{
            Found = $true
            DatabaseName = $dbName
            CleanedLine = ""  # Linha será removida completamente
        }
    }
    
    # Se não é comando \connect, retornar linha original
    return @{
        Found = $false
        DatabaseName = $null
        CleanedLine = $Line
    }
}

function Ensure-DatabaseExists {
    <#
    .SYNOPSIS
    Garante que um banco de dados existe, criando-o se necessário.
    
    .DESCRIPTION
    Verifica se o banco existe e o cria caso não exista, usando comandos Docker seguros.
    
    .PARAMETER DatabaseName
    Nome do banco de dados a verificar/criar
    
    .PARAMETER PostgresContainer
    Nome do contêiner PostgreSQL
    
    .RETURNS
    $true se o banco existe ou foi criado com sucesso, $false caso contrário
    #>
    param(
        [Parameter(Mandatory=$true)]
        [string]$DatabaseName,
        
        [Parameter(Mandatory=$true)]
        [string]$PostgresContainer
    )
    
    Log-Event "Verificando existência do banco: '$DatabaseName'" "SQLProcessor" "INFO"
    
    # Primeiro, tentar conectar ao banco para ver se existe
    $testArgs = @("exec", $PostgresContainer, "psql", "-U", "postgres", "-d", $DatabaseName, "-c", "SELECT 1")
    $testResult = Invoke-DockerCommand -Arguments $testArgs -ErrorMessage "Falha ao testar banco"
    
    if ($testResult.Success) {
        Log-Event "Banco '$DatabaseName' já existe e está acessível" "SQLProcessor" "SUCCESS"
        return $true
    }
    
    # Banco não existe - tentar criar
    Log-Event "Banco '$DatabaseName' não encontrado. Tentando criar..." "SQLProcessor" "WARN"
    
    $createArgs = @("exec", $PostgresContainer, "psql", "-U", "postgres", "-c", "CREATE DATABASE `"$DatabaseName`"")
    $createResult = Invoke-DockerCommand -Arguments $createArgs -ErrorMessage "Falha ao criar banco"
    
    if ($createResult.Success) {
        Log-Event "Banco '$DatabaseName' criado com sucesso" "SQLProcessor" "SUCCESS"
        return $true
    } else {
        # Verificar se a falha foi porque o banco já existe
        $errorOutput = $createResult.StdErr -join " "
        if ($errorOutput -match "already exists") {
            Log-Event "Banco '$DatabaseName' já existia (erro de criação ignorado)" "SQLProcessor" "INFO"
            return $true
        }
        
        Log-Event "ERRO: Não foi possível criar banco '$DatabaseName': $errorOutput" "SQLProcessor" "ERROR"
        return $false
    }
}

function New-UniqueTemporaryFile {
    <#
    .SYNOPSIS
    Cria um nome de arquivo temporário único para evitar colisões.
    
    .DESCRIPTION
    Gera um nome de arquivo temporário usando timestamp e GUID para garantir unicidade.
    
    .PARAMETER OriginalFileName
    Nome do arquivo original (usado como base)
    
    .RETURNS
    String com o caminho do arquivo temporário
    #>
    param(
        [Parameter(Mandatory=$true)]
        [string]$OriginalFileName
    )
    
    $timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
    $guid = [System.Guid]::NewGuid().ToString("N").Substring(0, 8)
    $baseName = [System.IO.Path]::GetFileNameWithoutExtension($OriginalFileName)
    $extension = [System.IO.Path]::GetExtension($OriginalFileName)
    
            $tempFileName = "$($baseName)_$($timestamp)_$($guid)$($extension)"
    $tempPath = "/tmp/$tempFileName"
    
    Log-Event "Arquivo temporário gerado: $tempPath" "SQLProcessor" "DEBUG"
    return $tempPath
}

# =====================================================
# CONFIGURAÇÃO E VALIDAÇÃO INICIAL
# =====================================================

Write-Host-Color "`n╔══════════════════════════════════════════════════════════╗" "Cyan"
Write-Host-Color "║          WORKFLOW ONBOARDING SYSTEM v2.1                 ║" "Cyan"
Write-Host-Color "║    Configuração Robusta de Workflows Específicos         ║" "Cyan"
Write-Host-Color "╚══════════════════════════════════════════════════════════╝" "Cyan"

Log-Event "Iniciando onboarding do workflow: $WorkflowName" "WorkflowOnboarding" "INFO"
Log-Event "Banco de dados alvo: $TargetDatabase" "WorkflowOnboarding" "INFO"
Log-Event "Contêiner PostgreSQL: $PostgresContainer" "WorkflowOnboarding" "INFO"

# =====================================================
# DESCOBERTA RIGOROSA DE WORKFLOWS (DESAMBIGUAÇÃO)
# =====================================================

$workflowBasePath = Join-Path $PSScriptRoot "workflows"

if (-not (Test-Path $workflowBasePath)) {
    Log-Event "Diretório de workflows não encontrado: $workflowBasePath" "WorkflowDiscovery" "ERROR"
    Write-Host-Color "`n❌ ERRO FATAL: Diretório 'workflows' não encontrado." "Red"
    Write-Host-Color "Certifique-se de que o script está sendo executado no diretório raiz do projeto." "Yellow"
    exit 1
}

Log-Event "Iniciando descoberta rigorosa de workflows em: $workflowBasePath" "WorkflowDiscovery" "INFO"

# Buscar todos os workflows com o nome especificado
$matchingWorkflows = Get-ChildItem -Path $workflowBasePath -Directory -Recurse | 
    Where-Object { $_.Name -eq $WorkflowName }

$workflowCount = ($matchingWorkflows | Measure-Object).Count

if ($workflowCount -eq 0) {
    # NENHUM workflow encontrado - listar disponíveis
    Log-Event "Nenhum workflow com nome '$WorkflowName' encontrado" "WorkflowDiscovery" "ERROR"
    Write-Host-Color "`n❌ ERRO: O workflow '$WorkflowName' não foi encontrado." "Red"
    Write-Host-Color "`n📋 Workflows disponíveis:" "Yellow"
    
    $availableWorkflows = Get-ChildItem -Path $workflowBasePath -Directory -Recurse | 
        Where-Object { $_.Parent.Name -ne "workflows" }
    
    if ($availableWorkflows.Count -eq 0) {
        Write-Host-Color "   Nenhum workflow encontrado no diretório workflows/" "Gray"
    } else {
        $availableWorkflows | ForEach-Object {
            $relativePath = $_.FullName.Replace([regex]::Escape($workflowBasePath), "").TrimStart('\', '/')
            Write-Host "   • $($_.Name)" -ForegroundColor White
            Write-Host "     Localização: $relativePath" -ForegroundColor Gray
        }
    }
    
    exit 1
} elseif ($workflowCount -gt 1) {
    # MÚLTIPLOS workflows encontrados - ambiguidade detectada
    Log-Event "Múltiplos workflows com nome '$WorkflowName' encontrados ($workflowCount)" "WorkflowDiscovery" "ERROR"
    Write-Host-Color "`n❌ ERRO: Ambiguidade detectada! Múltiplos workflows com o nome '$WorkflowName' foram encontrados." "Red"
    Write-Host-Color "`n📍 Localizações conflitantes:" "Yellow"
    
    $matchingWorkflows | ForEach-Object {
        $relativePath = $_.FullName.Replace([regex]::Escape($workflowBasePath), "").TrimStart('\', '/')
        Write-Host "   • $relativePath" -ForegroundColor White
    }
    
    Write-Host-Color "`n💡 Solução: Seja mais específico ou renomeie um dos workflows para evitar conflitos." "Cyan"
    Write-Host-Color "   Exemplo: Mova um dos workflows para um subdiretório diferente." "Gray"
    
    exit 1
} else {
    # EXATAMENTE UM workflow encontrado - sucesso!
    $workflowPath = $matchingWorkflows[0]
    $workflowFullPath = $workflowPath.FullName
    $relativePath = $workflowFullPath.Replace([regex]::Escape($workflowBasePath), "").TrimStart('\', '/')
    
    Log-Event "Workflow único encontrado com sucesso: $relativePath" "WorkflowDiscovery" "SUCCESS"
    Write-Host-Color "`n✅ Workflow encontrado: $($workflowPath.Name)" "Green"
    Write-Host-Color "   Localização: $relativePath" "Gray"
}

# =====================================================
# VERIFICAÇÃO DE PRÉ-REQUISITOS
# =====================================================

Write-Host-Color "`n📋 Verificando pré-requisitos..." "Yellow"

# Verificar se o PostgreSQL está rodando
if (-not (Test-ContainerRunning $PostgresContainer)) {
    Log-Event "Contêiner PostgreSQL '$PostgresContainer' não está rodando" "Prerequisites" "ERROR"
    Write-Host-Color "❌ ERRO: O contêiner PostgreSQL '$PostgresContainer' não está rodando." "Red"
    Write-Host-Color "Execute primeiro o script Start-Environment.ps1 para iniciar a infraestrutura." "Yellow"
    exit 1
}

Log-Event "✓ Contêiner PostgreSQL está rodando" "Prerequisites" "SUCCESS"

# Verificar conectividade com o banco usando comandos Docker seguros
Log-Event "Testando conectividade com banco de dados '$TargetDatabase'..." "Prerequisites" "INFO"

try {
    # Teste de conectividade inicial
    $testArgs = @("exec", $PostgresContainer, "psql", "-U", "postgres", "-d", $TargetDatabase, "-c", "SELECT 1")
    $testResult = Invoke-DockerCommand -Arguments $testArgs -ErrorMessage "Falha ao testar conectividade do banco"
    
    if (-not $testResult.Success) {
        # Banco pode não existir - tentar criar
        Log-Event "Banco '$TargetDatabase' não encontrado. Tentando criar..." "Prerequisites" "WARN"
        
        $createArgs = @("exec", $PostgresContainer, "psql", "-U", "postgres", "-c", "CREATE DATABASE `"$TargetDatabase`"")
        $createResult = Invoke-DockerCommand -Arguments $createArgs -ErrorMessage "Falha ao criar banco de dados"
        
        if (-not $createResult.Success) {
            # Se falhar ao criar, pode ser que já existe - testar novamente
            Log-Event "Falha ao criar banco (pode já existir). Testando conectividade novamente..." "Prerequisites" "INFO"
            $finalTestResult = Invoke-DockerCommand -Arguments $testArgs -ErrorMessage "Falha final no teste de conectividade"
            
            if (-not $finalTestResult.Success) {
                Log-Event "Erro detalhado do PostgreSQL: $($finalTestResult.StdErr -join '; ')" "Prerequisites" "ERROR"
                throw "Não foi possível conectar ao banco '$TargetDatabase'. Erros: $($finalTestResult.StdErr -join '; ')"
            }
        } else {
            Log-Event "Banco '$TargetDatabase' criado com sucesso" "Prerequisites" "SUCCESS"
        }
    }
    
    Log-Event "✓ Conectividade com banco de dados verificada" "Prerequisites" "SUCCESS"
} catch {
    Log-Event "Erro ao verificar banco de dados: $_" "Prerequisites" "ERROR"
    Write-Host-Color "❌ ERRO: Não foi possível conectar ao banco de dados '$TargetDatabase'." "Red"
    Write-Host-Color "   Detalhes: $_" "Red"
    exit 1
}

Write-Host-Color "✅ Todos os pré-requisitos atendidos!" "Green"

# =====================================================
# APLICAÇÃO DO ESQUEMA SQL - VERSÃO ULTRA-ROBUSTA
# =====================================================

$sqlPath = Join-Path $workflowFullPath "sql"
if (Test-Path $sqlPath) {
    Write-Host-Color "`n🗄️  Aplicando esquema SQL do workflow..." "Yellow"
    Log-Event "Diretório SQL encontrado: $sqlPath" "SQLSchema" "INFO"
    
    # Listar e ordenar arquivos SQL
    $sqlFiles = Get-ChildItem -Path $sqlPath -Filter "*.sql" | Sort-Object Name
    
    if ($sqlFiles.Count -eq 0) {
        Log-Event "Nenhum arquivo SQL encontrado em $sqlPath" "SQLSchema" "WARN"
        Write-Host-Color "⚠️  Nenhum arquivo SQL encontrado para aplicar." "Yellow"
    } else {
        Log-Event "Encontrados $($sqlFiles.Count) arquivos SQL para aplicar" "SQLSchema" "INFO"
        Write-Host-Color "📑 Encontrados $($sqlFiles.Count) arquivos SQL:" "Cyan"
        
        $totalFiles = $sqlFiles.Count
        $currentFile = 0
        
        foreach ($sqlFile in $sqlFiles) {
            $currentFile++
            $progressPercent = [math]::Round(($currentFile / $totalFiles) * 100)
            
            Write-Host ""
            Write-Host-Color "[$currentFile/$totalFiles] Processando: $($sqlFile.Name)" "White"
            Write-Progress -Activity "Aplicando esquema SQL" -Status "$($sqlFile.Name)" -PercentComplete $progressPercent
            
            # Variáveis para controle de limpeza
            $tempPath = $null
            $targetDatabase = $TargetDatabase  # Banco padrão
            $processedContent = @()
            
            try {
                                # ETAPA 1: Leitura robusta e validação do arquivo
                Log-Event "Iniciando processamento robusto melhorado de: $($sqlFile.Name)" "SQLProcessor" "INFO"
                
                $sqlReadResult = Read-SqlFileRobust-Enhanced -FilePath $sqlFile.FullName
                if (-not $sqlReadResult.Success) {
                    throw "Falha na leitura do arquivo $($sqlFile.Name): $($sqlReadResult.ErrorMessage)"
                }
                
                $sqlContent = $sqlReadResult
                Log-Event "Arquivo lido com sucesso usando encoding: $($sqlContent.Encoding). Processando $($sqlContent.Content.Count) linhas..." "SQLProcessor" "INFO"
                
                # ETAPA 2: Processamento linha por linha para detectar \connect
                $connectFound = $false
                $lineNumber = 0
                
                foreach ($line in $sqlContent.Content) {
                    $lineNumber++
                    
                    # Pular linhas vazias ou nulas
                    if ([string]::IsNullOrEmpty($line)) {
                        $processedContent += $line
                        continue
                    }
                    
                    # Detectar comando \connect de forma robusta
                    $connectResult = Find-ConnectCommand-Enhanced -Line $line
                    
                    if ($connectResult.Found) {
                        $connectFound = $true
                        $targetDatabase = $connectResult.DatabaseName
                        
                        Log-Event "Linha $lineNumber`: Comando \connect detectado para banco '$targetDatabase'" "SQLProcessor" "INFO"
                        
                        # ETAPA 3: Garantir que o banco de destino existe
                        if (-not (Ensure-DatabaseExists -DatabaseName $targetDatabase -PostgresContainer $PostgresContainer)) {
                            throw "Não foi possível criar ou acessar o banco '$targetDatabase' especificado na linha $lineNumber"
                        }
                        
                        # Não adicionar esta linha ao conteúdo processado (será removida)
                        Log-Event "Linha $lineNumber removida: comando \connect" "SQLProcessor" "DEBUG"
                    } else {
                        # Linha normal - adicionar ao conteúdo processado
                        $processedContent += $connectResult.CleanedLine
                    }
                }
                
                if ($connectFound) {
                    Log-Event "Arquivo processado: comando \connect removido, banco alvo: '$targetDatabase'" "SQLProcessor" "SUCCESS"
                } else {
                    Log-Event "Nenhum comando \connect encontrado. Usando banco padrão: '$targetDatabase'" "SQLProcessor" "INFO"
                }
                
                # ETAPA 4: Criar arquivo temporário com nome verdadeiramente único
                $tempFileResult = New-SecureTemporaryFile -OriginalFileName $sqlFile.Name
                if (-not $tempFileResult.Success) {
                    throw $tempFileResult.ErrorMessage
                }
                $tempPath = $tempFileResult.TempPath
                
                # ETAPA 5: Criar e processar arquivo temporário local com validação
                $localTempFile = [System.IO.Path]::GetTempFileName()
                $localFilesToClean = @($localTempFile)
                
                try {
                    # Escrever conteúdo processado no arquivo temporário local com validação
                    $processedContent | Out-File -FilePath $localTempFile -Encoding UTF8 -ErrorAction Stop
                    Log-Event "Arquivo temporário local criado: $localTempFile" "SQLProcessor" "DEBUG"
                    
                    # Validar que o arquivo foi criado corretamente
                    $localFileInfo = Get-Item -Path $localTempFile -ErrorAction Stop
                    if ($localFileInfo.Length -eq 0 -and $processedContent.Count -gt 0) {
                        throw "Arquivo temporário local está vazio apesar de haver conteúdo para escrever"
                    }
                    
                    Log-Event "Arquivo local validado. Tamanho: $($localFileInfo.Length) bytes" "SQLProcessor" "DEBUG"
                    
                    # ETAPA 6: Copiar arquivo processado para o contêiner com verificação
                    $copyArgs = @("cp", $localTempFile, "$($PostgresContainer):$($tempPath)")
                    Log-Event "Copiando arquivo processado para contêiner: $tempPath" "SQLProcessor" "DEBUG"
                    
                    $copyResult = Invoke-DockerCommand -Arguments $copyArgs -ErrorMessage "Falha ao copiar arquivo processado"
                    if (-not $copyResult.Success) {
                        throw "Falha ao copiar arquivo processado '$($sqlFile.Name)': $($copyResult.StdErr -join '; ')"
                    }
                    
                    # Verificar que o arquivo foi copiado corretamente no contêiner
                    $verifyArgs = @("exec", $PostgresContainer, "test", "-f", $tempPath)
                    $verifyResult = Invoke-DockerCommand -Arguments $verifyArgs -ErrorMessage "Falha ao verificar arquivo copiado"
                    if (-not $verifyResult.Success) {
                        throw "Arquivo não foi copiado corretamente para o contêiner: $tempPath"
                    }
                    
                    Log-Event "Arquivo copiado e verificado com sucesso no contêiner" "SQLProcessor" "SUCCESS"
                    
                } catch {
                    # Em caso de erro, garantir limpeza imediata dos arquivos locais
                    Invoke-CleanupManager -LocalFiles $localFilesToClean
                    throw
                }
                
                # ETAPA 7: Executar o script SQL processado no banco correto com análise robusta
                $execArgs = @("exec", $PostgresContainer, "psql", "-U", "postgres", "-d", $targetDatabase, "-f", $tempPath, "-v", "ON_ERROR_STOP=1")
                Log-Event "Executando script SQL processado no banco '$targetDatabase' com ON_ERROR_STOP" "SQLProcessor" "INFO"
                
                $execResult = Invoke-DockerCommand -Arguments $execArgs -ErrorMessage "Falha ao executar script SQL processado"
                
                # ETAPA 8: Análise avançada e categorização de resultados
                $notices = @()
                $errors = @()
                $warnings = @()
                $sqlErrors = @()
                $connectionErrors = @()
                $syntaxErrors = @()
                
                # Processar toda a saída para categorização inteligente
                $allOutput = @()
                if ($execResult.StdOut) { $allOutput += $execResult.StdOut }
                if ($execResult.StdErr) { $allOutput += $execResult.StdErr }
                
                # Análise inteligente de erro: verificar se há erros reais além de notices
                $hasRealErrors = $false
                foreach ($line in $allOutput) {
                    $trimmedLine = $line.Trim()
                    if ($trimmedLine -match "^ERROR:|^FATAL:|^PANIC:" -and $trimmedLine -notmatch "NOTICE:") {
                        $hasRealErrors = $true
                        break
                    }
                }
                
                # Determinar se houve erro real (não apenas notices informativos)
                $errorFound = $hasRealErrors -or ($execResult.ExitCode -ne 0 -and $allOutput | Where-Object { $_ -match "^ERROR:|^FATAL:" })
                
                foreach ($line in $allOutput) {
                    $trimmedLine = $line.Trim()
                    if ([string]::IsNullOrWhiteSpace($trimmedLine)) { continue }
                    
                    # Categorizar mensagens por tipo e severidade
                    if ($trimmedLine -match "^ERROR:|^FATAL:|^PANIC:") {
                        $errors += $trimmedLine
                        
                        # Sub-categorizar erros específicos
                        if ($trimmedLine -match "syntax error|ERROR.*syntax") {
                            $syntaxErrors += $trimmedLine
                        } elseif ($trimmedLine -match "connection|connect|authentication|permission denied") {
                            $connectionErrors += $trimmedLine
                        } else {
                            $sqlErrors += $trimmedLine
                        }
                    } elseif ($trimmedLine -match "^WARNING:") {
                        $warnings += $trimmedLine
                    } elseif ($trimmedLine -match "^NOTICE:|^INFO:") {
                        $notices += $trimmedLine
                    } elseif ($errorFound -and $trimmedLine -notmatch "NOTICE:.*already exists|NOTICE:.*skipping") {
                        # Se houve erro geral, incluir outras linhas como contexto, exceto notices informativos
                        $errors += $trimmedLine
                    }
                }
                
                # Adicionar informações de contexto se houve falha
                if ($errorFound) {
                    if (-not $errors -or $errors.Count -eq 0) {
                        $errors += "Comando psql falhou com código de saída $($execResult.ExitCode) mas sem mensagem de erro específica"
                    }
                    
                    # Log detalhado para auditoria
                    Log-Event "Execução SQL falhou. Código de saída: $($execResult.ExitCode)" "SQLProcessor" "ERROR"
                    Log-Event "Arquivo SQL: $($sqlFile.Name), Banco: $targetDatabase" "SQLProcessor" "ERROR"
                    Log-Event "Comando executado: $($execArgs -join ' ')" "SQLProcessor" "ERROR"
                }
                
                # ETAPA 9: Tratamento inteligente de erros e exibição de resultados
                if ($errorFound) {
                    Log-Event "Erros encontrados ao executar $($sqlFile.Name) no banco '$targetDatabase'" "SQLSchema" "ERROR"
                    Write-Host-Color "   ❌ Execução falhou com erros:" "Red"
                    
                    # Exibir erros categorizados para melhor diagnóstico
                    if ($syntaxErrors.Count -gt 0) {
                        Write-Host-Color "   🔍 Erros de Sintaxe SQL:" "Red"
                        $syntaxErrors | Select-Object -First 3 | ForEach-Object { 
                            Write-Host "      $_" -ForegroundColor Red 
                            Log-Event "SQL Syntax Error: $_" "SQLSchema" "ERROR"
                        }
                        if ($syntaxErrors.Count -gt 3) {
                            Write-Host "      ... e mais $($syntaxErrors.Count - 3) erros de sintaxe" -ForegroundColor Red
                        }
                    }
                    
                    if ($connectionErrors.Count -gt 0) {
                        Write-Host-Color "   🔌 Erros de Conexão/Autenticação:" "Red"
                        $connectionErrors | Select-Object -First 2 | ForEach-Object { 
                            Write-Host "      $_" -ForegroundColor Red 
                            Log-Event "SQL Connection Error: $_" "SQLSchema" "ERROR"
                        }
                        if ($connectionErrors.Count -gt 2) {
                            Write-Host "      ... e mais $($connectionErrors.Count - 2) erros de conexão" -ForegroundColor Red
                        }
                    }
                    
                    if ($sqlErrors.Count -gt 0) {
                        Write-Host-Color "   ⚡ Outros Erros SQL:" "Red"
                        $sqlErrors | Select-Object -First 3 | ForEach-Object { 
                            Write-Host "      $_" -ForegroundColor Red 
                            Log-Event "SQL Error: $_" "SQLSchema" "ERROR"
                        }
                        if ($sqlErrors.Count -gt 3) {
                            Write-Host "      ... e mais $($sqlErrors.Count - 3) erros SQL" -ForegroundColor Red
                        }
                    }
                    
                    # Exibir outros erros não categorizados
                    $otherErrors = $errors | Where-Object { 
                        $_ -notin $syntaxErrors -and $_ -notin $connectionErrors -and $_ -notin $sqlErrors 
                    }
                    if ($otherErrors.Count -gt 0) {
                        Write-Host-Color "   ❗ Erros Adicionais:" "Red"
                        $otherErrors | Select-Object -First 2 | ForEach-Object { 
                            Write-Host "      $_" -ForegroundColor Red 
                            Log-Event "Other Error: $_" "SQLSchema" "ERROR"
                        }
                        if ($otherErrors.Count -gt 2) {
                            Write-Host "      ... e mais $($otherErrors.Count - 2) erros" -ForegroundColor Red
                        }
                    }
                    
                    # Exibir warnings contextuais se existirem
                    if ($warnings.Count -gt 0) {
                        Write-Host-Color "   ⚠️  Warnings Relacionados:" "Yellow"
                        $warnings | Select-Object -First 2 | ForEach-Object { Write-Host "      $_" -ForegroundColor Yellow }
                        if ($warnings.Count -gt 2) {
                            Write-Host "      ... e mais $($warnings.Count - 2) warnings" -ForegroundColor Yellow
                        }
                    }
                    
                    # Fornecer sugestões de solução baseadas no tipo de erro
                    Write-Host-Color "`n   💡 Sugestões de Solução:" "Cyan"
                    if ($syntaxErrors.Count -gt 0) {
                        Write-Host "      • Verifique a sintaxe SQL no arquivo $($sqlFile.Name)" -ForegroundColor Cyan
                        Write-Host "      • Confirme compatibilidade com a versão do PostgreSQL" -ForegroundColor Cyan
                    }
                    if ($connectionErrors.Count -gt 0) {
                        Write-Host "      • Verifique se o banco '$targetDatabase' existe e está acessível" -ForegroundColor Cyan
                        Write-Host "      • Confirme as credenciais e permissões do usuário postgres" -ForegroundColor Cyan
                    }
                    if ($sqlErrors.Count -gt 0) {
                        Write-Host "      • Revise as dependências e ordem de execução dos scripts" -ForegroundColor Cyan
                        Write-Host "      • Verifique se objetos referenciados existem" -ForegroundColor Cyan
                    }
                    
                    # Decisão inteligente sobre continuar ou parar
                    if ($syntaxErrors.Count -gt 0 -or $connectionErrors.Count -gt 0) {
                        Write-Host-Color "`n   🛑 Erros críticos detectados. Recomenda-se corrigir antes de continuar." "Red"
                    }
                    
                    $continue = Read-Host "`n   Deseja continuar com os próximos arquivos? (S/N)"
                    if ($continue -ne 'S' -and $continue -ne 's') {
                        throw "Processo interrompido pelo usuário após análise de erros"
                    }
                    
                } else {
                    # Sucesso - exibir informações detalhadas
                    Write-Host-Color "   ✅ Aplicado com sucesso no banco '$targetDatabase'!" "Green"
                    
                    if ($connectFound) {
                        Write-Host-Color "   🔄 Comando \connect processado e removido automaticamente" "Cyan"
                    }
                    
                    # Estatísticas de processamento
                    $totalLines = $sqlContent.Content.Count
                    $processedLines = $processedContent.Count
                    if ($totalLines -ne $processedLines) {
                        Write-Host-Color "   📊 Linhas processadas: $processedLines de $totalLines ($(($totalLines - $processedLines)) linhas de comando removidas)" "Gray"
                    }
                    
                    # Exibir warnings informativos se existirem
                    if ($warnings.Count -gt 0) {
                        Write-Host-Color "   ⚠️  Avisos (não críticos):" "Yellow"
                        $warnings | Select-Object -First 3 | ForEach-Object { 
                            Write-Host "      $_" -ForegroundColor Yellow 
                        }
                        if ($warnings.Count -gt 3) {
                            Write-Host "      ... e mais $($warnings.Count - 3) avisos" -ForegroundColor Yellow
                        }
                    }
                    
                    # Exibir notices informativos se existirem
                    if ($notices.Count -gt 0) {
                        Write-Host-Color "   📝 Informações:" "Gray"
                        $notices | Select-Object -First 3 | ForEach-Object { 
                            Write-Host "      $_" -ForegroundColor Gray 
                        }
                        if ($notices.Count -gt 3) {
                            Write-Host "      ... e mais $($notices.Count - 3) informações" -ForegroundColor Gray
                        }
                    }
                    
                    # Log de sucesso para auditoria
                    Log-Event "Arquivo SQL executado com sucesso: $($sqlFile.Name) -> $targetDatabase" "SQLSchema" "SUCCESS"
                }
                
            } catch {
                Log-Event "Erro fatal ao processar $($sqlFile.Name): $_" "SQLSchema" "ERROR"
                Write-Host-Color "   ❌ ERRO FATAL: $_" "Red"
                Write-Progress -Activity "Aplicando esquema SQL" -Completed
                exit 1
            } finally {
                # ETAPA 10: Limpeza garantida e abrangente de recursos temporários
                Log-Event "Iniciando limpeza abrangente de recursos temporários" "SQLProcessor" "DEBUG"
                
                # Coletar todos os arquivos para limpeza
                $localCleanupFiles = @()
                $remoteCleanupFiles = @()
                
                # Adicionar arquivo temporário local se existe
                if ($localTempFile -and (Test-Path $localTempFile -ErrorAction SilentlyContinue)) {
                    $localCleanupFiles += $localTempFile
                }
                
                # Adicionar outros arquivos locais temporários se existirem
                if ($localFilesToClean) {
                    $localCleanupFiles += $localFilesToClean
                }
                
                # Adicionar arquivo temporário remoto se foi criado
                if ($tempPath) {
                    $remoteCleanupFiles += $tempPath
                }
                
                # Executar limpeza centralizada
                try {
                    Invoke-CleanupManager -LocalFiles $localCleanupFiles -RemoteFiles $remoteCleanupFiles -PostgresContainer $PostgresContainer
                } catch {
                    Log-Event "Erro durante limpeza de recursos: $($_.Exception.Message)" "SQLProcessor" "WARN"
                    # Não interromper o processo por falha na limpeza
                }
                
                Log-Event "Limpeza de recursos concluída para arquivo: $($sqlFile.Name)" "SQLProcessor" "DEBUG"
            }
        }
        
        Write-Progress -Activity "Aplicando esquema SQL" -Completed
        Write-Host ""
        Write-Host-Color "✅ Esquema SQL aplicado com sucesso!" "Green"
        Log-Event "Todos os arquivos SQL foram processados com robustez total" "SQLSchema" "SUCCESS"
    }
} else {
    Log-Event "Diretório SQL não encontrado para este workflow" "SQLSchema" "INFO"
    Write-Host-Color "`n📝 Este workflow não possui esquema SQL para aplicar." "Gray"
}

# =====================================================
# EXIBIÇÃO DO GUIA DE CONFIGURAÇÃO
# =====================================================

$configPath = Join-Path $workflowFullPath "config"
if (Test-Path $configPath) {
    Write-Host-Color "`n📋 Verificando guias de configuração..." "Yellow"
    Log-Event "Diretório de configuração encontrado: $configPath" "Config" "INFO"
    
    # Procurar por arquivos de configuração
    $configFiles = @()
    $configFiles += Get-ChildItem -Path $configPath -Filter "*.env.example" -ErrorAction SilentlyContinue
    $configFiles += Get-ChildItem -Path $configPath -Filter "config.md" -ErrorAction SilentlyContinue
    $configFiles += Get-ChildItem -Path $configPath -Filter "*.json" -ErrorAction SilentlyContinue | 
                    Where-Object { $_.Name -like "*config*" }
    
    if ($configFiles.Count -gt 0) {
        Write-Host ""
        Write-Host-Color "╔══════════════════════════════════════════════════════════╗" "Magenta"
        Write-Host-Color "║      📋 AÇÕES MANUAIS DE CONFIGURAÇÃO NECESSÁRIAS       ║" "Magenta"
        Write-Host-Color "╚══════════════════════════════════════════════════════════╝" "Magenta"
        
        foreach ($configFile in $configFiles) {
            Write-Host ""
            Write-Host-Color "📄 Arquivo: $($configFile.Name)" "Cyan"
            Write-Host-Color ("─" * 60) "Gray"
            
            if ($configFile.Extension -eq ".json") {
                # Para arquivos JSON, extrair as variáveis de ambiente necessárias
                try {
                    $jsonContent = Get-Content $configFile.FullName -Raw | ConvertFrom-Json
                    
                    # Usar função pura para encontrar variáveis de ambiente
                    $foundVars = Find-EnvironmentVariables -Object $jsonContent
                    $uniqueVars = $foundVars | Select-Object -Unique | Sort-Object
                    
                    if ($uniqueVars.Count -gt 0) {
                        Write-Host-Color "`n🔧 Variáveis de ambiente necessárias:" "Yellow"
                        $uniqueVars | ForEach-Object {
                            Write-Host "   • $_" -ForegroundColor White
                        }
                        
                        # Log das variáveis encontradas para auditoria
                        Log-Event "Variáveis de ambiente encontradas em $($configFile.Name): $($uniqueVars -join ', ')" "Config" "INFO"
                    }
                    
                    # Mostrar seções importantes do JSON
                    if ($jsonContent.PSObject.Properties["database"]) {
                        Write-Host-Color "`n💾 Configuração de Banco de Dados:" "Yellow"
                        Write-Host "   Host: $($jsonContent.database.host)" -ForegroundColor Gray
                        Write-Host "   Database: $($jsonContent.database.database)" -ForegroundColor Gray
                        Write-Host "   Schema: $($jsonContent.database.schema)" -ForegroundColor Gray
                    }
                    
                    if ($jsonContent.PSObject.Properties["integrations"]) {
                        Write-Host-Color "`n🔌 Integrações Necessárias:" "Yellow"
                        $jsonContent.integrations.PSObject.Properties | ForEach-Object {
                            Write-Host "   • $($_.Name)" -ForegroundColor White
                        }
                    }
                    
                } catch {
                    # Se falhar ao parsear JSON, mostrar conteúdo raw
                    Get-Content $configFile.FullName | Select-Object -First 50 | ForEach-Object {
                        Write-Host "   $_" -ForegroundColor Gray
                    }
                }
            } else {
                # Para outros arquivos, mostrar conteúdo
                Get-Content $configFile.FullName | Select-Object -First 50 | ForEach-Object {
                    Write-Host "   $_" -ForegroundColor Gray
                }
            }
            
            Write-Host ""
        }
        
        Write-Host-Color ("═" * 60) "Magenta"
        Write-Host-Color "⚠️  IMPORTANTE: Configure as credenciais acima no n8n antes" "Yellow"
        Write-Host-Color "   de executar os workflows deste pacote!" "Yellow"
        Write-Host-Color ("═" * 60) "Magenta"
        
    } else {
        Write-Host-Color "📝 Nenhum guia de configuração encontrado." "Gray"
    }
} else {
    Log-Event "Diretório de configuração não encontrado" "Config" "INFO"
}

# =====================================================
# RESUMO FINAL E PRÓXIMOS PASSOS
# =====================================================

Write-Host ""
Write-Host-Color "╔══════════════════════════════════════════════════════════╗" "Green"
Write-Host-Color "║           ✅ ONBOARDING CONCLUÍDO COM SUCESSO!          ║" "Green"
Write-Host-Color "╚══════════════════════════════════════════════════════════╝" "Green"

Write-Host ""
Write-Host-Color "📊 Resumo da Operação:" "Cyan"
Write-Host "   • Workflow: $WorkflowName" -ForegroundColor White
Write-Host "   • Banco de dados: $TargetDatabase" -ForegroundColor White
Write-Host "   • Esquemas SQL aplicados: $(if($sqlFiles) { $sqlFiles.Count } else { 0 })" -ForegroundColor White
Write-Host "   • Status: " -NoNewline -ForegroundColor White
Write-Host-Color "Pronto para uso!" "Green"

Write-Host ""
Write-Host-Color "🚀 Próximos Passos:" "Yellow"
Write-Host "   1. Acesse o n8n em: " -NoNewline -ForegroundColor White
Write-Host-Color "http://localhost:5678" "Cyan"
Write-Host "   2. Importe os workflows do diretório:" -ForegroundColor White
Write-Host "      $workflowFullPath" -ForegroundColor Gray
Write-Host "   3. Configure as credenciais necessárias conforme indicado acima" -ForegroundColor White
Write-Host "   4. Ative e teste os workflows!" -ForegroundColor White

Write-Host ""
Write-Host-Color "💡 Dica: Para configurar outro workflow, execute:" "Gray"
Write-Host "   .\Onboard-Workflow.ps1 -WorkflowName " -NoNewline -ForegroundColor Gray
Write-Host-Color '"NomeDoWorkflow"' "Yellow"

Log-Event "Onboarding do workflow '$WorkflowName' concluído com sucesso!" "WorkflowOnboarding" "SUCCESS"

# =====================================================
# FIM DO SCRIPT
# ===================================================== 