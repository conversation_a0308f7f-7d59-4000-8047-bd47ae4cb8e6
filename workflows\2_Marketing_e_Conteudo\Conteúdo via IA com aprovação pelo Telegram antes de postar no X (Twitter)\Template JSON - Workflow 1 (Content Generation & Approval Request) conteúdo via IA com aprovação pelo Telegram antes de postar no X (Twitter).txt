{
  "name": "AI Content Gen - Step 1: Generate & Request Approval",
  "nodes": [
    {
      "parameters": {},
      "name": "Start",
      "type": "n8n-nodes-base.manualTrigger",
      "typeVersion": 1,
      "position": [
        -200,
        300
      ],
      "notes": "Inicia manualmente para testes.\nSubstitua por Google Sheets ou Schedule para automação completa."
    },
    {
      "parameters": {
        "url": "={{ $json.urlToProcess }}",
        "options": {
          "headers": {
            "header": [
              {
                "name": "Accept",
                "value": "text/markdown"
              }
            ]
          }
        },
        "responseFormat": "string"
      },
      "name": "Scrape Content (Gina AI)",
      "type": "n8n-nodes-base.httpRequest",
      "typeVersion": 4.1,
      "position": [
        220,
        300
      ],
      "notes": "Usa Gina AI para extrair conteúdo da URL.\nEndpoint público: https://r.jina.ai/URL_AQUI"
    },
    {
      "parameters": {
        "fields": {
          "field": [
            {
              "name": "urlToProcess",
              "value": "https://n8n.io/blog/"
            }
          ]
        },
        "options": {}
      },
      "name": "Input URL",
      "type": "n8n-nodes-base.set",
      "typeVersion": 2.3,
      "position": [
        0,
        300
      ],
      "notes": "Define a URL a ser processada.\nColoque a URL desejada no campo 'Value' para testes."
    },
    {
      "parameters": {
        "model": "gpt-3.5-turbo",
        "jsonOutput": false,
        "messages": [
          {
            "role": "system",
            "content": "Você é um assistente especialista em análise de texto. Sua tarefa é ler o conteúdo fornecido e extrair a ideia central, conceito principal ou a mensagem mais impactante em uma frase curta e concisa."
          },
          {
            "role": "user",
            "content": "={{ $node['Clean Scraped Content'].json.scrapedContent }}"
          }
        ],
        "options": {}
      },
      "name": "Extract Key Idea (OpenAI)",
      "type": "n8n-nodes-ai-assistant.openAiChat",
      "typeVersion": 1,
      "position": [
        660,
        300
      ],
      "credentials": {
        "openAiApi": {
          "id": "YOUR_OPENAI_CREDENTIAL_ID",
          "name": "Your OpenAI Credentials"
        }
      },
      "notes": "Usa OpenAI para extrair a ideia principal do conteúdo raspado."
    },
    {
      "parameters": {
        "model": "gpt-3.5-turbo",
        "jsonOutput": false,
        "messages": [
          {
            "role": "system",
            "content": "Você é Mayk Brito, um educador de programação carismático e didático focado em iniciantes e transição de carreira. Seu tom é inspirador, direto e encorajador. Use emojis relevantes. Gere um tweet (máximo 280 caracteres) baseado na ideia principal fornecida, mantendo sua persona."
          },
          {
            "role": "user",
            "content": "Ideia Principal: {{ $node['Extract Key Idea (OpenAI)'].json.content }}"
          }
        ],
        "options": {}
      },
      "name": "Generate Tweet (OpenAI)",
      "type": "n8n-nodes-ai-assistant.openAiChat",
      "typeVersion": 1,
      "position": [
        880,
        300
      ],
      "credentials": {
        "openAiApi": {
          "id": "YOUR_OPENAI_CREDENTIAL_ID",
          "name": "Your OpenAI Credentials"
        }
      },
      "notes": "Usa OpenAI para gerar o texto do tweet com base na ideia e na persona definida.\n**IMPORTANTE: Ajuste a persona no prompt do sistema!**"
    },
    {
      "parameters": {
        "chatId": "YOUR_TELEGRAM_CHAT_ID",
        "text": "=🤖 **Tweet para Aprovação:**\n\n{{ $node['Generate Tweet (OpenAI)'].json.content }}\n\n---\n*Fonte:* {{ $node['Input URL'].json.urlToProcess }}",
        "additionalFields": {
          "reply_markup": "={{ ({ \"inline_keyboard\": [[ { \"text\": \"✅ Aprovar\", \"callback_data\": \"approve::\" + $execution.id + \"::\" + encodeURIComponent($node['Generate Tweet (OpenAI)'].json.content) + \"::\" + encodeURIComponent($node['Input URL'].json.urlToProcess) }, { \"text\": \"❌ Recusar\", \"callback_data\": \"decline::\" + $execution.id } ]] }) }}"
        }
      },
      "name": "Send to Telegram for Approval",
      "type": "n8n-nodes-base.telegram",
      "typeVersion": 1.1,
      "position": [
        1120,
        300
      ],
      "credentials": {
        "telegramApi": {
          "id": "YOUR_TELEGRAM_CREDENTIAL_ID",
          "name": "Your Telegram Bot Credentials"
        }
      },
      "notes": "Envia o tweet gerado para o Telegram com botões Aprovar/Recusar.\nO Callback Data inclui a ação, ID da execução, o texto do tweet (codificado) e a URL original (codificada)."
    },
    {
      "parameters": {
        "value": "={{ $node['Scrape Content (Gina AI)'].json.data }}",
        "options": {}
      },
      "name": "Clean Scraped Content",
      "type": "n8n-nodes-base.set",
      "typeVersion": 2.3,
      "position": [
        440,
        300
      ],
      "notes": "Prepara o conteúdo raspado para a IA.\nPode adicionar limpeza/parsing extra aqui se necessário."
    }
  ],
  "connections": {
    "Start": {
      "main": [
        [
          {
            "node": "Input URL",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Scrape Content (Gina AI)": {
      "main": [
        [
          {
            "node": "Clean Scraped Content",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Input URL": {
      "main": [
        [
          {
            "node": "Scrape Content (Gina AI)",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Extract Key Idea (OpenAI)": {
      "main": [
        [
          {
            "node": "Generate Tweet (OpenAI)",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Generate Tweet (OpenAI)": {
      "main": [
        [
          {
            "node": "Send to Telegram for Approval",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Clean Scraped Content": {
      "main": [
        [
          {
            "node": "Extract Key Idea (OpenAI)",
            "type": "main",
            "index": 0
          }
        ]
      ]
    }
  },
  "settings": {},
  "staticData": null,
  "pinData": {},
  "versionId": "a1b2c3d4-e5f6-7890-abcd-ef1234567890", // Example Version ID
  "triggerCount": 0,
  "tags": [
    {
      "id": "1",
      "name": "AI"
    },
    {
      "id": "2",
      "name": "Social Media"
    },
    {
      "id": "3",
      "name": "Telegram"
    },
    {
      "id": "4",
      "name": "Twitter"
    }
  ]
}