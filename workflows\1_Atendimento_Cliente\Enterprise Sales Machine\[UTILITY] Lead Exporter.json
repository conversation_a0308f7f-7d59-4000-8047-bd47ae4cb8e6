{"name": "[UTILITY] Lead Exporter", "nodes": [{"parameters": {"path": "export-leads", "responseMode": "onReceived", "options": {}}, "id": "528974a6-1698-4c6e-8120-f192b0c169b1", "name": "Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 1.2, "position": [860, 300], "credentials": {"http बेसिक auth": {"id": "1", "name": "Webhook basic auth"}}}, {"parameters": {"values": {"string": [{"name": "fileName", "value": "={{ 'lead_export_' + $now.toFormat('yyyy-MM-dd_HH-mm-ss') + '.csv' }}"}]}, "options": {}}, "id": "b3e3e4a9-0d19-4c17-9195-256667a42a03", "name": "Set File Name", "type": "n8n-nodes-base.set", "typeVersion": 2.2, "position": [1260, 300]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT c.id, c.telefone, c.nome, c.data_primeira_interacao, c.tags, ip.email, ip.redes_sociais, c.enriched_data FROM agent.contatos c LEFT JOIN agent.informacoes_pessoais ip ON c.id = ip.id_contato WHERE c.tags @> '{{ $json.body.filters ? JSON.stringify($json.body.filters.tags) : '[\"prospect\"]' }}'::jsonb;", "options": {}}, "id": "7616223e-633b-468b-ac24-5d57d5983792", "name": "Get Leads from DB", "type": "n8n-nodes-base.postgres", "typeVersion": 3.1, "position": [1060, 300], "credentials": {"postgres": {"id": "1", "name": "Postgres"}}}, {"parameters": {"fileName": "={{ $node[\"Set File Name\"].json[\"fileName\"] }}", "fileFormat": "csv", "options": {}}, "id": "49d8c30d-29c4-42f3-a3d2-3162fc8034a7", "name": "Convert to CSV", "type": "n8n-nodes-base.spreadsheetFile", "typeVersion": 1, "position": [1460, 300]}, {"parameters": {"responseCode": 200, "options": {"response": {"response": {"body": "={{ $binary.data }}", "headers": [{"name": "Content-Disposition", "value": "={{ 'attachment; filename=\"' + $node[\"Set File Name\"].json[\"fileName\"] + '\"' }}"}, {"name": "Content-Type", "value": "text/csv"}]}}}}, "id": "a9e69317-063f-42e7-91a5-3a7a6c9e992b", "name": "Respond with File", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1880, 300]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "INSERT INTO agent.export_history (file_name, filters_used, exported_by, row_count) VALUES ('{{ $node[\"Set File Name\"].json[\"fileName\"] }}', '{{ JSON.stringify($json.body.filters || {tags: [\"prospect\"]}) }}'::jsonb, '{{ $json.headers['x-forwarded-user'] || 'api_user' }}', {{ $items('Get Leads from DB').length }});", "options": {}}, "id": "5a5e3b6d-a9f4-4a8e-a22a-2895f87b322a", "name": "Log Export", "type": "n8n-nodes-base.postgres", "typeVersion": 3.1, "position": [1660, 300], "credentials": {"postgres": {"id": "1", "name": "Postgres"}}}], "connections": {"Webhook": {"main": [[{"node": "Get Leads from DB", "type": "main", "index": 0}]]}, "Set File Name": {"main": [[{"node": "Convert to CSV", "type": "main", "index": 0}]]}, "Get Leads from DB": {"main": [[{"node": "Set File Name", "type": "main", "index": 0}]]}, "Convert to CSV": {"main": [[{"node": "Log Export", "type": "main", "index": 0}]]}, "Log Export": {"main": [[{"node": "Respond with File", "type": "main", "index": 0}]]}}, "pinData": {}}