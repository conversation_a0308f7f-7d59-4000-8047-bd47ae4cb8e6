{"name": "[METRICS] Cycle Metrics Monitor", "nodes": [{"parameters": {"httpMethod": "POST", "path": "cycle-metrics", "options": {}}, "id": "f47ac10b-58cc-4372-a567-0e02b2c3d479", "name": "📊 Webhook - Metrics Request", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300], "webhookId": "cycle-metrics"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "c1", "leftValue": "={{ $json.action }}", "rightValue": "update_daily_metrics", "operator": {"type": "string", "operation": "equals"}}, {"id": "c2", "leftValue": "={{ $json.action }}", "rightValue": "get_dashboard_data", "operator": {"type": "string", "operation": "equals"}}, {"id": "c3", "leftValue": "={{ $json.action }}", "rightValue": "generate_report", "operator": {"type": "string", "operation": "equals"}}, {"id": "c4", "leftValue": "={{ $json.action }}", "rightValue": "check_alerts", "operator": {"type": "string", "operation": "equals"}}], "combinator": "or"}, "options": {}}, "id": "8f14e45f-ceea-467a-9b87-7df96c6f135e", "name": "🔀 Route Action", "type": "n8n-nodes-base.switch", "typeVersion": 3, "position": [460, 300]}, {"parameters": {"jsCode": "// 📊 Calcular métricas diárias do ciclo humano-IA\nconst today = new Date();\nconst yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000);\nconst startOfDay = new Date(yesterday.setHours(0, 0, 0, 0)).toISOString();\nconst endOfDay = new Date(yesterday.setHours(23, 59, 59, 999)).toISOString();\n\nreturn [{\n  date_range: {\n    start: startOfDay,\n    end: endOfDay,\n    date_key: yesterday.toISOString().split('T')[0]\n  }\n}];"}, "id": "8f14e45f-ceea-467a-9b87-7df96c6f135f", "name": "📅 Calculate Date Range", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [680, 120]}, {"parameters": {"operation": "select", "schema": {"value": "agent"}, "table": {"value": "published_content"}, "where": {"values": [{"column": "created_at", "condition": "greaterThanOrEqual", "value": "={{ $('📅 Calculate Date Range').first().json.date_range.start }}"}, {"column": "created_at", "condition": "lessThanOrEqual", "value": "={{ $('📅 Calculate Date Range').first().json.date_range.end }}"}]}, "options": {}}, "id": "6ba7b810-9dad-11d1-80b4-00c04fd430c8", "name": "📝 Get Daily Content", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [900, 80], "credentials": {"postgres": {"id": "postgres-main", "name": "PostgreSQL - Main DB"}}}, {"parameters": {"operation": "select", "schema": {"value": "agent"}, "table": {"value": "human_feedback"}, "where": {"values": [{"column": "created_at", "condition": "greaterThanOrEqual", "value": "={{ $('📅 Calculate Date Range').first().json.date_range.start }}"}, {"column": "created_at", "condition": "lessThanOrEqual", "value": "={{ $('📅 Calculate Date Range').first().json.date_range.end }}"}]}, "options": {}}, "id": "6ba7b811-9dad-11d1-80b4-00c04fd430c8", "name": "👥 Get Daily Feedback", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [900, 160], "credentials": {"postgres": {"id": "postgres-main", "name": "PostgreSQL - Main DB"}}}, {"parameters": {"operation": "select", "schema": {"value": "agent"}, "table": {"value": "llm_responses"}, "where": {"values": [{"column": "created_at", "condition": "greaterThanOrEqual", "value": "={{ $('📅 Calculate Date Range').first().json.date_range.start }}"}, {"column": "created_at", "condition": "lessThanOrEqual", "value": "={{ $('📅 Calculate Date Range').first().json.date_range.end }}"}]}, "options": {}}, "id": "6ba7b812-9dad-11d1-80b4-00c04fd430c8", "name": "🤖 Get AI Usage", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [900, 240], "credentials": {"postgres": {"id": "postgres-main", "name": "PostgreSQL - Main DB"}}}, {"parameters": {"jsCode": "// 📊 Processar métricas diárias\nconst dateRange = $('📅 Calculate Date Range').first().json.date_range;\nconst content = $('📝 Get Daily Content').all();\nconst feedback = $('👥 Get Daily Feedback').all();\nconst aiUsage = $('🤖 Get AI Usage').all();\n\n// Métricas de conteúdo\nconst contentMetrics = {\n  total_generated: content.length,\n  by_platform: {},\n  by_influencer: {},\n  total_engagement: 0,\n  avg_engagement: 0\n};\n\ncontent.forEach(item => {\n  const data = item.json;\n  \n  // Por plataforma\n  if (!contentMetrics.by_platform[data.platform]) {\n    contentMetrics.by_platform[data.platform] = 0;\n  }\n  contentMetrics.by_platform[data.platform]++;\n  \n  // Por influencer\n  if (!contentMetrics.by_influencer[data.influencer_id]) {\n    contentMetrics.by_influencer[data.influencer_id] = 0;\n  }\n  contentMetrics.by_influencer[data.influencer_id]++;\n  \n  // Engajamento\n  const engagement = (data.likes || 0) + (data.comments || 0) + (data.shares || 0);\n  contentMetrics.total_engagement += engagement;\n});\n\ncontentMetrics.avg_engagement = content.length > 0 ? \n  Math.round(contentMetrics.total_engagement / content.length) : 0;\n\n// Métricas de feedback humano\nconst feedbackMetrics = {\n  total_reviews: feedback.length,\n  approvals: 0,\n  rejections: 0,\n  approval_rate: 0,\n  avg_quality_score: 0,\n  avg_brand_alignment: 0,\n  avg_engagement_potential: 0,\n  review_time_avg: 0\n};\n\nlet qualityScores = [];\nlet brandScores = [];\nlet engagementScores = [];\nlet reviewTimes = [];\n\nfeedback.forEach(item => {\n  const data = item.json;\n  \n  if (data.feedback_type === 'approval') {\n    feedbackMetrics.approvals++;\n  } else if (data.feedback_type === 'rejection') {\n    feedbackMetrics.rejections++;\n  }\n  \n  if (data.quality_score) qualityScores.push(data.quality_score);\n  if (data.brand_alignment_score) brandScores.push(data.brand_alignment_score);\n  if (data.engagement_potential_score) engagementScores.push(data.engagement_potential_score);\n  \n  // Calcular tempo de review (se disponível)\n  if (data.created_at && data.content_created_at) {\n    const reviewTime = new Date(data.created_at) - new Date(data.content_created_at);\n    if (reviewTime > 0) {\n      reviewTimes.push(reviewTime / (1000 * 60)); // em minutos\n    }\n  }\n});\n\nfeedbackMetrics.approval_rate = feedback.length > 0 ? \n  Math.round((feedbackMetrics.approvals / feedback.length) * 100) : 0;\n\nfeedbackMetrics.avg_quality_score = qualityScores.length > 0 ? \n  Math.round((qualityScores.reduce((a, b) => a + b, 0) / qualityScores.length) * 10) / 10 : 0;\n\nfeedbackMetrics.avg_brand_alignment = brandScores.length > 0 ? \n  Math.round((brandScores.reduce((a, b) => a + b, 0) / brandScores.length) * 10) / 10 : 0;\n\nfeedbackMetrics.avg_engagement_potential = engagementScores.length > 0 ? \n  Math.round((engagementScores.reduce((a, b) => a + b, 0) / engagementScores.length) * 10) / 10 : 0;\n\nfeedbackMetrics.review_time_avg = reviewTimes.length > 0 ? \n  Math.round(reviewTimes.reduce((a, b) => a + b, 0) / reviewTimes.length) : 0;\n\n// Métricas de IA\nconst aiMetrics = {\n  total_requests: aiUsage.length,\n  total_cost: 0,\n  avg_cost_per_request: 0,\n  total_tokens: 0,\n  avg_response_time: 0,\n  success_rate: 0,\n  by_model: {}\n};\n\nlet responseTimes = [];\nlet successCount = 0;\n\naiUsage.forEach(item => {\n  const data = item.json;\n  \n  aiMetrics.total_cost += data.cost_usd || 0;\n  aiMetrics.total_tokens += (data.input_tokens || 0) + (data.output_tokens || 0);\n  \n  if (data.response_time_ms) {\n    responseTimes.push(data.response_time_ms);\n  }\n  \n  if (data.status === 'success') {\n    successCount++;\n  }\n  \n  // Por modelo\n  const model = data.model_used || 'unknown';\n  if (!aiMetrics.by_model[model]) {\n    aiMetrics.by_model[model] = {\n      requests: 0,\n      cost: 0,\n      tokens: 0\n    };\n  }\n  aiMetrics.by_model[model].requests++;\n  aiMetrics.by_model[model].cost += data.cost_usd || 0;\n  aiMetrics.by_model[model].tokens += (data.input_tokens || 0) + (data.output_tokens || 0);\n});\n\naiMetrics.avg_cost_per_request = aiUsage.length > 0 ? \n  Math.round((aiMetrics.total_cost / aiUsage.length) * 10000) / 10000 : 0;\n\naiMetrics.avg_response_time = responseTimes.length > 0 ? \n  Math.round(responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length) : 0;\n\naiMetrics.success_rate = aiUsage.length > 0 ? \n  Math.round((successCount / aiUsage.length) * 100) : 0;\n\n// Métricas de eficiência do ciclo\nconst cycleMetrics = {\n  content_to_feedback_ratio: feedback.length > 0 && content.length > 0 ? \n    Math.round((feedback.length / content.length) * 100) / 100 : 0,\n  ai_cost_per_content: content.length > 0 ? \n    Math.round((aiMetrics.total_cost / content.length) * 10000) / 10000 : 0,\n  engagement_per_dollar: aiMetrics.total_cost > 0 ? \n    Math.round((contentMetrics.total_engagement / aiMetrics.total_cost) * 100) / 100 : 0,\n  quality_efficiency: feedbackMetrics.avg_quality_score > 0 && aiMetrics.avg_cost_per_request > 0 ? \n    Math.round((feedbackMetrics.avg_quality_score / aiMetrics.avg_cost_per_request) * 100) / 100 : 0\n};\n\nreturn [{\n  date: dateRange.date_key,\n  content_metrics: contentMetrics,\n  feedback_metrics: feedbackMetrics,\n  ai_metrics: aiMetrics,\n  cycle_metrics: cycleMetrics,\n  summary: {\n    total_content_generated: contentMetrics.total_generated,\n    approval_rate: feedbackMetrics.approval_rate,\n    total_ai_cost: Math.round(aiMetrics.total_cost * 100) / 100,\n    avg_engagement: contentMetrics.avg_engagement,\n    cycle_efficiency_score: Math.round(\n      (feedbackMetrics.approval_rate * 0.4 + \n       feedbackMetrics.avg_quality_score * 10 * 0.3 + \n       (aiMetrics.success_rate || 0) * 0.3) * 10\n    ) / 10\n  }\n}];"}, "id": "8f14e45f-ceea-467a-9b87-7df96c6f1360", "name": "📊 Process Daily Metrics", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1120, 160]}, {"parameters": {"operation": "insertOrUpdate", "schema": {"value": "agent"}, "table": {"value": "cycle_metrics"}, "columns": {"mappingMode": "defineBelow", "values": [{"column": "date", "value": "={{ $json.date }}"}, {"column": "content_generated", "value": "={{ $json.content_metrics.total_generated }}"}, {"column": "content_approved", "value": "={{ $json.feedback_metrics.approvals }}"}, {"column": "content_rejected", "value": "={{ $json.feedback_metrics.rejections }}"}, {"column": "total_engagement", "value": "={{ $json.content_metrics.total_engagement }}"}, {"column": "ai_cost_usd", "value": "={{ $json.ai_metrics.total_cost }}"}, {"column": "approval_rate", "value": "={{ $json.feedback_metrics.approval_rate }}"}, {"column": "avg_quality_score", "value": "={{ $json.feedback_metrics.avg_quality_score }}"}, {"column": "avg_engagement", "value": "={{ $json.content_metrics.avg_engagement }}"}, {"column": "cycle_efficiency_score", "value": "={{ $json.summary.cycle_efficiency_score }}"}, {"column": "detailed_metrics", "value": "={{ JSON.stringify($json) }}"}]}, "conflictColumns": {"mappingMode": "defineBelow", "values": [{"column": "date"}]}, "options": {}}, "id": "6ba7b813-9dad-11d1-80b4-00c04fd430c8", "name": "💾 Save Daily Metrics", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [1340, 160], "credentials": {"postgres": {"id": "postgres-main", "name": "PostgreSQL - Main DB"}}}, {"parameters": {"operation": "select", "schema": {"value": "agent"}, "table": {"value": "cycle_metrics"}, "where": {"values": [{"column": "date", "condition": "greaterThan", "value": "={{ new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0] }}"}]}, "sort": {"values": [{"column": "date", "direction": "DESC"}]}, "options": {}}, "id": "6ba7b814-9dad-11d1-80b4-00c04fd430c8", "name": "📈 Get Dashboard Data", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [680, 280], "credentials": {"postgres": {"id": "postgres-main", "name": "PostgreSQL - Main DB"}}}, {"parameters": {"jsCode": "// 📊 Preparar dados do dashboard\nconst metricsData = $input.all();\n\nif (metricsData.length === 0) {\n  return [{\n    dashboard_data: {\n      summary: {\n        message: 'Nenhum dado disponível para o período solicitado'\n      },\n      charts: [],\n      kpis: {}\n    }\n  }];\n}\n\n// Calcular KPIs principais\nconst latestMetrics = metricsData[0].json;\nconst totalDays = metricsData.length;\n\n// Somar totais do período\nconst periodTotals = metricsData.reduce((acc, item) => {\n  const data = item.json;\n  return {\n    content_generated: acc.content_generated + (data.content_generated || 0),\n    content_approved: acc.content_approved + (data.content_approved || 0),\n    content_rejected: acc.content_rejected + (data.content_rejected || 0),\n    total_engagement: acc.total_engagement + (data.total_engagement || 0),\n    ai_cost_usd: acc.ai_cost_usd + (data.ai_cost_usd || 0)\n  };\n}, {\n  content_generated: 0,\n  content_approved: 0,\n  content_rejected: 0,\n  total_engagement: 0,\n  ai_cost_usd: 0\n});\n\n// Calcular médias\nconst averages = {\n  approval_rate: metricsData.reduce((sum, item) => sum + (item.json.approval_rate || 0), 0) / totalDays,\n  quality_score: metricsData.reduce((sum, item) => sum + (item.json.avg_quality_score || 0), 0) / totalDays,\n  engagement_per_content: periodTotals.content_generated > 0 ? \n    periodTotals.total_engagement / periodTotals.content_generated : 0,\n  cost_per_content: periodTotals.content_generated > 0 ? \n    periodTotals.ai_cost_usd / periodTotals.content_generated : 0,\n  efficiency_score: metricsData.reduce((sum, item) => sum + (item.json.cycle_efficiency_score || 0), 0) / totalDays\n};\n\n// Preparar dados para gráficos\nconst chartData = {\n  daily_content: metricsData.reverse().map(item => ({\n    date: item.json.date,\n    generated: item.json.content_generated || 0,\n    approved: item.json.content_approved || 0,\n    rejected: item.json.content_rejected || 0\n  })),\n  \n  engagement_trend: metricsData.map(item => ({\n    date: item.json.date,\n    total_engagement: item.json.total_engagement || 0,\n    avg_engagement: item.json.avg_engagement || 0\n  })),\n  \n  cost_analysis: metricsData.map(item => ({\n    date: item.json.date,\n    total_cost: Math.round((item.json.ai_cost_usd || 0) * 100) / 100,\n    cost_per_content: item.json.content_generated > 0 ? \n      Math.round((item.json.ai_cost_usd / item.json.content_generated) * 10000) / 10000 : 0\n  })),\n  \n  quality_metrics: metricsData.map(item => ({\n    date: item.json.date,\n    approval_rate: item.json.approval_rate || 0,\n    quality_score: item.json.avg_quality_score || 0,\n    efficiency_score: item.json.cycle_efficiency_score || 0\n  }))\n};\n\n// Identificar tendências\nconst trends = {\n  content_generation: 'stable',\n  approval_rate: 'stable',\n  cost_efficiency: 'stable',\n  engagement: 'stable'\n};\n\nif (metricsData.length >= 7) {\n  const recent = metricsData.slice(0, 7);\n  const older = metricsData.slice(7, 14);\n  \n  if (recent.length > 0 && older.length > 0) {\n    const recentAvgContent = recent.reduce((sum, item) => sum + (item.json.content_generated || 0), 0) / recent.length;\n    const olderAvgContent = older.reduce((sum, item) => sum + (item.json.content_generated || 0), 0) / older.length;\n    \n    if (recentAvgContent > olderAvgContent * 1.1) trends.content_generation = 'increasing';\n    else if (recentAvgContent < olderAvgContent * 0.9) trends.content_generation = 'decreasing';\n    \n    const recentAvgApproval = recent.reduce((sum, item) => sum + (item.json.approval_rate || 0), 0) / recent.length;\n    const olderAvgApproval = older.reduce((sum, item) => sum + (item.json.approval_rate || 0), 0) / older.length;\n    \n    if (recentAvgApproval > olderAvgApproval + 5) trends.approval_rate = 'improving';\n    else if (recentAvgApproval < olderAvgApproval - 5) trends.approval_rate = 'declining';\n  }\n}\n\n// Alertas e recomendações\nconst alerts = [];\nconst recommendations = [];\n\nif (averages.approval_rate < 70) {\n  alerts.push({\n    type: 'warning',\n    message: `Taxa de aprovação baixa: ${Math.round(averages.approval_rate)}%`,\n    priority: 'high'\n  });\n  recommendations.push('Revisar prompts e diretrizes de conteúdo');\n}\n\nif (averages.quality_score < 7) {\n  alerts.push({\n    type: 'warning',\n    message: `Score de qualidade abaixo do ideal: ${Math.round(averages.quality_score * 10) / 10}/10`,\n    priority: 'medium'\n  });\n  recommendations.push('Implementar melhorias na qualidade do conteúdo');\n}\n\nif (averages.cost_per_content > 0.50) {\n  alerts.push({\n    type: 'info',\n    message: `Custo por conteúdo elevado: $${Math.round(averages.cost_per_content * 100) / 100}`,\n    priority: 'medium'\n  });\n  recommendations.push('Otimizar seleção de modelos de IA');\n}\n\nif (trends.approval_rate === 'declining') {\n  alerts.push({\n    type: 'warning',\n    message: 'Tendência de queda na taxa de aprovação',\n    priority: 'high'\n  });\n}\n\nreturn [{\n  dashboard_data: {\n    summary: {\n      period_days: totalDays,\n      total_content_generated: periodTotals.content_generated,\n      total_content_approved: periodTotals.content_approved,\n      total_engagement: periodTotals.total_engagement,\n      total_ai_cost: Math.round(periodTotals.ai_cost_usd * 100) / 100,\n      avg_approval_rate: Math.round(averages.approval_rate * 10) / 10,\n      avg_quality_score: Math.round(averages.quality_score * 10) / 10,\n      avg_efficiency_score: Math.round(averages.efficiency_score * 10) / 10\n    },\n    \n    kpis: {\n      content_velocity: Math.round(periodTotals.content_generated / totalDays * 10) / 10,\n      approval_rate: Math.round(averages.approval_rate * 10) / 10,\n      engagement_per_content: Math.round(averages.engagement_per_content),\n      cost_efficiency: Math.round(averages.cost_per_content * 10000) / 10000,\n      quality_score: Math.round(averages.quality_score * 10) / 10,\n      cycle_efficiency: Math.round(averages.efficiency_score * 10) / 10\n    },\n    \n    charts: chartData,\n    trends: trends,\n    alerts: alerts,\n    recommendations: recommendations,\n    \n    last_updated: new Date().toISOString()\n  }\n}];"}, "id": "8f14e45f-ceea-467a-9b87-7df96c6f1361", "name": "📊 Prepare Dashboard Data", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [900, 280]}, {"parameters": {"operation": "select", "schema": {"value": "agent"}, "table": {"value": "cycle_metrics"}, "where": {"values": [{"column": "date", "condition": "greaterThan", "value": "={{ new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0] }}"}]}, "sort": {"values": [{"column": "date", "direction": "DESC"}]}, "options": {}}, "id": "6ba7b815-9dad-11d1-80b4-00c04fd430c8", "name": "📋 Get Weekly Data", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [680, 400], "credentials": {"postgres": {"id": "postgres-main", "name": "PostgreSQL - Main DB"}}}, {"parameters": {"jsCode": "// 📋 Gerar relatório semanal\nconst weeklyData = $input.all();\nconst request = $('📊 Webhook - Metrics Request').first().json;\n\nif (weeklyData.length === 0) {\n  return [{\n    report: {\n      message: 'Dados insuficientes para gerar relatório',\n      period: 'última semana'\n    }\n  }];\n}\n\n// Calcular totais da semana\nconst weeklyTotals = weeklyData.reduce((acc, item) => {\n  const data = item.json;\n  return {\n    content_generated: acc.content_generated + (data.content_generated || 0),\n    content_approved: acc.content_approved + (data.content_approved || 0),\n    content_rejected: acc.content_rejected + (data.content_rejected || 0),\n    total_engagement: acc.total_engagement + (data.total_engagement || 0),\n    ai_cost_usd: acc.ai_cost_usd + (data.ai_cost_usd || 0)\n  };\n}, {\n  content_generated: 0,\n  content_approved: 0,\n  content_rejected: 0,\n  total_engagement: 0,\n  ai_cost_usd: 0\n});\n\n// Calcular médias\nconst weeklyAverages = {\n  daily_content: Math.round(weeklyTotals.content_generated / weeklyData.length * 10) / 10,\n  approval_rate: weeklyData.reduce((sum, item) => sum + (item.json.approval_rate || 0), 0) / weeklyData.length,\n  quality_score: weeklyData.reduce((sum, item) => sum + (item.json.avg_quality_score || 0), 0) / weeklyData.length,\n  engagement_per_content: weeklyTotals.content_generated > 0 ? \n    weeklyTotals.total_engagement / weeklyTotals.content_generated : 0,\n  daily_cost: weeklyTotals.ai_cost_usd / weeklyData.length,\n  efficiency_score: weeklyData.reduce((sum, item) => sum + (item.json.cycle_efficiency_score || 0), 0) / weeklyData.length\n};\n\n// Identificar melhor e pior dia\nconst bestDay = weeklyData.reduce((best, current) => \n  (current.json.cycle_efficiency_score || 0) > (best.json.cycle_efficiency_score || 0) ? current : best\n);\n\nconst worstDay = weeklyData.reduce((worst, current) => \n  (current.json.cycle_efficiency_score || 0) < (worst.json.cycle_efficiency_score || 0) ? current : worst\n);\n\n// Análise de tendências\nconst firstHalf = weeklyData.slice(Math.floor(weeklyData.length / 2));\nconst secondHalf = weeklyData.slice(0, Math.floor(weeklyData.length / 2));\n\nconst trends = {};\nif (firstHalf.length > 0 && secondHalf.length > 0) {\n  const firstHalfAvgApproval = firstHalf.reduce((sum, item) => sum + (item.json.approval_rate || 0), 0) / firstHalf.length;\n  const secondHalfAvgApproval = secondHalf.reduce((sum, item) => sum + (item.json.approval_rate || 0), 0) / secondHalf.length;\n  \n  trends.approval_rate = {\n    direction: secondHalfAvgApproval > firstHalfAvgApproval ? 'improving' : \n               secondHalfAvgApproval < firstHalfAvgApproval ? 'declining' : 'stable',\n    change: Math.round((secondHalfAvgApproval - firstHalfAvgApproval) * 10) / 10\n  };\n}\n\n// Insights e recomendações\nconst insights = [];\nconst recommendations = [];\n\nif (weeklyAverages.approval_rate >= 85) {\n  insights.push('Excelente taxa de aprovação mantida durante a semana');\n} else if (weeklyAverages.approval_rate >= 70) {\n  insights.push('Taxa de aprovação dentro do esperado');\n  recommendations.push('Buscar oportunidades de melhoria para atingir 85%+');\n} else {\n  insights.push('Taxa de aprovação abaixo do ideal');\n  recommendations.push('Revisar urgentemente prompts e processo de geração');\n}\n\nif (weeklyAverages.quality_score >= 8) {\n  insights.push('Alta qualidade do conteúdo mantida');\n} else if (weeklyAverages.quality_score >= 7) {\n  insights.push('Qualidade do conteúdo satisfatória');\n} else {\n  insights.push('Qualidade do conteúdo precisa de atenção');\n  recommendations.push('Implementar melhorias nos prompts de qualidade');\n}\n\nif (weeklyTotals.ai_cost_usd > 50) {\n  insights.push('Custos de IA significativos na semana');\n  recommendations.push('Avaliar otimizações de custo e seleção de modelos');\n}\n\nif (trends.approval_rate?.direction === 'improving') {\n  insights.push('Tendência positiva na taxa de aprovação');\n} else if (trends.approval_rate?.direction === 'declining') {\n  insights.push('Tendência preocupante de queda na aprovação');\n  recommendations.push('Investigar causas da queda e implementar correções');\n}\n\n// Comparação com metas (assumindo metas padrão)\nconst goals = {\n  daily_content: 10,\n  approval_rate: 80,\n  quality_score: 7.5,\n  daily_cost: 5\n};\n\nconst goalComparison = {\n  daily_content: {\n    actual: weeklyAverages.daily_content,\n    goal: goals.daily_content,\n    achievement: Math.round((weeklyAverages.daily_content / goals.daily_content) * 100)\n  },\n  approval_rate: {\n    actual: Math.round(weeklyAverages.approval_rate * 10) / 10,\n    goal: goals.approval_rate,\n    achievement: Math.round((weeklyAverages.approval_rate / goals.approval_rate) * 100)\n  },\n  quality_score: {\n    actual: Math.round(weeklyAverages.quality_score * 10) / 10,\n    goal: goals.quality_score,\n    achievement: Math.round((weeklyAverages.quality_score / goals.quality_score) * 100)\n  },\n  daily_cost: {\n    actual: Math.round(weeklyAverages.daily_cost * 100) / 100,\n    goal: goals.daily_cost,\n    achievement: weeklyAverages.daily_cost <= goals.daily_cost ? 100 : \n      Math.round((goals.daily_cost / weeklyAverages.daily_cost) * 100)\n  }\n};\n\nreturn [{\n  report: {\n    period: `${weeklyData[weeklyData.length - 1].json.date} a ${weeklyData[0].json.date}`,\n    summary: {\n      total_days: weeklyData.length,\n      content_generated: weeklyTotals.content_generated,\n      content_approved: weeklyTotals.content_approved,\n      approval_rate: Math.round(weeklyAverages.approval_rate * 10) / 10,\n      total_engagement: weeklyTotals.total_engagement,\n      total_cost: Math.round(weeklyTotals.ai_cost_usd * 100) / 100,\n      avg_efficiency: Math.round(weeklyAverages.efficiency_score * 10) / 10\n    },\n    \n    performance: {\n      best_day: {\n        date: bestDay.json.date,\n        efficiency_score: bestDay.json.cycle_efficiency_score,\n        content_generated: bestDay.json.content_generated,\n        approval_rate: bestDay.json.approval_rate\n      },\n      worst_day: {\n        date: worstDay.json.date,\n        efficiency_score: worstDay.json.cycle_efficiency_score,\n        content_generated: worstDay.json.content_generated,\n        approval_rate: worstDay.json.approval_rate\n      }\n    },\n    \n    trends: trends,\n    goal_comparison: goalComparison,\n    insights: insights,\n    recommendations: recommendations,\n    \n    generated_at: new Date().toISOString(),\n    report_type: request.report_type || 'weekly_summary'\n  }\n}];"}, "id": "8f14e45f-ceea-467a-9b87-7df96c6f1362", "name": "📋 Generate Weekly Report", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [900, 400]}, {"parameters": {"operation": "select", "schema": {"value": "agent"}, "table": {"value": "cycle_metrics"}, "where": {"values": [{"column": "date", "condition": "equal", "value": "={{ new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString().split('T')[0] }}"}]}, "options": {}}, "id": "6ba7b816-9dad-11d1-80b4-00c04fd430c8", "name": "⚠️ Get Latest Metrics", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [680, 520], "credentials": {"postgres": {"id": "postgres-main", "name": "PostgreSQL - Main DB"}}}, {"parameters": {"jsCode": "// ⚠️ Verificar alertas críticos\nconst latestMetrics = $input.first()?.json;\nconst request = $('📊 Webhook - Metrics Request').first().json;\n\nif (!latestMetrics) {\n  return [{\n    alerts: [{\n      type: 'warning',\n      level: 'medium',\n      message: 'Nenhuma métrica disponível para verificação de alertas',\n      timestamp: new Date().toISOString()\n    }]\n  }];\n}\n\n// Definir thresholds para alertas\nconst thresholds = {\n  approval_rate: {\n    critical: 50,\n    warning: 70,\n    good: 85\n  },\n  quality_score: {\n    critical: 5,\n    warning: 6.5,\n    good: 8\n  },\n  daily_cost: {\n    warning: 10,\n    critical: 20\n  },\n  efficiency_score: {\n    critical: 30,\n    warning: 50,\n    good: 70\n  }\n};\n\nconst alerts = [];\n\n// Verificar taxa de aprovação\nif (latestMetrics.approval_rate <= thresholds.approval_rate.critical) {\n  alerts.push({\n    type: 'critical',\n    level: 'high',\n    metric: 'approval_rate',\n    message: `Taxa de aprovação crítica: ${latestMetrics.approval_rate}%`,\n    value: latestMetrics.approval_rate,\n    threshold: thresholds.approval_rate.critical,\n    action_required: 'Revisar imediatamente prompts e processo de geração',\n    timestamp: new Date().toISOString()\n  });\n} else if (latestMetrics.approval_rate <= thresholds.approval_rate.warning) {\n  alerts.push({\n    type: 'warning',\n    level: 'medium',\n    metric: 'approval_rate',\n    message: `Taxa de aprovação baixa: ${latestMetrics.approval_rate}%`,\n    value: latestMetrics.approval_rate,\n    threshold: thresholds.approval_rate.warning,\n    action_required: 'Monitorar e considerar ajustes nos prompts',\n    timestamp: new Date().toISOString()\n  });\n}\n\n// Verificar qualidade\nif (latestMetrics.avg_quality_score <= thresholds.quality_score.critical) {\n  alerts.push({\n    type: 'critical',\n    level: 'high',\n    metric: 'quality_score',\n    message: `Score de qualidade crítico: ${latestMetrics.avg_quality_score}/10`,\n    value: latestMetrics.avg_quality_score,\n    threshold: thresholds.quality_score.critical,\n    action_required: 'Implementar melhorias urgentes na qualidade',\n    timestamp: new Date().toISOString()\n  });\n} else if (latestMetrics.avg_quality_score <= thresholds.quality_score.warning) {\n  alerts.push({\n    type: 'warning',\n    level: 'medium',\n    metric: 'quality_score',\n    message: `Score de qualidade baixo: ${latestMetrics.avg_quality_score}/10`,\n    value: latestMetrics.avg_quality_score,\n    threshold: thresholds.quality_score.warning,\n    action_required: 'Revisar diretrizes de qualidade',\n    timestamp: new Date().toISOString()\n  });\n}\n\n// Verificar custos\nif (latestMetrics.ai_cost_usd >= thresholds.daily_cost.critical) {\n  alerts.push({\n    type: 'critical',\n    level: 'high',\n    metric: 'daily_cost',\n    message: `Custo diário crítico: $${Math.round(latestMetrics.ai_cost_usd * 100) / 100}`,\n    value: latestMetrics.ai_cost_usd,\n    threshold: thresholds.daily_cost.critical,\n    action_required: 'Otimizar imediatamente seleção de modelos',\n    timestamp: new Date().toISOString()\n  });\n} else if (latestMetrics.ai_cost_usd >= thresholds.daily_cost.warning) {\n  alerts.push({\n    type: 'warning',\n    level: 'medium',\n    metric: 'daily_cost',\n    message: `Custo diário elevado: $${Math.round(latestMetrics.ai_cost_usd * 100) / 100}`,\n    value: latestMetrics.ai_cost_usd,\n    threshold: thresholds.daily_cost.warning,\n    action_required: 'Monitorar custos e considerar otimizações',\n    timestamp: new Date().toISOString()\n  });\n}\n\n// Verificar eficiência do ciclo\nif (latestMetrics.cycle_efficiency_score <= thresholds.efficiency_score.critical) {\n  alerts.push({\n    type: 'critical',\n    level: 'high',\n    metric: 'efficiency_score',\n    message: `Eficiência do ciclo crítica: ${latestMetrics.cycle_efficiency_score}/100`,\n    value: latestMetrics.cycle_efficiency_score,\n    threshold: thresholds.efficiency_score.critical,\n    action_required: 'Revisar todo o processo do ciclo humano-IA',\n    timestamp: new Date().toISOString()\n  });\n} else if (latestMetrics.cycle_efficiency_score <= thresholds.efficiency_score.warning) {\n  alerts.push({\n    type: 'warning',\n    level: 'medium',\n    metric: 'efficiency_score',\n    message: `Eficiência do ciclo baixa: ${latestMetrics.cycle_efficiency_score}/100`,\n    value: latestMetrics.cycle_efficiency_score,\n    threshold: thresholds.efficiency_score.warning,\n    action_required: 'Identificar gargalos no processo',\n    timestamp: new Date().toISOString()\n  });\n}\n\n// Verificar se não houve geração de conteúdo\nif (latestMetrics.content_generated === 0) {\n  alerts.push({\n    type: 'critical',\n    level: 'high',\n    metric: 'content_generation',\n    message: 'Nenhum conteúdo gerado no último dia',\n    value: 0,\n    threshold: 1,\n    action_required: 'Verificar sistema de geração de conteúdo',\n    timestamp: new Date().toISOString()\n  });\n}\n\n// Adicionar alertas positivos se tudo estiver bem\nif (alerts.length === 0) {\n  alerts.push({\n    type: 'success',\n    level: 'info',\n    message: 'Todas as métricas dentro dos parâmetros esperados',\n    timestamp: new Date().toISOString()\n  });\n}\n\n// Calcular score geral de saúde\nconst healthScore = Math.round(\n  (Math.min(latestMetrics.approval_rate / thresholds.approval_rate.good, 1) * 25 +\n   Math.min(latestMetrics.avg_quality_score / thresholds.quality_score.good, 1) * 25 +\n   Math.min(latestMetrics.cycle_efficiency_score / thresholds.efficiency_score.good, 1) * 25 +\n   (latestMetrics.ai_cost_usd <= thresholds.daily_cost.warning ? 25 : \n    latestMetrics.ai_cost_usd <= thresholds.daily_cost.critical ? 15 : 5)) * 10\n) / 10;\n\nreturn [{\n  alerts: alerts,\n  health_score: healthScore,\n  metrics_date: latestMetrics.date,\n  total_alerts: alerts.length,\n  critical_alerts: alerts.filter(a => a.type === 'critical').length,\n  warning_alerts: alerts.filter(a => a.type === 'warning').length,\n  thresholds_used: thresholds,\n  checked_at: new Date().toISOString()\n}];"}, "id": "8f14e45f-ceea-467a-9b87-7df96c6f1363", "name": "⚠️ Check Critical Alerts", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [900, 520]}, {"parameters": {"respondWith": "json", "responseBody": "={{ $json }}", "options": {}}, "id": "f47ac10b-58cc-4372-a567-0e02b2c3d480", "name": "📤 Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1340, 400]}, {"parameters": {"jsCode": "// 📤 Formatar resposta final\nconst action = $('📊 Webhook - Metrics Request').first().json.action;\nconst input = $input.first().json;\n\nlet response = {\n  success: true,\n  action: action,\n  timestamp: new Date().toISOString()\n};\n\nswitch (action) {\n  case 'update_daily_metrics':\n    response.message = 'Métricas diárias atualizadas com sucesso';\n    response.data = {\n      date: input.date,\n      content_generated: input.content_metrics.total_generated,\n      approval_rate: input.feedback_metrics.approval_rate,\n      total_cost: Math.round(input.ai_metrics.total_cost * 100) / 100,\n      efficiency_score: input.summary.cycle_efficiency_score\n    };\n    break;\n    \n  case 'get_dashboard_data':\n    response.data = input.dashboard_data;\n    break;\n    \n  case 'generate_report':\n    response.data = input.report;\n    break;\n    \n  case 'check_alerts':\n    response.data = {\n      alerts: input.alerts,\n      health_score: input.health_score,\n      summary: {\n        total_alerts: input.total_alerts,\n        critical_alerts: input.critical_alerts,\n        warning_alerts: input.warning_alerts\n      }\n    };\n    break;\n}\n\nreturn response;"}, "id": "8f14e45f-ceea-467a-9b87-7df96c6f1364", "name": "📋 Format Final Response", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1120, 400]}, {"parameters": {"operation": "insert", "schema": {"value": "agent"}, "table": {"value": "system_logs"}, "columns": {"mappingMode": "defineBelow", "values": [{"column": "event_type", "value": "metrics_process"}, {"column": "event_data", "value": "={{ JSON.stringify({action: $('📊 Webhook - Metrics Request').first().json.action, result: $json}) }}"}, {"column": "severity", "value": "info"}]}, "options": {}}, "id": "6ba7b817-9dad-11d1-80b4-00c04fd430c8", "name": "📝 Log Metrics Process", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [1340, 320], "credentials": {"postgres": {"id": "postgres-main", "name": "PostgreSQL - Main DB"}}}, {"parameters": {"jsCode": "// 🚨 Tratamento de erro\nconst error = $input.first().json;\n\nreturn {\n  success: false,\n  error: {\n    message: error.message || 'Erro interno do servidor',\n    code: error.code || 'METRICS_ERROR',\n    details: error.details || null\n  },\n  timestamp: new Date().toISOString()\n};"}, "id": "8f14e45f-ceea-467a-9b87-7df96c6f1365", "name": "🚨 <PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1120, 600]}, {"parameters": {"operation": "insert", "schema": {"value": "agent"}, "table": {"value": "system_logs"}, "columns": {"mappingMode": "defineBelow", "values": [{"column": "event_type", "value": "metrics_error"}, {"column": "event_data", "value": "={{ JSON.stringify({error: $json.error, action: $('📊 Webhook - Metrics Request').first().json.action}) }}"}, {"column": "severity", "value": "error"}]}, "options": {}}, "id": "6ba7b818-9dad-11d1-80b4-00c04fd430c8", "name": "📝 Log Error", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [1340, 600], "credentials": {"postgres": {"id": "postgres-main", "name": "PostgreSQL - Main DB"}}}], "connections": {"📊 Webhook - Metrics Request": {"main": [[{"node": "🔀 Route Action", "type": "main", "index": 0}]]}, "🔀 Route Action": {"main": [[{"node": "📅 Calculate Date Range", "type": "main", "index": 0}], [{"node": "📈 Get Dashboard Data", "type": "main", "index": 0}], [{"node": "📋 Get Weekly Data", "type": "main", "index": 0}], [{"node": "⚠️ Get Latest Metrics", "type": "main", "index": 0}]]}, "📅 Calculate Date Range": {"main": [[{"node": "📝 Get Daily Content", "type": "main", "index": 0}, {"node": "👥 Get Daily Feedback", "type": "main", "index": 0}, {"node": "🤖 Get AI Usage", "type": "main", "index": 0}]]}, "📝 Get Daily Content": {"main": [[{"node": "📊 Process Daily Metrics", "type": "main", "index": 0}]]}, "👥 Get Daily Feedback": {"main": [[{"node": "📊 Process Daily Metrics", "type": "main", "index": 0}]]}, "🤖 Get AI Usage": {"main": [[{"node": "📊 Process Daily Metrics", "type": "main", "index": 0}]]}, "📊 Process Daily Metrics": {"main": [[{"node": "💾 Save Daily Metrics", "type": "main", "index": 0}]]}, "💾 Save Daily Metrics": {"main": [[{"node": "📋 Format Final Response", "type": "main", "index": 0}]]}, "📈 Get Dashboard Data": {"main": [[{"node": "📊 Prepare Dashboard Data", "type": "main", "index": 0}]]}, "📊 Prepare Dashboard Data": {"main": [[{"node": "📋 Format Final Response", "type": "main", "index": 0}]]}, "📋 Get Weekly Data": {"main": [[{"node": "📋 Generate Weekly Report", "type": "main", "index": 0}]]}, "📋 Generate Weekly Report": {"main": [[{"node": "📋 Format Final Response", "type": "main", "index": 0}]]}, "⚠️ Get Latest Metrics": {"main": [[{"node": "⚠️ Check Critical Alerts", "type": "main", "index": 0}]]}, "⚠️ Check Critical Alerts": {"main": [[{"node": "📋 Format Final Response", "type": "main", "index": 0}]]}, "📋 Format Final Response": {"main": [[{"node": "📝 Log Metrics Process", "type": "main", "index": 0}, {"node": "📤 Response", "type": "main", "index": 0}]]}}, "pinData": {}, "settings": {"executionOrder": "v1"}, "staticData": null, "tags": [{"createdAt": "2024-01-15T10:00:00.000Z", "updatedAt": "2024-01-15T10:00:00.000Z", "id": "metrics-cycle", "name": "Metrics & Cycle"}], "triggerCount": 0, "updatedAt": "2024-01-15T10:00:00.000Z", "versionId": "1"}