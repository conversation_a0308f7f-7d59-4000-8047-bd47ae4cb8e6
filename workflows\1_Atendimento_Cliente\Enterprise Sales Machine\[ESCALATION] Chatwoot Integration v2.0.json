{"name": "[ESCALATION] Chatwoot Integration v2.0", "nodes": [{"parameters": {"httpMethod": "POST", "path": "escalation-chatwoot-webhook", "options": {"noResponseBody": false}}, "id": "chatwoot-webhook-trigger", "name": "🎯 Chatwoot Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [240, 300], "webhookId": "chatwoot-escalation-webhook"}, {"parameters": {"jsCode": "// Validar e processar dados do webhook do Chatwoot\nconst body = $input.first().json.body || $input.first().json;\n\n// Verificar se é um evento válido do Chatwoot\nif (!body.event || !body.data) {\n  throw new Error('Webhook inválido: evento ou dados ausentes');\n}\n\n// Mapear tipos de eventos do Chatwoot\nconst eventMapping = {\n  'conversation_created': 'new_conversation',\n  'conversation_updated': 'conversation_update',\n  'message_created': 'new_message',\n  'conversation_status_changed': 'status_change',\n  'conversation_assignee_changed': 'assignee_change'\n};\n\nconst eventType = eventMapping[body.event] || body.event;\nconst conversationData = body.data;\n\n// Extrair informações relevantes\nconst processedData = {\n  event_type: eventType,\n  original_event: body.event,\n  timestamp: new Date().toISOString(),\n  chatwoot_data: {\n    conversation_id: conversationData.id,\n    account_id: conversationData.account_id,\n    inbox_id: conversationData.inbox_id,\n    status: conversationData.status,\n    priority: conversationData.priority || 'medium',\n    assignee_id: conversationData.assignee?.id,\n    assignee_name: conversationData.assignee?.name,\n    contact_id: conversationData.contact?.id,\n    contact_name: conversationData.contact?.name,\n    contact_email: conversationData.contact?.email,\n    contact_phone: conversationData.contact?.phone_number,\n    labels: conversationData.labels || [],\n    custom_attributes: conversationData.custom_attributes || {},\n    messages_count: conversationData.messages_count || 0,\n    last_activity_at: conversationData.last_activity_at,\n    created_at: conversationData.created_at,\n    updated_at: conversationData.updated_at\n  }\n};\n\n// Verificar se precisa de escalação baseado em critérios\nconst needsEscalation = checkEscalationCriteria(conversationData);\nprocessedData.escalation_required = needsEscalation.required;\nprocessedData.escalation_reason = needsEscalation.reason;\nprocessedData.escalation_priority = needsEscalation.priority;\n\n// Função para verificar critérios de escalação\nfunction checkEscalationCriteria(data) {\n  const criteria = {\n    required: false,\n    reason: [],\n    priority: 'medium'\n  };\n\n  // Verificar labels de escalação\n  const escalationLabels = ['escalation', 'urgent', 'vip', 'complaint', 'technical-issue'];\n  const hasEscalationLabel = data.labels?.some(label => \n    escalationLabels.some(escLabel => label.title.toLowerCase().includes(escLabel))\n  );\n\n  if (hasEscalationLabel) {\n    criteria.required = true;\n    criteria.reason.push('escalation_label_detected');\n    criteria.priority = 'high';\n  }\n\n  // Verificar prioridade alta\n  if (data.priority === 'urgent' || data.priority === 'high') {\n    criteria.required = true;\n    criteria.reason.push('high_priority_conversation');\n    criteria.priority = 'high';\n  }\n\n  // Verificar atributos customizados\n  if (data.custom_attributes) {\n    if (data.custom_attributes.escalation_requested === 'true') {\n      criteria.required = true;\n      criteria.reason.push('manual_escalation_request');\n      criteria.priority = 'high';\n    }\n    \n    if (data.custom_attributes.customer_tier === 'vip') {\n      criteria.required = true;\n      criteria.reason.push('vip_customer');\n      criteria.priority = 'critical';\n    }\n  }\n\n  // Verificar tempo de resposta (se não atribuído por muito tempo)\n  if (!data.assignee_id && data.created_at) {\n    const createdTime = new Date(data.created_at);\n    const now = new Date();\n    const hoursSinceCreated = (now - createdTime) / (1000 * 60 * 60);\n    \n    if (hoursSinceCreated > 2) {\n      criteria.required = true;\n      criteria.reason.push('unassigned_timeout');\n      criteria.priority = 'medium';\n    }\n  }\n\n  // Verificar número de mensagens (conversas longas podem precisar de escalação)\n  if (data.messages_count > 20) {\n    criteria.required = true;\n    criteria.reason.push('long_conversation');\n    criteria.priority = 'medium';\n  }\n\n  return criteria;\n}\n\nreturn [{ json: processedData }];"}, "id": "process-chatwoot-data", "name": "⚙️ Process Chatwoot Data", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [460, 300]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "needs-escalation", "leftValue": "={{ $json.escalation_required }}", "rightValue": true, "operator": {"type": "boolean", "operation": "equal"}}], "combinator": "and"}, "options": {}}, "id": "check-escalation-needed", "name": "❓ Escalation Needed?", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [680, 300]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "-- Verificar se já existe escalação para esta conversa\nSELECT \n  e.escalation_id,\n  e.status,\n  e.priority,\n  e.created_at,\n  e.assigned_agent_id,\n  e.assigned_queue_id\nFROM agent.escalations e\nWHERE e.external_conversation_id = $1\n  AND e.source_system = 'chatwoot'\n  AND e.status NOT IN ('resolved', 'cancelled')\nORDER BY e.created_at DESC\nLIMIT 1;", "options": {"queryReplacement": "={{ $json.chatwoot_data.conversation_id.toString() }}"}}, "id": "check-existing-escalation", "name": "🔍 Check Existing Escalation", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [900, 200], "credentials": {"postgres": {"id": "postgres-escalation-db", "name": "PostgreSQL Escalation DB"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "no-existing", "leftValue": "={{ $json.length }}", "rightValue": 0, "operator": {"type": "number", "operation": "equal"}}], "combinator": "and"}, "options": {}}, "id": "check-no-existing", "name": "❓ No Existing?", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [1120, 200]}, {"parameters": {"jsCode": "// Preparar dados para criação de escalação\nconst chatwootData = $('process-chatwoot-data').item.json;\n\n// Determinar categoria baseada nos dados do Chatwoot\nlet category = 'general';\nif (chatwootData.chatwoot_data.labels) {\n  const labels = chatwootData.chatwoot_data.labels.map(l => l.title.toLowerCase());\n  if (labels.some(l => l.includes('technical') || l.includes('bug'))) {\n    category = 'technical';\n  } else if (labels.some(l => l.includes('billing') || l.includes('payment'))) {\n    category = 'billing';\n  } else if (labels.some(l => l.includes('complaint') || l.includes('issue'))) {\n    category = 'complaint';\n  } else if (labels.some(l => l.includes('sales') || l.includes('commercial'))) {\n    category = 'sales';\n  }\n}\n\n// Preparar contexto da escalação\nconst escalationContext = {\n  source_system: 'chatwoot',\n  external_conversation_id: chatwootData.chatwoot_data.conversation_id.toString(),\n  customer_info: {\n    contact_id: chatwootData.chatwoot_data.contact_id,\n    name: chatwootData.chatwoot_data.contact_name,\n    email: chatwootData.chatwoot_data.contact_email,\n    phone: chatwootData.chatwoot_data.contact_phone\n  },\n  conversation_context: {\n    inbox_id: chatwootData.chatwoot_data.inbox_id,\n    account_id: chatwootData.chatwoot_data.account_id,\n    status: chatwootData.chatwoot_data.status,\n    messages_count: chatwootData.chatwoot_data.messages_count,\n    labels: chatwootData.chatwoot_data.labels,\n    custom_attributes: chatwootData.chatwoot_data.custom_attributes,\n    last_activity_at: chatwootData.chatwoot_data.last_activity_at\n  },\n  escalation_triggers: chatwootData.escalation_reason,\n  original_assignee: {\n    id: chatwootData.chatwoot_data.assignee_id,\n    name: chatwootData.chatwoot_data.assignee_name\n  }\n};\n\nconst escalationData = {\n  title: `Escalação Chatwoot - Conversa #${chatwootData.chatwoot_data.conversation_id}`,\n  description: `Escalação automática da conversa ${chatwootData.chatwoot_data.conversation_id} do Chatwoot.\\nMotivos: ${chatwootData.escalation_reason.join(', ')}\\nCliente: ${chatwootData.chatwoot_data.contact_name || 'N/A'}\\nEmail: ${chatwootData.chatwoot_data.contact_email || 'N/A'}`,\n  priority: chatwootData.escalation_priority,\n  category: category,\n  source_system: 'chatwoot',\n  external_conversation_id: chatwootData.chatwoot_data.conversation_id.toString(),\n  customer_data: escalationContext.customer_info,\n  context_data: escalationContext,\n  urgency_level: chatwootData.escalation_priority === 'critical' ? 'immediate' : \n                 chatwootData.escalation_priority === 'high' ? 'urgent' : 'normal',\n  estimated_resolution_time: chatwootData.escalation_priority === 'critical' ? 30 : \n                            chatwootData.escalation_priority === 'high' ? 120 : 240\n};\n\nreturn [{ json: escalationData }];"}, "id": "prepare-escalation-data", "name": "📋 Prepare Escalation Data", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1340, 100]}, {"parameters": {"url": "http://localhost:5678/webhook/escalation-main", "options": {}, "bodyParametersUi": {"parameter": [{"name": "title", "value": "={{ $json.title }}"}, {"name": "description", "value": "={{ $json.description }}"}, {"name": "priority", "value": "={{ $json.priority }}"}, {"name": "category", "value": "={{ $json.category }}"}, {"name": "source_system", "value": "={{ $json.source_system }}"}, {"name": "external_conversation_id", "value": "={{ $json.external_conversation_id }}"}, {"name": "customer_data", "value": "={{ JSON.stringify($json.customer_data) }}"}, {"name": "context_data", "value": "={{ JSON.stringify($json.context_data) }}"}, {"name": "urgency_level", "value": "={{ $json.urgency_level }}"}, {"name": "estimated_resolution_time", "value": "={{ $json.estimated_resolution_time }}"}]}}, "id": "create-escalation", "name": "🚀 Create Escalation", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1560, 100]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "-- Atualizar escalação existente com novos dados\nUPDATE agent.escalations \nSET \n  description = description || '\\n\\n--- Atualização Chatwoot ---\\n' || $2,\n  context_data = context_data || $3::jsonb,\n  updated_at = NOW(),\n  last_chatwoot_update = NOW()\nWHERE escalation_id = $1\nRETURNING escalation_id, title, status, priority;", "options": {"queryReplacement": "={{ $('check-existing-escalation').item.json.escalation_id }},Atualização da conversa Chatwoot #{{ $('process-chatwoot-data').item.json.chatwoot_data.conversation_id }} - Status: {{ $('process-chatwoot-data').item.json.chatwoot_data.status }},{{ JSON.stringify($('process-chatwoot-data').item.json) }}"}}, "id": "update-existing-escalation", "name": "🔄 Update Existing Escalation", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [1340, 300], "credentials": {"postgres": {"id": "postgres-escalation-db", "name": "PostgreSQL Escalation DB"}}}, {"parameters": {"url": "={{ $vars.CHATWOOT_API_URL }}/api/v1/accounts/{{ $('process-chatwoot-data').item.json.chatwoot_data.account_id }}/conversations/{{ $('process-chatwoot-data').item.json.chatwoot_data.conversation_id }}/labels", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "options": {}, "bodyParametersUi": {"parameter": [{"name": "labels", "value": "[\"escalated\", \"human-review\"]"}]}}, "id": "add-escalation-labels", "name": "🏷️ Add Escalation Labels", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1560, 300], "credentials": {"httpHeaderAuth": {"id": "chatwoot-api-auth", "name": "Chatwoot API Auth"}}}, {"parameters": {"url": "={{ $vars.CHATWOOT_API_URL }}/api/v1/accounts/{{ $('process-chatwoot-data').item.json.chatwoot_data.account_id }}/conversations/{{ $('process-chatwoot-data').item.json.chatwoot_data.conversation_id }}/messages", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "options": {}, "bodyParametersUi": {"parameter": [{"name": "content", "value": "🚀 Esta conversa foi escalada para análise humana.\n\n**Detal<PERSON> da Escalação:**\n- Prioridade: {{ $('process-chatwoot-data').item.json.escalation_priority }}\n- Motivos: {{ $('process-chatwoot-data').item.json.escalation_reason.join(', ') }}\n- Timestamp: {{ new Date().toLocaleString('pt-BR') }}\n\nUm agente especializado será designado em breve."}, {"name": "message_type", "value": "outgoing"}, {"name": "private", "value": "true"}]}}, "id": "send-escalation-message", "name": "💬 Send Escalation Message", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1780, 200], "credentials": {"httpHeaderAuth": {"id": "chatwoot-api-auth", "name": "Chatwoot API Auth"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "-- Registrar integração Chatwoot\nINSERT INTO agent.chatwoot_integrations (\n  escalation_id,\n  conversation_id,\n  account_id,\n  inbox_id,\n  contact_id,\n  event_type,\n  integration_status,\n  chatwoot_data,\n  processed_at\n) VALUES (\n  $1,\n  $2,\n  $3,\n  $4,\n  $5,\n  $6,\n  'processed',\n  $7,\n  NOW()\n)\nRETURNING integration_id, escalation_id, conversation_id;", "options": {"queryReplacement": "={{ $('create-escalation').item.json.escalation_id || $('update-existing-escalation').item.json.escalation_id }},{{ $('process-chatwoot-data').item.json.chatwoot_data.conversation_id }},{{ $('process-chatwoot-data').item.json.chatwoot_data.account_id }},{{ $('process-chatwoot-data').item.json.chatwoot_data.inbox_id }},{{ $('process-chatwoot-data').item.json.chatwoot_data.contact_id }},{{ $('process-chatwoot-data').item.json.event_type }},{{ JSON.stringify($('process-chatwoot-data').item.json.chatwoot_data) }}"}}, "id": "log-chatwoot-integration", "name": "📝 Log Chatwoot Integration", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [1780, 400], "credentials": {"postgres": {"id": "postgres-escalation-db", "name": "PostgreSQL Escalation DB"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "-- Registrar event<PERSON> não-escal<PERSON>ção\nINSERT INTO agent.chatwoot_events (\n  conversation_id,\n  account_id,\n  event_type,\n  event_data,\n  escalation_required,\n  processed_at\n) VALUES (\n  $1,\n  $2,\n  $3,\n  $4,\n  false,\n  NOW()\n)\nRETURNING event_id, conversation_id, event_type;", "options": {"queryReplacement": "={{ $json.chatwoot_data.conversation_id }},{{ $json.chatwoot_data.account_id }},{{ $json.event_type }},{{ JSON.stringify($json) }}"}}, "id": "log-non-escalation-event", "name": "📋 Log Non-Escalation Event", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [680, 500], "credentials": {"postgres": {"id": "postgres-escalation-db", "name": "PostgreSQL Escalation DB"}}}, {"parameters": {"jsCode": "// Resposta para eventos que não precisam de escalação\nconst data = $input.first().json;\n\nreturn [{\n  json: {\n    status: 'processed',\n    escalation_required: false,\n    event_type: data.event_type,\n    conversation_id: data.chatwoot_data.conversation_id,\n    message: 'Evento processado - escalação não necessária',\n    timestamp: new Date().toISOString(),\n    processing_details: {\n      event_analyzed: true,\n      escalation_criteria_checked: true,\n      escalation_triggered: false,\n      reason: 'Evento não atende aos critérios de escalação'\n    }\n  }\n}];"}, "id": "format-non-escalation-response", "name": "📄 Format Non-Escalation Response", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [900, 500]}, {"parameters": {"jsCode": "// Resposta final para escalações processadas\nconst escalationData = $('create-escalation').item?.json || $('update-existing-escalation').item?.json;\nconst chatwootData = $('process-chatwoot-data').item.json;\n\nreturn [{\n  json: {\n    status: 'escalation_created',\n    escalation_id: escalationData.escalation_id,\n    conversation_id: chatwootData.chatwoot_data.conversation_id,\n    priority: chatwootData.escalation_priority,\n    message: 'Escalação criada/atualizada com sucesso',\n    timestamp: new Date().toISOString(),\n    processing_details: {\n      event_type: chatwootData.event_type,\n      escalation_reasons: chatwootData.escalation_reason,\n      chatwoot_labels_added: true,\n      escalation_message_sent: true,\n      integration_logged: true\n    },\n    next_steps: [\n      'Escalação será roteada para agente apropriado',\n      'Cliente será notificado sobre o progresso',\n      'Monitoramento automático ativado'\n    ]\n  }\n}];"}, "id": "format-escalation-response", "name": "📄 Format Escalation Response", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [2000, 300]}], "pinData": {}, "connections": {"chatwoot-webhook-trigger": {"main": [[{"node": "process-chatwoot-data", "type": "main", "index": 0}]]}, "process-chatwoot-data": {"main": [[{"node": "check-escalation-needed", "type": "main", "index": 0}]]}, "check-escalation-needed": {"main": [[{"node": "check-existing-escalation", "type": "main", "index": 0}], [{"node": "log-non-escalation-event", "type": "main", "index": 0}]]}, "check-existing-escalation": {"main": [[{"node": "check-no-existing", "type": "main", "index": 0}]]}, "check-no-existing": {"main": [[{"node": "prepare-escalation-data", "type": "main", "index": 0}], [{"node": "update-existing-escalation", "type": "main", "index": 0}]]}, "prepare-escalation-data": {"main": [[{"node": "create-escalation", "type": "main", "index": 0}]]}, "create-escalation": {"main": [[{"node": "send-escalation-message", "type": "main", "index": 0}, {"node": "add-escalation-labels", "type": "main", "index": 0}]]}, "update-existing-escalation": {"main": [[{"node": "add-escalation-labels", "type": "main", "index": 0}]]}, "add-escalation-labels": {"main": [[{"node": "log-chatwoot-integration", "type": "main", "index": 0}]]}, "send-escalation-message": {"main": [[{"node": "log-chatwoot-integration", "type": "main", "index": 0}]]}, "log-chatwoot-integration": {"main": [[{"node": "format-escalation-response", "type": "main", "index": 0}]]}, "log-non-escalation-event": {"main": [[{"node": "format-non-escalation-response", "type": "main", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1", "saveManualExecutions": true, "callerPolicy": "workflowsFromSameOwner", "errorWorkflow": "error-handler-workflow"}, "versionId": "chatwoot-integration-v2.0", "meta": {"templateCredsSetupCompleted": true, "instanceId": "escalation-chatwoot-integration"}, "id": "chatwoot-integration-workflow", "tags": [{"createdAt": "2024-01-15T10:00:00.000Z", "updatedAt": "2024-01-15T10:00:00.000Z", "id": "escalation-integration", "name": "escalation-integration"}]}