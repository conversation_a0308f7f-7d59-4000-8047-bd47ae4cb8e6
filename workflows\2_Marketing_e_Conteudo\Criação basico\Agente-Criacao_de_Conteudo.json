{"name": "Agente de Conteúdo", "nodes": [{"parameters": {"options": {"systemMessage": "=# Visão Geral  \nVocê é um agente de IA da equipe de marketing. Seu trabalho é ajudar o usuário a criar e editar imagens com base na solicitação, ou criar conteúdo.\n\n## Ferramentas \n\ncreateImage – Use esta ferramenta para criar uma imagem. Envie o prompt da imagem solicitada para esta ferramenta.\n\neditImage – Use esta ferramenta para editar uma imagem. O usuário também pode dizer \"make\" em vez de \"edit\".\n\nblogPost - Use this to create a blog post.\n\nredesPost – Use esta ferramenta para criar um post para o LinkedIn.  \nvideo – Use esta ferramenta para criar um vídeo.\n\nThink – Use esta ferramenta se precisar de ajuda para tomar uma decisão.\n\n## Instruções \n\n- Se o usuário pedir para \"editar essa imagem\" ou \"fazer isso...\", isso indica que ele deseja editar a última imagem no banco de dados, a mais recente.  \n- Se o usuário solicitar um post de blog ou um post para o LinkedIn, use a ferramenta \"blogPost\" ou \"linkedinPost\" e retorne: \"Here's that post you requested. Hope you enjoy it.\"\n\n## Saída  \n- Certifique-se de retornar o link da imagem como um link clicável.\n\n"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.9, "position": [-180, -40], "id": "9a459673-1c1d-4d84-8cf2-a092274407da", "name": "AI Agent"}, {"parameters": {"content": "### ✍️ Agente de Criação de Conteúdo\n\n**Propósito**: Este agente é um especialista em marketing de conteúdo. Sua função é auxiliar o usuário a criar diversos tipos de mídia, como imagens, posts para blog, textos para redes sociais e roteiros de vídeo.\n\n**Como Funciona**:\n1. O `Telegram Trigger` recebe uma solicitação (texto ou áudio).\n2. O `AI Agent` interpreta o pedido do usuário.\n3. Ele escolhe a ferramenta apropriada (`createImage`, `blogPost`, etc.) para gerar o conteúdo.\n4. Cada ferramenta é um sub-workflow que executa a tarefa específica.\n5. O resultado é devolvido ao usuário via Telegram.", "height": 340, "width": 460, "color": 3}, "type": "n8n-nodes-base.stickyNote", "position": [-620, -280], "typeVersion": 1, "id": "a2b1c0b9-a8b7-c6d5-e4f3-a2b1c0b9a8b7", "name": "Nota Explicativa"}, {"parameters": {"model": {"__rl": true, "value": "gpt-4.1-mini", "mode": "list", "cachedResultName": "gpt-4.1-mini"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [-200, -180], "id": "5a3ee747-1591-438e-86d6-02869462fe51", "name": "OpenAI Chat Model", "credentials": {"openAiApi": {"id": "T6foWC2wK6ivunma", "name": "OpenAi account"}}}, {"parameters": {"contextWindowLength": 10}, "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "typeVersion": 1.3, "position": [-100, -180], "id": "3d5dbee9-7b5c-44ee-a1d8-808b60028ce8", "name": "Simple Memory"}, {"parameters": {}, "type": "@n8n/n8n-nodes-langchain.toolThink", "typeVersion": 1, "position": [0, -180], "id": "af68e5ae-71dd-4fc7-a5e8-99cee42982f4", "name": "Think"}, {"parameters": {"updates": ["message"], "additionalFields": {}}, "type": "n8n-nodes-base.telegramTrigger", "typeVersion": 1.2, "position": [-1200, -80], "id": "fe7f5562-5a37-42a1-897e-53f8d3515b71", "name": "<PERSON>eg<PERSON>", "webhookId": "17c725b1-b270-4e4c-a883-9e86a47d5cc0", "credentials": {"telegramApi": {"id": "ye8Byaa58HxxjJgo", "name": "Telegram AI News"}}}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{ $json.message.voice.file_id }}", "rightValue": "", "operator": {"type": "string", "operation": "exists", "singleValue": true}, "id": "fe2f2b65-9eb2-4395-ba04-1688f6043e43"}], "combinator": "and"}, "renameOutput": true, "outputKey": "Audio"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "5be4e0d8-0667-4602-8887-ceabbdd5854c", "leftValue": "={{ $json.message.text }}", "rightValue": "", "operator": {"type": "string", "operation": "exists", "singleValue": true}}], "combinator": "and"}, "renameOutput": true, "outputKey": "Text"}]}, "options": {}}, "type": "n8n-nodes-base.switch", "typeVersion": 3.2, "position": [-980, -80], "id": "704e2807-6f86-49ac-843b-42366f4982fb", "name": "Switch"}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-760, 20], "id": "e554578b-6989-481c-982e-367ad835097f", "name": "Texto"}, {"parameters": {"resource": "file", "fileId": "={{ $json.message.voice.file_id }}"}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-760, -180], "id": "94b6b13c-5638-49d7-a6f9-a52c4da97a3f", "name": "Download audio", "webhookId": "594d3284-2bb8-4732-9af9-d902551923d5", "credentials": {"telegramApi": {"id": "ye8Byaa58HxxjJgo", "name": "Telegram AI News"}}}, {"parameters": {"resource": "audio", "operation": "transcribe", "options": {}}, "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.8, "position": [-580, -180], "id": "d11202aa-31ac-4c1c-a69b-30a97484b155", "name": "Transcribe Audio", "credentials": {"openAiApi": {"id": "T6foWC2wK6ivunma", "name": "OpenAi account"}}}, {"parameters": {}, "type": "n8n-nodes-base.noOp", "typeVersion": 1, "position": [-420, -40], "id": "3e3e018b-e204-4175-b8ec-86cb062781db", "name": "No Operation, do nothing"}, {"parameters": {"workflowId": {"__rl": true, "value": "qxLa5jkTrAoWZhwz", "mode": "id"}, "workflowInputs": {"mappingMode": "defineBelow", "value": {}, "matchingColumns": [], "schema": [], "attemptToConvertTypes": false, "convertFieldsToString": false}}, "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "typeVersion": 2.2, "position": [-220, 240], "id": "f9538d24-dc0b-428f-a2a6-9a9fb5259982", "name": "video"}, {"parameters": {"description": "Use essa tool para criar imagens", "workflowId": {"__rl": true, "value": "Do8SVOl8bOGEZSIT", "mode": "id"}, "workflowInputs": {"mappingMode": "defineBelow", "value": {"imageTitle": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('imageTitle', ``, 'string') }}", "imagePrompt": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('imagePrompt', ``, 'string') }}", "chatID": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('chatID', ``, 'string') }}"}, "matchingColumns": [], "schema": [{"id": "imageTitle", "displayName": "imageTitle", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string"}, {"id": "imagePrompt", "displayName": "imagePrompt", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string"}, {"id": "chatID", "displayName": "chatID", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string"}], "attemptToConvertTypes": false, "convertFieldsToString": false}}, "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "typeVersion": 2.2, "position": [-20, 240], "id": "bb540357-6eaf-43e5-9515-8267819b7a59", "name": "createImage"}, {"parameters": {"description": "Use essa tool para criar post no blog.", "workflowId": {"__rl": true, "value": "PxhwqYgA1urvCaNy", "mode": "id"}, "workflowInputs": {"mappingMode": "defineBelow", "value": {"blogTopic": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('blogTopic', `o topico do blog`, 'string') }}", "targetAudience": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('targetAudience', `a audiencia do blog`, 'string') }}", "chatID": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('chatID', ``, 'string') }}"}, "matchingColumns": [], "schema": [{"id": "blogTopic", "displayName": "blogTopic", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string"}, {"id": "targetAudience", "displayName": "targetAudience", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string"}, {"id": "chatID", "displayName": "chatID", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string"}], "attemptToConvertTypes": false, "convertFieldsToString": false}}, "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "typeVersion": 2.2, "position": [140, 240], "id": "0425f9cb-df0d-47a1-b421-59ca81b4eb09", "name": "blogPost"}, {"parameters": {"chatId": "={{ $('<PERSON><PERSON><PERSON> Trigger').first().json.message.chat.id }}", "text": "={{ $json.output }}", "additionalFields": {"appendAttribution": false}}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [260, -40], "id": "aea3be01-3786-4495-a206-b48ed140cfe1", "name": "Telegram", "webhookId": "fbdb3bd7-490b-4d3e-961e-e2d398294649", "credentials": {"telegramApi": {"id": "ye8Byaa58HxxjJgo", "name": "Telegram AI News"}}}], "pinData": {}, "connections": {"OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Simple Memory": {"ai_memory": [[{"node": "AI Agent", "type": "ai_memory", "index": 0}]]}, "Think": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Telegram Trigger": {"main": [[{"node": "Switch", "type": "main", "index": 0}]]}, "Switch": {"main": [[{"node": "Download audio", "type": "main", "index": 0}], [{"node": "Texto", "type": "main", "index": 0}]]}, "Download audio": {"main": [[{"node": "Transcribe Audio", "type": "main", "index": 0}]]}, "Transcribe Audio": {"main": [[{"node": "No Operation, do nothing", "type": "main", "index": 0}]]}, "Texto": {"main": [[{"node": "No Operation, do nothing", "type": "main", "index": 0}]]}, "No Operation, do nothing": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "video": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "createImage": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "blogPost": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "AI Agent": {"main": [[{"node": "Telegram", "type": "main", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1"}, "versionId": "f51ce37f-b45b-4547-85bd-6f0912bbfdea", "meta": {"templateCredsSetupCompleted": true, "instanceId": "40a3608cbbf4da5cb6dca30256d0a1a7360d80edc139e81ae91f6a674256a117"}, "id": "gMDk00D6jwRgsIU4", "tags": []}