{"name": "[ESCALATION] Queue Monitor & Metrics", "nodes": [{"parameters": {"rule": {"interval": [{"field": "minutes", "minutesInterval": 5}]}}, "id": "schedule-trigger", "name": "⏰ Trigger: Every 5 Minutes", "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1.1, "position": [240, 300]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "-- Consulta de métricas de filas\nSELECT \n  hq.queue_name,\n  hq.specialization,\n  hq.max_capacity,\n  hq.current_load,\n  hq.is_active,\n  hq.sla_target_hours,\n  -- Escalações pendentes\n  COUNT(CASE WHEN ie.status IN ('pending', 'assigned') THEN 1 END) as pending_escalations,\n  -- Escalações críticas\n  COUNT(CASE WHEN ie.urgency_level = 'critical' AND ie.status IN ('pending', 'assigned') THEN 1 END) as critical_escalations,\n  -- Escalações próximas ao SLA\n  COUNT(CASE WHEN ie.sla_deadline < NOW() + INTERVAL '1 hour' AND ie.status IN ('pending', 'assigned') THEN 1 END) as sla_risk_escalations,\n  -- Tempo médio de resposta (últimas 24h)\n  COALESCE(AVG(EXTRACT(EPOCH FROM (ie.resolved_at - ie.created_at))/3600), 0) as avg_resolution_hours_24h,\n  -- Agentes disponíveis\n  COUNT(CASE WHEN ha.status = 'available' AND ha.assigned_queue = hq.queue_name THEN 1 END) as available_agents,\n  -- Agentes ocupados\n  COUNT(CASE WHEN ha.status = 'busy' AND ha.assigned_queue = hq.queue_name THEN 1 END) as busy_agents,\n  -- Taxa de utilização\n  CASE \n    WHEN hq.max_capacity > 0 THEN ROUND((hq.current_load::decimal / hq.max_capacity::decimal) * 100, 2)\n    ELSE 0\n  END as utilization_percentage,\n  -- Última atualização\n  hq.updated_at as last_updated\nFROM agent.human_queues hq\nLEFT JOIN agent.intelligent_escalations ie ON ie.assigned_queue = hq.queue_name \n  AND ie.created_at > NOW() - INTERVAL '24 hours'\nLEFT JOIN agent.human_agents ha ON ha.assigned_queue = hq.queue_name\nWHERE hq.is_active = true\nGROUP BY \n  hq.queue_name, hq.specialization, hq.max_capacity, \n  hq.current_load, hq.is_active, hq.sla_target_hours, hq.updated_at\nORDER BY \n  critical_escalations DESC, \n  sla_risk_escalations DESC, \n  utilization_percentage DESC"}, "id": "queue-metrics", "name": "📊 DB: Get Queue Metrics", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [460, 300], "credentials": {"postgres": {"id": "postgres-main", "name": "PostgreSQL - Main Database"}}}, {"parameters": {"jsCode": "// =====================================================\n// QUEUE ANALYZER - INTELLIGENT MONITORING\n// =====================================================\n\nconst timestamp = new Date().toISOString();\nconst queues = $json;\n\n// Função para determinar status da fila\nfunction getQueueStatus(queue) {\n  const {\n    utilization_percentage,\n    critical_escalations,\n    sla_risk_escalations,\n    available_agents,\n    pending_escalations\n  } = queue;\n  \n  // Crítico\n  if (critical_escalations > 5 || utilization_percentage > 90 || available_agents === 0) {\n    return 'critical';\n  }\n  \n  // Alto risco\n  if (critical_escalations > 2 || utilization_percentage > 75 || sla_risk_escalations > 3) {\n    return 'high_risk';\n  }\n  \n  // Atenção\n  if (utilization_percentage > 50 || sla_risk_escalations > 0) {\n    return 'attention';\n  }\n  \n  // Normal\n  return 'normal';\n}\n\n// Função para gerar alertas\nfunction generateAlerts(queue) {\n  const alerts = [];\n  const queueName = queue.queue_name;\n  \n  if (queue.critical_escalations > 0) {\n    alerts.push({\n      type: 'critical_escalations',\n      severity: 'high',\n      message: `${queue.critical_escalations} escalações críticas pendentes em ${queueName}`,\n      action_required: 'Alocar agentes imediatamente'\n    });\n  }\n  \n  if (queue.sla_risk_escalations > 0) {\n    alerts.push({\n      type: 'sla_risk',\n      severity: 'medium',\n      message: `${queue.sla_risk_escalations} escalações próximas ao SLA em ${queueName}`,\n      action_required: 'Priorizar atendimento'\n    });\n  }\n  \n  if (queue.available_agents === 0 && queue.pending_escalations > 0) {\n    alerts.push({\n      type: 'no_agents',\n      severity: 'high',\n      message: `Nenhum agente disponível em ${queueName} com ${queue.pending_escalations} escalações pendentes`,\n      action_required: 'Realocar agentes ou ativar agentes offline'\n    });\n  }\n  \n  if (queue.utilization_percentage > 85) {\n    alerts.push({\n      type: 'high_utilization',\n      severity: 'medium',\n      message: `Alta utilização (${queue.utilization_percentage}%) em ${queueName}`,\n      action_required: 'Considerar aumentar capacidade'\n    });\n  }\n  \n  if (queue.avg_resolution_hours_24h > queue.sla_target_hours * 1.5) {\n    alerts.push({\n      type: 'slow_resolution',\n      severity: 'medium',\n      message: `Tempo de resolução acima do SLA em ${queueName} (${queue.avg_resolution_hours_24h.toFixed(1)}h vs ${queue.sla_target_hours}h)`,\n      action_required: 'Revisar processos e treinamento'\n    });\n  }\n  \n  return alerts;\n}\n\n// Função para calcular recomendações\nfunction generateRecommendations(queue) {\n  const recommendations = [];\n  const queueName = queue.queue_name;\n  \n  // Recomendações baseadas em utilização\n  if (queue.utilization_percentage > 80) {\n    recommendations.push({\n      type: 'capacity',\n      priority: 'high',\n      action: 'Aumentar capacidade da fila',\n      details: `Fila ${queueName} operando em ${queue.utilization_percentage}% de capacidade`\n    });\n  }\n  \n  // Recomendações baseadas em agentes\n  const totalAgents = queue.available_agents + queue.busy_agents;\n  const agentUtilization = totalAgents > 0 ? (queue.busy_agents / totalAgents) * 100 : 0;\n  \n  if (agentUtilization > 90) {\n    recommendations.push({\n      type: 'staffing',\n      priority: 'medium',\n      action: 'Adicionar mais agentes',\n      details: `${agentUtilization.toFixed(1)}% dos agentes estão ocupados em ${queueName}`\n    });\n  }\n  \n  // Recomendações baseadas em performance\n  if (queue.avg_resolution_hours_24h > queue.sla_target_hours) {\n    recommendations.push({\n      type: 'performance',\n      priority: 'medium',\n      action: 'Otimizar processos de resolução',\n      details: `Tempo médio de resolução (${queue.avg_resolution_hours_24h.toFixed(1)}h) acima do SLA (${queue.sla_target_hours}h)`\n    });\n  }\n  \n  return recommendations;\n}\n\n// Processar cada fila\nconst processedQueues = queues.map(queue => {\n  const status = getQueueStatus(queue);\n  const alerts = generateAlerts(queue);\n  const recommendations = generateRecommendations(queue);\n  \n  return {\n    ...queue,\n    status,\n    alerts,\n    recommendations,\n    health_score: calculateHealthScore(queue),\n    analyzed_at: timestamp\n  };\n});\n\n// Função para calcular score de saúde\nfunction calculateHealthScore(queue) {\n  let score = 100;\n  \n  // Penalizar por utilização alta\n  if (queue.utilization_percentage > 90) score -= 30;\n  else if (queue.utilization_percentage > 75) score -= 20;\n  else if (queue.utilization_percentage > 50) score -= 10;\n  \n  // Penalizar por escalações críticas\n  score -= queue.critical_escalations * 15;\n  \n  // Penalizar por risco de SLA\n  score -= queue.sla_risk_escalations * 10;\n  \n  // Penalizar por falta de agentes\n  if (queue.available_agents === 0 && queue.pending_escalations > 0) {\n    score -= 25;\n  }\n  \n  // Penalizar por tempo de resolução alto\n  if (queue.avg_resolution_hours_24h > queue.sla_target_hours) {\n    const ratio = queue.avg_resolution_hours_24h / queue.sla_target_hours;\n    score -= Math.min((ratio - 1) * 20, 20);\n  }\n  \n  return Math.max(score, 0);\n}\n\n// Calcular estatísticas globais\nconst globalStats = {\n  total_queues: processedQueues.length,\n  active_queues: processedQueues.filter(q => q.is_active).length,\n  critical_queues: processedQueues.filter(q => q.status === 'critical').length,\n  high_risk_queues: processedQueues.filter(q => q.status === 'high_risk').length,\n  total_pending_escalations: processedQueues.reduce((sum, q) => sum + q.pending_escalations, 0),\n  total_critical_escalations: processedQueues.reduce((sum, q) => sum + q.critical_escalations, 0),\n  total_available_agents: processedQueues.reduce((sum, q) => sum + q.available_agents, 0),\n  total_busy_agents: processedQueues.reduce((sum, q) => sum + q.busy_agents, 0),\n  avg_health_score: processedQueues.reduce((sum, q) => sum + q.health_score, 0) / processedQueues.length,\n  timestamp\n};\n\n// Determinar se alertas críticos devem ser enviados\nconst criticalAlerts = processedQueues\n  .flatMap(q => q.alerts)\n  .filter(alert => alert.severity === 'high');\n\nconst shouldSendCriticalAlert = criticalAlerts.length > 0;\n\nconsole.log('Queue monitoring completed', {\n  totalQueues: globalStats.total_queues,\n  criticalQueues: globalStats.critical_queues,\n  totalCriticalEscalations: globalStats.total_critical_escalations,\n  avgHealthScore: globalStats.avg_health_score.toFixed(1)\n});\n\nreturn [{\n  json: {\n    queues: processedQueues,\n    global_stats: globalStats,\n    critical_alerts: criticalAlerts,\n    should_send_alert: shouldSendCriticalAlert,\n    analysis_timestamp: timestamp\n  }\n}];"}, "id": "queue-analyzer", "name": "🔍 Analyzer: Queue Health Analysis", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [680, 300]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "critical-alert-needed", "leftValue": "={{ $json.should_send_alert }}", "rightValue": true, "operator": {"type": "boolean", "operation": "equal"}}], "combinator": "and"}, "options": {}}, "id": "alert-check", "name": "🚨 Check: Critical Alerts", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [900, 300]}, {"parameters": {"jsCode": "// =====================================================\n// SLACK ALERT BUILDER - CRITICAL NOTIFICATIONS\n// =====================================================\n\nconst { critical_alerts, global_stats, queues } = $json;\nconst timestamp = new Date().toLocaleString('pt-BR', {\n  timeZone: 'America/Sao_Paulo',\n  day: '2-digit',\n  month: '2-digit',\n  year: 'numeric',\n  hour: '2-digit',\n  minute: '2-digit'\n});\n\n// Função para determinar emoji baseado na severidade\nfunction getSeverityEmoji(severity) {\n  const emojiMap = {\n    high: '🚨',\n    medium: '⚠️',\n    low: '📝'\n  };\n  return emojiMap[severity] || '📝';\n}\n\n// Função para determinar cor baseada na severidade\nfunction getSeverityColor(severity) {\n  const colorMap = {\n    high: '#FF0000',\n    medium: '#FF8C00',\n    low: '#FFD700'\n  };\n  return colorMap[severity] || '#FFD700';\n}\n\n// Agrupar alertas por severidade\nconst alertsBySeverity = critical_alerts.reduce((acc, alert) => {\n  if (!acc[alert.severity]) acc[alert.severity] = [];\n  acc[alert.severity].push(alert);\n  return acc;\n}, {});\n\n// Construir campos de alerta\nconst alertFields = [];\n\n// Estatísticas globais\nalertFields.push({\n  title: '📊 Estatísticas Globais',\n  value: `• Filas Críticas: ${global_stats.critical_queues}/${global_stats.total_queues}\\n• Escalações Críticas: ${global_stats.total_critical_escalations}\\n• Agentes Disponíveis: ${global_stats.total_available_agents}\\n• Score de Saúde Médio: ${global_stats.avg_health_score.toFixed(1)}%`,\n  short: false\n});\n\n// Alertas por severidade\nObject.entries(alertsBySeverity).forEach(([severity, alerts]) => {\n  const emoji = getSeverityEmoji(severity);\n  const alertList = alerts.map(alert => \n    `• ${alert.message}\\n  *Ação:* ${alert.action_required}`\n  ).join('\\n\\n');\n  \n  alertFields.push({\n    title: `${emoji} Alertas ${severity.toUpperCase()}`,\n    value: alertList,\n    short: false\n  });\n});\n\n// Filas mais críticas\nconst criticalQueues = queues\n  .filter(q => q.status === 'critical')\n  .sort((a, b) => a.health_score - b.health_score)\n  .slice(0, 3);\n\nif (criticalQueues.length > 0) {\n  const queueList = criticalQueues.map(q => \n    `• *${q.queue_name}*: ${q.health_score.toFixed(1)}% saúde, ${q.critical_escalations} críticas, ${q.utilization_percentage}% utilização`\n  ).join('\\n');\n  \n  alertFields.push({\n    title: '🔥 Filas Mais Críticas',\n    value: queueList,\n    short: false\n  });\n}\n\n// Construir mensagem Slack\nconst slackMessage = {\n  channel: '#critical-escalations',\n  username: 'Queue Monitor',\n  icon_emoji: ':warning:',\n  attachments: [\n    {\n      color: '#FF0000',\n      title: '🚨 ALERTA CRÍTICO - Sistema de Escalação',\n      title_link: 'https://dashboard.empresa.com/queues/monitor',\n      fields: alertFields,\n      actions: [\n        {\n          type: 'button',\n          text: 'Ver Dashboard',\n          style: 'primary',\n          url: 'https://dashboard.empresa.com/queues/monitor'\n        },\n        {\n          type: 'button',\n          text: 'Gerenciar Filas',\n          url: 'https://dashboard.empresa.com/queues/manage'\n        },\n        {\n          type: 'button',\n          text: 'Alocar Agentes',\n          url: 'https://dashboard.empresa.com/agents/allocate'\n        }\n      ],\n      footer: 'Queue Monitor v1.0',\n      footer_icon: 'https://empresa.com/assets/monitor-icon.png',\n      ts: Math.floor(Date.now() / 1000)\n    }\n  ]\n};\n\nconsole.log('Critical alert notification prepared', {\n  alertCount: critical_alerts.length,\n  criticalQueues: global_stats.critical_queues,\n  timestamp\n});\n\nreturn [{ json: slackMessage }];"}, "id": "slack-alert-builder", "name": "📢 Slack: Build Critical Alert", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1120, 200]}, {"parameters": {"authentication": "predefinedCredentialType", "nodeCredentialType": "slackApi", "resource": "message", "operation": "post", "channel": "={{ $json.channel }}", "text": "Alerta crítico do sistema de escalação", "attachments": "={{ $json.attachments }}", "otherOptions": {"username": "={{ $json.username }}", "icon_emoji": "={{ $json.icon_emoji }}"}}, "id": "slack-send-alert", "name": "📤 Slack: Send Critical Alert", "type": "n8n-nodes-base.slack", "typeVersion": 2.1, "position": [1340, 200], "credentials": {"slackApi": {"id": "slack-main", "name": "Slack API - Main"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "-- Inserir métricas de monitoramento\nINSERT INTO agent.escalation_metrics (\n  escalation_id, metric_type, metric_name, metric_value, \n  unit, component, operation, metadata\n) \nSELECT \n  NULL as escalation_id,\n  'monitoring' as metric_type,\n  unnest(ARRAY[\n    'total_queues', 'critical_queues', 'high_risk_queues',\n    'total_pending_escalations', 'total_critical_escalations',\n    'total_available_agents', 'total_busy_agents', 'avg_health_score'\n  ]) as metric_name,\n  unnest(ARRAY[\n    $1::decimal, $2::decimal, $3::decimal,\n    $4::decimal, $5::decimal, $6::decimal, $7::decimal, $8::decimal\n  ]) as metric_value,\n  unnest(ARRAY[\n    'count', 'count', 'count',\n    'count', 'count', 'count', 'count', 'percentage'\n  ]) as unit,\n  'queue_monitor' as component,\n  'monitor' as operation,\n  $9::jsonb as metadata", "options": {"queryParameters": "={{ [\n            $json.global_stats.total_queues,\n            $json.global_stats.critical_queues,\n            $json.global_stats.high_risk_queues,\n            $json.global_stats.total_pending_escalations,\n            $json.global_stats.total_critical_escalations,\n            $json.global_stats.total_available_agents,\n            $json.global_stats.total_busy_agents,\n            $json.global_stats.avg_health_score,\n            JSON.stringify({\n              timestamp: $json.analysis_timestamp,\n              alert_sent: $json.should_send_alert,\n              critical_alerts_count: $json.critical_alerts.length\n            })\n          ] }}"}}, "id": "store-monitoring-metrics", "name": "💾 DB: Store Monitoring Metrics", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [900, 500], "credentials": {"postgres": {"id": "postgres-main", "name": "PostgreSQL - Main Database"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "-- Atualizar status das filas baseado na análise\nUPDATE agent.human_queues \nSET \n  current_load = subq.pending_escalations,\n  last_health_check = NOW(),\n  health_score = subq.health_score,\n  status_details = subq.status_details,\n  updated_at = NOW()\nFROM (\n  SELECT \n    unnest($1::text[]) as queue_name,\n    unnest($2::int[]) as pending_escalations,\n    unnest($3::decimal[]) as health_score,\n    unnest($4::jsonb[]) as status_details\n) subq\nWHERE agent.human_queues.queue_name = subq.queue_name", "options": {"queryParameters": "={{ [\n            $json.queues.map(q => q.queue_name),\n            $json.queues.map(q => q.pending_escalations),\n            $json.queues.map(q => q.health_score),\n            $json.queues.map(q => JSON.stringify({\n              status: q.status,\n              alerts_count: q.alerts.length,\n              recommendations_count: q.recommendations.length,\n              last_analyzed: q.analyzed_at\n            }))\n          ] }}"}}, "id": "update-queue-status", "name": "🔄 DB: Update Queue Status", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [1120, 500], "credentials": {"postgres": {"id": "postgres-main", "name": "PostgreSQL - Main Database"}}}, {"parameters": {"jsCode": "// =====================================================\n// DASHBOARD DATA BUILDER - REAL-TIME METRICS\n// =====================================================\n\nconst { queues, global_stats, analysis_timestamp } = $json;\n\n// Preparar dados para dashboard\nconst dashboardData = {\n  // Timestamp\n  last_updated: analysis_timestamp,\n  \n  // Estatísticas globais\n  overview: {\n    total_queues: global_stats.total_queues,\n    active_queues: global_stats.active_queues,\n    critical_queues: global_stats.critical_queues,\n    high_risk_queues: global_stats.high_risk_queues,\n    normal_queues: global_stats.total_queues - global_stats.critical_queues - global_stats.high_risk_queues,\n    avg_health_score: Math.round(global_stats.avg_health_score)\n  },\n  \n  // Estatísticas de escalações\n  escalations: {\n    total_pending: global_stats.total_pending_escalations,\n    total_critical: global_stats.total_critical_escalations,\n    sla_risk: queues.reduce((sum, q) => sum + q.sla_risk_escalations, 0)\n  },\n  \n  // Estatísticas de agentes\n  agents: {\n    total_available: global_stats.total_available_agents,\n    total_busy: global_stats.total_busy_agents,\n    total_agents: global_stats.total_available_agents + global_stats.total_busy_agents,\n    utilization_rate: global_stats.total_available_agents + global_stats.total_busy_agents > 0 ? \n      Math.round((global_stats.total_busy_agents / (global_stats.total_available_agents + global_stats.total_busy_agents)) * 100) : 0\n  },\n  \n  // Detalhes das filas\n  queue_details: queues.map(queue => ({\n    name: queue.queue_name,\n    specialization: queue.specialization,\n    status: queue.status,\n    health_score: Math.round(queue.health_score),\n    utilization: queue.utilization_percentage,\n    pending_escalations: queue.pending_escalations,\n    critical_escalations: queue.critical_escalations,\n    sla_risk_escalations: queue.sla_risk_escalations,\n    available_agents: queue.available_agents,\n    busy_agents: queue.busy_agents,\n    avg_resolution_hours: Math.round(queue.avg_resolution_hours_24h * 10) / 10,\n    sla_target_hours: queue.sla_target_hours,\n    alerts_count: queue.alerts.length,\n    recommendations_count: queue.recommendations.length\n  })),\n  \n  // Top 5 filas por prioridade\n  priority_queues: queues\n    .sort((a, b) => {\n      // Priorizar por: críticas > alto risco > utilização > escalações pendentes\n      if (a.status !== b.status) {\n        const statusPriority = { critical: 3, high_risk: 2, attention: 1, normal: 0 };\n        return (statusPriority[b.status] || 0) - (statusPriority[a.status] || 0);\n      }\n      if (a.critical_escalations !== b.critical_escalations) {\n        return b.critical_escalations - a.critical_escalations;\n      }\n      if (a.utilization_percentage !== b.utilization_percentage) {\n        return b.utilization_percentage - a.utilization_percentage;\n      }\n      return b.pending_escalations - a.pending_escalations;\n    })\n    .slice(0, 5)\n    .map(queue => ({\n      name: queue.queue_name,\n      status: queue.status,\n      health_score: Math.round(queue.health_score),\n      critical_escalations: queue.critical_escalations,\n      utilization: queue.utilization_percentage,\n      priority_reason: queue.critical_escalations > 0 ? 'Escalações críticas' :\n                      queue.utilization_percentage > 80 ? 'Alta utilização' :\n                      queue.sla_risk_escalations > 0 ? 'Risco de SLA' : 'Escalações pendentes'\n    })),\n  \n  // Alertas ativos\n  active_alerts: queues\n    .flatMap(queue => queue.alerts.map(alert => ({\n      queue_name: queue.queue_name,\n      type: alert.type,\n      severity: alert.severity,\n      message: alert.message,\n      action_required: alert.action_required\n    })))\n    .sort((a, b) => {\n      const severityOrder = { high: 3, medium: 2, low: 1 };\n      return (severityOrder[b.severity] || 0) - (severityOrder[a.severity] || 0);\n    }),\n  \n  // Recomendações\n  recommendations: queues\n    .flatMap(queue => queue.recommendations.map(rec => ({\n      queue_name: queue.queue_name,\n      type: rec.type,\n      priority: rec.priority,\n      action: rec.action,\n      details: rec.details\n    })))\n    .sort((a, b) => {\n      const priorityOrder = { high: 3, medium: 2, low: 1 };\n      return (priorityOrder[b.priority] || 0) - (priorityOrder[a.priority] || 0);\n    })\n    .slice(0, 10), // Top 10 recomendações\n  \n  // Métricas de performance\n  performance: {\n    avg_resolution_time: Math.round(\n      queues.reduce((sum, q) => sum + q.avg_resolution_hours_24h, 0) / queues.length * 10\n    ) / 10,\n    sla_compliance_rate: Math.round(\n      queues.filter(q => q.avg_resolution_hours_24h <= q.sla_target_hours).length / queues.length * 100\n    ),\n    total_capacity: queues.reduce((sum, q) => sum + q.max_capacity, 0),\n    total_load: queues.reduce((sum, q) => sum + q.current_load, 0)\n  }\n};\n\nconsole.log('Dashboard data prepared', {\n  totalQueues: dashboardData.overview.total_queues,\n  criticalQueues: dashboardData.overview.critical_queues,\n  activeAlerts: dashboardData.active_alerts.length,\n  recommendations: dashboardData.recommendations.length\n});\n\nreturn [{ json: dashboardData }];"}, "id": "dashboard-builder", "name": "📊 Dashboard: Build Real-time Data", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1340, 500]}, {"parameters": {"url": "https://dashboard.empresa.com/api/v1/queues/metrics", "authentication": "predefinedCredentialType", "nodeCredentialType": "httpHeaderAuth", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "data", "value": "={{ $json }}"}, {"name": "timestamp", "value": "={{ $json.last_updated }}"}, {"name": "source", "value": "queue_monitor_v1"}]}, "options": {"timeout": 15000, "retry": {"enabled": true, "maxTries": 2}}}, "id": "dashboard-update", "name": "📡 Dashboard: Update Real-time Data", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1560, 500], "credentials": {"httpHeaderAuth": {"id": "dashboard-api", "name": "Dashboard API - Auth"}}}], "pinData": {}, "connections": {"schedule-trigger": {"main": [[{"node": "queue-metrics", "type": "main", "index": 0}]]}, "queue-metrics": {"main": [[{"node": "queue-analyzer", "type": "main", "index": 0}]]}, "queue-analyzer": {"main": [[{"node": "alert-check", "type": "main", "index": 0}, {"node": "store-monitoring-metrics", "type": "main", "index": 0}]]}, "alert-check": {"main": [[{"node": "slack-alert-builder", "type": "main", "index": 0}], []]}, "slack-alert-builder": {"main": [[{"node": "slack-send-alert", "type": "main", "index": 0}]]}, "store-monitoring-metrics": {"main": [[{"node": "update-queue-status", "type": "main", "index": 0}]]}, "update-queue-status": {"main": [[{"node": "dashboard-builder", "type": "main", "index": 0}]]}, "dashboard-builder": {"main": [[{"node": "dashboard-update", "type": "main", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1", "saveManualExecutions": false, "callerPolicy": "workflowsFromSameOwner", "errorWorkflow": "error-handler-workflow"}, "versionId": "1.0.0", "meta": {"templateCredsSetupCompleted": true, "instanceId": "queue-monitor-v1"}, "id": "queue-monitor-metrics", "tags": [{"createdAt": "2024-01-15T10:00:00.000Z", "updatedAt": "2024-01-15T10:00:00.000Z", "id": "monitoring", "name": "monitoring"}, {"createdAt": "2024-01-15T10:00:00.000Z", "updatedAt": "2024-01-15T10:00:00.000Z", "id": "metrics", "name": "metrics"}, {"createdAt": "2024-01-15T10:00:00.000Z", "updatedAt": "2024-01-15T10:00:00.000Z", "id": "enterprise", "name": "enterprise"}]}