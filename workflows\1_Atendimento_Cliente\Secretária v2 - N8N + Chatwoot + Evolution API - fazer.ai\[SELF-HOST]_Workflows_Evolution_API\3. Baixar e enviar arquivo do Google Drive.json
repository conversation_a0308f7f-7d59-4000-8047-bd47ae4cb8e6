{"nodes": [{"parameters": {"workflowInputs": {"values": [{"name": "file_id"}, {"name": "instancia"}, {"name": "telefone"}, {"name": "tipo"}, {"name": "nome_documento"}]}}, "type": "n8n-nodes-base.executeWorkflowTrigger", "typeVersion": 1.1, "position": [-120, -20], "id": "8e467f12-fed6-4300-820b-78bb0eabe326", "name": "When Executed by Another Workflow"}, {"parameters": {"resource": "messages-api", "operation": "send-image", "instanceName": "={{ $('When Executed by Another Workflow').item.json.instancia }}", "remoteJid": "={{ $('When Executed by Another Workflow').item.json.telefone }}", "media": "={{ $json.data }}", "options_message": {}}, "type": "n8n-nodes-evolution-api.evolutionApi", "typeVersion": 1, "position": [840, -120], "id": "2a1858c5-ad72-48a8-84cd-81afca6193c6", "name": "Enviar imagem", "credentials": {}}, {"parameters": {"resource": "messages-api", "operation": "send-document", "instanceName": "={{ $('When Executed by Another Workflow').item.json.instancia }}", "remoteJid": "={{ $('When Executed by Another Workflow').item.json.telefone }}", "media": "={{ $json.data }}", "fileName": "={{ $('When Executed by Another Workflow').item.json.nome_documento || '' }}", "options_message": {}}, "type": "n8n-nodes-evolution-api.evolutionApi", "typeVersion": 1, "position": [840, 60], "id": "0522cb7e-5b03-46d4-a4fb-09fbda727327", "name": "Enviar documento", "credentials": {}}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{ $('When Executed by Another Workflow').item.json.tipo }}", "rightValue": "imagem", "operator": {"type": "string", "operation": "equals"}, "id": "de580b52-c344-4832-ba99-e4453d158173"}], "combinator": "and"}, "renameOutput": true, "outputKey": "Imagem"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "1cff2119-c28d-43a2-9715-0ff5b944f743", "leftValue": "={{ $('When Executed by Another Workflow').item.json.tipo }}", "rightValue": "documento", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "Documento"}]}, "options": {}}, "type": "n8n-nodes-base.switch", "typeVersion": 3.2, "position": [540, -20], "id": "49b5661c-bc84-4aa7-8aa0-d5c7862f7b39", "name": "<PERSON><PERSON> ou documento"}, {"parameters": {"operation": "binaryToPropery", "options": {}}, "type": "n8n-nodes-base.extractFromFile", "typeVersion": 1, "position": [320, -20], "id": "9dc9549c-1de8-4157-a89a-80bcaf57cea5", "name": "Converter arquivo"}, {"parameters": {"operation": "download", "fileId": {"__rl": true, "value": "={{ $json.file_id }}", "mode": "id"}, "options": {}}, "type": "n8n-nodes-base.googleDrive", "typeVersion": 3, "position": [100, -20], "id": "c2793d47-c16b-4731-a5c9-fee8c04b01dd", "name": "Baixar arquivo", "credentials": {}}, {"parameters": {"assignments": {"assignments": [{"id": "71dda3d7-4505-4d03-b70f-63f5c005c0c3", "name": "sucesso", "value": "={{ $json.success }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1100, -20], "id": "1a653abd-d0a8-47a7-bce4-c7281b1fcf09", "name": "Output"}, {"parameters": {"content": "[![fazer.ai](https://framerusercontent.com/images/HqY9djLTzyutSKnuLLqBr92KbM.png?scale-down-to=256)](https://fazer.ai?utm_source=n8n&utm_campaign=sec-ep2&utm_medium=evo-3)\n\n## Esse é um template faça você mesmo do canal\n## <PERSON> Moreira\n\n### Inscreva-se no nosso canal no YouTube\n[![YouTube Lucas Moreira](https://img.shields.io/youtube/channel/subscribers/UCtmp6SxzLscu0GRTbgM8FTw?style=flat-square&logo=youtube&label=Inscreva-se&color=f00)](https://youtube.com/@eulucassmoreira?si=0lH7hwX9pukjhmPQ)\n\n### Siga nosso GitHub\n[![GitHub fazer.ai](https://img.shields.io/badge/github-%23121011.svg?style=for-the-badge&logo=github&logoColor=white&label)](https://github.com/fazer-ai)\n", "height": 440, "width": 550, "color": 7}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [0, -600], "id": "ef825156-0687-4168-9c16-d0e4cfb59870", "name": "Sticky Note10"}, {"parameters": {"content": "## Quer entender como funciona?\n\n\n### Assista o vídeo, deixe um like, e se inscreva no canal para ter acesso a mais workflows como esse!\n\n[![IMAGE ALT TEXT HERE](https://i1.ytimg.com/vi_webp/cvTWGNJGAu4/maxresdefault.webp)](https://www.youtube.com/watch?v=cvTWGNJGAu4)", "height": 440, "width": 500, "color": 7}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [560, -600], "id": "8531ce65-54f4-403b-acac-d2f613242822", "name": "Sticky Note13"}, {"parameters": {"content": "![Evolution API](https://mintlify.s3.us-west-1.amazonaws.com/evolution/logo/dark.svg)", "height": 100, "width": 280, "color": 7}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [220, -300], "id": "3a13c3ae-71e8-4a31-a422-2e076a4b0793", "name": "Sticky Note20"}], "connections": {"When Executed by Another Workflow": {"main": [[{"node": "Baixar arquivo", "type": "main", "index": 0}]]}, "Enviar imagem": {"main": [[{"node": "Output", "type": "main", "index": 0}]]}, "Enviar documento": {"main": [[{"node": "Output", "type": "main", "index": 0}]]}, "Imagem ou documento": {"main": [[{"node": "Enviar imagem", "type": "main", "index": 0}], [{"node": "Enviar documento", "type": "main", "index": 0}]]}, "Converter arquivo": {"main": [[{"node": "<PERSON><PERSON> ou documento", "type": "main", "index": 0}]]}, "Baixar arquivo": {"main": [[{"node": "Converter arquivo", "type": "main", "index": 0}]]}}, "pinData": {}, "meta": {}}