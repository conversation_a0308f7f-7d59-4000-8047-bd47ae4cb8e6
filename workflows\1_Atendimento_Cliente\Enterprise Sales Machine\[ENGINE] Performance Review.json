{"name": "[ENGINE] Performance Review v1.1 - Cost-Aware", "nodes": [{"parameters": {"rule": "cron", "cronTime": "0 10 1 * *"}, "id": "c1a9c3d4-b8b8-47e8-b8b8-c1a9c3d4b8b8", "name": "TRIGGER: Monthly (1st day)", "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1.1, "position": [400, 300], "notes": "C.2: Roda no início de cada mês para analisar a performance."}, {"parameters": {"content": "### MOTOR DE ANÁLISE DE PERFORMANCE\n\n**Propósito:** Este workflow analisa os resultados de campanhas passadas, usa IA para gerar insights estratégicos e sugere otimizações para o próximo ciclo.\n\n**Funcionalidades:**\n- **<PERSON><PERSON><PERSON><PERSON> de Dados:** Calcula métricas de conversão para campanhas.\n- **IA Estratégica:** Usa um modelo de IA avançado para atuar como um CRO, analisando os dados e propondo novas estratégias.\n- **Ciclo de Melhoria Contínua:** Apresenta as sugestões da IA para aprovação gerencial, fechando o learning loop.", "height": 340}, "id": "docu_sticky_note", "name": "DOCUMENTAÇÃO", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [360, -100]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT \n  c.name as campaign_name,\n  c.base_prompt,\n  COUNT(ci.id) as total_sent,\n  COUNT(CASE WHEN ci.achieved_goal = TRUE THEN 1 END) as total_goals_achieved,\n  (COUNT(CASE WHEN ci.achieved_goal = TRUE THEN 1 END)::float * 100 / COUNT(ci.id)::float) as conversion_rate\nFROM agent.campaigns c\nJOIN agent.campaign_interactions ci ON c.id = ci.campaign_id\nWHERE c.status = 'archived' AND c.updated_at > NOW() - INTERVAL '1 month'\nGROUP BY c.id\nHAVING COUNT(ci.id) > 0;", "options": {}}, "id": "e2c3d4e5-f6a7-48b8-b9c1-d2e3f4a5b6c7", "name": "DB: Get Last Month's Campaign Performance", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [680, 300], "continueOnFail": true, "credentials": {"postgres": {"id": "YOUR_POSTGRES_CREDENTIAL_ID"}}}, {"parameters": {"conditions": {"number": [{"value1": "={{$items.length}}", "operation": "larger", "value2": 0}]}}, "id": "if_data_found_2", "name": "IF: Data Found?", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [920, 300]}, {"parameters": {}, "id": "no_op_stop_2", "name": "Stop: No Data", "type": "n8n-nodes-base.noOp", "typeVersion": 1, "position": [1160, 500]}, {"parameters": {"workflowId": "={{ $env.DISPATCHER_WORKFLOW_ID }}", "data": "={{ { body: { \n    prompt: `Você é um Chief Revenue Officer (CRO) analisando a performance de campanhas de marketing do último mês. Sua tarefa é extrair insights e propor uma estratégia acionável para o próximo mês.\\n\\n**DADOS DE PERFORMANCE:**\\n${JSON.stringify($items)}\\n\\n**SUA ANÁLISE:**\\n1.  **Resumo Executivo:** Qual foi o tema ou abordagem que mais funcionou? Qual teve o pior desempenho? Seja conciso.\\n2.  **Hipótese:** Por que você acredita que a campanha vencedora foi mais eficaz?\\n3.  **Plano de Ação:** Com base na sua hipótese, sugira duas novas variações de prompt (A e B) para testarmos no próximo mês. As novas mensagens devem ser uma evolução da campanha vencedora. Apresente apenas os prompts.`,\n    task_type: 'complex_analysis',\n    calling_workflow_id: $workflow.id,\n    calling_node_id: 'call_dispatcher_for_review'\n} } }}", "options": {}}, "id": "call_dispatcher_for_review", "name": "Call Dispatcher for Review", "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 2, "position": [1160, 300]}, {"parameters": {"method": "POST", "url": "={{$credentials.slackWebhook.url}}", "sendBody": true, "contentType": "application/json", "body": "={{ \n  {\n    \"blocks\": [\n      {\n        \"type\": \"header\",\n        \"text\": {\n          \"type\": \"plain_text\",\n          \"text\": \"📈 Relatório de Performance e Estratégia da IA\",\n          \"emoji\": true\n        }\n      },\n      {\n        \"type\": \"section\",\n        \"text\": {\n          \"type\": \"mrkdwn\",\n          \"text\": \"O motor de análise de performance concluiu a revisão do último mês. Abaixo estão os insights e sugestões gerados pela IA para sua aprovação:\"\n        }\n      },\n      {\n        \"type\": \"divider\"\n      },\n      {\n        \"type\": \"section\",\n        \"text\": {\n          \"type\": \"mrkdwn\",\n          \"text\": \"{{$json.choices[0].message.content}}\"\n        }\n      },\n      {\n        \"type\": \"divider\"\n      },\n      {\n        \"type\": \"context\",\n        \"elements\": [\n          {\n            \"type\": \"mrkdwn\",\n            \"text\": \"*Ação Recomendada:* Use estas sugestões para guiar a criação das próximas campanhas no motor de prospecção autônoma.\"\n          }\n        ]\n      }\n    ]\n  }\n}}", "options": {}}, "id": "f7g8h9i1-j2k3-4l4m-n5o6-p7q8r9s1t2u3", "name": "SLACK: Notify Manager with Insights", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [1400, 300], "notes": "Envia o relatório estratégico para o gerente via Slack."}], "connections": {"TRIGGER: Monthly (1st day)": {"main": [[{"node": "DB: Get Last Month's Campaign Performance", "type": "main", "index": 0}]]}, "DB: Get Last Month's Campaign Performance": {"main": [[{"node": "if_data_found_2", "type": "main", "index": 0}]]}, "if_data_found_2": {"main": [[{"node": "call_dispatcher_for_review", "type": "main", "index": 0}], [{"node": "no_op_stop_2", "type": "main", "index": 0}]]}, "call_dispatcher_for_review": {"main": [[{"node": "SLACK: Notify Manager with Insights", "type": "main", "index": 0}]]}}}