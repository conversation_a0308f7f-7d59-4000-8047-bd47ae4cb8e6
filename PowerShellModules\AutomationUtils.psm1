# AutomationUtils.psm1
# Módulo de utilitários compartilhados para scripts de automação PowerShell
# Versão: 3.0 - Módulo centralizado, robusto e completamente refatorado

# =====================================================
# CONFIGURAÇÃO INICIAL DO SISTEMA DE LOG
# =====================================================

# Definir o caminho do log (escopo do módulo)
Set-Variable -Name 'LogFile' -Scope 'Script' -Value 'install.log'

# Função para inicializar o sistema de log (chamada automaticamente na importação)
function Initialize-LogSystem {
    try {
        # Limpa o log antigo no início da execução
        "" | Out-File -FilePath $script:LogFile -Encoding utf8 -ErrorAction Stop
        Write-Host "[SYSTEM] Sistema de log inicializado: ${script:LogFile}" -ForegroundColor Green
    } catch {
        Write-Error "ERRO FATAL: Não foi possível inicializar o sistema de log em '${script:LogFile}': $($_.Exception.Message)"
        throw
    }
}

# =====================================================
# FUNÇÕES DE LOG E SAÍDA
# =====================================================

function Log-Event {
    param(
        [string]$Message,
        [string]$Component = "Orchestrator",
        [string]$Level = "INFO"
    )
    
    if (-not $script:LogFile) {
        Write-Error "Sistema de log não inicializado!"
        return
    }

    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logLine = "[${timestamp}][${Level}][${Component}] ${Message}"

    try {
        Add-Content -Path $script:LogFile -Value $logLine -ErrorAction Stop
    } catch {
        Write-Warning "Falha ao escrever no log: $($_.Exception.Message)"
    }

    $colorMap = @{
        "INFO"    = "White"
        "WARN"    = "Yellow"
        "ERROR"   = "Red"
        "FATAL"   = "Magenta"
        "DEBUG"   = "Gray"
        "SUCCESS" = "Green"
    }
    $consoleColor = $colorMap[$Level]
    if (-not $consoleColor) { $consoleColor = "White" }

    Write-Host $logLine -ForegroundColor $consoleColor

    # Incrementar contador global de erros críticos para scripts que desejem avaliar código de saída
    if ($Level -eq 'ERROR') {
        $currentCritical = 0
        try { $currentCritical = (Get-Variable -Name 'CriticalErrors' -Scope Global -ValueOnly -ErrorAction SilentlyContinue) } catch {}
        if (-not $currentCritical) { $currentCritical = 0 }
        Set-Variable -Name 'CriticalErrors' -Scope Global -Value ($currentCritical + 1)
    }
}

function Write-Host-Color {
    param(
        [string]$Message,
        [string]$Color
    )
    Write-Host $Message -ForegroundColor $Color
}

# =====================================================
# FUNÇÕES DE GERAÇÃO DE CREDENCIAIS
# =====================================================

function Generate-RandomPassword {
    param([int]$Length = 16)
    
    if ($Length -lt 8) {
        Log-Event "Comprimento de senha muito pequeno (${Length}). Usando mínimo de 8 caracteres." "Security" "WARN"
        $Length = 8
    }
    
    $chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#%^&*-_=+'
    
    try {
        $password = -join ($chars.ToCharArray() | Get-Random -Count $Length)
        Log-Event "Senha aleatória gerada com ${Length} caracteres." "Security" "DEBUG"
        return $password
    } catch {
        Log-Event "Erro ao gerar senha aleatória: $($_.Exception.Message)" "Security" "ERROR"
        throw
    }
}

function Generate-SecureKey {
    param([int]$Length = 32)
    
    if ($Length -lt 16) {
        Log-Event "Comprimento de chave muito pequeno (${Length}). Usando mínimo de 16 bytes." "Security" "WARN"
        $Length = 16
    }
    
    $validPattern = '^[A-Za-z0-9]+$'
    $attempt = 0
    $maxAttempts = 3
    do {
        $attempt++
        try {
            $bytes = New-Object byte[] $Length
            [System.Security.Cryptography.RandomNumberGenerator]::Create().GetBytes($bytes)
            $candidate = ([System.Convert]::ToBase64String($bytes)).TrimEnd('=')
            # Manter somente caracteres alfanuméricos
            $candidate = ($candidate -replace '[^A-Za-z0-9]', '')

            if ($candidate.Length -ge $Length -and ($candidate -match $validPattern)) {
                $key = $candidate.Substring(0, $Length)
                Log-Event "Chave segura gerada (tentativa ${attempt}) compatível com MinIO." "Security" "DEBUG"
                return $key
            } else {
                Log-Event "Chave gerada continha caracteres incompatíveis. Tentando novamente... (tentativa ${attempt})" "Security" "WARN"
            }
        } catch {
            Log-Event "Erro ao gerar chave segura na tentativa ${attempt}: $($_.Exception.Message)" "Security" "ERROR"
            # Continua o loop para próxima tentativa
        }
    } while ($attempt -lt $maxAttempts)

    Log-Event "Falha ao gerar chave compatível após ${maxAttempts} tentativas. Retornando chave possivelmente incompatível." "Security" "WARN"
    return $candidate.Substring(0, [math]::Min($candidate.Length, $Length))
}

# =====================================================
# FUNÇÕES DE CONTROLE DE EXECUÇÃO
# =====================================================

function Abort-Install {
    param(
        [string]$Message,
        [string]$Component = "Orchestrator"
    )
    Log-Event $Message $Component "FATAL"
    Log-Event "A instalação não pode continuar. Capturando informações de diagnóstico..." "Diagnostics" "INFO"

    # Tenta capturar o estado do Docker se o comando existir
    if (Get-Command docker -ErrorAction SilentlyContinue) {
        Log-Event "Status atual dos contêineres Docker:" "Diagnostics" "INFO"
        try {
            $dockerPsOutput = docker ps -a 2>&1
            $dockerPsOutput | ForEach-Object { Log-Event $_ "Docker" "DEBUG" }
        } catch {
            Log-Event "Erro ao capturar status do Docker: $($_.Exception.Message)" "Diagnostics" "WARN"
        }

        Log-Event "Coletando logs dos contêineres..." "Diagnostics" "INFO"
        try {
            $containerNames = docker ps -a --format "{{.Names}}" 2>$null
            foreach ($containerName in $containerNames) {
                if ($containerName) {
                    Log-Event "--- Logs para o contêiner: ${containerName} (últimas 50 linhas) ---" "Diagnostics" "INFO"
                    $dockerLogsOutput = docker logs $containerName --tail 50 2>&1
                    $dockerLogsOutput | ForEach-Object { Log-Event $_ $containerName "DEBUG" }
                    Log-Event "--- Fim dos logs para: ${containerName} ---" "Diagnostics" "INFO"
                }
            }
        } catch {
            Log-Event "Erro ao coletar logs dos contêineres: $($_.Exception.Message)" "Diagnostics" "WARN"
        }
    } else {
        Log-Event "Comando 'docker' não encontrado. Não foi possível capturar diagnósticos do Docker." "Diagnostics" "WARN"
    }
    
    Write-Host "[FATAL] Instalação abortada. Verifique o arquivo '${script:LogFile}' para detalhes completos." -ForegroundColor Red
    exit 1
}

function Run-Command {
    param(
        [string]$Command,
        [string]$ErrorMessage,
        [string]$Component = "Shell"
    )
    
    if ([string]::IsNullOrWhiteSpace($Command)) {
        Log-Event "Comando vazio fornecido para execução." $Component "ERROR"
        throw "Comando não pode ser vazio"
    }
    
    Log-Event "Executando comando: ${Command}" $Component "DEBUG"
    
    try {
        $output = Invoke-Expression $Command 2>&1
        if ($LASTEXITCODE -ne 0) {
            $output | ForEach-Object { Log-Event $_ $Component "ERROR" }
            Abort-Install "${ErrorMessage} (código de saída: ${LASTEXITCODE})" $Component
        } else {
            $output | ForEach-Object { Log-Event $_ $Component "DEBUG" }
            Log-Event "Comando executado com sucesso." $Component "DEBUG"
        }
    } catch {
        Log-Event "Erro ao executar comando '${Command}': $($_.Exception.Message)" $Component "ERROR"
        Abort-Install "${ErrorMessage} (exceção: $($_.Exception.Message))" $Component
    }
}

# =====================================================
# FUNÇÕES DE VERIFICAÇÃO DE SAÚDE E CONECTIVIDADE
# =====================================================

function Wait-For-Service-Healthy {
    param(
        [string]$ServiceName,
        [int]$TimeoutSeconds = 300,
        [int]$IntervalSeconds = 5
    )
    
    if ([string]::IsNullOrWhiteSpace($ServiceName)) {
        Log-Event "Nome do serviço não pode ser vazio." "HealthCheck" "ERROR"
        return $false
    }
    
    Log-Event "Aguardando o serviço '${ServiceName}' ficar saudável (timeout: ${TimeoutSeconds}s)..." "HealthCheck" "INFO"
    $startTime = Get-Date
    
    while ((Get-Date) -lt $startTime.AddSeconds($TimeoutSeconds)) {
        try {
            $status = docker inspect --format "{{.State.Health.Status}}" "$ServiceName" 2>$null
            if ($status -eq "healthy") {
                $elapsed = (Get-Date) - $startTime
                Log-Event "Serviço '${ServiceName}' está saudável após $([math]::Round($elapsed.TotalSeconds))s." "HealthCheck" "SUCCESS"
                return $true
            } elseif ($status) {
                Log-Event "Status de saúde de '${ServiceName}': ${status}. Aguardando..." "HealthCheck" "DEBUG"
            } else {
                # Verificar se o contêiner está rodando mesmo sem health check
                $containerStatus = docker inspect --format "{{.State.Status}}" "$ServiceName" 2>$null
                if ($containerStatus -eq "running") {
                    Log-Event "Contêiner '${ServiceName}' está rodando (sem health-check definido). Aguardando..." "HealthCheck" "DEBUG"
                } else {
                    Log-Event "Contêiner '${ServiceName}' ainda não encontrado ou não está rodando. Status: ${containerStatus}" "HealthCheck" "DEBUG"
                }
            }
        } catch {
            Log-Event "Erro ao inspecionar '${ServiceName}': $($_.Exception.Message)" "HealthCheck" "WARN"
        }
        Start-Sleep -Seconds $IntervalSeconds
    }
    
    try {
        $healthInfo = docker inspect "${ServiceName}" --format "{{json .State.Health}}" 2>$null
        Log-Event "Tempo limite excedido aguardando '${ServiceName}' ficar saudável. Detalhes: ${healthInfo}" "HealthCheck" "ERROR"
    } catch {
        Log-Event "Tempo limite excedido aguardando '${ServiceName}' ficar saudável. Não foi possível obter detalhes." "HealthCheck" "ERROR"
    }
    
    return $false
}

function Wait-For-Port {
    param(
        [string]$HostName,
        [int]$Port,
        [int]$TimeoutSeconds = 120
    )
    
    if ([string]::IsNullOrWhiteSpace($HostName) -or $Port -le 0) {
        Log-Event "Parâmetros inválidos: HostName='${HostName}', Port='${Port}'" "PortCheck" "ERROR"
        return $false
    }
    
    Log-Event "Aguardando a porta ${Port} em '${HostName}' ficar disponível (timeout: ${TimeoutSeconds}s)..." "PortCheck" "INFO"
    $startTime = Get-Date
    
    while ((Get-Date) -lt $startTime.AddSeconds($TimeoutSeconds)) {
        $tcpClient = $null
        try {
            $tcpClient = New-Object System.Net.Sockets.TcpClient
            $tcpClient.Connect($HostName, $Port)
            if ($tcpClient.Connected) {
                $elapsed = (Get-Date) - $startTime
                Log-Event "Porta ${Port} em '${HostName}' está acessível após $([math]::Round($elapsed.TotalSeconds))s." "PortCheck" "SUCCESS"
                return $true
            }
        } catch {
            # A conexão falhou, aguarda e tenta novamente
        } finally {
            if ($tcpClient) { 
                try { $tcpClient.Close() } catch {}
                try { $tcpClient.Dispose() } catch {}
            }
        }
        Start-Sleep -Seconds 5
    }
    
    Log-Event "Tempo limite excedido aguardando pela porta ${Port} em '${HostName}'." "PortCheck" "ERROR"
    return $false
}

# =====================================================
# FUNÇÕES DE DIAGNÓSTICO ESPECÍFICO
# =====================================================

function Test-RedisConnectivity {
    param(
        [string]$ContainerName = "redis_aula",
        [string]$RedisHost = "localhost",
        [int]$RedisPort = 6379
    )
    
    Log-Event "Iniciando diagnóstico completo do Redis..." "Redis-Diagnostics" "INFO"
    
    # Teste 1: Verificar se o contêiner está rodando
    try {
        $containerStatus = docker inspect --format "{{.State.Status}}" $ContainerName 2>$null
        if ($containerStatus -eq "running") {
            Log-Event "✓ Contêiner Redis está rodando" "Redis-Diagnostics" "SUCCESS"
        } else {
            Log-Event "✗ Contêiner Redis não está rodando. Status: ${containerStatus}" "Redis-Diagnostics" "ERROR"
            return $false
        }
    } catch {
        Log-Event "✗ Erro ao verificar status do contêiner Redis: $($_.Exception.Message)" "Redis-Diagnostics" "ERROR"
        return $false
    }
    
    # Teste 2: Verificar conectividade TCP na porta
    Log-Event "Testando conectividade TCP na porta ${RedisPort}..." "Redis-Diagnostics" "INFO"
    if (-not (Wait-For-Port $RedisHost $RedisPort 30)) {
        Log-Event "✗ Porta ${RedisPort} não está acessível" "Redis-Diagnostics" "ERROR"
        return $false
    }
    Log-Event "✓ Porta ${RedisPort} está acessível" "Redis-Diagnostics" "SUCCESS"
    
    # Teste 3: Ping via docker exec
    try {
        Log-Event "Executando 'redis-cli ping' dentro do contêiner..." "Redis-Diagnostics" "INFO"
        $pingOutput = docker exec $ContainerName redis-cli ping 2>&1
        if ($pingOutput -like "*PONG*") {
            Log-Event "✓ Redis responde ao PING: ${pingOutput}" "Redis-Diagnostics" "SUCCESS"
        } else {
            Log-Event "✗ Redis não responde ao PING. Saída: ${pingOutput}" "Redis-Diagnostics" "ERROR"
            return $false
        }
    } catch {
        Log-Event "✗ Erro ao executar PING no Redis: $($_.Exception.Message)" "Redis-Diagnostics" "ERROR"
        return $false
    }
    
    # Teste 4: Verificar database específico (/6) usado pela Evolution API
    try {
        Log-Event "Testando acesso ao database 6 (usado pela Evolution API)..." "Redis-Diagnostics" "INFO"
        $selectDb = docker exec $ContainerName redis-cli -n 6 ping 2>&1
        if ($selectDb -like "*PONG*") {
            Log-Event "✓ Database 6 do Redis está acessível" "Redis-Diagnostics" "SUCCESS"
        } else {
            Log-Event "⚠ Database 6 retornou: ${selectDb}" "Redis-Diagnostics" "WARN"
        }
    } catch {
        Log-Event "⚠ Não foi possível testar database 6: $($_.Exception.Message)" "Redis-Diagnostics" "WARN"
    }
    
    # Teste 5: Verificar configurações específicas
    try {
        Log-Event "Verificando configurações do Redis relacionadas aos issues conhecidos..." "Redis-Diagnostics" "INFO"
        
        $keepalive = docker exec $ContainerName redis-cli CONFIG GET tcp-keepalive 2>&1
        Log-Event "Configuração tcp-keepalive: ${keepalive}" "Redis-Diagnostics" "DEBUG"

        $timeout = docker exec $ContainerName redis-cli CONFIG GET timeout 2>&1
        Log-Event "Configuração timeout: ${timeout}" "Redis-Diagnostics" "DEBUG"

        $maxclients = docker exec $ContainerName redis-cli CONFIG GET maxclients 2>&1
        Log-Event "Configuração maxclients: ${maxclients}" "Redis-Diagnostics" "DEBUG"
        
    } catch {
        Log-Event "⚠ Não foi possível verificar configurações avançadas: $($_.Exception.Message)" "Redis-Diagnostics" "WARN"
    }
    
    Log-Event "✓ Diagnóstico do Redis concluído com sucesso" "Redis-Diagnostics" "SUCCESS"
    return $true
}

function Test-EvolutionRedisConnection {
    param(
        [string]$EvolutionContainer = "evolution_aula"
    )
    
    Log-Event "Testando conectividade Evolution API -> Redis..." "Evolution-Redis" "INFO"
    
    try {
        # Tenta fazer ping do contêiner da Evolution para o Redis
        $evolutionPing = docker exec $EvolutionContainer sh -c "curl -f http://redis:6379 2>/dev/null || nc -zv redis 6379 2>&1 || echo 'Connection test failed'" 2>$null
        Log-Event "Teste de conectividade Evolution->Redis: $evolutionPing" "Evolution-Redis" "DEBUG"
        
        # Verificar variáveis de ambiente do Redis na Evolution
        $redisVars = docker exec $EvolutionContainer env | Select-String -Pattern "REDIS" 2>$null
        if ($redisVars) {
            Log-Event "Variáveis REDIS na Evolution API:" "Evolution-Redis" "INFO"
            $redisVars | ForEach-Object { Log-Event "  $_" "Evolution-Redis" "DEBUG" }
        }
        
        return $true
    } catch {
        Log-Event "Erro ao testar conectividade Evolution->Redis: $($_.Exception.Message)" "Evolution-Redis" "ERROR"
        return $false
    }
}

function Test-EvolutionQRCodeReadiness {
    param(
        [string]$EvolutionContainer = "evolution_aula",
        [string]$ApiKey = "",
        [int]$TimeoutSeconds = 60
    )
    
    Log-Event "Iniciando diagnóstico completo do QR Code da Evolution API..." "Evolution-QRCode" "INFO"
    
    # Verificar se a variável CONFIG_SESSION_PHONE_VERSION está definida
    try {
        $phoneVersionVar = docker exec $EvolutionContainer env | Select-String -Pattern "CONFIG_SESSION_PHONE_VERSION" 2>$null
        if ($phoneVersionVar) {
            Log-Event "✓ CONFIG_SESSION_PHONE_VERSION encontrada: $phoneVersionVar" "Evolution-QRCode" "SUCCESS"
        } else {
            Log-Event "❌ CONFIG_SESSION_PHONE_VERSION não encontrada - problema crítico para QR Code" "Evolution-QRCode" "ERROR"
            return $false
        }
    } catch {
        Log-Event "Erro ao verificar CONFIG_SESSION_PHONE_VERSION: $($_.Exception.Message)" "Evolution-QRCode" "ERROR"
        return $false
    }
    
    # Verificar logs específicos para problemas de QR Code
    try {
        Log-Event "Analisando logs da Evolution API para problemas de QR Code..." "Evolution-QRCode" "INFO"
        $recentLogs = docker logs $EvolutionContainer --tail 50 --since 300s 2>&1
        
        $qrCodeIssues = @()
        $whatsappIssues = @()
        
        foreach ($log in $recentLogs) {
            # Detectar problemas específicos do QR Code
            if ($log -match "QR.*[Ee]rror|[Ee]rror.*QR|qr.*fail|fail.*qr|WhatsApp.*connection.*fail|connection.*WhatsApp.*fail") {
                $qrCodeIssues += $log.Trim()
            }
            
            # Detectar problemas de versão do WhatsApp
            if ($log -match "version.*incompatible|incompatible.*version|outdated.*version|version.*outdated") {
                $whatsappIssues += $log.Trim()
            }
        }
        
        if ($qrCodeIssues.Count -gt 0) {
            Log-Event "⚠️ Problemas de QR Code detectados nos logs:" "Evolution-QRCode" "WARN"
            $qrCodeIssues | ForEach-Object { Log-Event "  • $_" "Evolution-QRCode" "WARN" }
        } else {
            Log-Event "✓ Nenhum problema de QR Code detectado nos logs recentes" "Evolution-QRCode" "SUCCESS"
        }
        
        if ($whatsappIssues.Count -gt 0) {
            Log-Event "⚠️ Problemas de compatibilidade de versão WhatsApp detectados:" "Evolution-QRCode" "WARN"
            $whatsappIssues | ForEach-Object { Log-Event "  • $_" "Evolution-QRCode" "WARN" }
        }
        
    } catch {
        Log-Event "Erro ao analisar logs para problemas de QR Code: $($_.Exception.Message)" "Evolution-QRCode" "ERROR"
    }
    
    # Testar endpoint específico da Evolution API para QR Code
    if (-not [string]::IsNullOrWhiteSpace($ApiKey)) {
        try {
            Log-Event "Testando endpoint da Evolution API..." "Evolution-QRCode" "INFO"
            $headers = @{ "apikey" = $ApiKey }
            $response = Invoke-RestMethod -Uri "http://localhost:8080" -Headers $headers -Method GET -TimeoutSec 30 -ErrorAction Stop
            Log-Event "✓ Evolution API respondendo corretamente" "Evolution-QRCode" "SUCCESS"
        } catch {
            Log-Event "❌ Evolution API não respondeu: $($_.Exception.Message)" "Evolution-QRCode" "ERROR"
            return $false
        }
    }
    
    # Verificar se há instâncias ativas que podem estar causando conflitos
    try {
        Log-Event "Verificando instâncias ativas da Evolution API..." "Evolution-QRCode" "INFO"
        $instancesLogs = docker logs $EvolutionContainer --grep "instance" --tail 20 2>&1
        if ($instancesLogs) {
            Log-Event "Instâncias detectadas nos logs:" "Evolution-QRCode" "INFO"
            $instancesLogs | ForEach-Object { Log-Event "  • $_" "Evolution-QRCode" "DEBUG" }
        }
    } catch {
        Log-Event "Erro ao verificar instâncias ativas: $($_.Exception.Message)" "Evolution-QRCode" "WARN"
    }
    
    Log-Event "✓ Diagnóstico de QR Code concluído" "Evolution-QRCode" "SUCCESS"
    return $true
}

function Repair-EvolutionQRCode {
    param(
        [string]$EvolutionContainer = "evolution_aula",
        [string]$ApiKey = "",
        [string]$PhoneVersion = "2.3000.1020885143"
    )
    
    Log-Event "Iniciando reparação automática do QR Code da Evolution API..." "Evolution-QRCode-Repair" "INFO"
    
    # Verificar se o contêiner está rodando
    try {
        $containerStatus = docker inspect --format "{{.State.Status}}" $EvolutionContainer 2>$null
        if ($containerStatus -ne "running") {
            Log-Event "❌ Contêiner $EvolutionContainer não está rodando: $containerStatus" "Evolution-QRCode-Repair" "ERROR"
            return $false
        }
    } catch {
        Log-Event "❌ Erro ao verificar status do contêiner: $($_.Exception.Message)" "Evolution-QRCode-Repair" "ERROR"
        return $false
    }
    
    # Etapa 1: Verificar e definir CONFIG_SESSION_PHONE_VERSION no ambiente
    try {
        Log-Event "Verificando CONFIG_SESSION_PHONE_VERSION no contêiner..." "Evolution-QRCode-Repair" "INFO"
        $envCheck = docker exec $EvolutionContainer env | Select-String -Pattern "CONFIG_SESSION_PHONE_VERSION" 2>$null
        
        if (-not $envCheck) {
            Log-Event "⚠️ CONFIG_SESSION_PHONE_VERSION não encontrada. Isto indica que o contêiner precisa ser recriado." "Evolution-QRCode-Repair" "WARN"
            Log-Event "📋 AÇÃO NECESSÁRIA: Execute 'docker-compose down && docker-compose up -d' para recriar com novas variáveis." "Evolution-QRCode-Repair" "WARN"
        } else {
            Log-Event "✓ CONFIG_SESSION_PHONE_VERSION encontrada: $envCheck" "Evolution-QRCode-Repair" "SUCCESS"
        }
    } catch {
        Log-Event "Erro ao verificar CONFIG_SESSION_PHONE_VERSION: $($_.Exception.Message)" "Evolution-QRCode-Repair" "ERROR"
    }
    
    # Etapa 2: Limpar cache e dados temporários
    try {
        Log-Event "Limpando cache e dados temporários..." "Evolution-QRCode-Repair" "INFO"
        
        # Limpar diretório temporário da Evolution API
        docker exec $EvolutionContainer sh -c "rm -rf /evolution/instances/*/store/* 2>/dev/null || true" 2>$null
        docker exec $EvolutionContainer sh -c "rm -rf /tmp/evolution-* 2>/dev/null || true" 2>$null
        
        Log-Event "✓ Cache limpo com sucesso" "Evolution-QRCode-Repair" "SUCCESS"
    } catch {
        Log-Event "⚠️ Erro ao limpar cache: $($_.Exception.Message)" "Evolution-QRCode-Repair" "WARN"
    }
    
    # Etapa 3: Reiniciar contêiner com timeout
    try {
        Log-Event "Reiniciando contêiner Evolution API..." "Evolution-QRCode-Repair" "INFO"
        docker restart $EvolutionContainer
        
        if ($LASTEXITCODE -eq 0) {
            Log-Event "✓ Contêiner reiniciado com sucesso" "Evolution-QRCode-Repair" "SUCCESS"
            
            # Aguardar até 3 minutos para o contêiner estar pronto
            $maxWait = 180
            $waited = 0
            while ($waited -lt $maxWait) {
                try {
                    $status = docker inspect --format "{{.State.Status}}" $EvolutionContainer 2>$null
                    if ($status -eq "running") {
                        Log-Event "✓ Contêiner está rodando após reinício" "Evolution-QRCode-Repair" "SUCCESS"
                        break
                    }
                } catch { }
                
                Start-Sleep -Seconds 5
                $waited += 5
                Log-Event "Aguardando contêiner ficar pronto... ($waited s/$maxWait s)" "Evolution-QRCode-Repair" "INFO"
            }
            
            if ($waited -ge $maxWait) {
                Log-Event "❌ Contêiner não ficou pronto dentro do tempo limite" "Evolution-QRCode-Repair" "ERROR"
                return $false
            }
        } else {
            Log-Event "❌ Erro ao reiniciar contêiner" "Evolution-QRCode-Repair" "ERROR"
            return $false
        }
    } catch {
        Log-Event "❌ Erro ao reiniciar contêiner: $($_.Exception.Message)" "Evolution-QRCode-Repair" "ERROR"
        return $false
    }
    
    # Etapa 4: Testar conectividade pós-reinício
    try {
        Log-Event "Testando conectividade após reinício..." "Evolution-QRCode-Repair" "INFO"
        
        $testAttempts = 0
        $maxAttempts = 12
        $testSuccess = $false
        
        while ($testAttempts -lt $maxAttempts -and -not $testSuccess) {
            try {
                $testResponse = Invoke-RestMethod -Uri "http://localhost:8080" -TimeoutSec 10 -ErrorAction Stop
                $testSuccess = $true
                Log-Event "✓ Evolution API respondendo após reinício" "Evolution-QRCode-Repair" "SUCCESS"
            } catch {
                $testAttempts++
                Log-Event "Tentativa $testAttempts de $maxAttempts - API ainda não responde" "Evolution-QRCode-Repair" "DEBUG"
                Start-Sleep -Seconds 10
            }
        }
        
        if (-not $testSuccess) {
            Log-Event "❌ Evolution API não respondeu após reinício dentro do tempo limite" "Evolution-QRCode-Repair" "ERROR"
            return $false
        }
        
    } catch {
        Log-Event "❌ Erro ao testar conectividade: $($_.Exception.Message)" "Evolution-QRCode-Repair" "ERROR"
        return $false
    }
    
    # Etapa 5: Verificar logs pós-reinício
    try {
        Log-Event "Verificando logs pós-reinício..." "Evolution-QRCode-Repair" "INFO"
        Start-Sleep -Seconds 10  # Aguardar logs aparecerem
        
        $postRestartLogs = docker logs $EvolutionContainer --tail 20 --since 60s 2>&1
        $hasErrors = $false
        
        foreach ($log in $postRestartLogs) {
            if ($log -match "[Ee]rror|[Ff]ail|[Ee]xception") {
                Log-Event "⚠️ Erro detectado nos logs: $log" "Evolution-QRCode-Repair" "WARN"
                $hasErrors = $true
            }
        }
        
        if (-not $hasErrors) {
            Log-Event "✓ Nenhum erro detectado nos logs pós-reinício" "Evolution-QRCode-Repair" "SUCCESS"
        }
        
    } catch {
        Log-Event "⚠️ Erro ao verificar logs pós-reinício: $($_.Exception.Message)" "Evolution-QRCode-Repair" "WARN"
    }
    
    Log-Event "✅ Reparação automática do QR Code concluída" "Evolution-QRCode-Repair" "SUCCESS"
    return $true
}

# =====================================================
# FUNÇÃO DE DETECÇÃO DE IP PRIVADO DO HOST (ROBUSTA)
# =====================================================
function Get-PrimaryPrivateIPv4 {
    <#
    .SYNOPSIS
    Retorna o primeiro endereço IPv4 privado não‐loopback disponível na máquina host.

    .DESCRIPTION
    Em máquinas com múltiplas interfaces (VPN, WSL2, Hyper-V, etc.) a abordagem
    usando somente `Get-NetConnectionProfile` pode falhar ou retornar interfaces
    virtuais.  Esta função percorre todas as interfaces ativas, ordena por
    prioridade (físicas > virtuais) e devolve o primeiro IP no range privado
    (10.x.x.x, 172.16-31.x.x ou 192.168.x.x).  Caso nenhum IP seja encontrado é
    retornado "127.0.0.1" como fallback seguro.
    #>
    [CmdletBinding()]
    param()

    try {
        $ipList = Get-NetIPAddress -AddressFamily IPv4 -ErrorAction Stop |
                  Where-Object { $_.IPAddress -and $_.IPAddress -ne '127.0.0.1' -and $_.InterfaceOperationalStatus -eq 'Up' }

        $privateRanges = @(
            { param($ip) $ip -like '10.*' },
            { param($ip) $ip -like '172.1[6-9].*' -or $ip -like '172.2[0-9].*' -or $ip -like '172.3[0-1].*' },
            { param($ip) $ip -like '192.168.*' }
        )

        foreach ($filter in $privateRanges) {
            $candidate = $ipList | Where-Object { & $filter $_.IPAddress } | Select-Object -First 1
            if ($candidate) { return $candidate.IPAddress }
        }
    } catch {
        Log-Event "Falha ao detectar IP privado: $($_.Exception.Message)" "IPDetect" "WARN"
    }

    # Fallback
    return '127.0.0.1'
}

# =====================================================
# FUNÇÕES DE UTILITÁRIOS GERAIS
# =====================================================

function Safe-HtmlEncode {
    param([string]$Text)
    
    if ([string]::IsNullOrEmpty($Text)) {
        return ""
    }
    
    try {
        Add-Type -AssemblyName System.Web -ErrorAction SilentlyContinue
        if ([System.Web.HttpUtility]) {
            return [System.Web.HttpUtility]::HtmlEncode($Text)
        }
    } catch {
        Log-Event "Não foi possível usar System.Web.HttpUtility. Usando encoding manual." "HtmlEncode" "DEBUG"
    }
    
    # Fallback para encoding manual
    return $Text -replace '&', '&amp;' -replace '<', '&lt;' -replace '>', '&gt;' -replace '"', '&quot;' -replace "'", '&#39;'
}

function Test-ContainerRunning {
    param([string]$ContainerName)
    
    if ([string]::IsNullOrWhiteSpace($ContainerName)) {
        return $false
    }
    
    try {
        $status = docker inspect --format "{{.State.Status}}" $ContainerName 2>$null
        return ($status -eq "running")
    } catch {
        return $false
    }
}

# =====================================================
# FUNÇÕES DE NAVEGAÇÃO WEB
# =====================================================

function Open-UrlInChrome {
    param(
        [string]$Url,
        [string]$Component = "Browser"
    )

    Log-Event "Tentando abrir URL '$Url' no Google Chrome..." $Component "INFO"

    $chromePath = $null

    # Tentar encontrar Chrome em locais comuns
    $programFiles = [System.Environment]::GetFolderPath('ProgramFiles')
    $programFilesX86 = [System.Environment]::GetFolderPath('ProgramFilesX86')

    $possiblePaths = @(
        Join-Path $programFiles "Google\Chrome\Application\chrome.exe",
        Join-Path $programFilesX86 "Google\Chrome\Application\chrome.exe",
        Join-Path $env:LOCALAPPDATA "Google\Chrome\Application\chrome.exe"
    )

    foreach ($path in $possiblePaths) {
        if (Test-Path $path) {
            $chromePath = $path
            break
        }
    }

    if ($chromePath) {
        try {
            Log-Event "Google Chrome encontrado em: '$chromePath'. Abrindo URL..." $Component "INFO"
            Start-Process -FilePath $chromePath -ArgumentList $Url -ErrorAction Stop
            Log-Event "URL '$Url' aberto com sucesso no Chrome." $Component "SUCCESS"
            return $true
        } catch {
            Log-Event "Falha ao abrir URL '$Url' no Chrome: $($_.Exception.Message)" $Component "ERROR"
            return $false
        }
    } else {
        Log-Event "Google Chrome não encontrado em locais padrão. Tentando abrir com o navegador padrão..." $Component "WARN"
        try {
            Start-Process -ArgumentList $Url -ErrorAction Stop
            Log-Event "URL '$Url' aberto com sucesso no navegador padrão." $Component "SUCCESS"
            return $true
        } catch {
            Log-Event "Falha ao abrir URL '$Url' no navegador padrão: $($_.Exception.Message)" $Component "ERROR"
            return $false
        }
    }
}

# =====================================================
# INICIALIZAÇÃO DO MÓDULO
# =====================================================

# Inicializar o sistema de log automaticamente quando o módulo é importado
try {
    Initialize-LogSystem
    Log-Event "Módulo AutomationUtils v3.0 carregado com sucesso." "ModuleLoader" "SUCCESS"
} catch {
    Write-Error "ERRO FATAL: Falha ao inicializar o módulo AutomationUtils: $($_.Exception.Message)"
    throw
}

# =====================================================
# FUNÇÕES DE GERENCIAMENTO DE WORKFLOWS
# =====================================================

function Scan-WorkflowsDirectory {
    <#
    .SYNOPSIS
    Escaneia o diretório de workflows para identificar workflows que precisam de ajustes de infraestrutura.

    .DESCRIPTION
    Esta função analisa todos os workflows no diretório 'workflows' e identifica aqueles que:
    - Contêm arquivos SQL que precisam ser aplicados
    - Requerem configurações específicas de infraestrutura
    - Estão prontos para onboarding

    .PARAMETER WorkflowsPath
    Caminho para o diretório de workflows (padrão: workflows)

    .RETURNS
    Hashtable com informações sobre workflows encontrados
    #>
    param(
        [Parameter()]
        [string]$WorkflowsPath = "workflows"
    )

    Log-Event "Iniciando escaneamento do diretório de workflows: $WorkflowsPath" "WorkflowScanner" "INFO"

    if (-not (Test-Path $WorkflowsPath)) {
        Log-Event "Diretório de workflows não encontrado: $WorkflowsPath" "WorkflowScanner" "WARN"
        return @{
            Success = $false
            TotalWorkflows = 0
            WorkflowsNeedingAdjustment = @()
            ReadyWorkflows = @()
            ErrorMessage = "Diretório de workflows não encontrado"
        }
    }

    try {
        # Buscar todos os diretórios de workflows
        $workflowDirs = Get-ChildItem -Path $WorkflowsPath -Directory -Recurse |
            Where-Object { $_.Parent.Name -ne "workflows" -or $_.Parent.Parent.Name -eq "workflows" }

        $workflowsNeedingAdjustment = @()
        $readyWorkflows = @()

        foreach ($workflowDir in $workflowDirs) {
            $workflowInfo = @{
                Name = $workflowDir.Name
                Path = $workflowDir.FullName
                RelativePath = $workflowDir.FullName.Replace([regex]::Escape((Resolve-Path $WorkflowsPath).Path), "").TrimStart('\', '/')
                HasSQL = $false
                HasJSON = $false
                SQLFiles = @()
                JSONFiles = @()
                NeedsAdjustment = $false
            }

            # Verificar se há arquivos SQL
            $sqlPath = Join-Path $workflowDir.FullName "sql"
            if (Test-Path $sqlPath) {
                $sqlFiles = Get-ChildItem -Path $sqlPath -Filter "*.sql" -ErrorAction SilentlyContinue
                if ($sqlFiles.Count -gt 0) {
                    $workflowInfo.HasSQL = $true
                    $workflowInfo.SQLFiles = $sqlFiles.Name
                    $workflowInfo.NeedsAdjustment = $true
                }
            }

            # Verificar se há arquivos JSON de workflow
            $jsonFiles = Get-ChildItem -Path $workflowDir.FullName -Filter "*.json" -ErrorAction SilentlyContinue
            if ($jsonFiles.Count -gt 0) {
                $workflowInfo.HasJSON = $true
                $workflowInfo.JSONFiles = $jsonFiles.Name
            }

            # Categorizar workflow
            if ($workflowInfo.NeedsAdjustment) {
                $workflowsNeedingAdjustment += $workflowInfo
                Log-Event "Workflow requer ajuste: $($workflowInfo.Name) (SQL: $($workflowInfo.SQLFiles.Count) arquivos)" "WorkflowScanner" "INFO"
            } else {
                $readyWorkflows += $workflowInfo
                Log-Event "Workflow pronto: $($workflowInfo.Name)" "WorkflowScanner" "DEBUG"
            }
        }

        $totalWorkflows = $workflowDirs.Count
        $needingAdjustmentCount = $workflowsNeedingAdjustment.Count

        Log-Event "Escaneamento concluído: $totalWorkflows workflows encontrados, $needingAdjustmentCount precisam de ajustes" "WorkflowScanner" "SUCCESS"

        return @{
            Success = $true
            TotalWorkflows = $totalWorkflows
            WorkflowsNeedingAdjustment = $workflowsNeedingAdjustment
            ReadyWorkflows = $readyWorkflows
            ErrorMessage = $null
        }

    } catch {
        Log-Event "Erro durante escaneamento de workflows: $($_.Exception.Message)" "WorkflowScanner" "ERROR"
        return @{
            Success = $false
            TotalWorkflows = 0
            WorkflowsNeedingAdjustment = @()
            ReadyWorkflows = @()
            ErrorMessage = $_.Exception.Message
        }
    }
}

function Show-WorkflowSelectionMenu {
    <#
    .SYNOPSIS
    Exibe menu de seleção de workflows para aplicar ajustes de infraestrutura.

    .DESCRIPTION
    Apresenta uma interface interativa para o usuário selecionar quais workflows
    devem ter seus ajustes de infraestrutura aplicados via Onboard-Workflow.ps1

    .PARAMETER WorkflowsNeedingAdjustment
    Array de workflows que precisam de ajustes

    .RETURNS
    Array de workflows selecionados pelo usuário
    #>
    param(
        [Parameter(Mandatory=$true)]
        [array]$WorkflowsNeedingAdjustment
    )

    if ($WorkflowsNeedingAdjustment.Count -eq 0) {
        Write-Host-Color "`n✅ Nenhum workflow requer ajustes de infraestrutura." "Green"
        Write-Host-Color "Todos os workflows estão prontos para uso!" "Green"
        return @()
    }

    Write-Host-Color "`n📋 Workflows que requerem ajustes de infraestrutura:" "Yellow"
    Write-Host-Color "═══════════════════════════════════════════════════" "Yellow"

    for ($i = 0; $i -lt $WorkflowsNeedingAdjustment.Count; $i++) {
        $workflow = $WorkflowsNeedingAdjustment[$i]
        $index = $i + 1

        Write-Host-Color "`n[$index] $($workflow.Name)" "White"
        Write-Host "    📁 Localização: $($workflow.RelativePath)" -ForegroundColor Gray

        if ($workflow.HasSQL) {
            Write-Host "    🗄️  Arquivos SQL: $($workflow.SQLFiles.Count) arquivo(s)" -ForegroundColor Cyan
            $workflow.SQLFiles | ForEach-Object { Write-Host "       • $_" -ForegroundColor Gray }
        }

        if ($workflow.HasJSON) {
            Write-Host "    📄 Workflows JSON: $($workflow.JSONFiles.Count) arquivo(s)" -ForegroundColor Blue
        }
    }

    Write-Host-Color "`n═══════════════════════════════════════════════════" "Yellow"
    Write-Host-Color "Opções de seleção:" "Yellow"
    Write-Host "  • Digite números separados por vírgula (ex: 1,3,5)" -ForegroundColor White
    Write-Host "  • Digite 'all' para selecionar todos" -ForegroundColor White
    Write-Host "  • Digite 'none' ou pressione Enter para pular" -ForegroundColor White

    do {
        Write-Host "`nSua escolha: " -NoNewline -ForegroundColor Yellow
        $userInput = Read-Host

        if ([string]::IsNullOrWhiteSpace($userInput) -or $userInput.ToLower() -eq "none") {
            Write-Host-Color "Nenhum workflow selecionado." "Gray"
            return @()
        }

        if ($userInput.ToLower() -eq "all") {
            Write-Host-Color "Todos os workflows selecionados." "Green"
            return $WorkflowsNeedingAdjustment
        }

        # Processar seleção por números
        try {
            $selectedIndices = $userInput -split ',' | ForEach-Object {
                $trimmed = $_.Trim()
                if ($trimmed -match '^\d+$') {
                    [int]$trimmed
                } else {
                    throw "Entrada inválida: '$trimmed'"
                }
            }

            $validIndices = $selectedIndices | Where-Object { $_ -ge 1 -and $_ -le $WorkflowsNeedingAdjustment.Count }

            if ($validIndices.Count -ne $selectedIndices.Count) {
                $invalidIndices = $selectedIndices | Where-Object { $_ -lt 1 -or $_ -gt $WorkflowsNeedingAdjustment.Count }
                Write-Host-Color "Números inválidos: $($invalidIndices -join ', '). Use números de 1 a $($WorkflowsNeedingAdjustment.Count)." "Red"
                continue
            }

            $selectedWorkflows = $validIndices | ForEach-Object { $WorkflowsNeedingAdjustment[$_ - 1] }

            Write-Host-Color "`n✅ Workflows selecionados:" "Green"
            $selectedWorkflows | ForEach-Object { Write-Host "   • $($_.Name)" -ForegroundColor White }

            return $selectedWorkflows

        } catch {
            Write-Host-Color "Entrada inválida. Use números separados por vírgula, 'all' ou 'none'." "Red"
            continue
        }

    } while ($true)
}

# =====================================================
# FUNÇÕES DE MENU E INTERFACE DE USUÁRIO
# =====================================================

function Show-CountdownTimer {
    <#
    .SYNOPSIS
    Exibe um timer de contagem regressiva com opção de interrupção.

    .DESCRIPTION
    Mostra uma contagem regressiva visual que pode ser interrompida pelo usuário
    pressionando qualquer tecla.

    .PARAMETER Seconds
    Número de segundos para a contagem regressiva

    .PARAMETER Message
    Mensagem a ser exibida durante a contagem

    .RETURNS
    $true se o timer completou, $false se foi interrompido
    #>
    param(
        [Parameter(Mandatory=$true)]
        [int]$Seconds,

        [Parameter()]
        [string]$Message = "Prosseguindo automaticamente em"
    )

    Write-Host "`n$Message " -NoNewline -ForegroundColor Yellow

    for ($i = $Seconds; $i -gt 0; $i--) {
        Write-Host "$i" -NoNewline -ForegroundColor Red

        # Verificar se há tecla pressionada
        if ([Console]::KeyAvailable) {
            $key = [Console]::ReadKey($true)
            Write-Host "`n" # Nova linha após interrupção
            Log-Event "Countdown interrompido pelo usuário (tecla: $($key.Key))" "UserInterface" "INFO"
            return $false
        }

        Start-Sleep -Seconds 1

        if ($i -gt 1) {
            Write-Host "..." -NoNewline -ForegroundColor Yellow
        }
    }

    Write-Host "`n" # Nova linha após completar
    Log-Event "Countdown completado ($Seconds segundos)" "UserInterface" "INFO"
    return $true
}

# =====================================================
# EXPORTAÇÃO EXPLÍCITA DE TODAS AS FUNÇÕES
# =====================================================

Export-ModuleMember -Function @(
    'Log-Event',
    'Write-Host-Color',
    'Generate-RandomPassword',
    'Generate-SecureKey',
    'Abort-Install',
    'Run-Command',
    'Wait-For-Service-Healthy',
    'Wait-For-Port',
    'Test-RedisConnectivity',
    'Test-EvolutionRedisConnection',
    'Safe-HtmlEncode',
    'Test-ContainerRunning',
    'Get-PrimaryPrivateIPv4',
    'Test-EvolutionQRCodeReadiness',
    'Repair-EvolutionQRCode',
    'Scan-WorkflowsDirectory',
    'Show-WorkflowSelectionMenu',
    'Show-CountdownTimer'
)