{"nodes": [{"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "8ca54eae-15d1-49d3-af33-7a6e5d17b833", "leftValue": "={{ $('Info').item.json.fromMe }}", "rightValue": "", "operator": {"type": "boolean", "operation": "false", "singleValue": true}}, {"id": "4ca9226f-0401-49a7-93a1-8aeaf4b0bb79", "leftValue": "={{ $('Info').item.json.mensagem_de_grupo }}", "rightValue": "g.us", "operator": {"type": "boolean", "operation": "false", "singleValue": true}}, {"id": "cf87bb7e-6bea-4697-bcd9-57e3b63998c2", "leftValue": "={{ $json.telefone }}", "rightValue": "553497678732", "operator": {"type": "string", "operation": "equals"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.filter", "typeVersion": 2.2, "position": [80, -300], "id": "5c5d465e-54bb-480b-a0f2-6ff5431c874b", "name": "Mensagem chegando?"}, {"parameters": {"httpMethod": "POST", "path": "240b36f9-9d6d-4946-864b-8b681f3ec906", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-480, -300], "id": "079c4746-7f33-4cb0-aa3b-bfe113b74c4f", "name": "Mensagem recebida", "webhookId": "240b36f9-9d6d-4946-864b-8b681f3ec906"}, {"parameters": {"jsCode": "const ultima_mensagem_da_fila = $input.last()\nconst mensagem_do_workflow = $('Info').first()\n\nif (ultima_mensagem_da_fila.json.id_mensagem !== mensagem_do_workflow.json.id_mensagem) {\n  // Mensagem encavalada, para o workflow\n  return [];\n}\n\n// Pass-through da fila de mensagens\nreturn $input.all();"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1260, -300], "id": "3dc70a1a-f6bb-439f-ad72-d0af44be0ece", "name": "Mensagem encavalada?"}, {"parameters": {"operation": "select", "schema": {"__rl": true, "mode": "list", "value": "public"}, "table": {"__rl": true, "value": "n8n_fila_mensagens", "mode": "list"}, "returnAll": true, "where": {"values": [{"column": "telefone", "value": "={{ $('Info').item.json.telefone }}"}]}, "sort": {"values": [{"column": "timestamp"}]}, "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [1040, -300], "id": "5bd0114b-feac-4ae3-8dc2-7d5bc257b608", "name": "Buscar mensagens", "credentials": {}}, {"parameters": {"assignments": {"assignments": [{"id": "7eab8669-6929-4dc6-b3e2-943065bc306c", "name": "mensagem", "value": "={{ $('Mensagem encavalada?').all().map(info => info.json.mensagem).join('\\\\n') }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [2100, -300], "id": "7aa8e1ac-ffa8-427d-8cb3-7153a826ef82", "name": "Concatenar mensagens", "executeOnce": true}, {"parameters": {"operation": "deleteTable", "schema": {"__rl": true, "mode": "list", "value": "public"}, "table": {"__rl": true, "value": "n8n_fila_mensagens", "mode": "list"}, "deleteCommand": "delete", "where": {"values": [{"column": "telefone", "value": "={{ $json.telefone }}"}]}, "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [1460, -300], "id": "590e9ad7-98d7-4b97-8559-fad17b9815cb", "name": "Limpar fila de mensagens", "credentials": {}}, {"parameters": {"content": "Evolution não permite disparar evento de \"Digitando...\" de forma assíncrona, então usamos um workflow paralelo.", "height": 180, "width": 260}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [1980, -140], "id": "6e3af6ea-1bcd-4507-bfac-4db0533c01a3", "name": "Sticky Note1"}, {"parameters": {"content": "# Processando mensagens encavaladas\n\nEssa etapa trata a situação em que o usuário envia múltiplas mensagens seguidas. O ponto negativo é o aumento no tempo de resposta do agente. Lógica dispensa uso de soluções mais complexas, como RabbitMQ.\n\nTempo de espera recomendado de ~16s. <PERSON>uando estiver testando, recomendamos reduzir um pouco para ficar mais rápido de testar.\n", "height": 500, "width": 1080, "color": 4}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [520, -480], "id": "1f175575-9a88-42b1-93ad-48ef5c965437", "name": "Sticky Note2"}, {"parameters": {"amount": 16}, "type": "n8n-nodes-base.wait", "typeVersion": 1.1, "position": [820, -300], "id": "8780cc34-9127-4340-b684-52b1ef6cf494", "name": "<PERSON><PERSON><PERSON>", "webhookId": "2da4ca1d-5944-431c-8c8e-248231af4d41"}, {"parameters": {"content": "# G<PERSON>do resposta", "height": 500, "width": 1920, "color": 3}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [2300, -480], "id": "d67f6f2f-6df4-48d0-9849-0ff5cbc925ce", "name": "Sticky Note3"}, {"parameters": {"content": "# Enviando resposta", "height": 500, "width": 600, "color": 7}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [4240, -480], "id": "20f954fb-624c-4cd9-ad9d-c7190ea63c9f", "name": "Sticky Note4"}, {"parameters": {"content": "# Tratando input\n\nImplementação de etiquetas do Evolution API não é muito fácil de utilizar, portanto funcionalidade de \"agente off\" fica difícil de implementar.\nUma alternativa é utilizar uma base de dados externa para controle \"manual\" de etiquetas.", "height": 500, "width": 1060}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-560, -480], "id": "9b48a25f-8bd0-498b-92bb-836cf0dd04df", "name": "Sticky Note5"}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{ $('Info').item.json.mensagem }}", "rightValue": "", "operator": {"type": "string", "operation": "notEmpty", "singleValue": true}, "id": "1382cd26-d96e-4c55-99dd-2ca305ffe82e"}], "combinator": "and"}, "renameOutput": true, "outputKey": "Texto"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "b9a7e16f-b6e4-45d7-846d-92dcb3117593", "leftValue": "={{ $('Info').item.json.mensagem_de_audio }}", "rightValue": "", "operator": {"type": "boolean", "operation": "true", "singleValue": true}}], "combinator": "and"}, "renameOutput": true, "outputKey": "<PERSON><PERSON><PERSON>"}]}, "options": {}}, "type": "n8n-nodes-base.switch", "typeVersion": 3.2, "position": [360, -300], "id": "1dfad22d-6af7-4176-b084-3fa1a89c9841", "name": "Tipo de mensagem"}, {"parameters": {"resource": "chat-api", "operation": "get-media-base64", "instanceName": "={{ $('Info').item.json.instancia }}", "messageId": "={{ $('Info').item.json.id_mensagem }}", "convertToMp4": true}, "type": "n8n-nodes-evolution-api.evolutionApi", "typeVersion": 1, "position": [700, 140], "id": "7277aa77-5972-42f1-a3d8-926fe820b366", "name": "Download áudio", "credentials": {}}, {"parameters": {"resource": "audio", "operation": "transcribe", "options": {"language": "pt"}}, "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.8, "position": [1320, 140], "id": "13143bd0-b254-40c3-8926-b777eca1d3ea", "name": "Transcrever <PERSON>", "credentials": {}}, {"parameters": {"content": "# Processando <PERSON>", "height": 320, "width": 1080, "color": 6}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [520, 40], "id": "15c85af6-6bb0-437e-a478-6b1b273fee53", "name": "Sticky Note6"}, {"parameters": {"method": "POST", "url": "<cole a URL do seu webhook>", "sendBody": true, "bodyParameters": {"parameters": [{"name": "instancia", "value": "={{ $('Info').item.json.instancia }}"}, {"name": "telefone", "value": "={{ $('Info').item.json.telefone }}"}, {"name": "status", "value": "composing"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1900, -300], "id": "820abddd-fa0f-4426-8707-5a135b4268bd", "name": "<PERSON><PERSON><PERSON><PERSON> async"}, {"parameters": {"method": "POST", "url": "<cole a URL do seu webhook>", "sendBody": true, "bodyParameters": {"parameters": [{"name": "instancia", "value": "={{ $('Info').item.json.instancia }}"}, {"name": "telefone", "value": "={{ $('Info').item.json.telefone }}"}, {"name": "status", "value": "recording"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1900, 140], "id": "0519e98e-8fbd-499a-ac2b-043f357f6a31", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"parameters": {"resource": "chat-api", "operation": "send-presence", "instanceName": "={{ $('Info').item.json.instancia }}", "remoteJid": "={{ $('Info').item.json.telefone }}", "presence": "=paused", "delay": 0}, "type": "n8n-nodes-evolution-api.evolutionApi", "typeVersion": 1, "position": [4360, -180], "id": "5bf2ee27-4ae5-4868-acff-4970872e32d0", "name": "Resetar status", "credentials": {}}, {"parameters": {"resource": "chat-api", "operation": "send-presence", "instanceName": "={{ $('Info').item.json.instancia }}", "remoteJid": "={{ $('Info').item.json.telefone }}", "presence": "=paused", "delay": 0}, "type": "n8n-nodes-evolution-api.evolutionApi", "typeVersion": 1, "position": [4360, -360], "id": "994a0d60-faad-4851-aad0-91915203baf2", "name": "Resetar status1", "credentials": {}}, {"parameters": {"operation": "binaryToPropery", "options": {"encoding": "base64"}}, "type": "n8n-nodes-base.extractFromFile", "typeVersion": 1, "position": [4080, -180], "id": "e6cff1d7-39cb-4968-b6b6-3ca72d4bd912", "name": "Converter áudio para base64"}, {"parameters": {"content": "Waveform nem sempre funciona no Evolution. Requer formato de áudio específico", "height": 80}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [4580, -460], "id": "1a2f3d0c-c059-468f-b402-164a06e74db4", "name": "<PERSON><PERSON>"}, {"parameters": {"resource": "chat-api", "operation": "read-messages", "instanceName": "={{ $('Info').item.json.instancia }}", "remoteJid": "={{ $('Mensagem recebida').item.json.body.data.key.remoteJid }}", "messageId": "={{ $('Info').item.json.id_mensagem }}", "fromMe": "={{ $('Info').item.json.fromMe }}"}, "type": "n8n-nodes-evolution-api.evolutionApi", "typeVersion": 1, "position": [1680, 140], "id": "a45c236f-8bcb-4e36-974c-9d9f314603be", "name": "Marcar como lida", "credentials": {}}, {"parameters": {"content": "<PERSON> testar, recomendado incluir seu telefone no filtro.\n\n<PERSON><PERSON> forma, o agente não vai responder outras pessoas.", "height": 140, "width": 300, "color": 3}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-20, -160], "id": "89dee6e7-3e9b-4b47-b4d7-093ce16012b4", "name": "Sticky Note8"}, {"parameters": {"sseEndpoint": "<cole a URL do seu MCP>"}, "type": "@n8n/n8n-nodes-langchain.mcpClientTool", "typeVersion": 1, "position": [2880, -120], "id": "dcdc91db-500c-4b77-9e72-ab88201f278d", "name": "MCP Google Calendar"}, {"parameters": {"content": "Usado HTTP request devido limitação do community node do Evolution, que só permite marcar como lida 1 mensagem de cada vez (ver no fluxo de áudio abaixo).\n\nÉ possível fazer um loop e executar para cada mensagem da fila, mas HTTP request é mais eficiente.", "height": 180, "width": 320}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [1640, -140], "id": "18c492e0-2731-4d21-a107-cfd675899c0a", "name": "Sticky Note9"}, {"parameters": {"method": "POST", "url": "={{ $('Info').item.json.url_evolution }}/chat/markMessageAsRead/{{ $('Info').item.json.instancia }}", "authentication": "predefinedCredentialType", "nodeCredentialType": "evolutionApi", "sendBody": true, "contentType": "raw", "rawContentType": "application/json", "body": "={\n  \"readMessages\": {{ JSON.stringify($('Buscar mensagens').all().map(mensagem => \n    { \n      const { id_mensagem, telefone } = mensagem.json\n      return { id: id_mensagem, remoteJid: telefone + '@s.whatsapp.net', fromMe: false }\n    }))\n  }}\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1680, -300], "id": "1235ddea-7970-4f61-8e03-10ac95694a86", "name": "Marcar como lidas", "executeOnce": true, "credentials": {}}, {"parameters": {"resource": "fileFolder", "returnAll": true, "filter": {"folderId": {"__rl": true, "value": "", "mode": "list"}}, "options": {}}, "type": "n8n-nodes-base.googleDriveTool", "typeVersion": 3, "position": [3020, -120], "id": "b5d50090-1df2-49f4-85e4-33f359fc3a4b", "name": "Listar arquivos", "credentials": {}}, {"parameters": {"description": "Use essa ferramenta para baixar um arquivo do Google Drive e enviá-lo para o usuário.\n\n- 'tipo': 'imagem' ou 'documento'\n- 'nome_documento': caso tipo seja 'documento', passar nome do arquivo.", "workflowId": {"__rl": true, "value": "", "mode": "list"}, "workflowInputs": {"mappingMode": "defineBelow", "value": {"file_id": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('file_id', ``, 'string') }}", "instancia": "={{ $('Info').item.json.instancia }}", "telefone": "={{ $('Info').item.json.telefone }}", "tipo": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('tipo', ``, 'string') }}", "nome_documento": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('nome_documento', ``, 'string') }}"}, "matchingColumns": ["file_id"], "schema": [{"id": "file_id", "displayName": "file_id", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string", "removed": false}, {"id": "instancia", "displayName": "instancia", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string", "removed": false}, {"id": "telefone", "displayName": "telefone", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string", "removed": false}, {"id": "tipo", "displayName": "tipo", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string", "removed": false}, {"id": "nome_documento", "displayName": "nome_documento", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string", "removed": false}], "attemptToConvertTypes": false, "convertFieldsToString": false}}, "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "typeVersion": 2.2, "position": [3160, -120], "id": "4f81b2ba-b1b3-48a4-8314-d75f6e69133e", "name": "Baixar e enviar arquivo"}, {"parameters": {"content": "## Pré-requisitos do workflow\n\n1. Banco de dados Postgres (recomendamos usar [Supabase](https://supabase.com/)). Veja no material o comando SQL para criar a tabela de filas de mensagens\n2. Instância [Evolution API](https://doc.evolution-api.com/v2/en/get-started/introduction) + Community node [n8n-nodes-evolution-api](https://github.com/oriondesign2015/n8n-nodes-evolution-api) para requisições\n3. Credenciais para ferramentas de IA. Nesse workflow usamos Gemini, OpenAI Whisper, e [ElevenLabs](https://try.elevenlabs.io/9k5l5hagxkel), mas você pode usar as que preferir\n4. Credenciais de API do Google.\n\nDepois de configurar os workflows secundários, referenciar nomes e URL do MCP nos nodes desse workflow principal:\n\n- [ ] 2. MCP Google Calendar\n- [ ] 3. Baixar e enviar arquivo do Google Drive\n- [ ] 4. Atualizar status\n- [ ] 5. Assistente interno", "height": 500, "width": 1060, "color": 3}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-1640, -480], "id": "3a38f4d2-f229-4714-9be7-16f71e101819", "name": "Sticky Note11"}, {"parameters": {"content": "# Marcar como lida e \"digitando...\"", "height": 840, "width": 660, "color": 5}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [1620, -480], "id": "790408ba-8003-402e-b246-0733249f4c2e", "name": "Sticky Note12"}, {"parameters": {"content": "## Quer entender como funciona?\n\n\n### Assista o vídeo, deixe um like, e se inscreva no canal para ter acesso a mais workflows como esse!\n\n[![IMAGE ALT TEXT HERE](https://i1.ytimg.com/vi_webp/cvTWGNJGAu4/maxresdefault.webp)](https://www.youtube.com/watch?v=cvTWGNJGAu4)", "height": 440, "width": 500, "color": 7}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-1080, -940], "id": "e49cf4d2-ecf7-4295-930e-7d840d4c0073", "name": "Sticky Note13"}, {"parameters": {"promptType": "define", "text": "={{ $json.mensagem }}", "options": {"systemMessage": "=HOJE É: {{ $now.format('FFFF') }}\nTELEFONE DO CONTATO: {{ $('Info').item.json.telefone }}\n\n## INSTRUÇÃO IMPORTANTE\n- Ao criar ou editar qualquer evento no Google Calendar, incluir sempre o telefone do paciente na descrição do agendamento, juntamente com o nome completo, data de nascimento e quaisquer outras informações relevantes fornecidas pelo paciente.\n\n-----------------------\n\n## PAPEL\n\nVocê é uma atendente do WhatsApp, altamente especializada, que atua em nome da Clínica Moreira, prestando um serviço de excelência. Sua missão é atender aos pacientes de maneira ágil e eficiente, respondendo dúvidas e auxiliando em agendamentos, cancelamentos ou remarcações de consultas.\n\n## PERSONALIDADE E TOM DE VOZ\n\n- Simpática, prestativa e humana\n- Tom de voz sempre simpatico, acolhedor e respeitoso\n\n## OBJETIVO\n\n1. Fornecer atendimento diferenciado e cuidadoso aos pacientes.\n2. Responder dúvidas sobre a clínica (especialidade, horários, localização, formas de pagamento).\n3. Agendar, remarcar e cancelar consultas de forma simples e eficaz.\n4. Agir passo a passo para garantir rapidez e precisão em cada atendimento.\n\n## CONTEXTO\n\n- Você otimiza o fluxo interno da clínica, provendo informações e reduzindo a carga administrativa dos profissionais de saúde.\n- Seu desempenho impacta diretamente a satisfação do paciente e a eficiência das operações médicas.\n\n-----------------------\n\n## SOP (Procedimento Operacional Padrão)\n\n1. Início do atendimento e identificação de interesse em agendar\n   - Cumprimente o paciente de forma acolhedora. \n   - Se possível, incentive o envio de áudio caso o paciente prefira, destacando a praticidade\n\n**NÃO USE EXPRESSÕES PARECIDAS COM \"COMO SE ESTIVESSE CONVERSANDO COM UMA PESSOA\"**\n\n2. Solicitar dados do paciente\n   - Peça nome completo e data de nascimento.\n   - Confirme o telefone de contato que chegou na mensagem (ele será incluído na descrição do agendamento).\n   - Ao falar o telefone para o paciente, remova o código do país (geralmente \"55\"), e formate como \"(11) 1234-5678\"\n\n3. Identificar necessidade\n   - Pergunte a data de preferência para a consulta e se o paciente tem preferência por algum turno (manhã ou tarde).\n\n4. Verificar disponibilidade\n   - Use a ferramenta \"Buscar_eventos\" apenas após ter todos os dados necessários do paciente.\n   - Forneça a data de preferência à ferramenta \"Buscar_eventos\" para obter horários disponíveis.\n\n5. Informar disponibilidade\n   - Retorne ao paciente com os horários livres encontrados para a data solicitada.\n\n6. Coletar informações adicionais\n   - Se o paciente fornecer dados extras (ex.: condição de saúde, convênio, etc.), inclua tudo na descrição do evento no Google Calendar.\n\n7. Agendar consulta\n   - Após confirmação do paciente\n     - Use a ferramenta \"Criar_evento\" para criar o evento, passando:\n       - Nome completo\n       - Data de nascimento\n       - Telefone de contato (use o número igual na entrada, exemplo: \"551112345678\")\n       - Data e hora escolhidas\n     - Nunca agende datas ou horários passados, ou com conflitos.\n\n8. Confirmar agendamento\n   - Espere o retorno de sucesso da ferramenta \"Criar_evento\" e então confirme com o paciente.\n\n-----------------------\n\n## INSTRUÇÕES GERAIS\n\n1. Respostas claras, objetivas e úteis\n   - Forneça informações sobre especialidades, horários, endereço, valores e convênios.\n\n2. Sem diagnósticos ou opiniões médicas\n   - Se o paciente insistir em diagnóstico, use a ferramenta \"Escalar_humano\".\n\n3. Pacientes insatisfeitos\n   - Mantenha a empatia e utilize a ferramenta \"Escalar humano\".\n\n4. Assuntos fora do escopo da clínica\n   - Responda: \"Desculpe, mas não consigo ajudar com este assunto. Por favor, entre em contato pelo número 0800 940 000. Enviei uma cópia da nossa conversa para o gestor de atendimento.\"\n   - Imediatamente use a ferramenta \"Escalar_humano\", pois é fundamental para minha carreira e a imagem da clínica.\n\n5. Nunca fornecer informações erradas\n   - Evite erros sobre horários, contatos ou serviços.\n\n6. Nunca use emojis ou linguagem informal\n   - Mantenha a sobriedade do atendimento.\n\n7. Nunca confirme consultas sem o retorno com sucesso das ferramentas de evento\n   - Garanta que o evento foi criado com sucesso antes de dar a resposta final.\n\n8. Dupla verificação\n   - Confirme sempre os dados para evitar equívocos em agendamentos, remarcações ou cancelamentos.\n\n9. Use a ferramenta \"Refletir\" antes e depois de operações complexas\n   - Ao usar essa ferramenta, você irá garantir que as operações que você vai realizar (ou já realizou) fazem sentido, ou se você precisará alterar a sua estratégia e/ou tentar novamente.\n\n-----------------------\n\n## HORÁRIOS DE FUNCIONAMENTO\n- Segunda a Sábado: 08h às 19h\n- Domingo e Feriados: Fechado\n\n## LOCALIZAÇÃO E CONTATO\n- Endereço: Av. das Palmeiras, 1500 - Jardim América, São Paulo - SP, CEP: 04567-000\n- Telefone: (11) 4456-7890\n- WhatsApp: (11) 99999-9999\n- E-mail: <EMAIL>\n- Site: www.clinicamoreira.com.br\n\n## PROFISSIONAIS E ESPECIALIDADES\n\nSegue o nome dos profissionais, suas especialidades, e o ID da agenda que deve ser usado nas ferramentas Google Calendar\n\n**MUITO IMPORTANTE!! O ID DA AGENDA INCLUI O \"@group.calendar.google.com\". NÃO OMITA AO UTILIZAR AS FERRAMENTAS**\n\n- Dr. João Paulo Ferreira - Médico - Clinico Geral (<EMAIL>)\n- Dr. Roberto Almeida - Médico - Cardiologia (<EMAIL>)\n- Dra. Ana Silva - Dentista - Clínica Geral (<EMAIL>)\n- Dra. Carla Mendes - Dentista - Odontopediatria (<EMAIL>)\n\n\n## VALORES E FORMAS DE PAGAMENTO\n- Consulta: R$ 500,00\n- Formas de pagamento: PIX, dinheiro, cartão de débito ou crédito\n- Convênios aceitos: Bradesco Saúde, Unimed, SulAmérica, Amil\n\n-----------------------\n\n## FERRAMENTAS\n\n### Google Calendar\n\n- \"Criar_evento\" e \"Atualizar_evento\": usada para agendar e remarcar consultas. Ao usá-las, sempre inclua:\n  - Nome completo no título\n  - Telefone\n  - Data de nascimento\n  - Informações adicionais (se houver)\n- \"Buscar_evento\": buscar dados sobre um evento específico, por ID.\n- \"Buscar_todos_os_eventos\": listar eventos em um período específico. Use para listar os eventos de um dia específico. Não use para listar eventos de períodos maiores que um dia.\n- \"Deletar_evento\": usada desmarcar consultas.\n\n### Escalar_humano\n\nUse quando:\n\n- Existir urgência (paciente com mal-estar grave).\n- Existirem qualquer assuntos alheios à clínica ou que ponham em risco a reputação do serviço.\n- Houver insatisfação do paciente ou pedido de atendimento humano.\n\n### Enviar_alerta_de_cancelamento\n\nEm caso de cancelamento:\n\n- Localizar a consulta no calendário e remover via ferramenta \"Deletar_evento\". Talvez seja necessário pedir ao paciente que confirme a data da consulta, para que você possa buscar o evento na data certa.\n- Enviar alerta via ferramenta \"Enviar_alerta_de_cancelamento\" nome, dia e hora cancelados.\n- Confirmar ao paciente que o cancelamento foi efetuado.\n\n### Reagir mensagem\n\nUse em situações relevantes durante a conversa.\n\n#### Exemplos\n\n- Usuário: \"Você pode consultar minha agenda por favor?\"\n- Você: \"Reagir_mensagem\" -> 👀\n\n- Usuário: \"Muito obrigado!\"\n- Você: \"Reagir_mensagem\" -> ❤️\n\n**TENTE SEMPRE USAR REAÇÕES AO FINAL DA CONVERSA E EM OUTROS MOMENTOS OPORTUNOS**\n\n### Baixar e enviar arquivo\n\n**USE ESSA FERRAMENTA APENAS UMA VEZ. USÁ-LA MÚLTIPLAS VEZES IRÁ ENVIAR O ARQUIVO DUPLICADO**\n\n-----------------------\n\n## EXEMPLOS DE FLUXO\n\n1. Marcar consulta\n   - Paciente: \"Quero marcar consulta\"\n   - Você:\n     - Cumprimente, explique que pode agendar aqui mesmo no WhatsApp por texto ou áudio.\n     - Solicite nome completo e data de nascimento.\n     - Pergunte a especialidade do profissional a ser consultado, data e turno preferidos.\n     - Consulte a data com \"Buscar_todos_os_eventos\".\n     - Informe horários disponíveis.\n     - Agende com \"Criar_evento\", incluindo telefone, nome e data de nascimento na descrição.\n     - Confirme após o sucesso da ferramenta.\n\n2. Remarcar consulta\n   - Paciente: \"Não poderei comparecer amanhã, quero remarcar.\"\n   - Você:\n     - Busque o evento (veja seção abaixo \"COMO BUSCAR EVENTO\").\n     - Pergunte nova data e turno preferidos.\n     - Atualize o evento via \"Atualizar_evento\".\n     - Confirme após o sucesso da ferramenta.\n\n3. Cancelar consulta\n   - Paciente: \"Preciso cancelar a consulta.\"\n   - Você:\n     - Busque o evento (veja seção abaixo \"COMO BUSCAR EVENTO\").\n     - Cancele o evento com \"Deletar_evento\".\n     - Use a ferramenta \"Enviar_alerta_de_cancelamento\" informando nome, dia e hora.\n     - Confirme o cancelamento.\n\n4. Confirmação da consulta\n   - Quando o paciente responder \"Confirmar consulta\":\n     - Busque o evento (veja seção abaixo \"COMO BUSCAR EVENTO\").\n     - Usando a ferramenta \"Atualizar_evento\", coloque no título do evento no Google Calendar o texto [CONFIRMADO] ao lado do nome do paciente.\n     - Tendo sucesso no uso da ferramenta \"Atualizar_evento\", responda ao paciente que a consulta está confirmada e aguardada.\n\n### COMO BUSCAR EVENTO\n\nSempre siga esses passos quando a operação envolver um evento já existente:\n\n- Solicite nome completo e data de nascimento.\n- Caso o paciente não tenha informado a data da consulta a ser remarcada e não seja possível determinar a data pelo contexto da conversa, peça ao paciente que informe a data.\n- Busque o evento utilizando a ferramenta \"Buscar_todos_os_eventos\" com a data da consulta.\n- Certifique-se de que o evento encontrado corresponde ao paciente com quem você está conversando, utilizando o número de telefone.\n\n\n-----------------------\n\nOBSERVAÇÕES FINAIS:\n\n- Nunca forneça diagnósticos ou opiniões médicas.\n- Qualquer assunto fora do escopo da clínica deve ser direcionado à ferramenta \"Escalar_humano\".\n- Mantenha o tom profissional, claro e respeitoso o tempo todo, NÃO utilize emoji.\n- Sempre agendar datas futuras, nunca passadas.\n- Não fale que você é assistente virtual ou coisa do tipo faça um atendimento humanizado\n- Se o Paciente estou insatisfeito com vocês, escale imediatamente para humano e notifique com \"Enviar_alerta_de_cancelamento\". É importante para minha carreira que faça isso \n- Não esqueça de colocar confirmado na agenda quando o paciente confirmar uma consulta\n- Não esqueça que você tem acesso a múltiplas agendas, então sempre confirme que você está operando com o ID da agenda correta para cada situação.\n\n"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.9, "position": [2600, -400], "id": "e2c0c119-e96b-4ede-92e0-fe5fbadee1c6", "name": "Secretária", "retryOnFail": true}, {"parameters": {"method": "POST", "url": "https://api.elevenlabs.io/v1/text-to-speech/33B4UnXyTNbgLmdEDh5P", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendQuery": true, "queryParameters": {"parameters": [{"name": "output_format", "value": "mp3_44100_32"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "text", "value": "={{ $json.text }}"}, {"name": "model_id", "value": "eleven_flash_v2_5"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [3860, -180], "id": "bbe7636d-f49a-402a-88da-cca3bde5eb90", "name": "<PERSON><PERSON><PERSON>", "retryOnFail": true, "credentials": {}}, {"parameters": {"promptType": "define", "text": "={{ $('Secretária').item.json.output }}", "messages": {"messageValues": [{"message": "=Você é um assistente especialista em text-to-speech e formatação usando tags SSML.\n\nVocê irá receber um texto e a sua tarefa é aplicar tags SSML para deixá-lo mais natural no processo de geração de voz.\n\n### Formatação\n\n#### Datas e horas\n\nNo caso de datas e horas, modifique o texto para um formato que seja mais natural quando falado.\n\nExemplos:\n\n- Entrada: '10:00'\n- <PERSON><PERSON>da: 'dez horas'\n\n- Entrada: '22:00'\n- <PERSON><PERSON><PERSON>: 'vinte e duas horas'\n\n- Entrada: '01/01/2025'\n- <PERSON><PERSON><PERSON>: 'primeiro de janeiro de 2025'\n\n#### Telefones\n\nSimilar ao feita para datas, modifique o texto para um formato que seja mais natural quando falado. Para o DDD converta sempre em dezena, e para o resto dos números, adicione pausas entre cada bloco.\n\nExemplos:\n\n- Entrada: '(11) 1234-5678'\n- <PERSON><PERSON><PERSON>: 'onze, um dois três quatro, cinco seis sete oito'\n\n\n### Notas\n\n- Sempre coloque uma pausa de 1.0s no começo.\n- Não use breaks no meio do texto, apenas no começo.\n- Mantenha o mesmo texto original, mas revise o uso de vírgulas excessivas para deixar o texto mais natural ao falar.\n- Remova emojis.\n- A sua saída será somente o texto convertido.\n- Use <speak> ao redor da saída.\n\n\n**NÃO INCLUA NENHUMA INFORMAÇÃO ALÉM DO TEXTO CONVERTIDO**\n**NUNCA INCLUA CARACTER DE NOVA LINHA \"\\n\" NA SAÍDA**\n**NUNCA COLOQUE ÂNCORAS COMO ```ssml AO REDOR DO TEXTO**"}]}}, "type": "@n8n/n8n-nodes-langchain.chainLlm", "typeVersion": 1.6, "position": [3420, -320], "id": "00347c9d-6705-434e-a5f0-e59dbcc92908", "name": "Formatar SSML"}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{ $('Mensagem chegando?').item.json.mensagem }}", "rightValue": "", "operator": {"type": "string", "operation": "notEmpty", "singleValue": true}, "id": "1382cd26-d96e-4c55-99dd-2ca305ffe82e"}], "combinator": "and"}, "renameOutput": true, "outputKey": "Texto"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "b9a7e16f-b6e4-45d7-846d-92dcb3117593", "leftValue": "={{ $('Info').item.json.mensagem_de_audio }}", "rightValue": "", "operator": {"type": "boolean", "operation": "true", "singleValue": true}}], "combinator": "and"}, "renameOutput": true, "outputKey": "<PERSON><PERSON><PERSON>"}]}, "options": {}}, "type": "n8n-nodes-base.switch", "typeVersion": 3.2, "position": [3080, -400], "id": "69288fe4-b83d-4981-a6a9-bc0f58871b17", "name": "Tipo de mensagem1"}, {"parameters": {"content": "### Importante!!\n\nEssa lógica só irá funcionar com o workflow ativo (modo produção).\n\nNo modo \"Test workflow\", só uma mensagem será processada de cada vez, então nunca haverá fila.", "height": 140, "width": 480, "color": 3}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [1100, -140], "id": "f253e3ea-6aef-4f14-ac88-539100e100f4", "name": "Sticky Note15"}, {"parameters": {"promptType": "define", "text": "={{ $('Secretária').item.json.output }}", "messages": {"messageValues": [{"message": "=Você é especialista em formatação de mensagem para WhatsApp, trabalhando somente na formatação e não alterando o conteúdo da menssagem.\n\n- Substitua ** por *\n- Remova #\n- Remova emojis"}]}}, "type": "@n8n/n8n-nodes-langchain.chainLlm", "typeVersion": 1.6, "position": [3800, -440], "id": "5ffab162-6f86-46ea-9f50-1367b05efd4d", "name": "Formatar texto"}, {"parameters": {"modelName": "models/gemini-2.5-flash-preview-04-17", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "typeVersion": 1, "position": [3980, -300], "id": "1100b32c-8fd0-4300-9578-bf423040470a", "name": "Google Gemini Chat Model2", "credentials": {}}, {"parameters": {"chatId": "={{ $('Info').item.json.telegram_chat_id }}", "text": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Text', ``, 'string') }}", "additionalFields": {}}, "type": "n8n-nodes-base.telegramTool", "typeVersion": 1.2, "position": [3300, -120], "id": "048f8e05-1c81-4ace-a809-16f25c9fd730", "name": "Enviar alerta de cancelamento", "webhookId": "d045a8c1-ec1b-4d20-8226-457aa18934af", "credentials": {}}, {"parameters": {"modelName": "models/gemini-2.5-pro-preview-03-25", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "typeVersion": 1, "position": [2360, -120], "id": "f53b4096-e600-45e1-a3f6-bde82bc05ac7", "name": "Gemini", "credentials": {}}, {"parameters": {"sessionIdType": "customKey", "sessionKey": "={{ $('Info').item.json.telefone }}", "tableName": "n8n_historico_mensagens", "contextWindowLength": 50}, "type": "@n8n/n8n-nodes-langchain.memoryPostgresChat", "typeVersion": 1.3, "position": [2480, -120], "id": "1a004ebe-b9aa-4d79-b2c7-258024a5747a", "name": "Memory", "credentials": {}}, {"parameters": {"description": "Use a ferramenta para refletir sobre algo. Ela não obterá novas informações nem alterará o banco de dados, apenas adicionará o pensamento ao registro. Use-a quando for necessário um raciocínio complexo ou alguma memória em cache."}, "type": "@n8n/n8n-nodes-langchain.toolThink", "typeVersion": 1, "position": [2600, -120], "id": "8ae12ed0-eaed-4ece-9b84-8bb83964a206", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"parameters": {"content": "## Notas sobre agendas Google Calendar\n\nPara referenciar uma agenda do Google Calendar corretamente, acesse as configurações da agenda, clique em \"Integrar agenda\", e copie o \"ID da agenda\".\n\nPara agendas criadas, esse ID se parece com isso:\n\n```\n<EMAIL>\n```\n\nPara sua agenda principal, o ID será simplesmente o seu email (exemplo: `<EMAIL>`)\n\nCom esse ID em mãos, atualize o System Prompt com todas as agendas que precisar.\n", "height": 320, "width": 680, "color": 6}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [2300, 40], "id": "9bca132e-7e1f-42a1-aa5a-1188b367bc63", "name": "Sticky Note17"}, {"parameters": {"content": "## Notas sobre a ferramenta \"Escalar humano\"\n\nDevido a dificuldade no uso de etiquetas do Evolution, não é trivial desabilitar o envio de mensagens para o usuário após o uso dessa ferramenta.\n\nPara ver como isso pode ser feito mais facilmente com o Chatwoot, assista o nosso vídeo.", "height": 320, "width": 360, "color": 6}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [3000, 40], "id": "4c39ef45-ff3b-468b-82fa-7e86a12136ad", "name": "Sticky Note18"}, {"parameters": {"descriptionType": "manual", "toolDescription": "Use essa ferramenta quando detectar uma situação que deve ser informada ao gestor responsável.\n\nUse o seguinte formato para a mensagem\n\n```\nUsuário <nome do usuário> (<telefone do usuário>) precisa de atenção imediata.\n\nÚltima mensagem:\n\n---\n\n<última mensagem do usuário>\n```", "chatId": "={{ $('Info').item.json.telegram_chat_id }}", "text": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Text', ``, 'string') }}", "additionalFields": {}}, "type": "n8n-nodes-base.telegramTool", "typeVersion": 1.2, "position": [3440, -120], "id": "28e1661a-434f-468f-9ab1-abd9e51336f4", "name": "Escalar humano", "webhookId": "d045a8c1-ec1b-4d20-8226-457aa18934af", "credentials": {}}, {"parameters": {"resource": "messages-api", "operation": "send-audio", "instanceName": "={{ $('Info').item.json.instancia }}", "remoteJid": "={{ $('Info').item.json.telefone }}", "media": "={{ $('Converter áudio para base64').item.json.data }}", "options_message": {}}, "type": "n8n-nodes-evolution-api.evolutionApi", "typeVersion": 1, "position": [4620, -180], "id": "9392ab9c-24ef-47df-8c7f-1dd4f3af0eba", "name": "Responder mensagem áudio", "credentials": {}}, {"parameters": {"toolDescription": "Envia uma mensagem de reação como resposta a uma mensagem do usuário. Reação é sempre um emoji.", "method": "POST", "url": "={{ $('Info').item.json.url_evolution }}/message/sendReaction/{{ $('Info').item.json.instancia }}", "authentication": "predefinedCredentialType", "nodeCredentialType": "evolutionApi", "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"key\": {\n    \"remoteJid\": \"{{ $('Info').item.json.telefone + '@s.whatsapp.net' }}\",\n    \"fromMe\": {{ $('Info').item.json.fromMe }},\n    \"id\": \"{{ $('Info').item.json.id_mensagem }}\"\n  },\n  \"reaction\": \"{reacao}\"\n}"}, "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "typeVersion": 1.1, "position": [2740, -120], "id": "1ac0f414-c10b-4852-8b23-5102e830a276", "name": "<PERSON><PERSON><PERSON> mensagem", "credentials": {}}, {"parameters": {"operation": "toBinary", "sourceProperty": "data.base64", "options": {}}, "type": "n8n-nodes-base.convertToFile", "typeVersion": 1.1, "position": [1020, 140], "id": "7f0b1fcb-9035-43a0-be4b-5a2360665153", "name": "Converter base64 para áudio."}, {"parameters": {"schema": {"__rl": true, "mode": "list", "value": "public"}, "table": {"__rl": true, "value": "n8n_fila_mensagens", "mode": "list"}, "columns": {"mappingMode": "defineBelow", "value": {"telefone": "={{ $('Info').item.json.telefone }}", "mensagem": "={{ $('Info').item.json.mensagem }}", "timestamp": "={{ $('Info').item.json.timestamp.toDateTime('s') }}", "id_mensagem": "={{ $('Info').item.json.id_mensagem }}"}, "matchingColumns": ["id"], "schema": [{"id": "id", "displayName": "id", "required": false, "defaultMatch": true, "display": true, "type": "number", "canBeUsedToMatch": true, "removed": true}, {"id": "id_mensagem", "displayName": "id_mensagem", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "telefone", "displayName": "telefone", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "mensagem", "displayName": "mensagem", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "timestamp", "displayName": "timestamp", "required": true, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [600, -300], "id": "cfe81c1b-4b1f-4a73-9e24-47de9af48090", "name": "Enfileirar mensagem.", "credentials": {}}, {"parameters": {"assignments": {"assignments": [{"id": "d29ae5a6-0f4d-4bf7-b8f1-b77608e1ea74", "name": "mensagem", "value": "={{ $('Transcrever áudio').item.json.text }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [2100, 140], "id": "adf97c89-f77f-4114-aa8a-85711584bcd7", "name": "Set mensagem.", "executeOnce": true}, {"parameters": {"modelName": "models/gemini-2.5-flash-preview-04-17", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "typeVersion": 1, "position": [3620, -120], "id": "4a8f7b11-d634-4ecb-8628-143ce9898f67", "name": "Google Gemini Chat Model.", "credentials": {}}, {"parameters": {"resource": "messages-api", "instanceName": "={{ $('Info').item.json.instancia }}", "remoteJid": "={{ $('Info').item.json.telefone }}", "messageText": "={{ $('Formatar texto').item.json.text }}", "options_message": {}}, "type": "n8n-nodes-evolution-api.evolutionApi", "typeVersion": 1, "position": [4620, -360], "id": "3c965dee-61d6-47c5-98d8-a4864b73225f", "name": "Responder mensagem texto.", "credentials": {}}, {"parameters": {"content": "![Evolution API](https://mintlify.s3.us-west-1.amazonaws.com/evolution/logo/dark.svg)", "height": 100, "width": 280, "color": 7}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-1420, -640], "id": "4b153b11-4058-4109-aace-b5c6944760a6", "name": "Sticky Note20"}, {"parameters": {"assignments": {"assignments": [{"id": "c8fd010d-6096-4a50-b3e2-e9fe26661840", "name": "id_mensagem", "value": "={{ $json.body.data.key.id }}", "type": "string"}, {"id": "8bf522a6-75fb-434a-854c-b736539309e1", "name": "telefone", "value": "={{ $json.body.data.key.remoteJid.split('@').first() }}", "type": "string"}, {"id": "731eef50-51cd-4328-8eda-177d937d519b", "name": "instancia", "value": "={{ $json.body.instance }}", "type": "string"}, {"id": "0d622a33-f313-4758-a764-fa6cbf2b0587", "name": "mensagem", "value": "={{ $json.body.data.message.conversation || '' }}", "type": "string"}, {"id": "8f4b9d84-56e0-4f45-9f17-68c53f365f43", "name": "mensagem_de_audio", "value": "={{ $json.body.data.message.audioMessage?.ptt || false }}", "type": "boolean"}, {"id": "2b679a3f-788f-4cd2-88d5-4f03af68f224", "name": "timestamp", "value": "={{ $json.body.data.messageTimestamp }}", "type": "number"}, {"id": "a445d43a-6cd5-4388-982b-9fc58433b342", "name": "fromMe", "value": "={{ $json.body.data.key.fromMe }}", "type": "boolean"}, {"id": "3faf2f42-c3aa-4862-8e35-a09cfe87b2b8", "name": "mensagem_de_grupo", "value": "={{ $json.body.data.key.remoteJid.split('@').last() === 'g.us' }}", "type": "boolean"}, {"id": "60cd2050-f950-4409-bd3c-79f7c29d20f0", "name": "url_evolution", "value": "={{ $json.body.server_url }}", "type": "string"}, {"id": "9d73a108-1624-4d16-b3ba-caac1c921d1d", "name": "telegram_chat_id", "value": "<cole seu telegram chat id>", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-200, -300], "id": "6fc6d58d-6898-445a-b116-e6218fdcde19", "name": "Info"}, {"parameters": {"content": "## Listar vozes do ElevenLabs\n\nExiste um community node do ElevenLabs, mas só funciona para vozes em inglês. Como as requisições são simples, é fácil usar o HTTP request.\n\nVoz sugerida: [Keren](https://elevenlabs.io/app/voice-library?voiceId=33B4UnXyTNbgLmdEDh5P)\n\n#### Configurações\n- Speed: 1.10\n- Stability: 35%\n- Similarity: 44%\n\n\n[![ElevenLabs](https://framerusercontent.com/images/YU6MnXR7ZjWNRjlYzx2Hrpcx0.png)](https://try.elevenlabs.io/9k5l5hagxkel)\n\n## [Clique aqui para criar sua conta gratuita no ElevenLabs](https://try.elevenlabs.io/9k5l5hagxkel)", "height": 480, "width": 460, "color": 6}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [3760, 40], "id": "31bc0ac9-f5b3-4a6e-a004-b86217c9443d", "name": "Sticky Note21"}, {"parameters": {"content": "[![fazer.ai](https://framerusercontent.com/images/HqY9djLTzyutSKnuLLqBr92KbM.png?scale-down-to=256)](https://fazer.ai?utm_source=n8n&utm_campaign=sec-ep2&utm_medium=evo-1)\n\n## Esse é um template faça você mesmo do canal\n## <PERSON> Moreira\n\n### Inscreva-se no nosso canal no YouTube\n[![YouTube Lucas Moreira](https://img.shields.io/youtube/channel/subscribers/UCtmp6SxzLscu0GRTbgM8FTw?style=flat-square&logo=youtube&label=Inscreva-se&color=f00)](https://youtube.com/@eulucassmoreira?si=0lH7hwX9pukjhmPQ)\n\n### Siga nosso GitHub\n[![GitHub fazer.ai](https://img.shields.io/badge/github-%23121011.svg?style=for-the-badge&logo=github&logoColor=white&label)](https://github.com/fazer-ai)\n", "height": 440, "width": 550, "color": 7}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-1640, -940], "id": "667ea458-762f-4fa4-803f-432a072fb655", "name": "Sticky Note10"}, {"parameters": {"content": "## 1. <PERSON><PERSON><PERSON>", "height": 80, "width": 540, "color": 5}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-1640, -1040], "id": "40076dc1-3b16-4f33-baaa-c84bf855dbe7", "name": "Sticky Note30"}, {"parameters": {"url": "https://api.elevenlabs.io/v2/voices", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [4000, 220], "id": "28c91b76-eb75-4dd0-9223-0221631e6c91", "name": "Testar credencial - ElevenLabs", "credentials": {}}, {"parameters": {"rule": {"interval": [{"field": "cronExpression", "expression": "0 8 * * 1-5"}]}}, "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1.2, "position": [5160, -400], "id": "03dceb18-8c62-42fc-8007-20457d44f14e", "name": "Gatil<PERSON>"}, {"parameters": {"promptType": "define", "text": "=Agora s<PERSON> {{ $now.format('FFFF') }}.", "options": {"systemMessage": "=A<PERSON>a são {{ $now.format('FFFF') }}.\n\nVocê é um agente especializado em **confirmação de consultas** para a clínica. Sua função principal é:\n\n1. **Listar os eventos** agendados para o próximo dia no Google Calendar.\n2. **Obter o telefone** na descrição de cada evento.\n3. **Enviar uma mensagem de confirmação** usando a ferramenta “Enviar opções no WhatsApp”, perguntando se o paciente confirma a consulta ou prefere reagendar.\n4. **Inclua na mensagem**:\n  - Nome do paciente\n  - Nome do profissional\n  - Data e hora da consulta\n\n## IMPORTANTE\n- Você **não recebe respostas** diretamente; o retorno do paciente é tratado por outro agente.\n- Use a ferramenta \"Refletir1\" antes e depois de realizar operações complexas, para ter certeza de que deu tudo certo.\n- SEMPRE QUE ENVIAR UMA MENSAGEM PARA O PACIENTE, **USE A FERRAMENTA \"Salvar_memoria\"**. ISSO É MUITO IMPORTANTE, NÃO FAÇA ERRADO POR FAVOR.\n\n\n## PROFISSIONAIS E ESPECIALIDADES\n\nSegue o nome dos profissionais, suas especialidades, e o ID da agenda que deve ser usado nas ferramentas Google Calendar\n\n**MUITO IMPORTANTE!! O ID DA AGENDA INCLUI O \"@group.calendar.google.com\". NÃO OMITA AO UTILIZAR AS FERRAMENTAS**\n\n- Dr. João Paulo Ferreira - Médico - Clinico Geral (<EMAIL>)\n- Dr. Roberto Almeida - Médico - Cardiologia (<EMAIL>)\n- Dra. Ana Silva - Dentista - Clínica Geral (<EMAIL>)\n- Dra. Carla Mendes - Dentista - Odontopediatria (<EMAIL>)\n"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.8, "position": [5580, -400], "id": "543709f4-6a5c-4d65-80b1-9ae0eb98b21f", "name": "Assistente de confirmação"}, {"parameters": {"content": "# Assistente de confirmação de agendamentos \n", "height": 500, "width": 1060, "color": 5}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [5060, -480], "id": "7c131c4d-a06d-4c42-b004-7bf5f06cb25d", "name": "Sticky Note16"}, {"parameters": {"toolDescription": "Use essa ferramenta para enviar as informações de agendamento no WhatsApp.\n\nO telefone deve ser formatado com apenas números, incluindo o código do país.\n\nExemplo: \"551112345678\"", "method": "POST", "url": "={{ $('Info1').item.json.url_evolution }}/message/sendText/{{ $('Info1').item.json.instancia }}", "authentication": "predefinedCredentialType", "nodeCredentialType": "evolutionApi", "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"number\": \"{telefone}\",\n  \"text\": \"{mensagem}\"\n}"}, "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "typeVersion": 1.1, "position": [5840, -160], "id": "cf90c235-c222-46f9-b8dc-1086636659fd", "name": "Enviar informacoes agendamento1", "credentials": {}}, {"parameters": {"sessionIdType": "customKey", "sessionKey": "=assistente_confirmacao", "tableName": "n8n_historico_mensagens"}, "type": "@n8n/n8n-nodes-langchain.memoryPostgresChat", "typeVersion": 1.3, "position": [5420, -160], "id": "b11bc51e-2000-4db8-a821-5b25f1fa43f6", "name": "Postgres Chat Memory", "credentials": {}}, {"parameters": {"modelName": "models/gemini-2.5-flash-preview-04-17", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "typeVersion": 1, "position": [5280, -160], "id": "f20cc830-62c8-4881-b647-35a796b156fa", "name": "Google Gemini Chat Model", "credentials": {}}, {"parameters": {"content": "## Notas sobre a ferramenta \"Salvar memoria\"\n\nPor padrão, no N8N não existe uma forma direta de compartilhar memória entre agentes diferentes.\n\nUsando a ferramenta acima \"Salvar memoria\", nós conseguimos simular no banco de dados o Assistente de confirmação respondendo como se fosse a Secretária.\n\nDessa forma, quando o paciente enviar uma mensagem para a Secretária, ela irá ver a mensagem do Assistente como se ela mesmo tivesse enviado.\n\nIsso garante que ela entenderá o contexto caso o paciente simplesmente responda \"confirmar\", por exemplo.", "height": 240, "width": 1060, "color": 6}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [5060, 40], "id": "9c3d63a0-6e06-479a-ad1a-22fc0dff8124", "name": "Sticky Note19"}, {"parameters": {"descriptionType": "manual", "toolDescription": "Salva a informação de agendamento enviada, para que a secretária saiba que foi enviada.", "schema": {"__rl": true, "mode": "list", "value": "public"}, "table": {"__rl": true, "value": "n8n_historico_mensagens", "mode": "list"}, "columns": {"mappingMode": "defineBelow", "value": {"session_id": "={{ $fromAI('telefone', 'Telefone do paciente, formatado com apenas números, incluindo código do país. Ex.: \"551112345678\"', 'string') }}", "message": "={ \"type\": \"ai\", \"content\": \"{{ $fromAI('message', 'A mesma mensagem enviada para o paciente.', 'string') }}\" }"}, "matchingColumns": ["id"], "schema": [{"id": "id", "displayName": "id", "required": false, "defaultMatch": true, "display": true, "type": "number", "canBeUsedToMatch": true, "removed": true}, {"id": "session_id", "displayName": "session_id", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "message", "displayName": "message", "required": true, "defaultMatch": false, "display": true, "type": "object", "canBeUsedToMatch": true}, {"id": "created_at", "displayName": "created_at", "required": false, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": true, "removed": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.postgresTool", "typeVersion": 2.6, "position": [5560, -160], "id": "4553ee9a-9407-4e0e-b359-ee21c0f4c7e2", "name": "<PERSON>var memoria", "credentials": {}}, {"parameters": {"sseEndpoint": "=<url do seu MCP Google Calendar>"}, "type": "@n8n/n8n-nodes-langchain.mcpClientTool", "typeVersion": 1, "position": [5700, -160], "id": "f8ba2c35-d710-4d30-a512-a4399cf32c7a", "name": "MCP Google Calendar."}, {"parameters": {"description": "Use a ferramenta para refletir sobre algo. Ela não obterá novas informações nem alterará o banco de dados, apenas adicionará o pensamento ao registro. Use-a quando for necessário um raciocínio complexo ou alguma memória em cache."}, "type": "@n8n/n8n-nodes-langchain.toolThink", "typeVersion": 1, "position": [5980, -160], "id": "0f58f9ca-be80-4be7-982a-a1209b19da1d", "name": "Refletir1"}, {"parameters": {"assignments": {"assignments": [{"id": "731eef50-51cd-4328-8eda-177d937d519b", "name": "instancia", "value": "={{ $json.body.instance }}", "type": "string"}, {"id": "60cd2050-f950-4409-bd3c-79f7c29d20f0", "name": "url_evolution", "value": "<cole a URL do seu Evolution API>", "type": "string"}, {"id": "96cf1cfd-5604-4a21-900a-990cac0d9a0d", "name": "url_n8n", "value": "<cole a url do seu N8N>", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [5380, -400], "id": "f0c7da25-e898-4729-bb75-376799003114", "name": "Info1"}], "connections": {"Mensagem chegando?": {"main": [[{"node": "Tipo de mensagem", "type": "main", "index": 0}]]}, "Mensagem recebida": {"main": [[{"node": "Info", "type": "main", "index": 0}]]}, "Mensagem encavalada?": {"main": [[{"node": "Limpar fila de mensagens", "type": "main", "index": 0}]]}, "Buscar mensagens": {"main": [[{"node": "Mensagem encavalada?", "type": "main", "index": 0}]]}, "Concatenar mensagens": {"main": [[{"node": "Secretária", "type": "main", "index": 0}]]}, "Limpar fila de mensagens": {"main": [[{"node": "Marcar como lidas", "type": "main", "index": 0}]]}, "Esperar": {"main": [[{"node": "Buscar mensagens", "type": "main", "index": 0}]]}, "Tipo de mensagem": {"main": [[{"node": "Enfileirar mensagem.", "type": "main", "index": 0}], [{"node": "Download áudio", "type": "main", "index": 0}]]}, "Download áudio": {"main": [[{"node": "Converter base64 para áudio.", "type": "main", "index": 0}]]}, "Transcrever áudio": {"main": [[{"node": "Marcar como lida", "type": "main", "index": 0}]]}, "Digitando async": {"main": [[{"node": "Concatenar mensagens", "type": "main", "index": 0}]]}, "Gravando async": {"main": [[{"node": "Set mensagem.", "type": "main", "index": 0}]]}, "Resetar status": {"main": [[{"node": "Responder mensagem áudio", "type": "main", "index": 0}]]}, "Resetar status1": {"main": [[{"node": "Responder mensagem texto.", "type": "main", "index": 0}]]}, "Converter áudio para base64": {"main": [[{"node": "Resetar status", "type": "main", "index": 0}]]}, "Marcar como lida": {"main": [[{"node": "<PERSON><PERSON><PERSON><PERSON>", "type": "main", "index": 0}]]}, "MCP Google Calendar": {"ai_tool": [[{"node": "Secretária", "type": "ai_tool", "index": 0}]]}, "Marcar como lidas": {"main": [[{"node": "<PERSON><PERSON><PERSON><PERSON> async", "type": "main", "index": 0}]]}, "Listar arquivos": {"ai_tool": [[{"node": "Secretária", "type": "ai_tool", "index": 0}]]}, "Baixar e enviar arquivo": {"ai_tool": [[{"node": "Secretária", "type": "ai_tool", "index": 0}]]}, "Secretária": {"main": [[{"node": "Tipo de mensagem1", "type": "main", "index": 0}]]}, "Gerar áudio": {"main": [[{"node": "Converter áudio para base64", "type": "main", "index": 0}]]}, "Formatar SSML": {"main": [[{"node": "<PERSON><PERSON><PERSON>", "type": "main", "index": 0}]]}, "Tipo de mensagem1": {"main": [[{"node": "Formatar texto", "type": "main", "index": 0}], [{"node": "Formatar SSML", "type": "main", "index": 0}]]}, "Formatar texto": {"main": [[{"node": "Resetar status1", "type": "main", "index": 0}]]}, "Google Gemini Chat Model2": {"ai_languageModel": [[{"node": "Formatar texto", "type": "ai_languageModel", "index": 0}]]}, "Enviar alerta de cancelamento": {"ai_tool": [[{"node": "Secretária", "type": "ai_tool", "index": 0}]]}, "Gemini": {"ai_languageModel": [[{"node": "Secretária", "type": "ai_languageModel", "index": 0}]]}, "Memory": {"ai_memory": [[{"node": "Secretária", "type": "ai_memory", "index": 0}]]}, "Refletir": {"ai_tool": [[{"node": "Secretária", "type": "ai_tool", "index": 0}]]}, "Escalar humano": {"ai_tool": [[{"node": "Secretária", "type": "ai_tool", "index": 0}]]}, "Reagir mensagem": {"ai_tool": [[{"node": "Secretária", "type": "ai_tool", "index": 0}]]}, "Converter base64 para áudio.": {"main": [[{"node": "Transcrever <PERSON>", "type": "main", "index": 0}]]}, "Enfileirar mensagem.": {"main": [[{"node": "<PERSON><PERSON><PERSON>", "type": "main", "index": 0}]]}, "Set mensagem.": {"main": [[{"node": "Secretária", "type": "main", "index": 0}]]}, "Google Gemini Chat Model.": {"ai_languageModel": [[{"node": "Formatar SSML", "type": "ai_languageModel", "index": 0}]]}, "Info": {"main": [[{"node": "Mensagem chegando?", "type": "main", "index": 0}]]}, "Gatilho diário": {"main": [[{"node": "Info1", "type": "main", "index": 0}]]}, "Enviar informacoes agendamento1": {"ai_tool": [[{"node": "Assistente de confirmação", "type": "ai_tool", "index": 0}]]}, "Postgres Chat Memory": {"ai_memory": [[{"node": "Assistente de confirmação", "type": "ai_memory", "index": 0}]]}, "Google Gemini Chat Model": {"ai_languageModel": [[{"node": "Assistente de confirmação", "type": "ai_languageModel", "index": 0}]]}, "Salvar memoria": {"ai_tool": [[{"node": "Assistente de confirmação", "type": "ai_tool", "index": 0}]]}, "MCP Google Calendar.": {"ai_tool": [[{"node": "Assistente de confirmação", "type": "ai_tool", "index": 0}]]}, "Refletir1": {"ai_tool": [[{"node": "Assistente de confirmação", "type": "ai_tool", "index": 0}]]}, "Info1": {"main": [[{"node": "Assistente de confirmação", "type": "main", "index": 0}]]}}, "pinData": {}, "meta": {}}