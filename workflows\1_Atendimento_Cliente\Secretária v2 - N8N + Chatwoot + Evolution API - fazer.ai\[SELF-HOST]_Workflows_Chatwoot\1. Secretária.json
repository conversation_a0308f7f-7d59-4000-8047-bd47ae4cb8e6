{"nodes": [{"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "8ca54eae-15d1-49d3-af33-7a6e5d17b833", "leftValue": "={{ $json.tipo }}", "rightValue": "incoming", "operator": {"type": "string", "operation": "equals"}}, {"id": "82912d66-ee4b-439c-9d55-96090bc6ba62", "leftValue": "={{ $json.etiquetas }}", "rightValue": "agente-off", "operator": {"type": "array", "operation": "notContains", "rightType": "any"}}, {"id": "cf87bb7e-6bea-4697-bcd9-57e3b63998c2", "leftValue": "={{ $json.etiquetas }}", "rightValue": "testando-agente", "operator": {"type": "array", "operation": "contains", "rightType": "any"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.filter", "typeVersion": 2.2, "position": [80, 0], "id": "cf2b0cf0-ab75-497a-8f3f-1991d281f22e", "name": "Mensagem chegando?"}, {"parameters": {"httpMethod": "POST", "path": "f6ddc488-8680-4351-84dd-d9e73b2d102d", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-480, 0], "id": "2500b484-9367-46a5-8757-67334a26ea0c", "name": "Mensagem recebida", "webhookId": "f6ddc488-8680-4351-84dd-d9e73b2d102d"}, {"parameters": {"jsCode": "const ultima_mensagem_da_fila = $input.last()\nconst mensagem_do_workflow = $('Info').first()\n\nif (ultima_mensagem_da_fila.json.id_mensagem !== mensagem_do_workflow.json.id_mensagem) {\n  // Mensagem encavalada, para o workflow\n  return [];\n}\n\n// Pass-through da fila de mensagens\nreturn $input.all();"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1260, 0], "id": "0aeff4a3-89a1-4f18-b7fd-452b56b04ad0", "name": "Mensagem encavalada?"}, {"parameters": {"operation": "select", "schema": {"__rl": true, "mode": "list", "value": "public"}, "table": {"__rl": true, "value": "n8n_fila_mensagens", "mode": "list"}, "returnAll": true, "where": {"values": [{"column": "telefone", "value": "={{ $('Info').item.json.telefone }}"}]}, "sort": {"values": [{"column": "timestamp"}]}, "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [1040, 0], "id": "c5c135d7-7c0b-42de-8c5f-485b4afe4179", "name": "Buscar mensagens", "credentials": {}}, {"parameters": {"operation": "deleteTable", "schema": {"__rl": true, "mode": "list", "value": "public"}, "table": {"__rl": true, "value": "n8n_fila_mensagens", "mode": "list"}, "deleteCommand": "delete", "where": {"values": [{"column": "telefone", "value": "={{ $json.telefone }}"}]}, "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [1460, 0], "id": "6d1ac16e-17ec-4943-b03c-c38ccf1002fa", "name": "Limpar fila de mensagens", "credentials": {}}, {"parameters": {"content": "# Processando mensagens encavaladas\n\nEssa etapa trata a situação em que o usuário envia múltiplas mensagens seguidas. O ponto negativo é o aumento no tempo de resposta do agente. Lógica dispensa uso de soluções mais complexas, como RabbitMQ.\n\nTempo de espera recomendado de ~16s. <PERSON>uando estiver testando, recomendamos reduzir um pouco para ficar mais rápido de testar.\n", "height": 540, "width": 1080, "color": 4}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [520, -180], "id": "88cc750e-5448-4442-b47a-d9b418798bf5", "name": "Sticky Note2"}, {"parameters": {"amount": 3}, "type": "n8n-nodes-base.wait", "typeVersion": 1.1, "position": [820, 0], "id": "4e361515-ad9f-4385-9580-c236485a2db5", "name": "<PERSON><PERSON><PERSON>", "webhookId": "689b51a0-89fe-459d-a560-a59c79797611"}, {"parameters": {"content": "# G<PERSON>do resposta", "height": 540, "width": 2080, "color": 3}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [2300, -180], "id": "b35aa066-e073-4a18-b373-39f452d248c5", "name": "Sticky Note3"}, {"parameters": {"content": "# Enviando resposta", "height": 540, "width": 1200, "color": 7}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [4420, -180], "id": "da18dc8a-7942-45f3-b6c0-1a78b0624dc0", "name": "Sticky Note4"}, {"parameters": {"content": "# Tratando input\n", "height": 540, "width": 1060}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-560, -180], "id": "712cc6c0-9483-4dc0-9b56-b84313e31204", "name": "Sticky Note5"}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{ $('Info').item.json.mensagem }}", "rightValue": "", "operator": {"type": "string", "operation": "notEmpty", "singleValue": true}, "id": "1382cd26-d96e-4c55-99dd-2ca305ffe82e"}], "combinator": "and"}, "renameOutput": true, "outputKey": "Texto"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "b9a7e16f-b6e4-45d7-846d-92dcb3117593", "leftValue": "={{ $('Info').item.json.mensagem_de_audio }}", "rightValue": "", "operator": {"type": "boolean", "operation": "true", "singleValue": true}}], "combinator": "and"}, "renameOutput": true, "outputKey": "<PERSON><PERSON><PERSON>"}]}, "options": {}}, "type": "n8n-nodes-base.switch", "typeVersion": 3.2, "position": [360, 0], "id": "5b793211-d96b-4982-a38b-8af2fa951355", "name": "Tipo de mensagem"}, {"parameters": {"content": "# Processando <PERSON>", "height": 420, "width": 1080, "color": 6}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [520, 380], "id": "809564c1-bb25-4337-b689-e34871b36351", "name": "Sticky Note6"}, {"parameters": {"content": "Para testar, recomendamos criar uma tag \"testando-agente\" e usar no número que enviará as mensagens para a secretária durante os testes.\n\nVocê pode marcar o seu número no Chatwoot com essa tag para que o agente responda apenas você.\n\nDepois de testar e validar, só remover a regra do filtro pro seu agente responder todo mundo.\n\n", "height": 180, "width": 600, "color": 3}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-120, 160], "id": "6431bc8a-98be-474c-af0d-445177f9c223", "name": "Sticky Note8"}, {"parameters": {"sseEndpoint": "<colar url do seu MCP>"}, "type": "@n8n/n8n-nodes-langchain.mcpClientTool", "typeVersion": 1, "position": [2900, 220], "id": "30b92b5e-66cb-42dd-b87b-7559dda91ab3", "name": "MCP Google Calendar"}, {"parameters": {"resource": "fileFolder", "returnAll": true, "filter": {"folderId": {"__rl": true, "value": "", "mode": "list"}}, "options": {}}, "type": "n8n-nodes-base.googleDriveTool", "typeVersion": 3, "position": [3040, 220], "id": "4ddf1cfd-193b-4d9d-ace0-9b95b147ec5e", "name": "Listar arquivos", "credentials": {}}, {"parameters": {"description": "Use essa ferramenta para baixar um arquivo do Google Drive e enviá-lo para o usuário.\n", "workflowId": {"__rl": true, "value": "", "mode": "list"}, "workflowInputs": {"mappingMode": "defineBelow", "value": {"file_id": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('file_id', ``, 'string') }}", "id_conta": "={{ $('Info').item.json.id_conta }}", "id_conversa": "={{ $('Info').item.json.id_conversa }}", "url_chatwoot": "={{ $('Info').item.json.url_chatwoot }}"}, "matchingColumns": ["file_id"], "schema": [{"id": "file_id", "displayName": "file_id", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string", "removed": false}, {"id": "id_conta", "displayName": "id_conta", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string", "removed": false}, {"id": "id_conversa", "displayName": "id_conversa", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string", "removed": false}, {"id": "url_chatwoot", "displayName": "url_chatwoot", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string", "removed": false}], "attemptToConvertTypes": false, "convertFieldsToString": false}}, "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "typeVersion": 2.2, "position": [3180, 220], "id": "727d48ce-9e44-45a6-8411-d347073c1fdb", "name": "Baixar e enviar arquivo"}, {"parameters": {"content": "[![fazer.ai](https://framerusercontent.com/images/HqY9djLTzyutSKnuLLqBr92KbM.png?scale-down-to=256)](https://fazer.ai?utm_source=n8n&utm_campaign=sec-ep2&utm_medium=cw-1)\n\n## Esse é um template faça você mesmo do canal\n## <PERSON> Moreira\n\n### Inscreva-se no nosso canal no YouTube\n[![YouTube Lucas Moreira](https://img.shields.io/youtube/channel/subscribers/UCtmp6SxzLscu0GRTbgM8FTw?style=flat-square&logo=youtube&label=Inscreva-se&color=f00)](https://youtube.com/@eulucassmoreira?si=0lH7hwX9pukjhmPQ)\n\n### Siga nosso GitHub\n[![GitHub fazer.ai](https://img.shields.io/badge/github-%23121011.svg?style=for-the-badge&logo=github&logoColor=white&label)](https://github.com/fazer-ai)\n", "height": 440, "width": 550, "color": 7}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-2720, -640], "id": "0088cad9-7423-4446-910f-304a116c8cdc", "name": "Sticky Note10"}, {"parameters": {"content": "## Pré-requisitos do workflow\n", "height": 1520, "width": 2140, "color": 3}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-2720, -180], "id": "d6d5aef6-698b-44cf-a1b1-9c6569caeda0", "name": "Sticky Note11"}, {"parameters": {"content": "# Marcar como lida e \"digitando...\"", "height": 540, "width": 660, "color": 5}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [1620, -180], "id": "10cb4c76-d869-4a70-ab5f-c02de66978e2", "name": "Sticky Note12"}, {"parameters": {"content": "## Quer entender como funciona?\n\n\n### Assista o vídeo, deixe um like, e se inscreva no canal para ter acesso a mais workflows como esse!\n\n[![IMAGE ALT TEXT HERE](https://i1.ytimg.com/vi_webp/cvTWGNJGAu4/maxresdefault.webp)](https://www.youtube.com/watch?v=cvTWGNJGAu4)", "height": 440, "width": 500, "color": 7}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-2160, -640], "id": "d3bb39b1-2f74-4a38-924d-6309a9ee<PERSON>ac", "name": "Sticky Note13"}, {"parameters": {"rule": {"interval": [{"field": "cronExpression", "expression": "0 8 * * 1-5"}]}}, "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1.2, "position": [4540, 740], "id": "1f810126-fe2e-42ee-9444-0e014e91db9a", "name": "Gatil<PERSON>"}, {"parameters": {"promptType": "define", "text": "=Agora s<PERSON> {{ $now.format('FFFF') }}.", "options": {"systemMessage": "=Agora são {{ $now.format('FFFF') }}.\n\nVocê é um agente especializado em **confirmação de consultas** para a clínica. Sua função principal é:\n\n1. **Listar os eventos** agendados para o próximo dia no Google Calendar.\n2. **Obter o telefone** na descrição de cada evento.\n3. **Obter o ID da conversa** na descrição de cada evento.\n4. **Enviar uma mensagem de confirmação** usando a ferramenta \"Enviar_agendamento\", perguntando se o paciente confirma a consulta ou prefere reagendar.\n5. **Inclua na mensagem**:\n  - Nome do paciente\n  - Nome do profissional\n  - Data e hora da consulta\n\n**NÃO INCLUA O ID DA CONVERSA NA MENSAGEM**\n\n## IMPORTANTE\n- Você **não recebe respostas** diretamente; o retorno do paciente é tratado por outro agente.\n- Use a ferramenta \"Refletir1\" antes e depois de realizar operações complexas, para ter certeza de que deu tudo certo.\n- SEMPRE QUE ENVIAR UMA MENSAGEM PARA O PACIENTE, **USE A FERRAMENTA \"Salvar_memoria\"**. ISSO É MUITO IMPORTANTE, NÃO FAÇA ERRADO POR FAVOR.\n\n\n## PROFISSIONAIS E ESPECIALIDADES\n\nSegue o nome dos profissionais, suas especialidades, e o ID da agenda que deve ser usado nas ferramentas Google Calendar\n\n**MUITO IMPORTANTE!! O ID DA AGENDA INCLUI O \"@group.calendar.google.com\". NÃO OMITA AO UTILIZAR AS FERRAMENTAS**\n\n- Dr. João Paulo Ferreira - Médico - Clinico Geral (<EMAIL>)\n- Dr. Roberto Almeida - Médico - Cardiologia (<EMAIL>)\n- Dra. Ana Silva - Dentista - Clínica Geral (<EMAIL>)\n- Dra. Carla Mendes - Dentista - Odontopediatria (<EMAIL>)\n"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.8, "position": [5180, 740], "id": "54cd6fba-134a-4d58-97a3-607d20ab6102", "name": "Assistente de confirmação", "retryOnFail": true}, {"parameters": {"content": "# Assistente de confirmação de agendamentos \n", "height": 600, "width": 1200, "color": 5}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [4420, 580], "id": "713286fd-867b-400c-8374-50f9157fc7be", "name": "Sticky Note16"}, {"parameters": {"promptType": "define", "text": "={{ $json.mensagem || $json.mensagem_audio }}", "options": {"systemMessage": "=HOJE É: {{ $now.format('FFFF') }}\nTELEFONE DO CONTATO: {{ $('Info').item.json.telefone }}\nID DA CONVERSA: {{ $('Info').item.json.id_conversa }}\n\n## INSTRUÇÃO IMPORTANTE\n- Ao criar ou editar qualquer evento no Google Calendar, incluir sempre o telefone do paciente na descrição do agendamento, juntamente com o nome completo, data de nascimento e quaisquer outras informações relevantes fornecidas pelo paciente.\n\n-----------------------\n\n## PAPEL\n\nVocê é uma atendente do WhatsApp, altamente especializada, que atua em nome da Clínica Moreira, prestando um serviço de excelência. Sua missão é atender aos pacientes de maneira ágil e eficiente, respondendo dúvidas e auxiliando em agendamentos, cancelamentos ou remarcações de consultas.\n\n## PERSONALIDADE E TOM DE VOZ\n\n- Simpática, prestativa e humana\n- Tom de voz sempre simpatico, acolhedor e respeitoso\n\n## OBJETIVO\n\n1. Fornecer atendimento diferenciado e cuidadoso aos pacientes.\n2. Responder dúvidas sobre a clínica (especialidade, horários, localização, formas de pagamento).\n3. Agendar, remarcar e cancelar consultas de forma simples e eficaz.\n4. Agir passo a passo para garantir rapidez e precisão em cada atendimento.\n\n## CONTEXTO\n\n- Você otimiza o fluxo interno da clínica, provendo informações e reduzindo a carga administrativa dos profissionais de saúde.\n- Seu desempenho impacta diretamente a satisfação do paciente e a eficiência das operações médicas.\n\n-----------------------\n\n## SOP (Procedimento Operacional Padrão)\n\n1. Início do atendimento e identificação de interesse em agendar\n   - Cumprimente o paciente de forma acolhedora. \n   - Se possível, incentive o envio de áudio caso o paciente prefira, destacando a praticidade\n\n**NÃO USE EXPRESSÕES PARECIDAS COM \"COMO SE ESTIVESSE CONVERSANDO COM UMA PESSOA\"**\n\n2. Solicitar dados do paciente\n   - Peça nome completo e data de nascimento.\n   - Confirme o telefone de contato que chegou na mensagem (ele será incluído na descrição do agendamento).\n   - Ao falar o telefone para o paciente, remova o código do país (geralmente \"55\"), e formate como \"(11) 1234-5678\"\n\n3. Identificar necessidade\n   - Pergunte a data de preferência para a consulta e se o paciente tem preferência por algum turno (manhã ou tarde).\n\n4. Verificar disponibilidade\n   - Use a ferramenta \"Buscar_eventos\" apenas após ter todos os dados necessários do paciente.\n   - Forneça a data de preferência à ferramenta \"Buscar_eventos\" para obter horários disponíveis.\n\n5. Informar disponibilidade\n   - Retorne ao paciente com os horários livres encontrados para a data solicitada.\n\n6. Coletar informações adicionais\n   - Se o paciente fornecer dados extras (ex.: condição de saúde, convênio, etc.), inclua tudo na descrição do evento no Google Calendar.\n\n7. Agendar consulta\n   - Após confirmação do paciente\n     - Use a ferramenta \"Criar_evento\" para criar o evento, passando:\n       - Nome completo\n       - Data de nascimento\n       - Telefone de contato (use o número igual na entrada, exemplo: \"551112345678\")\n       - Data e hora escolhidas\n       - ID da conversa (número para controle interno, **ESSE NÚMERO É ESSENCIAL, NÃO SE ESQUEÇA DE INCLUÍ-LO!!**)\n     - Nunca agende datas ou horários passados, ou com conflitos.\n\n8. Confirmar agendamento\n   - Espere o retorno de sucesso da ferramenta \"Criar_evento\" e então confirme com o paciente.\n\n-----------------------\n\n## INSTRUÇÕES GERAIS\n\n1. Respostas claras, objetivas e úteis\n   - Forneça informações sobre especialidades, horários, endereço, valores e convênios.\n\n2. Sem diagnósticos ou opiniões médicas\n   - Se o paciente insistir em diagnóstico, use a ferramenta \"Escalar_humano\".\n\n3. Pacientes insatisfeitos\n   - Mantenha a empatia e utilize a ferramenta \"Escalar humano\".\n\n4. Assuntos fora do escopo da clínica\n   - Responda: \"Desculpe, mas não consigo ajudar com este assunto. Por favor, entre em contato pelo número 0800 940 000. Enviei uma cópia da nossa conversa para o gestor de atendimento.\"\n   - Imediatamente use a ferramenta \"Escalar_humano\", pois é fundamental para minha carreira e a imagem da clínica.\n\n5. Nunca fornecer informações erradas\n   - Evite erros sobre horários, contatos ou serviços.\n\n6. Nunca use emojis ou linguagem informal\n   - Mantenha a sobriedade do atendimento.\n\n7. Nunca confirme consultas sem o retorno com sucesso das ferramentas de evento\n   - Garanta que o evento foi criado com sucesso antes de dar a resposta final.\n\n8. Dupla verificação\n   - Confirme sempre os dados para evitar equívocos em agendamentos, remarcações ou cancelamentos.\n\n9. Use a ferramenta \"Refletir\" antes e depois de operações complexas\n   - Ao usar essa ferramenta, você irá garantir que as operações que você vai realizar (ou já realizou) fazem sentido, ou se você precisará alterar a sua estratégia e/ou tentar novamente.\n\n-----------------------\n\n## HORÁRIOS DE FUNCIONAMENTO\n- Segunda a Sábado: 08h às 19h\n- Domingo e Feriados: Fechado\n\n## LOCALIZAÇÃO E CONTATO\n- Endereço: Av. das Palmeiras, 1500 - Jardim América, São Paulo - SP, CEP: 04567-000\n- Telefone: (11) 4456-7890\n- WhatsApp: (11) 99999-9999\n- E-mail: <EMAIL>\n- Site: www.clinicamoreira.com.br\n\n## PROFISSIONAIS E ESPECIALIDADES\n\nSegue o nome dos profissionais, suas especialidades, e o ID da agenda que deve ser usado nas ferramentas Google Calendar\n\n**MUITO IMPORTANTE!! O ID DA AGENDA INCLUI O \"@group.calendar.google.com\". NÃO OMITA AO UTILIZAR AS FERRAMENTAS**\n\n- Dr. João Paulo Ferreira - Médico - Clinico Geral (<EMAIL>)\n- Dr. Roberto Almeida - Médico - Cardiologia (<EMAIL>)\n- Dra. Ana Silva - Dentista - Clínica Geral (<EMAIL>)\n- Dra. Carla Mendes - Dentista - Odontopediatria (<EMAIL>)\n\n\n## VALORES E FORMAS DE PAGAMENTO\n- Consulta: R$ 500,00\n- Formas de pagamento: PIX, dinheiro, cartão de débito ou crédito\n- Convênios aceitos: Bradesco Saúde, Unimed, SulAmérica, Amil\n\n-----------------------\n\n## FERRAMENTAS\n\n### Google Calendar\n\n- \"Criar_evento\" e \"Atualizar_evento\": usada para agendar e remarcar consultas. Ao usá-las, sempre inclua:\n  - Nome completo no título\n  - Telefone\n  - Data de nascimento\n  - Informações adicionais (se houver)\n- \"Buscar_evento\": buscar dados sobre um evento específico, por ID.\n- \"Buscar_todos_os_eventos\": listar eventos em um período específico. Use para listar os eventos de um dia específico. Não use para listar eventos de períodos maiores que um dia.\n- \"Deletar_evento\": usada desmarcar consultas.\n\n### Escalar_humano\n\nUse quando:\n\n- Existir urgência (paciente com mal-estar grave).\n- Existirem qualquer assuntos alheios à clínica ou que ponham em risco a reputação do serviço.\n- Houver insatisfação do paciente ou pedido de atendimento humano.\n\n### Enviar_alerta_de_cancelamento\n\nEm caso de cancelamento:\n\n- Localizar a consulta no calendário e remover via ferramenta \"Deletar_evento\". Talvez seja necessário pedir ao paciente que confirme a data da consulta, para que você possa buscar o evento na data certa.\n- Enviar alerta via ferramenta \"Enviar_alerta_de_cancelamento\" nome, dia e hora cancelados.\n- Confirmar ao paciente que o cancelamento foi efetuado.\n\n### Reagir mensagem\n\nUse em situações relevantes durante a conversa.\n\n#### Exemplos\n\n- Usuário: \"Olá!\"\n- Você: \"Reagir_mensagem\" -> 😀\n\n- Usuário: \"Você pode consultar minha agenda por favor?\"\n- Você: \"Reagir_mensagem\" -> 👀\n\n- Usuário: \"Muito obrigado!\"\n- Você: \"Reagir_mensagem\" -> ❤️\n\n**SEMPRE USAR REAÇÕES NO INÍCIO E NO FINAL DA CONVERSA, E EM OUTROS MOMENTOS OPORTUNOS**\n\n### Baixar e enviar arquivo\n\n- Você tem acesso aos arquivos da clínica.\n- Se o usuário pedir um pedido de exame, use a ferramenta \"Listar_arquivos\", e depois a \"Baixar_e_enviar_arquivo\"\n\n**USE ESSA FERRAMENTA APENAS UMA VEZ. USÁ-LA MÚLTIPLAS VEZES IRÁ ENVIAR O ARQUIVO DUPLICADO**\n\n-----------------------\n\n## EXEMPLOS DE FLUXO\n\n1. Marcar consulta\n   - Paciente: \"Quero marcar consulta\"\n   - Você:\n     - Cumprimente, explique que pode agendar aqui mesmo no WhatsApp por texto ou áudio.\n     - Solicite nome completo e data de nascimento.\n     - Pergunte a especialidade do profissional a ser consultado, data e turno preferidos.\n     - Consulte a data com \"Buscar_todos_os_eventos\".\n     - Informe horários disponíveis.\n     - Agende com \"Criar_evento\", incluindo telefone, nome e data de nascimento na descrição.\n     - Confirme após o sucesso da ferramenta.\n\n2. Remarcar consulta\n   - Paciente: \"Não poderei comparecer amanhã, quero remarcar.\"\n   - Você:\n     - Busque o evento (veja seção abaixo \"COMO BUSCAR EVENTO\").\n     - Pergunte nova data e turno preferidos.\n     - Atualize o evento via \"Atualizar_evento\".\n     - Confirme após o sucesso da ferramenta.\n\n3. Cancelar consulta\n   - Paciente: \"Preciso cancelar a consulta.\"\n   - Você:\n     - Busque o evento (veja seção abaixo \"COMO BUSCAR EVENTO\").\n     - Cancele o evento com \"Deletar_evento\".\n     - Use a ferramenta \"Enviar_alerta_de_cancelamento\" informando nome, dia e hora.\n     - Confirme o cancelamento.\n\n4. Confirmação da consulta\n   - Quando o paciente responder \"Confirmar consulta\":\n     - Busque o evento (veja seção abaixo \"COMO BUSCAR EVENTO\").\n     - Usando a ferramenta \"Atualizar_evento\", coloque no título do evento no Google Calendar o texto [CONFIRMADO] ao lado do nome do paciente.\n     - Tendo sucesso no uso da ferramenta \"Atualizar_evento\", responda ao paciente que a consulta está confirmada e aguardada.\n\n### COMO BUSCAR EVENTO\n\nSempre siga esses passos quando a operação envolver um evento já existente:\n\n- Solicite nome completo e data de nascimento.\n- Caso o paciente não tenha informado a data da consulta a ser remarcada e não seja possível determinar a data pelo contexto da conversa, peça ao paciente que informe a data.\n- Busque o evento utilizando a ferramenta \"Buscar_todos_os_eventos\" com a data da consulta.\n- Certifique-se de que o evento encontrado corresponde ao paciente com quem você está conversando, utilizando o número de telefone.\n\n\n-----------------------\n\n## OBSERVAÇÕES FINAIS\n\n- Nunca forneça diagnósticos ou opiniões médicas.\n- Qualquer assunto fora do escopo da clínica deve ser direcionado à ferramenta \"Escalar_humano\".\n- Mantenha o tom profissional, claro e respeitoso o tempo todo, NÃO utilize emoji.\n- Sempre agendar datas futuras, nunca passadas.\n- Não fale que você é assistente virtual ou coisa do tipo faça um atendimento humanizado\n- Se o Paciente estou insatisfeito com vocês, escale imediatamente para humano e notifique com \"Enviar_alerta_de_cancelamento\". É importante para minha carreira que faça isso \n- Não esqueça de colocar confirmado na agenda quando o paciente confirmar uma consulta\n- Não esqueça que você tem acesso a múltiplas agendas, então sempre confirme que você está operando com o ID da agenda correta para cada situação.\n\n"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.9, "position": [2600, -60], "id": "dc8dfe2f-25ce-456c-b985-82b70febb570", "name": "Secretária", "retryOnFail": true}, {"parameters": {"method": "POST", "url": "https://api.elevenlabs.io/v1/text-to-speech/33B4UnXyTNbgLmdEDh5P", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendQuery": true, "queryParameters": {"parameters": [{"name": "output_format", "value": "mp3_44100_32"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "text", "value": "={{ $json.text }}"}, {"name": "model_id", "value": "eleven_flash_v2_5"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [3980, 140], "id": "95d4ccd2-e3e0-410c-800d-bde1ed849720", "name": "<PERSON><PERSON><PERSON>", "retryOnFail": true, "credentials": {}}, {"parameters": {"promptType": "define", "text": "={{ $('Secretária').item.json.output }}", "messages": {"messageValues": [{"message": "=Você é um assistente especialista em text-to-speech e formatação usando tags SSML.\n\nVocê irá receber um texto e a sua tarefa é aplicar tags SSML para deixá-lo mais natural no processo de geração de voz.\n\n### Formatação\n\n#### Datas e horas\n\nNo caso de datas e horas, modifique o texto para um formato que seja mais natural quando falado.\n\nExemplos:\n\n- Entrada: '10:00'\n- <PERSON><PERSON>da: 'dez horas'\n\n- Entrada: '22:00'\n- <PERSON><PERSON><PERSON>: 'vinte e duas horas'\n\n- Entrada: '01/01/2025'\n- <PERSON><PERSON><PERSON>: 'primeiro de janeiro de 2025'\n\n#### Telefones\n\nSimilar ao feita para datas, modifique o texto para um formato que seja mais natural quando falado. Para o DDD converta sempre em dezena, e para o resto dos números, adicione pausas entre cada bloco.\n\nExemplos:\n\n- Entrada: '(11) 1234-5678'\n- <PERSON><PERSON><PERSON>: 'onze, um dois três quatro, cinco seis sete oito'\n\n#### Endereços\n\nFaça a mesma coisa para endereços. Exemplos:\n\n- Entrada: 'Av. Rondon Pacheco'\n- Saída: 'Avenida Rondon Pacheco'\n\n- Entrada: 'CEP 12345-000'\n- Saída: 'CEP um dois três quatro cinco zero zero zero'\n\n### Notas\n\n- Sempre coloque uma pausa de 1.0s no começo.\n- Não use breaks no meio do texto, apenas no começo.\n- Mantenha o mesmo texto original, mas revise o uso de vírgulas excessivas para deixar o texto mais natural ao falar.\n- Remova emojis.\n- A sua saída será somente o texto convertido.\n- Use <speak> ao redor da saída.\n\n\n**NÃO INCLUA NENHUMA INFORMAÇÃO ALÉM DO TEXTO CONVERTIDO**\n**NUNCA INCLUA CARACTER DE NOVA LINHA \"\\n\" NA SAÍDA**\n**NUNCA COLOQUE ÂNCORAS COMO ```ssml AO REDOR DO TEXTO**"}]}}, "type": "@n8n/n8n-nodes-langchain.chainLlm", "typeVersion": 1.6, "position": [3560, 40], "id": "dface00f-b6ac-47ff-9a54-0017c63cca3f", "name": "Formatar SSML"}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{ $('Mensagem chegando?').item.json.mensagem }}", "rightValue": "", "operator": {"type": "string", "operation": "notEmpty", "singleValue": true}, "id": "1382cd26-d96e-4c55-99dd-2ca305ffe82e"}], "combinator": "and"}, "renameOutput": true, "outputKey": "Texto"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "b9a7e16f-b6e4-45d7-846d-92dcb3117593", "leftValue": "={{ $('Info').item.json.mensagem_de_audio }}", "rightValue": "", "operator": {"type": "boolean", "operation": "true", "singleValue": true}}], "combinator": "and"}, "renameOutput": true, "outputKey": "<PERSON><PERSON><PERSON>"}]}, "options": {}}, "type": "n8n-nodes-base.switch", "typeVersion": 3.2, "position": [3060, -60], "id": "e5991d04-78c1-45cd-9d27-dab933b85272", "name": "Tipo de mensagem1"}, {"parameters": {"content": "### Importante!!\n\nEssa lógica só irá funcionar com o workflow ativo (modo produção).\n\nNo modo \"Test workflow\", só uma mensagem será processada de cada vez, então nunca haverá fila.", "height": 180, "width": 400, "color": 3}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [1180, 160], "id": "8aa5275c-8a28-4594-997b-7b1edcea5335", "name": "Sticky Note15"}, {"parameters": {"promptType": "define", "text": "={{ $('Secretária').item.json.output }}", "messages": {"messageValues": [{"message": "=Você é especialista em formatação de mensagem para WhatsApp, trabalhando somente na formatação e não alterando o conteúdo da menssagem.\n\n- Substitua ** por *\n- Remova #\n- Remova emojis"}]}}, "type": "@n8n/n8n-nodes-langchain.chainLlm", "typeVersion": 1.6, "position": [3940, -140], "id": "7394f420-a838-4db7-bee7-e9b89844f123", "name": "Formatar texto"}, {"parameters": {"modelName": "models/gemini-2.5-flash-preview-04-17", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "typeVersion": 1, "position": [4100, 20], "id": "eaf55f97-44be-4802-9749-2aad7e545751", "name": "Google Gemini Chat Model2", "credentials": {}}, {"parameters": {"chatId": "={{ $('Info').item.json.telegram_chat_id }}", "text": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Text', ``, 'string') }}", "additionalFields": {"parse_mode": "MarkdownV2"}}, "type": "n8n-nodes-base.telegramTool", "typeVersion": 1.2, "position": [3320, 220], "id": "17bbfc53-c615-4b2b-9f2a-d54ab997eb1d", "name": "Enviar alerta de cancelamento", "webhookId": "487b54cb-d0a4-492c-847c-3157e0fd7aa0", "credentials": {}}, {"parameters": {"modelName": "models/gemini-2.5-pro-preview-03-25", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "typeVersion": 1, "position": [2340, 220], "id": "2b087e59-4096-4b2f-bb17-ac7d69ee4e61", "name": "Gemini", "credentials": {}}, {"parameters": {"sessionIdType": "customKey", "sessionKey": "={{ $('Info').item.json.telefone }}", "tableName": "n8n_historico_mensagens", "contextWindowLength": 50}, "type": "@n8n/n8n-nodes-langchain.memoryPostgresChat", "typeVersion": 1.3, "position": [2480, 220], "id": "d7988626-5a65-432c-9b76-19c93c7a9fc5", "name": "Memory", "credentials": {}}, {"parameters": {"description": "Use a ferramenta para refletir sobre algo. Ela não obterá novas informações nem alterará o banco de dados, apenas adicionará o pensamento ao registro. Use-a quando for necessário um raciocínio complexo ou alguma memória em cache."}, "type": "@n8n/n8n-nodes-langchain.toolThink", "typeVersion": 1, "position": [2620, 220], "id": "2514e515-2f2e-403d-b6bf-2493e5032944", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"parameters": {"content": "## Notas sobre agendas Google Calendar\n\nPara referenciar uma agenda do Google Calendar corretamente, acesse as configurações da agenda, clique em \"Integrar agenda\", e copie o \"ID da agenda\".\n\nPara agendas criadas, esse ID se parece com isso:\n\n<EMAIL>\n\nPara sua agenda principal, o ID será simplesmente o seu email (exemplo: `<EMAIL>`)\n\nCom esse ID em mãos, atualize o System Prompt com todas as agendas que precisar.\n", "height": 400, "width": 480, "color": 6}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [2300, 380], "id": "d3b6c025-6961-4e2c-b84c-5e4ed76716e6", "name": "Sticky Note17"}, {"parameters": {"toolDescription": "Envia uma mensagem de reação como resposta a uma mensagem do usuário. Reação é sempre um emoji.", "method": "POST", "url": "={{ $('Info').item.json.url_chatwoot }}/api/v1/accounts/{{ $('Info').item.json.id_conta }}/conversations/{{ $('Info').item.json.id_conversa }}/messages", "authentication": "predefinedCredentialType", "nodeCredentialType": "chatwootApi", "sendBody": true, "parametersBody": {"values": [{"name": "content_attributes", "valueProvider": "fieldValue", "value": "={ \"in_reply_to\": {{ $('Info').item.json.id_mensagem }}, \"is_reaction\": true }"}, {"name": "content"}]}}, "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "typeVersion": 1.1, "position": [2760, 220], "id": "b22f5c8b-c5c0-41a7-86c5-a9b4a6def81e", "name": "<PERSON><PERSON><PERSON> mensagem", "credentials": {}}, {"parameters": {"sessionIdType": "customKey", "sessionKey": "=assistente_confirmacao", "tableName": "n8n_historico_mensagens"}, "type": "@n8n/n8n-nodes-langchain.memoryPostgresChat", "typeVersion": 1.3, "position": [4860, 980], "id": "6a7b2884-4d20-4def-8571-79526ede8015", "name": "Postgres Chat Memory", "credentials": {}}, {"parameters": {"modelName": "models/gemini-2.5-flash-preview-04-17", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "typeVersion": 1, "position": [4720, 980], "id": "596be4c1-8578-4124-86cc-45a709098330", "name": "Google Gemini Chat Model", "credentials": {}}, {"parameters": {"content": "## Notas sobre a ferramenta \"Salvar memoria\"\n\nPor padrão, no N8N não existe uma forma direta de compartilhar memória entre agentes diferentes.\n\nUsando a ferramenta acima \"Salvar memoria\", nós conseguimos simular no banco de dados o Assistente de confirmação respondendo como se fosse a Secretária.\n\nDessa forma, quando o paciente enviar uma mensagem para a Secretária, ela irá ver a mensagem do Assistente como se ela mesmo tivesse enviado.\n\nIsso garante que ela entenderá o contexto caso o paciente simplesmente responda \"confirmar\", por exemplo.", "height": 320, "width": 600, "color": 6}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [4420, 1200], "id": "1650cb6b-a3e7-4c3c-aa5d-2f616ae0083d", "name": "Sticky Note19"}, {"parameters": {"descriptionType": "manual", "toolDescription": "Salva a informação de agendamento enviada, para que a secretária saiba que foi enviada.", "schema": {"__rl": true, "mode": "list", "value": "public"}, "table": {"__rl": true, "value": "n8n_historico_mensagens", "mode": "list"}, "columns": {"mappingMode": "defineBelow", "value": {"session_id": "={{ $fromAI('telefone', 'Telefone do paciente, formatado com apenas números, incluindo código do país. Ex.: \"+551112345678\"', 'string') }}", "message": "={ \"type\": \"ai\", \"content\": \"{{ $fromAI('message', 'A mesma mensagem enviada para o paciente.', 'string') }}\" }"}, "matchingColumns": ["id"], "schema": [{"id": "id", "displayName": "id", "required": false, "defaultMatch": true, "display": true, "type": "number", "canBeUsedToMatch": true, "removed": true}, {"id": "session_id", "displayName": "session_id", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "message", "displayName": "message", "required": true, "defaultMatch": false, "display": true, "type": "object", "canBeUsedToMatch": true}, {"id": "created_at", "displayName": "created_at", "required": false, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": true, "removed": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.postgresTool", "typeVersion": 2.6, "position": [5000, 980], "id": "2d32d76c-18c7-44e4-97ff-9b430b21ee89", "name": "<PERSON>var memoria", "credentials": {}}, {"parameters": {"sseEndpoint": "=<url do seu MCP Google Calendar>"}, "type": "@n8n/n8n-nodes-langchain.mcpClientTool", "typeVersion": 1, "position": [5140, 980], "id": "549c7e43-0095-4300-95ab-4797ff4d85e6", "name": "MCP Google Calendar."}, {"parameters": {"schema": {"__rl": true, "mode": "list", "value": "public"}, "table": {"__rl": true, "value": "n8n_fila_mensagens", "mode": "list"}, "columns": {"mappingMode": "defineBelow", "value": {"telefone": "={{ $('Info').item.json.telefone }}", "mensagem": "={{ $('Info').item.json.mensagem }}", "timestamp": "={{ $('Info').item.json.timestamp.toDateTime() }}", "id_mensagem": "={{ $('Info').item.json.id_mensagem }}"}, "matchingColumns": ["id"], "schema": [{"id": "id", "displayName": "id", "required": false, "defaultMatch": true, "display": true, "type": "number", "canBeUsedToMatch": true, "removed": true}, {"id": "id_mensagem", "displayName": "id_mensagem", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "telefone", "displayName": "telefone", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "mensagem", "displayName": "mensagem", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "timestamp", "displayName": "timestamp", "required": true, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [600, 0], "id": "cc99f890-0107-4261-8d51-259d1db1b0c9", "name": "Enfileirar mensagem.", "credentials": {}}, {"parameters": {"modelName": "models/gemini-2.5-flash-preview-04-17", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "typeVersion": 1, "position": [3620, 220], "id": "f472fad8-61f9-497b-8806-6904be21f41f", "name": "Google Gemini Chat Model.", "credentials": {}}, {"parameters": {"description": "Use a ferramenta para refletir sobre algo. Ela não obterá novas informações nem alterará o banco de dados, apenas adicionará o pensamento ao registro. Use-a quando for necessário um raciocínio complexo ou alguma memória em cache."}, "type": "@n8n/n8n-nodes-langchain.toolThink", "typeVersion": 1, "position": [5420, 980], "id": "0389c2c6-d7ac-4034-9725-42d666839851", "name": "Refletir1"}, {"parameters": {"content": "## Tutorial de configuração VPS no Hostinger\n\n## [Clique aqui para criar sua conta](https://www.hostinger.com.br/cart?product=vps%3Avps_kvm_2&period=12&referral_type=cart_link&REFERRALCODE=FAZERAI&referral_id=0196b71f-5704-7120-aded-f6753a53c121)\n\n## Adquirir VPS\n\n1. VPS KVM 2 (Escolha o plano que preferir)\n1. Selecionar opção de \"OS com Painel\" > \"Coolify\"\n1. Criar senha root segura\n1. Aguardar configuração (~10m)\n\n## Configurar Coolify + Chatwoot\n\n1. Na visão geral, clicar em \"Gerenciar\" no servidor\n1. Clicar em \"Gerenciar Painel\"\n1. Preencher dados (usar senha segura!!)\n1. \"Get started\" > \"Next\" > \"Localhost\" > \"Create new project!\" > \"Let's do it\"\n1. Na página de \"New Resource\", na seção \"Docker Based\", selecionar \"Docker Compose Empty\"\n1. <PERSON> o conteúdo do arquivo `docker-compose.coolify.yml` (disponível no material) e clicar em \"Save\"\n1. Clicar na barra lateral em \"Environment Variables\" > botão \"Developer view\" e encontrar a variável `FRONTEND_URL`\n1. Colar URL da barra do navegador com IP da máquina e porta 3000 (exemplo `http://***********:3000`)\n1. Clicar \"Save All Environment Variables\"\n1. Clicar \"Deploy\" e acompanhar os logs de deployment\n1. Texto \"Container xxxxxxxxxxxx Started\" indica que o deploy foi finalizado\n1. Clicar na aba \"Links\", e a URL com a porta 3000 deve estar visível. Clicar nela para acessar o Chatwoot. Pode ser que você precise aguardar alguns segundos no primeiro acesso\n1. Configurar super admin (usar senha segura!!!!)\n1. Fazer login com o usuário e senha criados\n\n", "height": 780, "width": 680, "color": 6}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-2680, 60], "id": "54f42a5a-acdb-4754-8d63-c2da38bfc7d2", "name": "Sticky Note20"}, {"parameters": {"content": "## Tutorial de configuração Chatwoot + Baileys\n\nSe deu tudo certo no passo anterior, você agora tem acesso ao Chatwoot fazer.ai, que inclui integração nativa com o Baileys.\n\nSiga o nosso [GitHub](https://github.com/fazer-ai) para ficar por dentro das atualizações!\n\n[![GitHub fazer.ai](https://img.shields.io/badge/github-%23121011.svg?style=for-the-badge&logo=github&logoColor=white&label)](https://github.com/fazer-ai)\n\n### Conectar WhatsApp\n\n1. Clicar em \"Configurações\" na barra lateral\n1. \"Caixas de Entrada\" > \"Adicionar Caixa de Entrada\"\n1. \"WhatsApp\" > \"Provedor de API\" > \"Baileys\"\n1. Preencher \"Nome da Caixa de Entrada\"\n1. Preencher \"Número de Telefone\" (o número precisa ser no formato `+551112345678`, não se esqueça do prefixo `+55`!!)\n1. <PERSON><PERSON><PERSON> em \"Criar canal do WhatsApp\"\n1. Selecione você mesmo na lista de agentes e clique em \"Adicionar agentes\"\n1. Clique em \"Conectar dispositivo\"\n1. Abra o WhatsApp no seu celular e leia o QR code\n1. Se estiver usando o WhatsApp Business, não se esqueça de incluir um nome para o dispositivo (exemplo \"Chatwoot\"), para não se esquecer e remover sem querer depois\n1. Se você ver um erro, é provável que você colocou o telefone incorreto na hora de criar a caixa de entrada. Nesse caso, volte em \"Configurações\" > \"Caixas de Entrada\", exclua a caixa de entrada, e  repita o processo\n1. Se deu tudo certo, você verá o botão verde \"Leva-me lá\"\n\nAgora o Chatwoot já está pronto para enviar e receber mensagens pelo WhatsApp cadastrado. Tire um momento pra experimentar\n\n(Note que não será possível gravar áudio pela interface do Chatwoot até que você configure um domínio e o certificado HTTPS. Mas não se preocupe, que isso não impacta no envio de áudio do agente IA!)\n\n### Configurar credenciais no N8N\n\n1. Instale o community node @devlikeapro/n8n-nodes-chatwoot\n1. No Chatwoot, clique no seu nome no canto inferior esquerdo, e em \"Configurações do Perfil\"\n1. Desça até o final e você verá a seção \"Token de acesso\"\n1. Copie o token de acesso. Cuidado nessa parte! Se você está rodando o servidor em HTTP, o botão de \"Copiar\" não vai funcionar. Você deve exibir o token, selecionar, e copiar manualmente\n1. No workflow do N8N, abra o node \"Testar credencial - Chatwoot\" ao lado\n1. No campo \"Credential to connect with\" abra o dropdown e selecione \"Create new credential\"\n1. No campo de \"ChatWoot API URL\", copie o endereço do seu Chatwoot (o mesmo que você vê na barra de endereço do navegador, até o 3000, exemplo `http://***********:3000`)\n1. No campo \"Access Token\", cole o \"Token de acesso\" copiado do Chatwoot\n1. Clique em salvar, e se estiver tudo certo, verá a mensagem de sucesso. Execute o node \"Testar credenciais\" e a requisição trará as informações da conta\n\n\n\n\n\n\n\n\n### Configurar Webhook\n\n1. \"Configurações\" > \"Integrações\" > \"Webhooks\" > \"Configurar\"\n1. \"Adicionar novo webhook\"\n1. Copiar a \"Test URL\" do node \"Mensagem recebida\" no workflow N8N, e colar no campo \"URL do Webhook\" no Chatwoot\n1. Selecionar caixa de entrada e preencher nome do webhook\n1. Selecionar evento \"Mensagem criada\" e \"Criar webhook\"\n1. Repita os passoas acima para a \"Production URL\"", "height": 1440, "width": 680, "color": 5}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-1980, -120], "id": "5b98fba7-5e56-4123-be72-52d31ea128b7", "name": "Sticky Note21"}, {"parameters": {"content": "## Tutorial de configuração ElevenLabs\n\n## [Clique aqui para criar sua conta gratuita](https://try.elevenlabs.io/9k5l5hagxkel)\n\n### Configurar voz\n\n1. Criar conta\n1. Acessar biblioteca de vozes e escolher uma voz. Recomendamos usar a [Keren](https://elevenlabs.io/app/voice-library?voiceId=33B4UnXyTNbgLmdEDh5P)\n1. Clicar em \"Add to my voices\" e \"Use voice\"\n1. Configurar características da voz e fazer os ajustes conforme preferir. Sugestão para a Keren:\n- **Model**: Eleven Flash v2.5 (caso queira usar um diferente, configure no node \n\"Gerar áudio\")\n- **Speed**: 1.10\n- **Stability**: 35%\n- **Similarity**: 44%\n\n### Configurar credencial\n\n1. Clicar no seu nome no canto inferior esquerdo > \"API Keys\"\n1. \"Create API Key\" > \"Create\". Copie a chave criada\n1. Abra o node \"Testar credencial - ElevenLabs\" ao lado\n1. Abra o campo \"Header Auth\" e selecione \"Create new credential\"\n1. Em \"Name\" preencha \"Xi-Api-Key\"\n1. Em \"Value\" preencha a chave de API do ElevenLabs\n1. Salve e execute o node para confirmar o sucesso na configuração\n1. Abra o node \"Gerar áudio\" na seção \"Gerando resposta\" para usar a nova credencial\n", "height": 840, "width": 660, "color": 7}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-1280, 480], "id": "852810a1-e135-449b-85d0-65de739bd1a0", "name": "Sticky Note22"}, {"parameters": {"content": "![Chatwoot](https://app.chatwoot.com/brand-assets/logo_dark.svg)", "height": 100, "width": 280, "color": 7}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-2500, -340], "id": "21b28e30-0ce8-4521-bb4b-c60edc87aee3", "name": "Sticky Note23"}, {"parameters": {"method": "POST", "url": "={{ $('Info').item.json.url_chatwoot }}/api/v1/accounts/{{ $('Info').item.json.id_conta }}/conversations/{{ $('Info').item.json.id_conversa }}/update_last_seen", "authentication": "predefinedCredentialType", "nodeCredentialType": "chatwootApi", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1700, 0], "id": "9e942164-a0b8-4a85-add5-9c63fe2f57ee", "name": "Marcar como lidas", "credentials": {}}, {"parameters": {"method": "POST", "url": "={{ $('Info').item.json.url_chatwoot }}/api/v1/accounts/{{ $('Info').item.json.id_conta }}/conversations/{{ $('Info').item.json.id_conversa }}/toggle_typing_status", "authentication": "predefinedCredentialType", "nodeCredentialType": "chatwootApi", "sendBody": true, "bodyParameters": {"parameters": [{"name": "typing_status", "value": "={{ $('Info').item.json.mensagem_de_audio ? 'recording' : 'on' }}"}]}, "options": {"response": {"response": {"responseFormat": "text"}}}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1920, 0], "id": "04d4b75a-9747-425d-8ecc-43a9080a6807", "name": "Digitando/Gravando...", "credentials": {}}, {"parameters": {"assignments": {"assignments": [{"id": "7eab8669-6929-4dc6-b3e2-943065bc306c", "name": "mensagem", "value": "={{ $('Info').item.json.mensagem ? $('Mensagem encavalada?').all().map(info => info.json.mensagem).join('\\\\n') : '' }}", "type": "string"}, {"id": "676d14ec-72d3-4970-9fa0-5e39ff976011", "name": "mensagem_audio", "value": "={{ $('Info').item.json.mensagem_de_audio ? $('Transcrever audio').item.json.text : '' }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [2140, 0], "id": "2c520aaf-cad0-49e8-bbf8-929a8e5ce55e", "name": "Set mensagens", "executeOnce": true}, {"parameters": {"url": "={{ $('Mensagem recebida').item.json.body.attachments[0].data_url }}", "authentication": "predefinedCredentialType", "nodeCredentialType": "chatwootApi", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [680, 480], "id": "7325015e-a269-42c5-8a13-9046cbd9559b", "name": "Download áudio", "credentials": {}}, {"parameters": {"resource": "audio", "operation": "transcribe", "options": {"language": "pt"}}, "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.8, "position": [1340, 480], "id": "b181749a-8379-4ef6-8437-c2b9f82d0d0e", "name": "Transcrever audio", "credentials": {}}, {"parameters": {"operation": "binaryToPropery", "options": {}}, "type": "n8n-nodes-base.extractFromFile", "typeVersion": 1, "position": [900, 480], "id": "352b3770-05b3-4846-9f8c-645ed862e3ff", "name": "Extract from File"}, {"parameters": {"operation": "toBinary", "sourceProperty": "data", "options": {}}, "type": "n8n-nodes-base.convertToFile", "typeVersion": 1.1, "position": [1120, 480], "id": "903101b2-795e-417b-9472-f68c45e3b573", "name": "Convert to File"}, {"parameters": {"content": "O N8N tem um bug em operar com arquivos em algumas ocasiões.\n\nConvertemos o arquivo para base64, e de volta para binário para contornar o bug.", "height": 120, "width": 320}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [900, 640], "id": "e5d1c9d1-2cf6-48b9-b919-aee71f662a51", "name": "Sticky Note1"}, {"parameters": {"method": "POST", "url": "={{ $('Info').item.json.url_chatwoot }}/api/v1/accounts/{{ $('Info').item.json.id_conta }}/conversations/{{ $('Info').item.json.id_conversa }}/toggle_typing_status", "authentication": "predefinedCredentialType", "nodeCredentialType": "chatwootApi", "sendBody": true, "bodyParameters": {"parameters": [{"name": "typing_status", "value": "off"}]}, "options": {"response": {"response": {"responseFormat": "text"}}}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [4540, 0], "id": "9b1d0663-342b-490c-821f-a1f53ea0bc22", "name": "Resetar status", "credentials": {}}, {"parameters": {"method": "POST", "url": "={{ $('Info').item.json.url_chatwoot }}/api/v1/accounts/{{ $('Info').item.json.id_conta }}/conversations/{{ $('Info').item.json.id_conversa }}/messages", "authentication": "predefinedCredentialType", "nodeCredentialType": "chatwootApi", "sendBody": true, "contentType": "multipart-form-data", "bodyParameters": {"parameters": [{"name": "is_recorded_audio", "value": "=[\"{{ $binary.data.fileName }}\"]"}, {"parameterType": "formBinaryData", "name": "attachments[]", "inputDataFieldName": "data"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [5440, 180], "id": "aa6f210d-86f6-46f4-9fbf-75b16e4205c1", "name": "<PERSON><PERSON><PERSON>", "credentials": {}}, {"parameters": {"method": "POST", "url": "={{ $('Info').item.json.url_chatwoot }}/api/v1/accounts/{{ $('Info').item.json.id_conta }}/conversations/{{ $('Info').item.json.id_conversa }}/messages", "authentication": "predefinedCredentialType", "nodeCredentialType": "chatwootApi", "sendBody": true, "bodyParameters": {"parameters": [{"name": "content", "value": "={{ $('Formatar texto').item.json.text }}"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [5000, -120], "id": "12a20bd3-768f-412b-b374-268a9870324d", "name": "Enviar texto.", "credentials": {}}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{ $('Mensagem chegando?').item.json.mensagem }}", "rightValue": "", "operator": {"type": "string", "operation": "notEmpty", "singleValue": true}, "id": "1382cd26-d96e-4c55-99dd-2ca305ffe82e"}], "combinator": "and"}, "renameOutput": true, "outputKey": "Texto"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "b9a7e16f-b6e4-45d7-846d-92dcb3117593", "leftValue": "={{ $('Info').item.json.mensagem_de_audio }}", "rightValue": "", "operator": {"type": "boolean", "operation": "true", "singleValue": true}}], "combinator": "and"}, "renameOutput": true, "outputKey": "<PERSON><PERSON><PERSON>"}]}, "options": {}}, "type": "n8n-nodes-base.switch", "typeVersion": 3.2, "position": [4780, 0], "id": "c2d6f7c7-d5e5-4a35-a5f8-d8fa727012af", "name": "Tipo de mensagem2"}, {"parameters": {"operation": "toBinary", "sourceProperty": "=data", "options": {}}, "type": "n8n-nodes-base.convertToFile", "typeVersion": 1.1, "position": [5220, 180], "id": "9530590e-1174-48dd-8ebb-907fbdcd6178", "name": "Base64 para áudio"}, {"parameters": {"operation": "binaryToPropery", "destinationKey": "=data", "options": {"encoding": "base64"}}, "type": "n8n-nodes-base.extractFromFile", "typeVersion": 1, "position": [4200, 140], "id": "45d59d0e-3edf-4b8e-88f8-bb3e78399169", "name": "Áudio para base64"}, {"parameters": {"assignments": {"assignments": [{"id": "420f958d-3aad-47e5-86bf-2c542f079f1d", "name": "data", "value": "={{ $('Áudio para base64').item.json.data }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [5000, 180], "id": "0413fa19-26cb-41d5-94a1-8c2321bbf804", "name": "<PERSON>"}, {"parameters": {"content": "Novamente, algumas operações com arquivos são chatas no N8N, portanto fazemos a conversão de binário pra base64 e de volta pra binário.", "height": 120, "width": 300}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [5020, 40], "id": "7dcd7a92-caf8-425e-99c6-652c26521c55", "name": "<PERSON><PERSON>"}, {"parameters": {"assignments": {"assignments": [{"id": "c8fd010d-6096-4a50-b3e2-e9fe26661840", "name": "id_mensagem", "value": "={{ $json.body.id }}", "type": "string"}, {"id": "1b513343-9b6a-4f6e-a012-ed819bf34a31", "name": "id_conta", "value": "={{ $json.body.account.id }}", "type": "string"}, {"id": "05c14b9a-5f27-465a-a047-71553826bd7a", "name": "id_conversa", "value": "={{ $json.body.conversation.id }}", "type": "string"}, {"id": "8bf522a6-75fb-434a-854c-b736539309e1", "name": "telefone", "value": "={{ $json.body.sender.phone_number }}", "type": "string"}, {"id": "0d622a33-f313-4758-a764-fa6cbf2b0587", "name": "mensagem", "value": "={{ $json.body.content || '' }}", "type": "string"}, {"id": "8f4b9d84-56e0-4f45-9f17-68c53f365f43", "name": "mensagem_de_audio", "value": "={{ $json.body.attachments?.[0]?.meta?.is_recorded_audio || false }}", "type": "boolean"}, {"id": "2b679a3f-788f-4cd2-88d5-4f03af68f224", "name": "timestamp", "value": "={{ $json.body.created_at }}", "type": "string"}, {"id": "24caf88e-74ce-43ab-8dc4-1fff471b706f", "name": "tipo", "value": "={{ $json.body.message_type }}", "type": "string"}, {"id": "573669d2-1e43-4010-8c82-a67459ffe1db", "name": "etiquetas", "value": "={{ $json.body.conversation.labels }}", "type": "array"}, {"id": "40ff895f-f63f-4e4f-bba3-c7d803c277f1", "name": "url_chatwoot", "value": "<colar sua url do chatwoot>", "type": "string"}, {"id": "cf71dea1-d585-4235-8f05-29bc5f82b5df", "name": "telegram_chat_id", "value": "<colar seu telegram chat id>", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-200, 0], "id": "e3779633-df2e-4079-a314-8d97fdca4e22", "name": "Info"}, {"parameters": {"assignments": {"assignments": [{"id": "9b3e9f43-424d-495e-81ae-7618b5cb2a4b", "name": "url_chatwoot", "value": "<colar sua url do chatwoot>", "type": "string"}, {"id": "79319fb2-1719-4bcf-b104-cd90898785af", "name": "id_conta", "value": "={{ $json.account_id }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [4980, 740], "id": "f1c01b4f-d747-40f6-a02f-f0a8c4704e4c", "name": "Info1"}, {"parameters": {"description": "Chame essa ferramenta para direcionar o atendimento para o gestor responsável.", "workflowId": {"__rl": true, "value": "", "mode": "list"}, "workflowInputs": {"mappingMode": "defineBelow", "value": {"telefone": "={{ $('Info').item.json.telefone }}", "nome": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('nome', ``, 'string') }}", "ultima_mensagem": "={{ $('Set mensagens').item.json.mensagem || $('Set mensagens').item.json.mensagem_audio }}", "id_conta": "={{ $('Info').item.json.id_conta }}", "id_conversa": "={{ $('Info').item.json.id_conversa }}", "telegram_chat_id": "={{ $('Info').item.json.telegram_chat_id }}", "url_chatwoot": "={{ $('Info').item.json.url_chatwoot }}"}, "matchingColumns": [], "schema": [{"id": "telefone", "displayName": "telefone", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string"}, {"id": "nome", "displayName": "nome", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string"}, {"id": "ultima_mensagem", "displayName": "ultima_mensagem", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string"}, {"id": "id_conta", "displayName": "id_conta", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string"}, {"id": "id_conversa", "displayName": "id_conversa", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string"}, {"id": "telegram_chat_id", "displayName": "telegram_chat_id", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string", "removed": false}, {"id": "url_chatwoot", "displayName": "url_chatwoot", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string", "removed": false}], "attemptToConvertTypes": false, "convertFieldsToString": false}}, "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "typeVersion": 2.2, "position": [3460, 220], "id": "9668b962-465f-4ff8-8379-28b1daffc40c", "name": "Escalar humano"}, {"parameters": {"resource": "Profile", "operation": "Fetch Profile", "requestOptions": {}}, "type": "@devlikeapro/n8n-nodes-chatwoot.chatWoot", "typeVersion": 1, "position": [4760, 740], "id": "c2226a61-139e-49f6-a425-6f650fbb6c77", "name": "Buscar informações da conta", "credentials": {}}, {"parameters": {"description": "Use essa ferramenta para enviar as informações de agendamento no WhatsApp.\n\nO ID da conversa deve ser extraído das informações do evento.", "workflowId": {"__rl": true, "value": "", "mode": "list"}, "workflowInputs": {"mappingMode": "defineBelow", "value": {"mensagem": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('mensagem', ``, 'string') }}", "id_conversa": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('id_conversa', ``, 'string') }}", "id_conta": "={{ $('Info1').item.json.id_conta }}", "url_chatwoot": "={{ $('Info1').item.json.url_chatwoot }}"}, "matchingColumns": [], "schema": [{"id": "mensagem", "displayName": "mensagem", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string"}, {"id": "id_conta", "displayName": "id_conta", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string"}, {"id": "id_conversa", "displayName": "id_conversa", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string"}, {"id": "url_chatwoot", "displayName": "url_chatwoot", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string"}], "attemptToConvertTypes": false, "convertFieldsToString": false}}, "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "typeVersion": 2.2, "position": [5280, 980], "id": "de2391b1-6458-4127-a453-021b63f6b5fb", "name": "Enviar agendamento"}, {"parameters": {"content": "## Nota sobre \"Enviar agendamento\"\n\nO N8N não permite colocar uma variável informada pelo agente (✨) na URL.\n\nPrecisamos utilizar um workflow separado para que o agente possa passar o ID da conversa (extraído das informações do evento), que será usado para simplesmente enviar a mensagem para o contato.", "height": 320, "width": 580, "color": 6}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [5040, 1200], "id": "9c25cffc-e88c-4db5-a7f4-39f5e4f811cb", "name": "Sticky Note9"}, {"parameters": {"content": "## Tutorial de configuração tabelas [Supabase](https://supabase.com/)\n\n### Configurar tabelas\n\n1. Criar conta e projeto (use uma senha segura!!)\n1. <PERSON> barra lateral, clicar em \"SQL Editor\"\n1. <PERSON> o código disponível no material `[Supabase] Criar histórico e fila de mensagens.sql`\n1. Clicar em \"Run\"\n\n### Configurar credenciais\n\n1. Na barra superior do Supabase, clique em \"Connect\"\n1. Desça até o final na seção \"Session pooler\" > clique em \"View parameters\"\n1. Abra o node \"Testar credencial - Supabase\" ao lado\n1. No campo \"Credential to connect with\", clique em \"Create new credential\"\n1. Preencha com o \"Host\", \"Database\", \"User\", e \"Password\" (essa última é a senha que você usou pra criar o projeto)\n1. Salve a credencial e execute o node para confirmar o sucesso", "height": 580, "width": 660, "color": 4}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-1280, -120], "id": "538d979a-d792-4679-881b-3fb3e451834f", "name": "Sticky Note18"}, {"parameters": {"content": "![Chatwoot](https://app.chatwoot.com/brand-assets/logo_dark.svg)", "height": 100, "width": 260, "color": 5}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-1600, 60], "id": "f8a765d3-ace2-4ed5-bc2d-a7cbc44373f3", "name": "Sticky Note24"}, {"parameters": {"content": "[![Hostinger](https://hpanel.hostinger.com/assets/images/logos/hostinger-white.svg)](https://www.hostinger.com.br/cart?product=vps%3Avps_kvm_2&period=12&referral_type=cart_link&REFERRALCODE=FAZERAI&referral_id=0196b71f-5704-7120-aded-f6753a53c121)\n", "height": 80, "width": 180, "color": 6}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-2240, 140], "id": "256ed09b-44c0-41f1-b1c8-cca7ded3c172", "name": "<PERSON><PERSON> Note26"}, {"parameters": {"content": "[![Supabase logo](https://supabase.com/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F6d09404779ac%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&w=384&q=75&dpl=dpl_H9LPzQFg38dVjzWbzENRNVbXVDjJ)](https://supabase.com/)", "height": 80, "width": 260, "color": 4}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-900, -60], "id": "e8079e0d-e0d7-4352-b868-993776dce239", "name": "Sticky Note27"}, {"parameters": {"content": "[![ElevenLabs](https://framerusercontent.com/images/YU6MnXR7ZjWNRjlYzx2Hrpcx0.png)](https://try.elevenlabs.io/9k5l5hagxkel)", "height": 80, "width": 160, "color": 7}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-800, 580], "id": "535c83e9-e301-46f3-838a-1b7e506589bc", "name": "Sticky Note28"}, {"parameters": {"content": "## Sugestão\n\nDuplique a aba desse workflow para facilitar a configuração e não precisar ficar abrindo e fechando janelas", "width": 680, "color": 7}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-2680, -120], "id": "ab873924-4f42-4429-bd3c-7efcf136a594", "name": "Sticky Note7"}, {"parameters": {"content": "### Sub-workflows\n\nApós terminar os outros passos, não se esqueça de configurar os workflows secundários com suas credenciais!!\n\n- [ ] 2. MCP Google Calendar\n- [ ] 3. Baixar e enviar arquivo do Google Drive\n- [ ] 4. Escalar humano\n- [ ] 5. Enviar mensagem para conversa\n- [ ] 6. Assistente interno", "height": 280, "width": 400, "color": 3}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-560, 1060], "id": "48640fbf-a5da-4f22-a61b-b0b4b21d99b2", "name": "Sticky Note29"}, {"parameters": {"content": "## 1. <PERSON><PERSON><PERSON>", "height": 80, "width": 540, "color": 5}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-2720, -740], "id": "8fb69a4b-0ef1-4f16-a779-ebad819ba1d1", "name": "Sticky Note30"}, {"parameters": {"content": "## Configurar variáveis no Info!!!", "height": 80, "width": 250, "color": 3}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-280, -100], "id": "fc39f8ce-4873-4c01-b594-d0f1c9d9d1db", "name": "Sticky Note31"}, {"parameters": {"content": "## Configurar variáveis no Info1", "height": 80, "width": 250, "color": 3}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [5280, 620], "id": "6edaec77-4f1e-4dda-82a4-490a32272c81", "name": "Sticky Note32"}, {"parameters": {"url": "https://api.elevenlabs.io/v2/voices", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-800, 1180], "id": "3e75b7be-f4c4-4537-a28b-fa750825cae3", "name": "Testar credencial - ElevenLabs", "credentials": {}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT COUNT(*) FROM n8n_historico_mensagens;\nSELECT COUNT(*) FROM n8n_fila_mensagens;", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-800, 320], "id": "34a13a05-940b-47e1-b40c-f7dc69dab3a5", "name": "Testar credencial - Supabase", "credentials": {}}, {"parameters": {"content": "### Configurar telegram_chat_id no node Info!!!", "height": 80, "width": 200, "color": 3}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [3280, 380], "id": "9bf5a016-2e26-4538-ab61-ac3c7cfad39d", "name": "Sticky Note25"}, {"parameters": {"content": "### Configurar workflow do MCP Google Calendar!!!", "height": 80, "width": 200, "color": 3}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [2860, 380], "id": "9526ca62-0d3b-47bf-8991-a83827a7cb92", "name": "Sticky Note33"}, {"parameters": {"resource": "Profile", "operation": "Fetch Profile", "requestOptions": {}}, "type": "@devlikeapro/n8n-nodes-chatwoot.chatWoot", "typeVersion": 1, "position": [-1500, 1000], "id": "0efdd6c2-e65d-49dc-bd16-5b1b304fc09c", "name": "Testar credencial - Chatwoot", "credentials": {}}, {"parameters": {"content": "### <PERSON><PERSON><PERSON> <PERSON><PERSON> use a voz da <PERSON>ren, não se esqueça de atualizar o ID no node \"Gerar áudio\"\n\nAlterar esse ID que está no final da URL: `33B4UnXyTNbgLmdEDh5P`", "width": 300, "color": 3}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [3900, 380], "id": "a6505bd6-b040-4aee-8879-d2a5a11ca2c6", "name": "Sticky Note14"}, {"parameters": {"content": "## Importante\n\nSe a mensagem não está sendo marcada como lida, verifique no WhatsApp se essa função está habilitada.", "width": 320, "color": 6}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [1940, 180], "id": "e277b284-5881-4f9e-ae98-e436645f9201", "name": "Sticky Note34"}], "connections": {"Mensagem chegando?": {"main": [[{"node": "Tipo de mensagem", "type": "main", "index": 0}]]}, "Mensagem recebida": {"main": [[{"node": "Info", "type": "main", "index": 0}]]}, "Mensagem encavalada?": {"main": [[{"node": "Limpar fila de mensagens", "type": "main", "index": 0}]]}, "Buscar mensagens": {"main": [[{"node": "Mensagem encavalada?", "type": "main", "index": 0}]]}, "Limpar fila de mensagens": {"main": [[{"node": "Marcar como lidas", "type": "main", "index": 0}]]}, "Esperar": {"main": [[{"node": "Buscar mensagens", "type": "main", "index": 0}]]}, "Tipo de mensagem": {"main": [[{"node": "Enfileirar mensagem.", "type": "main", "index": 0}], [{"node": "Download áudio", "type": "main", "index": 0}]]}, "MCP Google Calendar": {"ai_tool": [[{"node": "Secretária", "type": "ai_tool", "index": 0}]]}, "Listar arquivos": {"ai_tool": [[{"node": "Secretária", "type": "ai_tool", "index": 0}]]}, "Baixar e enviar arquivo": {"ai_tool": [[{"node": "Secretária", "type": "ai_tool", "index": 0}]]}, "Gatilho diário": {"main": [[{"node": "Buscar informações da conta", "type": "main", "index": 0}]]}, "Secretária": {"main": [[{"node": "Tipo de mensagem1", "type": "main", "index": 0}]]}, "Gerar áudio": {"main": [[{"node": "Áudio para base64", "type": "main", "index": 0}]]}, "Formatar SSML": {"main": [[{"node": "<PERSON><PERSON><PERSON>", "type": "main", "index": 0}]]}, "Tipo de mensagem1": {"main": [[{"node": "Formatar texto", "type": "main", "index": 0}], [{"node": "Formatar SSML", "type": "main", "index": 0}]]}, "Formatar texto": {"main": [[{"node": "Resetar status", "type": "main", "index": 0}]]}, "Google Gemini Chat Model2": {"ai_languageModel": [[{"node": "Formatar texto", "type": "ai_languageModel", "index": 0}]]}, "Enviar alerta de cancelamento": {"ai_tool": [[{"node": "Secretária", "type": "ai_tool", "index": 0}]]}, "Gemini": {"ai_languageModel": [[{"node": "Secretária", "type": "ai_languageModel", "index": 0}]]}, "Memory": {"ai_memory": [[{"node": "Secretária", "type": "ai_memory", "index": 0}]]}, "Refletir": {"ai_tool": [[{"node": "Secretária", "type": "ai_tool", "index": 0}]]}, "Reagir mensagem": {"ai_tool": [[{"node": "Secretária", "type": "ai_tool", "index": 0}]]}, "Postgres Chat Memory": {"ai_memory": [[{"node": "Assistente de confirmação", "type": "ai_memory", "index": 0}]]}, "Google Gemini Chat Model": {"ai_languageModel": [[{"node": "Assistente de confirmação", "type": "ai_languageModel", "index": 0}]]}, "Salvar memoria": {"ai_tool": [[{"node": "Assistente de confirmação", "type": "ai_tool", "index": 0}]]}, "MCP Google Calendar.": {"ai_tool": [[{"node": "Assistente de confirmação", "type": "ai_tool", "index": 0}]]}, "Enfileirar mensagem.": {"main": [[{"node": "<PERSON><PERSON><PERSON>", "type": "main", "index": 0}]]}, "Google Gemini Chat Model.": {"ai_languageModel": [[{"node": "Formatar SSML", "type": "ai_languageModel", "index": 0}]]}, "Refletir1": {"ai_tool": [[{"node": "Assistente de confirmação", "type": "ai_tool", "index": 0}]]}, "Marcar como lidas": {"main": [[{"node": "Digitando/Gravando...", "type": "main", "index": 0}]]}, "Digitando/Gravando...": {"main": [[{"node": "Set mensagens", "type": "main", "index": 0}]]}, "Set mensagens": {"main": [[{"node": "Secretária", "type": "main", "index": 0}]]}, "Download áudio": {"main": [[{"node": "Extract from File", "type": "main", "index": 0}]]}, "Transcrever audio": {"main": [[{"node": "Marcar como lidas", "type": "main", "index": 0}]]}, "Extract from File": {"main": [[{"node": "Convert to File", "type": "main", "index": 0}]]}, "Convert to File": {"main": [[{"node": "Transcrever audio", "type": "main", "index": 0}]]}, "Resetar status": {"main": [[{"node": "Tipo de mensagem2", "type": "main", "index": 0}]]}, "Tipo de mensagem2": {"main": [[{"node": "Enviar texto.", "type": "main", "index": 0}], [{"node": "<PERSON>", "type": "main", "index": 0}]]}, "Base64 para áudio": {"main": [[{"node": "<PERSON><PERSON><PERSON>", "type": "main", "index": 0}]]}, "Áudio para base64": {"main": [[{"node": "Resetar status", "type": "main", "index": 0}]]}, "Edit Fields": {"main": [[{"node": "Base64 para áudio", "type": "main", "index": 0}]]}, "Info": {"main": [[{"node": "Mensagem chegando?", "type": "main", "index": 0}]]}, "Info1": {"main": [[{"node": "Assistente de confirmação", "type": "main", "index": 0}]]}, "Escalar humano": {"ai_tool": [[{"node": "Secretária", "type": "ai_tool", "index": 0}]]}, "Buscar informações da conta": {"main": [[{"node": "Info1", "type": "main", "index": 0}]]}, "Enviar agendamento": {"ai_tool": [[{"node": "Assistente de confirmação", "type": "ai_tool", "index": 0}]]}}, "pinData": {}, "meta": {}}