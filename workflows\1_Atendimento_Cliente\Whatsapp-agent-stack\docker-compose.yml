# docker-compose.yml
version: '3.8'

networks:
  agent-network:
    driver: bridge

volumes:
  postgres_data:
  redis_data:
  n8n_data:
  rabbitmq_data:
  chatwoot_data:
  metabase_data:
  prometheus_data:
  grafana_data:
  traefik_acme:

services:
  traefik:
    image: traefik:v2.10
    container_name: traefik
    command:
      - "--api.insecure=true"
      - "--providers.docker=true"
      - "--providers.docker.exposedbydefault=false"
      - "--entrypoints.web.address=:80"
      - "--entrypoints.websecure.address=:443"
      - "--certificatesresolvers.myresolver.acme.httpchallenge=true"
      - "--certificatesresolvers.myresolver.acme.httpchallenge.entrypoint=web"
      - "--certificatesresolvers.myresolver.acme.email=${ACME_EMAIL}"
      - "--certificatesresolvers.myresolver.acme.storage=/letsencrypt/acme.json"
    ports:
      - "80:80"
      - "443:443"
      - "8080:8080" # Traefik Dashboard
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - traefik_acme:/letsencrypt
    networks:
      - agent-network
    restart: unless-stopped

  postgres:
    image: postgres:15-alpine
    container_name: postgres
    environment:
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_DB: ${POSTGRES_DB}
      TZ: ${TZ}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./sql/:/docker-entrypoint-initdb.d/
    networks:
      - agent-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER} -d ${POSTGRES_DB}"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    container_name: redis
    volumes:
      - redis_data:/data
    networks:
      - agent-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped

  rabbitmq:
    image: rabbitmq:3.11-management-alpine
    container_name: rabbitmq
    environment:
      RABBITMQ_DEFAULT_USER: ${RABBITMQ_DEFAULT_USER}
      RABBITMQ_DEFAULT_PASS: ${RABBITMQ_DEFAULT_PASS}
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    networks:
      - agent-network
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.rabbitmq.rule=Host(`rabbitmq.${DOMAIN}`)"
      - "traefik.http.routers.rabbitmq.entrypoints=websecure"
      - "traefik.http.routers.rabbitmq.tls.certresolver=myresolver"
      - "traefik.http.services.rabbitmq.loadbalancer.server.port=15672"
    restart: unless-stopped

  evolution_api:
    image: atendai/evolution-api:latest
    container_name: evolution_api
    environment:
      - DATABASE_CONNECTION_URI=postgresql://${POSTGRES_USER}:${POSTGRES_PASSWORD}@postgres:5432/${POSTGRES_DB}
      - REDIS_URI=redis://redis:6379
      - API_KEY=${EVOLUTION_API_KEY}
      - GLOBAL_API_KEY=${EVOLUTION_GLOBAL_API_KEY}
      - CORS_ORIGIN=* # Para desenvolvimento. Em produção, restrinja para seus domínios.
    depends_on:
      postgres: { condition: service_healthy }
      redis: { condition: service_healthy }
    networks:
      - agent-network
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.evolution.rule=Host(`evolution.${DOMAIN}`)"
      - "traefik.http.routers.evolution.entrypoints=websecure"
      - "traefik.http.routers.evolution.tls.certresolver=myresolver"
      - "traefik.http.services.evolution.loadbalancer.server.port=8080"
    restart: unless-stopped

  n8n:
    image: n8nio/n8n:latest
    container_name: n8n
    environment:
      - N8N_HOST=${DOMAIN}
      - N8N_PORT=5678
      - N8N_PROTOCOL=https
      - NODE_ENV=production
      - WEBHOOK_URL=${N8N_WEBHOOK_URL}
      - EXECUTIONS_DATA_PRUNE=true # Auto-limpa dados de execuções
      - EXECUTIONS_DATA_MAX_AGE=168 # 7 dias
      - DB_TYPE=postgresdb
      - DB_POSTGRESDB_HOST=postgres
      - DB_POSTGRESDB_PORT=${POSTGRES_PORT}
      - DB_POSTGRESDB_DATABASE=${POSTGRES_DB}
      - DB_POSTGRESDB_USER=${POSTGRES_USER}
      - DB_POSTGRESDB_PASSWORD=${POSTGRES_PASSWORD}
      - QUEUE_BULL_REDIS_HOST=redis
    volumes:
      - n8n_data:/home/<USER>/.n8n
    depends_on:
      - postgres
      - redis
    networks:
      - agent-network
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.n8n.rule=Host(`n8n.${DOMAIN}`)"
      - "traefik.http.routers.n8n.entrypoints=websecure"
      - "traefik.http.routers.n8n.tls.certresolver=myresolver"
      - "traefik.http.services.n8n.loadbalancer.server.port=5678"
      - "prometheus.io/scrape=true"
      - "prometheus.io/path=/metrics"
      - "prometheus.io/port=5678"
    restart: unless-stopped

  chatwoot:
    image: chatwoot/chatwoot:v2.17
    container_name: chatwoot
    environment:
      - FRONTEND_URL=${CHATWOOT_FRONTEND_URL}
      - RAILS_ENV=production
      - SECRET_KEY_BASE=${CHATWOOT_SECRET_KEY_BASE}
      - POSTGRES_HOST=postgres
      - POSTGRES_USERNAME=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${CHATWOOT_DB_PASSWORD}
      - POSTGRES_DATABASE=${POSTGRES_DB}
      - REDIS_URL=redis://redis:6379
    volumes:
      - chatwoot_data:/app/storage
    depends_on:
      - postgres
      - redis
    networks:
      - agent-network
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.chatwoot.rule=Host(`chatwoot.${DOMAIN}`)"
      - "traefik.http.routers.chatwoot.entrypoints=websecure"
      - "traefik.http.routers.chatwoot.tls.certresolver=myresolver"
      - "traefik.http.services.chatwoot.loadbalancer.server.port=3000"
    restart: unless-stopped

  metabase:
    image: metabase/metabase:latest
    container_name: metabase
    environment:
      - MB_DB_TYPE=postgres
      - MB_DB_DBNAME=${POSTGRES_DB}
      - MB_DB_PORT=${POSTGRES_PORT}
      - MB_DB_USER=${POSTGRES_USER}
      - MB_DB_PASS=${POSTGRES_PASSWORD}
      - MB_DB_HOST=postgres
      - MB_JETTY_PORT=3000
    volumes:
      - metabase_data:/metabase-data
    depends_on:
      - postgres
    networks:
      - agent-network
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.metabase.rule=Host(`metabase.${DOMAIN}`)"
      - "traefik.http.routers.metabase.entrypoints=websecure"
      - "traefik.http.routers.metabase.tls.certresolver=myresolver"
      - "traefik.http.services.metabase.loadbalancer.server.port=3000"
    restart: unless-stopped

  ai_processor:
    build:
      context: ./services/ai_processor
    container_name: ai_processor
    environment:
      - DATABASE_URL=postgresql://${POSTGRES_USER}:${POSTGRES_PASSWORD}@postgres:${POSTGRES_PORT}/${POSTGRES_DB}
      - RABBITMQ_URL=amqp://${RABBITMQ_DEFAULT_USER}:${RABBITMQ_DEFAULT_PASS}@rabbitmq:5672/%2F
      - OPENAI_API_KEY=${OPENAI_API_KEY}
    depends_on:
      - postgres
      - rabbitmq
    networks:
      - agent-network
    restart: unless-stopped
    # Descomente a linha abaixo para usar GPU, se disponível no host
    # deploy:
    #   resources:
    #     reservations:
    #       devices:
    #         - driver: nvidia
    #           count: 1
    #           capabilities: [gpu]

  prometheus:
    image: prom/prometheus:latest
    container_name: prometheus
    volumes:
      - ./config/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
    depends_on:
      - n8n
      - traefik
    networks:
      - agent-network
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.prometheus.rule=Host(`prometheus.${DOMAIN}`)"
      - "traefik.http.routers.prometheus.entrypoints=websecure"
      - "traefik.http.routers.prometheus.tls.certresolver=myresolver"
      - "traefik.http.services.prometheus.loadbalancer.server.port=9090"
    restart: unless-stopped

  grafana:
    image: grafana/grafana:latest
    container_name: grafana
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=AdminGrafanaPass123! # Troque esta senha
    volumes:
      - grafana_data:/var/lib/grafana
    depends_on:
      - prometheus
    networks:
      - agent-network
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.grafana.rule=Host(`grafana.${DOMAIN}`)"
      - "traefik.http.routers.grafana.entrypoints=websecure"
      - "traefik.http.routers.grafana.tls.certresolver=myresolver"
      - "traefik.http.services.grafana.loadbalancer.server.port=3000"
    restart: unless-stopped