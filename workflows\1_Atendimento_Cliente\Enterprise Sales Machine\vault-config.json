{"vault_info": {"version": "1.0", "description": "Credential Vault - Sistema Centralizador de Credenciais", "last_updated": "2024-01-20", "operator_version": "v1.0", "environment": "production"}, "deployment_order": ["infrastructure", "security", "ai_services", "communication", "social_media", "ecommerce", "productivity", "data_enrichment"], "services": {"infrastructure": {"postgresql": {"service_type": "database", "priority": 10, "auto_configure": true, "credentials": {"host": "postgres", "port": 5432, "database": "agent_db", "username": "agent_user", "password": "${POSTGRES_PASSWORD}", "ssl_mode": "prefer"}, "n8n_setup": {"credential_type": "postgres", "credential_name": "postgres-main", "test_query": "SELECT 1"}, "docker_service": "postgres", "health_check": {"endpoint": null, "method": "connection_test"}}, "redis": {"service_type": "database", "priority": 15, "auto_configure": true, "credentials": {"host": "redis", "port": 6379, "password": "${REDIS_PASSWORD}", "database": 0}, "n8n_setup": {"credential_type": "redis", "credential_name": "redis-main"}, "docker_service": "redis", "health_check": {"endpoint": null, "method": "ping"}}}, "security": {"aes_encryption": {"service_type": "security", "priority": 5, "auto_configure": true, "credentials": {"key": "${AES_ENCRYPTION_KEY}", "algorithm": "aes-256-cbc", "iv_length": 16}, "n8n_setup": {"credential_type": "genericCredential", "credential_name": "DefaultAESKey", "fields": {"key": "${AES_ENCRYPTION_KEY}"}}, "dependencies": [], "health_check": {"method": "encryption_test"}}}, "ai_services": {"openai": {"service_type": "api", "priority": 20, "auto_configure": true, "credentials": {"api_key": "${OPENAI_API_KEY}", "organization": "${OPENAI_ORG_ID}", "model_preferences": {"default": "gpt-4o-mini", "premium": "gpt-4o", "embedding": "text-embedding-3-small"}}, "n8n_setup": {"credential_type": "openAiApi", "credential_name": "openai-main"}, "dependencies": ["aes_encryption"], "health_check": {"endpoint": "https://api.openai.com/v1/models", "method": "GET", "headers": {"Authorization": "Bearer ${OPENAI_API_KEY}"}}}, "openrouter": {"service_type": "api", "priority": 25, "auto_configure": true, "credentials": {"api_key": "${OPENROUTER_API_KEY}", "base_url": "https://openrouter.ai/api/v1", "site_url": "${DOMAIN}", "app_name": "Agent System Multi-Identity"}, "n8n_setup": {"credential_type": "httpHeaderAuth", "credential_name": "openrouter-api", "fields": {"name": "Authorization", "value": "Bearer ${OPENROUTER_API_KEY}"}}, "dependencies": [], "health_check": {"endpoint": "https://openrouter.ai/api/v1/models", "method": "GET"}}}, "communication": {"evolution_api": {"service_type": "api", "priority": 30, "auto_configure": true, "credentials": {"api_key": "${EVOLUTION_API_KEY}", "global_api_key": "${EVOLUTION_GLOBAL_API_KEY}", "base_url": "http://evolution_api:8080", "webhook_url": "https://n8n.${DOMAIN}/webhook/whatsapp"}, "n8n_setup": {"credential_type": "httpHeaderAuth", "credential_name": "evolution-api-main", "fields": {"name": "Authorization", "value": "Bearer ${EVOLUTION_API_KEY}"}}, "dependencies": ["postgresql"], "health_check": {"endpoint": "/manager/findInstances", "method": "GET", "base_url": "http://evolution_api:8080"}, "docker_service": "evolution_api"}, "slack_webhook": {"service_type": "webhook", "priority": 35, "auto_configure": true, "credentials": {"webhook_url": "${SLACK_WEBHOOK_URL}", "channel": "#agent-alerts", "username": "Agent System", "icon_emoji": ":robot_face:"}, "n8n_setup": {"credential_type": "httpHeaderAuth", "credential_name": "slack-webhook", "fields": {"url": "${SLACK_WEBHOOK_URL}"}}, "dependencies": [], "health_check": {"method": "webhook_test"}}}, "social_media": {"buffer_api": {"service_type": "api", "priority": 40, "auto_configure": true, "credentials": {"access_token": "${BUFFER_ACCESS_TOKEN}", "client_id": "${BUFFER_CLIENT_ID}", "client_secret": "${BUFFER_CLIENT_SECRET}"}, "n8n_setup": {"credential_type": "httpHeaderAuth", "credential_name": "buffer-api-main", "fields": {"name": "Authorization", "value": "Bearer ${BUFFER_ACCESS_TOKEN}"}}, "dependencies": [], "health_check": {"endpoint": "https://api.bufferapp.com/1/user.json", "method": "GET"}}, "twitter_api": {"service_type": "oauth2", "priority": 45, "auto_configure": true, "credentials": {"api_key": "${TWITTER_API_KEY}", "api_secret": "${TWITTER_API_SECRET}", "bearer_token": "${TWITTER_BEARER_TOKEN}", "access_token": "${TWITTER_ACCESS_TOKEN}", "access_token_secret": "${TWITTER_ACCESS_TOKEN_SECRET}"}, "n8n_setup": {"credential_type": "twitterOAuth2Api", "credential_name": "twitter-api-v2"}, "dependencies": [], "health_check": {"endpoint": "https://api.twitter.com/2/users/me", "method": "GET", "headers": {"Authorization": "Bearer ${TWITTER_BEARER_TOKEN}"}}}, "linkedin_api": {"service_type": "oauth2", "priority": 50, "auto_configure": true, "credentials": {"client_id": "${LINKEDIN_CLIENT_ID}", "client_secret": "${LINKEDIN_CLIENT_SECRET}", "redirect_uri": "https://n8n.${DOMAIN}/rest/oauth2-credential/callback", "access_token": "${LINKEDIN_ACCESS_TOKEN}"}, "n8n_setup": {"credential_type": "linkedInOAuth2Api", "credential_name": "linkedin-api-main"}, "dependencies": [], "health_check": {"endpoint": "https://api.linkedin.com/v2/me", "method": "GET"}}, "instagram_api": {"service_type": "oauth2", "priority": 55, "auto_configure": true, "credentials": {"app_id": "${INSTAGRAM_APP_ID}", "app_secret": "${INSTAGRAM_APP_SECRET}", "access_token": "${INSTAGRAM_ACCESS_TOKEN}", "business_account_id": "${INSTAGRAM_BUSINESS_ACCOUNT_ID}"}, "n8n_setup": {"credential_type": "facebookGraphApi", "credential_name": "instagram-api-main"}, "dependencies": [], "health_check": {"endpoint": "https://graph.facebook.com/me", "method": "GET"}}}, "ecommerce": {"mercadolivre": {"service_type": "oauth2", "priority": 60, "auto_configure": true, "credentials": {"client_id": "${MERCADOLIVRE_CLIENT_ID}", "client_secret": "${MERCADOLIVRE_CLIENT_SECRET}", "access_token": "${MERCADOLIVRE_ACCESS_TOKEN}", "refresh_token": "${MERCADOLIVRE_REFRESH_TOKEN}", "user_id": "${MERCADOLIVRE_USER_ID}"}, "n8n_setup": {"credential_type": "httpHeaderAuth", "credential_name": "mercadolivre-o<PERSON>h"}, "dependencies": [], "health_check": {"endpoint": "https://api.mercadolibre.com/users/me", "method": "GET"}}, "amazon_seller": {"service_type": "api", "priority": 65, "auto_configure": true, "credentials": {"seller_id": "${AMAZON_SELLER_ID}", "marketplace_id": "${AMAZON_MARKETPLACE_ID}", "access_key": "${AMAZON_ACCESS_KEY}", "secret_key": "${AMAZON_SECRET_KEY}", "region": "us-east-1"}, "n8n_setup": {"credential_type": "aws", "credential_name": "amazon-seller-api"}, "dependencies": [], "health_check": {"method": "aws_signature_test"}}}, "productivity": {"google_apis": {"service_type": "oauth2", "priority": 70, "auto_configure": true, "credentials": {"client_id": "${GOOGLE_CLIENT_ID}", "client_secret": "${GOOGLE_CLIENT_SECRET}", "project_id": "${GOOGLE_PROJECT_ID}", "auth_uri": "https://accounts.google.com/o/oauth2/auth", "token_uri": "https://oauth2.googleapis.com/token", "scopes": ["https://www.googleapis.com/auth/userinfo.email", "https://www.googleapis.com/auth/calendar", "https://www.googleapis.com/auth/gmail.send"]}, "n8n_setup": {"credential_type": "googleOAuth2Api", "credential_name": "google-apis-main"}, "dependencies": [], "health_check": {"endpoint": "https://www.googleapis.com/oauth2/v1/userinfo", "method": "GET"}}}, "data_enrichment": {"serper_api": {"service_type": "api", "priority": 75, "auto_configure": true, "credentials": {"api_key": "${SERPER_API_KEY}", "base_url": "https://google.serper.dev"}, "n8n_setup": {"credential_type": "httpHeaderAuth", "credential_name": "serper-api", "fields": {"name": "X-API-KEY", "value": "${SERPER_API_KEY}"}}, "dependencies": [], "health_check": {"endpoint": "https://google.serper.dev/search", "method": "POST", "test_payload": {"q": "test query", "num": 1}}}, "clearbit_api": {"service_type": "api", "priority": 80, "auto_configure": true, "credentials": {"api_key": "${CLEARBIT_API_KEY}", "base_url": "https://person.clearbit.com"}, "n8n_setup": {"credential_type": "httpHeaderAuth", "credential_name": "clearbit-api", "fields": {"name": "Authorization", "value": "Bearer ${CLEARBIT_API_KEY}"}}, "dependencies": [], "health_check": {"endpoint": "https://person.clearbit.com/v2/combined/find", "method": "GET", "test_params": "email=<EMAIL>"}}, "hunter_api": {"service_type": "api", "priority": 85, "auto_configure": true, "credentials": {"api_key": "${HUNTER_API_KEY}", "base_url": "https://api.hunter.io"}, "n8n_setup": {"credential_type": "httpHeaderAuth", "credential_name": "hunter-api", "fields": {"name": "Authorization", "value": "Bearer ${HUNTER_API_KEY}"}}, "dependencies": [], "health_check": {"endpoint": "https://api.hunter.io/v2/account", "method": "GET"}}}}, "workflows": {"core_workflows": [{"name": "[LOG] AI Usage Logger", "file": "[LOG] AI Usage Logger.json", "priority": 1, "dependencies": ["postgresql", "aes_encryption"]}, {"name": "[DISPATCHER] AI Model Selector", "file": "[DISPATCHER] AI Model Selector.json", "priority": 2, "dependencies": ["openai", "openrouter"]}, {"name": "[SUB] Send Message", "file": "[SUB] Send Message.json", "priority": 3, "dependencies": ["evolution_api"]}, {"name": "[AGENT] Autonomous Seller v2.2", "file": "[AGENT] Autonomous Seller v2.2 (Secure & Resilient).json", "priority": 4, "dependencies": ["postgresql", "openai", "aes_encryption"]}, {"name": "[PRI] Agente Inteligente v13.0", "file": "[PRI] Agente Inteligente v13.0 - Cost-Aware.json", "priority": 5, "dependencies": ["postgresql", "openai", "evolution_api", "aes_encryption"]}], "ecommerce_workflows": [{"name": "[ORDERS] Order Management", "file": "[ORDERS] Order Management.json", "dependencies": ["postgresql", "mercadolivre", "amazon_seller"]}, {"name": "[SYNC] Product & Stock Management", "file": "[SYNC] Product & Stock Management.json", "dependencies": ["postgresql", "mercadolivre", "amazon_seller"]}], "marketing_workflows": [{"name": "[CONTENT] Influencer Content Engine", "file": "[CONTENT] Influencer Content Engine.json", "dependencies": ["postgresql", "openai", "buffer_api"]}, {"name": "[INTERACTION] Social Listening & Engagement", "file": "[INTERACTION] Social Listening & Engagement.json", "dependencies": ["twitter_api", "linkedin_api", "instagram_api"]}, {"name": "[ANALYTICS] Influencer Performance Dashboard", "file": "[ANALYTICS] Influencer Performance Dashboard.json", "dependencies": ["postgresql"]}, {"name": "[CAMPAIGN] Product Launch Engine", "file": "[CAMPAIGN] Product Launch Engine.json", "dependencies": ["postgresql", "openai"]}], "strategy_workflows": [{"name": "[STRATEGIST] Market Intelligence Engine", "file": "[STRATEGIST] Market Intelligence Engine.json", "dependencies": ["postgresql", "openai", "serper_api"]}, {"name": "[MANAGER] Strategic Coordinator", "file": "[MANAGER] Strategic Coordinator.json", "dependencies": ["postgresql", "openai"]}, {"name": "[SUPERVISOR] Sales Performance", "file": "[SUPERVISOR] Sales Performance.json", "dependencies": ["postgresql"]}, {"name": "[SCOUT] Product Opportunity Finder", "file": "[SCOUT] Product Opportunity Finder.json", "dependencies": ["postgresql", "openai", "serper_api"]}, {"name": "[CREATOR] Infoproduct Generation Engine", "file": "[CREATOR] Infoproduct Generation Engine.json", "dependencies": ["postgresql", "openai"]}], "viral_workflows": [{"name": "[GROWTH] Viral Growth Engine v2.0", "file": "[GROWTH] Viral Growth Engine v2.0.json", "dependencies": ["postgresql", "openai", "buffer_api", "twitter_api"]}, {"name": "[REPURPOSE] Cross-Platform Content Engine", "file": "[REPURPOSE] Cross-Platform Content Engine.json", "dependencies": ["buffer_api", "twitter_api", "linkedin_api", "instagram_api"]}, {"name": "[UGC] User Generated Content Hunter", "file": "[UGC] User Generated Content Hunter.json", "dependencies": ["postgresql", "openai", "serper_api"]}, {"name": "[COLLAB] Influencer Collaboration Orchestrator", "file": "[COLLAB] Influencer Collaboration Orchestrator.json", "dependencies": ["postgresql", "openai"]}], "multi_identity_workflows": [{"name": "[IDENTITY] Multi-Agent Identity Manager", "file": "[IDENTITY] Multi-Agent Identity Manager.json", "dependencies": ["postgresql", "openai", "aes_encryption"]}, {"name": "[ROUTER] Smart Lead Identity Router", "file": "[ROUTER] Smart Lead Identity Router.json", "dependencies": ["postgresql", "openai"]}, {"name": "[ENGINE] Multi-Identity Autonomous Prospecting", "file": "[ENGINE] Multi-Identity Autonomous Prospecting.json", "dependencies": ["postgresql", "openai", "clearbit_api", "hunter_api"]}], "monitoring_workflows": [{"name": "[MONITOR] Alert Manager", "file": "[MONITOR] Alert Manager.json", "dependencies": ["postgresql", "slack_webhook"]}]}, "environment_variables": {"required": ["POSTGRES_PASSWORD", "AES_ENCRYPTION_KEY", "OPENAI_API_KEY", "EVOLUTION_API_KEY", "EVOLUTION_GLOBAL_API_KEY", "SLACK_WEBHOOK_URL", "DOMAIN"], "optional": ["OPENROUTER_API_KEY", "BUFFER_ACCESS_TOKEN", "TWITTER_BEARER_TOKEN", "LINKEDIN_CLIENT_ID", "INSTAGRAM_ACCESS_TOKEN", "MERCADOLIVRE_CLIENT_ID", "AMAZON_SELLER_ID", "GOOGLE_CLIENT_ID", "SERPER_API_KEY", "CLEARBIT_API_KEY", "HUNTER_API_KEY"]}, "deployment_config": {"retry_attempts": 3, "retry_delay_seconds": 10, "timeout_seconds": 300, "rollback_on_failure": true, "validate_after_deployment": true, "parallel_deployment": false, "backup_before_changes": true}, "notification_config": {"slack_channels": {"alerts": "#agent-alerts", "deployments": "#agent-deployments", "health": "#agent-health"}, "alert_levels": ["critical", "warning", "info"], "notification_methods": ["slack", "email", "webhook"]}}