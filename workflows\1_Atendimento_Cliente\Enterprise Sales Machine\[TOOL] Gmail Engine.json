{"name": "[TOOL] Gmail Engine", "nodes": [{"parameters": {"options": {}}, "id": "start_node", "name": "Start", "type": "n8n-nodes-base.start", "typeVersion": 1, "position": [-1220, 840]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT credentials FROM agent.user_credentials WHERE contact_id = $1 AND service = 'google' AND ('https://www.googleapis.com/auth/gmail.send' = ANY(scopes) OR 'https://mail.google.com/' = ANY(scopes));", "options": {"parameters": {"values": ["={{$json.contactId}}"]}}}, "id": "db_get_credentials", "name": "DB: Get Gmail Credentials", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [-1000, 840], "credentials": {"postgres": {"id": "YOUR_POSTGRES_CREDENTIAL_ID"}}}, {"parameters": {"conditions": {"string": [{"value1": "={{$json.credentials}}", "operation": "isEmpty"}]}}, "id": "if_no_credentials", "name": "IF: No Credentials or Scope", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [-780, 840]}, {"parameters": {"functionCode": "return { json: { error: 'Permissão para enviar e-mails via Gmail não concedida. Por favor, autorize o acesso.' } };", "options": {}}, "id": "return_auth_error", "name": "Return Auth Error", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-560, 940]}, {"parameters": {"authentication": "oAuth2", "resource": "message", "operation": "send", "to": "={{$json.to}}", "subject": "={{$json.subject}}", "text": "={{$json.body}}", "options": {}}, "id": "gmail_send_email", "name": "Gmail: Send Email", "type": "n8n-nodes-base.googleMail", "typeVersion": 4.1, "position": [-560, 740], "credentials": {"googleApi": {"id": "={{$json.credentials.id}}", "data": "={{$json.credentials.data}}"}}}], "connections": {"start_node": {"main": [[{"node": "db_get_credentials", "type": "main", "index": 0}]]}, "db_get_credentials": {"main": [[{"node": "if_no_credentials", "type": "main", "index": 0}]]}, "if_no_credentials": {"main": [[{"node": "gmail_send_email", "type": "main", "index": 0}], [{"node": "return_auth_error", "type": "main", "index": 0}]]}}, "pinData": {}}