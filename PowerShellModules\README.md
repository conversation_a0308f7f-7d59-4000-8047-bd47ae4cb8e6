# PowerShell Modules - Arquitetura Modular Robusta

## Visão Geral

Este diretório contém módulos PowerShell centralizados que fornecem funcionalidades reutilizáveis para todos os scripts de automação do projeto N8N Evolution Environment.

## Estrutura

```
PowerShellModules/
├── AutomationUtils.psm1    # Módulo principal com todas as funções utilitárias
└── README.md              # Este arquivo de documentação
```

## AutomationUtils.psm1

### Descrição
Módulo centralizado que contém todas as funções utilitárias essenciais para automação, incluindo:

- **Sistema de Log Centralizado**: Logging estruturado com níveis e componentes
- **Geração de Credenciais**: Senhas aleatórias e chaves criptográficas seguras
- **Controle de Execução**: Funções para abortar instalação e executar comandos
- **Verificação de Saúde**: Health checks para serviços e portas
- **Diagnósticos Específicos**: Testes especializados para Redis e Evolution API
- **Utilitários Gerais**: Encoding HTML, verificação de contêineres, etc.

### Funções Exportadas

#### Sistema de Log
- `Log-Event`: Logging centralizado com níveis e componentes
- `Write-Host-Color`: Saída colorida no console

#### Geração de Credenciais
- `Generate-RandomPassword`: Gera senhas aleatórias seguras
- `Generate-SecureKey`: Gera chaves criptográficas Base64

#### Controle de Execução
- `Abort-Install`: Aborta instalação com diagnósticos completos
- `Run-Command`: Executa comandos com tratamento de erro robusto

#### Verificação de Saúde
- `Wait-For-Service-Healthy`: Aguarda health check de contêineres Docker
- `Wait-For-Port`: Aguarda disponibilidade de portas TCP

#### Diagnósticos Específicos
- `Test-RedisConnectivity`: Diagnóstico completo do Redis (5 testes)
- `Test-EvolutionRedisConnection`: Teste de conectividade Evolution->Redis

#### Utilitários Gerais
- `Safe-HtmlEncode`: Encoding HTML seguro
- `Test-ContainerRunning`: Verifica se contêiner está rodando

### Inicialização Automática

O módulo se inicializa automaticamente quando importado:
1. Configura o sistema de log (`install.log`)
2. Valida a integridade do módulo
3. Registra o carregamento bem-sucedido

## Como Usar nos Scripts

### Importação Padrão

Todos os scripts PowerShell devem usar este padrão de importação:

```powershell
# =====================================================
# IMPORTAR MÓDULO DE UTILITÁRIOS DE AUTOMAÇÃO
# =====================================================

$utilityModuleName = "AutomationUtils"
$modulePath = Join-Path $PSScriptRoot ".\PowerShellModules\$utilityModuleName.psm1"

if (Test-Path $modulePath) {
    Import-Module $modulePath -Force -ErrorAction Stop
    if (-not (Get-Command Log-Event -ErrorAction SilentlyContinue)) {
        Write-Error "ERRO FATAL: O módulo '$utilityModuleName' importado está corrompido ou incompleto (falta Log-Event). Verifique '$modulePath'."
        exit 1
    }
    Log-Event "Módulo '$utilityModuleName' carregado com sucesso." "ModuleLoader" "SUCCESS"
} else {
    Write-Error "ERRO FATAL: Módulo de utilidades do PowerShell não encontrado em '$modulePath'. O projeto não pode continuar sem funções de suporte essenciais."
    exit 1
}
```

### Scripts Que Usam o Módulo

- `Start-Environment.ps1`: Script principal de orquestração
- `post-setup-automation.ps1`: Script de configuração avançada
- `test-module.ps1`: Script de validação do módulo

## Benefícios da Arquitetura Modular

### 1. **Reutilização Máxima de Código**
- Eliminação completa de duplicação de funções
- Funcionalidades centralizadas e testadas
- Manutenção simplificada

### 2. **Robustez e Confiabilidade**
- Tratamento de erro consistente em todos os scripts
- Validação de integridade do módulo
- Logging estruturado e rastreável

### 3. **Escalabilidade**
- Fácil adição de novas funcionalidades
- Versionamento centralizado
- Testes unitários centralizados

### 4. **Prevenção de Regressões**
- Mudanças em um local afetam todos os scripts
- Validação automática na importação
- Testes de integridade incluídos

## Logs e Diagnósticos

### Sistema de Log
- **Arquivo**: `install.log` (criado automaticamente)
- **Níveis**: INFO, WARN, ERROR, FATAL, DEBUG, SUCCESS
- **Formato**: `[timestamp][nivel][componente] mensagem`
- **Saída**: Arquivo + console colorido

### Diagnósticos Automáticos
- Captura de estado do Docker em caso de falha
- Logs de contêineres para troubleshooting
- Verificações de integridade específicas do Redis

## Validação e Testes

### Script de Teste
Execute `test-module.ps1` para validar o módulo:

```powershell
.\test-module.ps1
```

### Testes Incluídos
- Importação do módulo
- Funcionamento do sistema de log
- Geração de credenciais
- Encoding HTML
- Integridade das funções exportadas

## Troubleshooting

### Problemas Comuns

1. **Módulo não encontrado**
   - Verifique se o arquivo `AutomationUtils.psm1` existe
   - Confirme o caminho relativo correto

2. **Erro de importação**
   - Execute `test-module.ps1` para diagnóstico
   - Verifique permissões de execução do PowerShell

3. **Funções não disponíveis**
   - Confirme se a importação foi bem-sucedida
   - Verifique se as funções estão na lista de Export-ModuleMember

### Logs de Diagnóstico
- Verifique `install.log` para detalhes completos
- Use `Log-Event` com nível DEBUG para troubleshooting
- Monitore a saída colorida no console

## Manutenção

### Adicionando Novas Funções
1. Adicione a função no `AutomationUtils.psm1`
2. Inclua na lista `Export-ModuleMember`
3. Adicione teste no `test-module.ps1`
4. Atualize esta documentação

### Versionamento
- Mantenha a versão no cabeçalho do módulo
- Documente mudanças significativas
- Teste compatibilidade com scripts existentes

---

**Nota**: Esta arquitetura modular garante máxima reutilização de código, robustez e prevenção de regressões, seguindo as melhores práticas de desenvolvimento PowerShell. 