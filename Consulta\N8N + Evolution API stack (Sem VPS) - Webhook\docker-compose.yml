services:
  postgres:
    image: ankane/pgvector
    container_name: postgres
    restart: unless-stopped
    ports:
      - "5432:5432"
    env_file:
      - .env
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - minha_rede

  evolution-api:
    container_name: lab_evolution_api
    image: atendai/evolution-api:v2.2.3
    restart: always
    ports:
      - 8080:8080
    volumes:
      - lab_evolution_instances:/lab_evolution/instances
    env_file:
      - .env
    networks:
      - minha_rede

  ngrok:
    image: ngrok/ngrok:alpine
    container_name: ngrok
    restart: always
    ports:
      - "4040:4040"  # interface web do ngrok
    env_file:
      - .env
    volumes:
      - ./ngrok.yml:/etc/ngrok.yml
    command: start --all --config /etc/ngrok.yml
    depends_on:
      - n8n
      - evolution-api
    networks:
      - minha_rede

  redis:
    image: bitnami/redis:latest
    restart: always
    env_file:
      - .env
    ports:
      - "6380:6379"
    networks:
      - minha_rede
    volumes:
      - 'redis_data:/bitnami/redis/data'

  adminer:
    image: adminer
    container_name: adminer
    restart: always
    ports:
      - "8081:8080"
    depends_on:
      - postgres
    networks:
      - minha_rede

  n8n:
    image: docker.n8n.io/n8nio/n8n
    restart: always
    ports:
      - 5678:5678
    env_file:
      - .env
    volumes:
      - n8n_data:/home/<USER>/.n8n
      - ${DATA_FOLDER}/local_files:/files
    networks:
      - minha_rede

volumes:
  n8n_data:
    external: true
  postgres_data:
  lab_evolution_instances:
  redis_data:
    driver: local

networks:
  minha_rede:
    driver: bridge