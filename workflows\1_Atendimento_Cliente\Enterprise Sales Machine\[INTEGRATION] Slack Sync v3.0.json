{"name": "[INTEGRATION] 🔄 Slack Sync v3.0", "nodes": [{"parameters": {"httpMethod": "POST", "path": "slack-integration-v3", "options": {}}, "id": "slack-webhook-trigger", "name": "🔗 <PERSON><PERSON>ck Webhook Trigger", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300], "webhookId": "slack-integration-v3"}, {"parameters": {"jsCode": "// Enhanced Slack Data Processing\nconst slackData = $input.first().json;\n\n// Validate Slack webhook data\nif (!slackData || typeof slackData !== 'object') {\n  return [{ json: { error: 'Invalid Slack webhook data', processed: false } }];\n}\n\n// Enhanced event mapping\nconst eventMapping = {\n  'message': 'message_created',\n  'app_mention': 'mention_received',\n  'reaction_added': 'reaction_added',\n  'reaction_removed': 'reaction_removed',\n  'slash_command': 'command_executed',\n  'interactive_message': 'interactive_action',\n  'block_actions': 'block_interaction',\n  'view_submission': 'modal_submission',\n  'shortcut': 'shortcut_triggered'\n};\n\nconst eventType = slackData.type || slackData.event?.type;\nconst mappedEvent = eventMapping[eventType] || eventType;\n\n// Extract comprehensive data\nconst extractedData = {\n  // Basic event info\n  event_type: mappedEvent,\n  original_type: eventType,\n  team_id: slackData.team_id,\n  api_app_id: slackData.api_app_id,\n  event_id: slackData.event_id,\n  event_time: slackData.event_time || Date.now() / 1000,\n  \n  // Message data\n  message: {\n    text: slackData.event?.text || slackData.text || '',\n    user: slackData.event?.user || slackData.user_id,\n    channel: slackData.event?.channel || slackData.channel_id,\n    ts: slackData.event?.ts || slackData.ts,\n    thread_ts: slackData.event?.thread_ts,\n    message_ts: slackData.event?.message_ts,\n    blocks: slackData.event?.blocks || slackData.blocks,\n    attachments: slackData.event?.attachments || slackData.attachments,\n    files: slackData.event?.files || slackData.files\n  },\n  \n  // User and channel context\n  user: {\n    id: slackData.event?.user || slackData.user_id,\n    name: slackData.event?.user_name || slackData.user_name,\n    real_name: slackData.event?.user_profile?.real_name,\n    email: slackData.event?.user_profile?.email,\n    is_bot: slackData.event?.bot_id ? true : false,\n    bot_id: slackData.event?.bot_id\n  },\n  \n  // Channel context\n  channel: {\n    id: slackData.event?.channel || slackData.channel_id,\n    name: slackData.event?.channel_name || slackData.channel_name,\n    type: slackData.event?.channel_type,\n    is_private: slackData.event?.channel_type === 'private_channel'\n  },\n  \n  // Interactive elements\n  interactive: {\n    callback_id: slackData.callback_id,\n    action_ts: slackData.action_ts,\n    actions: slackData.actions,\n    trigger_id: slackData.trigger_id,\n    response_url: slackData.response_url,\n    view: slackData.view\n  },\n  \n  // Command data\n  command: {\n    command: slackData.command,\n    command_text: slackData.text,\n    response_url: slackData.response_url\n  },\n  \n  // Reaction data\n  reaction: {\n    reaction: slackData.event?.reaction,\n    item: slackData.event?.item,\n    item_user: slackData.event?.item_user\n  }\n};\n\n// Enhanced escalation criteria check\nfunction checkSlackEscalation(data) {\n  let urgencyScore = 0;\n  let escalationReasons = [];\n  \n  // Keyword-based escalation\n  const urgentKeywords = ['urgent', 'emergency', 'critical', 'asap', 'immediately', 'help', 'issue', 'problem', 'bug', 'error', 'down', 'broken'];\n  const messageText = (data.message.text || '').toLowerCase();\n  \n  urgentKeywords.forEach(keyword => {\n    if (messageText.includes(keyword)) {\n      urgencyScore += 15;\n      escalationReasons.push(`Urgent keyword detected: ${keyword}`);\n    }\n  });\n  \n  // Command-based escalation\n  if (data.command.command) {\n    urgencyScore += 10;\n    escalationReasons.push('Slash command used');\n  }\n  \n  // Interactive element escalation\n  if (data.interactive.actions && data.interactive.actions.length > 0) {\n    urgencyScore += 8;\n    escalationReasons.push('Interactive action triggered');\n  }\n  \n  // Channel-based escalation\n  const escalationChannels = ['support', 'help', 'urgent', 'escalation', 'critical'];\n  if (data.channel.name && escalationChannels.some(ch => data.channel.name.includes(ch))) {\n    urgencyScore += 20;\n    escalationReasons.push(`Posted in escalation channel: ${data.channel.name}`);\n  }\n  \n  // Time-based escalation (outside business hours)\n  const now = new Date();\n  const hour = now.getHours();\n  const isWeekend = now.getDay() === 0 || now.getDay() === 6;\n  \n  if (hour < 8 || hour > 18 || isWeekend) {\n    urgencyScore += 5;\n    escalationReasons.push('Outside business hours');\n  }\n  \n  // User role-based escalation (if user is admin/owner)\n  // This would need to be enhanced with actual user role data\n  \n  // Thread length escalation\n  if (data.message.thread_ts && data.message.ts !== data.message.thread_ts) {\n    urgencyScore += 3;\n    escalationReasons.push('Part of thread conversation');\n  }\n  \n  // File attachment escalation\n  if (data.message.files && data.message.files.length > 0) {\n    urgencyScore += 5;\n    escalationReasons.push('Contains file attachments');\n  }\n  \n  return {\n    requires_escalation: urgencyScore >= 15,\n    urgency_score: urgencyScore,\n    escalation_reasons: escalationReasons,\n    priority: urgencyScore >= 30 ? 'high' : urgencyScore >= 15 ? 'medium' : 'low'\n  };\n}\n\nconst escalationAnalysis = checkSlackEscalation(extractedData);\n\nreturn [{\n  json: {\n    ...extractedData,\n    escalation_analysis: escalationAnalysis,\n    processed_at: new Date().toISOString(),\n    processing_version: '3.0'\n  }\n}];"}, "id": "process-slack-data", "name": "🔄 Enhanced Slack Processing", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [460, 300]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "escalation-required", "leftValue": "={{ $json.escalation_analysis.requires_escalation }}", "rightValue": true, "operator": {"type": "boolean", "operation": "equals"}}], "combinator": "and"}, "options": {}}, "id": "check-slack-escalation", "name": "❓ Check Escalation Required", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [680, 300]}], "connections": {"slack-webhook-trigger": {"main": [[{"node": "process-slack-data", "type": "main", "index": 0}]]}, "process-slack-data": {"main": [[{"node": "check-slack-escalation", "type": "main", "index": 0}]]}}, "pinData": {}, "settings": {"executionOrder": "v1"}, "staticData": null, "tags": [{"createdAt": "2024-01-15T10:00:00.000Z", "updatedAt": "2024-01-15T10:00:00.000Z", "id": "integration", "name": "Integration"}, {"createdAt": "2024-01-15T10:00:00.000Z", "updatedAt": "2024-01-15T10:00:00.000Z", "id": "slack", "name": "<PERSON><PERSON>ck"}, {"createdAt": "2024-01-15T10:00:00.000Z", "updatedAt": "2024-01-15T10:00:00.000Z", "id": "escalation", "name": "Escalation"}]}