{"nodes": [{"parameters": {"chatId": "={{ $('Info').item.json.telegram_chat_id }}", "text": "={{ $json.output }}", "additionalFields": {}}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [2200, -120], "id": "6e022342-fba8-4774-a86c-01ce829d704b", "name": "Responder Telegram", "webhookId": "21855174-4f7f-49f5-b8f4-a284d6ee4ddf", "credentials": {}}, {"parameters": {"updates": ["message"], "additionalFields": {}}, "type": "n8n-nodes-base.telegramTrigger", "typeVersion": 1.2, "position": [180, -120], "id": "22b3ba29-5ac3-4721-9408-737fc1c8b0aa", "name": "Receber Mensagem Telegram", "webhookId": "f2b29356-d5d3-4f5d-9ef1-273001c0a820", "credentials": {}}, {"parameters": {"promptType": "define", "text": "={{ $json.mensagem }}", "options": {"systemMessage": "=Agora são {{ $now.format('FFFF') }}.\n\n## PAPEL\n\nVocê é um assistente interno de reagendamento no escritório, acionado diretamente por um profissional via mensagem para gerenciar situações de remarcação de consultas, incluir lembretes na lista de compras ou agendar tarefas.\n\n## OBJETIVO GERAL\n\n1. Reagendar consultas a pedido do profissional.  \n2. Adicionar lembretes na lista de compras quando solicitado.  \n3. Agendar tarefas a pedido do profissional\n\n\n## RESUMO DE RESPONSABILIDADES\n\n1. Reagendamento de leads  \n   - Acesse o Google Calendar por meio das ferramentas de evento para identificar as consultas afetadas.\n   - Extraia o número de telefone e o ID da conversa na descrição do evento.\n   - Use a ferramenta \"Enviar_reagendamento\" para enviar mensagens de reagendamento aos pacientes.\n   - Envie a pergunta de preferência de nova data e horário.\n2. Lista de compras do escritório  \n   - Se o profissional solicitar a inclusão de um item na lista de compras, utilize a ferramenta \"Criar_tarefa\" para adicionar o lembrete.\n3. Agendamento de tarefas do escritório\n   - Se o profissional solicitar o agendamento de uma tarefa na agenda do Google Calendar, utilize a ferramenta \"Criar_tarefa\" para adicionar o lembrete.\n4. Ler e resumir lista de emails\n   - Se o profissional solicitar informação sobre os emails recebidos, use a ferramenta \"Ler_emails\" e resuma as informações\n\n## ORIENTAÇÕES DE LINGUAGEM E PROCEDIMENTO\n\n- Use uma abordagem empática, profissional e acolhedora.\n- Nunca envie mensagens para pacientes sem autorização explícita do profissional.\n- Quando listar eventos ou tarefas, seja objetivo e organizado.\n- Mantenha clareza e concisão em todas as interações.\n\n## PROFISSIONAIS E ESPECIALIDADES\n\nSegue o nome dos profissionais, suas especialidades, e o ID da agenda que deve ser usado nas ferramentas Google Calendar\n\n**MUITO IMPORTANTE!! O ID DA AGENDA INCLUI O \"@group.calendar.google.com\". NÃO OMITA AO UTILIZAR AS FERRAMENTAS**\n\n- Dr. João Paulo Ferreira - Médico - Clinico Geral (<EMAIL>)\n- Dr. Roberto Almeida - Médico - Cardiologia (<EMAIL>)\n- Dra. Ana Silva - Dentista - Clínica Geral (<EMAIL>)\n- Dra. Carla Mendes - Dentista - Odontopediatria (<EMAIL>)\n\n## IMPORTANTE\n\n- Use a ferramenta \"Refletir\" antes e depois de realizar operações complexas, para ter certeza de que deu tudo certo.\n- SEMPRE QUE ENVIAR UMA MENSAGEM PARA O PACIENTE, **USE A FERRAMENTA \"Salvar_memoria\"**. ISSO É MUITO IMPORTANTE, NÃO FAÇA ERRADO POR FAVOR.\n\n## INSTRUÇÕES FINAIS\n\n- Atenda exclusivamente às solicitações de reagendamento e inclusão de lembretes.\n- A remarcação de consultas ocorre somente quando o profissional pede, utilizando as ferramentas de evento para identificar os pacientes e a ferramenta \"Enviar_reagendamento\" para enviar a mensagem.\n- Para a lista de compras e lembretes, sempre use \"Criar_tarefa\".\n- Para a sua resposta que será enviada para o profissional que fez a soliticação, **NÃO UTILIZE FORMATAÇÃO MARKDOWN**.\n"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.8, "position": [1840, -120], "id": "940b1742-ce99-4c14-b3da-3faae823de3d", "name": "Assistente do escritório interno"}, {"parameters": {"content": "## Assistente interno\n", "height": 560, "width": 2300, "color": 5}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [120, -280], "id": "1255228a-7d04-433a-9ba7-0b929578b936", "name": "Sticky Note4"}, {"parameters": {"content": "[![fazer.ai](https://framerusercontent.com/images/HqY9djLTzyutSKnuLLqBr92KbM.png?scale-down-to=256)](https://fazer.ai?utm_source=n8n&utm_campaign=sec-ep2&utm_medium=cw-6)\n\n## Esse é um template faça você mesmo do canal\n## <PERSON> Moreira\n\n### Inscreva-se no nosso canal no YouTube\n[![YouTube Lucas Moreira](https://img.shields.io/youtube/channel/subscribers/UCtmp6SxzLscu0GRTbgM8FTw?style=flat-square&logo=youtube&label=Inscreva-se&color=f00)](https://youtube.com/@eulucassmoreira?si=0lH7hwX9pukjhmPQ)\n\n### Siga nosso GitHub\n[![GitHub fazer.ai](https://img.shields.io/badge/github-%23121011.svg?style=for-the-badge&logo=github&logoColor=white&label)](https://github.com/fazer-ai)\n", "height": 440, "width": 550, "color": 7}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [120, -740], "id": "9bd83262-c27f-479c-8579-45de4d333e0c", "name": "Sticky Note10"}, {"parameters": {"content": "## Quer entender como funciona?\n\n\n### Assista o vídeo, deixe um like, e se inscreva no canal para ter acesso a mais workflows como esse!\n\n[![IMAGE ALT TEXT HERE](https://i1.ytimg.com/vi_webp/cvTWGNJGAu4/maxresdefault.webp)](https://www.youtube.com/watch?v=cvTWGNJGAu4)", "height": 440, "width": 500, "color": 7}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [680, -740], "id": "f4f6f309-a910-4f94-a431-26a4baf34d8b", "name": "Sticky Note13"}, {"parameters": {"content": "![Chatwoot](https://app.chatwoot.com/brand-assets/logo_dark.svg)", "height": 100, "width": 280, "color": 7}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [340, -440], "id": "d31e4a26-c763-47e5-8c75-b0e0674ea0ca", "name": "Sticky Note23"}, {"parameters": {"descriptionType": "manual", "toolDescription": "Salva a informação de agendamento enviada, para que a secretária saiba que foi enviada.", "schema": {"__rl": true, "mode": "list", "value": "public"}, "table": {"__rl": true, "value": "n8n_historico_mensagens", "mode": "list"}, "columns": {"mappingMode": "defineBelow", "value": {"session_id": "={{ $fromAI('telefone', 'Telefone do paciente, formatado com apenas números, incluindo código do país. Ex.: \"551112345678\"', 'string') }}", "message": "={ \"type\": \"ai\", \"content\": \"{{ $fromAI('message', 'A mesma mensagem enviada para o paciente.', 'string') }}\" }"}, "matchingColumns": ["id"], "schema": [{"id": "id", "displayName": "id", "required": false, "defaultMatch": true, "display": true, "type": "number", "canBeUsedToMatch": true, "removed": true}, {"id": "session_id", "displayName": "session_id", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "message", "displayName": "message", "required": true, "defaultMatch": false, "display": true, "type": "object", "canBeUsedToMatch": true}, {"id": "created_at", "displayName": "created_at", "required": false, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": true, "removed": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.postgresTool", "typeVersion": 2.6, "position": [1600, 120], "id": "0afe5910-b5b9-4483-9dd0-d060937c3859", "name": "<PERSON>var memoria", "credentials": {}}, {"parameters": {"operation": "getAll", "limit": 5, "filters": {}}, "type": "n8n-nodes-base.gmailTool", "typeVersion": 2.1, "position": [2140, 120], "id": "e519e43d-0790-43a8-ac9f-757f85e3c919", "name": "Ler emails", "webhookId": "dde17a90-60a1-42ad-9a09-06cf2fbb3105", "credentials": {}}, {"parameters": {"task": "YXdwUHJaRVoxWDdudFNGNg", "title": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Title', ``, 'string') }}", "additionalFields": {"dueDate": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Due_Date', ``, 'string') }}", "notes": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Notes', ``, 'string') }}", "status": "needsAction"}}, "type": "n8n-nodes-base.googleTasksTool", "typeVersion": 1, "position": [2000, 120], "id": "2adf3113-7901-4cb4-b5e8-829365e0d691", "name": "<PERSON><PERSON><PERSON> tarefa", "credentials": {}}, {"parameters": {"assignments": {"assignments": [{"id": "439cbe58-9652-4831-bf25-f077216f1d55", "name": "url_chatwoot", "value": "<colar sua url do chatwoot>", "type": "string"}, {"id": "0a3fa735-aced-46e9-b9de-1734f9f2b537", "name": "id_conta", "value": "={{ $json.account_id }}", "type": "string"}, {"id": "2c4f57f2-8175-43e9-8d59-2796b2ebf488", "name": "telegram_chat_id", "value": "={{ $('Receber Mensagem Telegram').item.json.message.chat.id }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [620, -120], "id": "8544e445-00eb-440b-aa19-7dd8dea1a292", "name": "Info"}, {"parameters": {"resource": "Profile", "operation": "Fetch Profile", "requestOptions": {}}, "type": "@devlikeapro/n8n-nodes-chatwoot.chatWoot", "typeVersion": 1, "position": [400, -120], "id": "3c15d9e3-69ca-4928-a457-ec8b89270f23", "name": "Buscar informações da conta", "credentials": {}}, {"parameters": {"sessionIdType": "customKey", "sessionKey": "assistente_interno", "tableName": "n8n_historico_mensagens", "contextWindowLength": 10}, "type": "@n8n/n8n-nodes-langchain.memoryPostgresChat", "typeVersion": 1.3, "position": [1460, 120], "id": "b0d3a73f-08fe-4ba0-b613-07a035579359", "name": "Postgres Chat Memory", "credentials": {}}, {"parameters": {"modelName": "models/gemini-2.5-pro-preview-03-25", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "typeVersion": 1, "position": [1320, 120], "id": "53cfd361-b3b5-4c7b-a8fe-839c2a7920d8", "name": "Google Gemini Chat Model", "credentials": {}}, {"parameters": {"sseEndpoint": "<url do seu MCP Google Calendar>"}, "type": "@n8n/n8n-nodes-langchain.mcpClientTool", "typeVersion": 1, "position": [1720, 120], "id": "a2732f74-d9fb-4950-a38d-8b77588e3a8e", "name": "MCP Google Calendar"}, {"parameters": {}, "type": "@n8n/n8n-nodes-langchain.toolThink", "typeVersion": 1, "position": [2280, 120], "id": "06ba33ca-cfcc-4d94-b86a-3d235ef008b7", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{ $('Receber Mensagem Telegram').item.json.message.voice }}", "rightValue": "", "operator": {"type": "object", "operation": "exists", "singleValue": true}, "id": "48feeb05-d1a0-45a7-9e6b-04faef4b5175"}], "combinator": "and"}, "renameOutput": true, "outputKey": "<PERSON><PERSON><PERSON>"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "8cf1a12e-bd0a-4ac6-a33c-6748438c9c8a", "leftValue": "={{ $('Receber Mensagem Telegram').item.json.message.text }}", "rightValue": "", "operator": {"type": "string", "operation": "exists", "singleValue": true}}], "combinator": "and"}, "renameOutput": true, "outputKey": "Texto"}]}, "options": {}}, "type": "n8n-nodes-base.switch", "typeVersion": 3.2, "position": [840, -120], "id": "606188c9-2379-4b76-bfeb-08bb8c899e08", "name": "Tipo de mensagem"}, {"parameters": {"resource": "audio", "operation": "transcribe", "options": {"language": "pt"}}, "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.8, "position": [1340, -220], "id": "64460862-c9bb-4dcd-b55b-eeb60a9a321e", "name": "Transcrever <PERSON>", "credentials": {}}, {"parameters": {"assignments": {"assignments": [{"id": "45efc554-3f74-4922-a2cf-f64d2ea084c0", "name": "mensagem", "value": "={{ $('Receber Mensagem Telegram').item.json.message.text }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1120, -20], "id": "cfca33d3-ecbd-4e80-8b5f-b777f2f9b6d7", "name": "Set mensagem texto"}, {"parameters": {"assignments": {"assignments": [{"id": "0e95db55-1b2e-4762-a547-9481029291e9", "name": "mensagem", "value": "={{ $json.text }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1560, -220], "id": "d8c17a63-7989-442e-bdd7-e416d13461ab", "name": "Set mensagem áudio"}, {"parameters": {"content": "## 6. <PERSON><PERSON><PERSON> interno", "height": 80, "width": 540, "color": 5}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [120, -840], "id": "d8a58981-dbeb-4ceb-8440-e3d5bd48d7af", "name": "Sticky Note30"}, {"parameters": {"description": "Use essa ferramenta para enviar as informações de reagendamento no WhatsApp.\n\nO ID da conversa deve ser extraído das informações do evento.", "workflowId": {"__rl": true, "value": "", "mode": "list"}, "workflowInputs": {"mappingMode": "defineBelow", "value": {"mensagem": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('mensagem', ``, 'string') }}", "id_conversa": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('id_conversa', ``, 'string') }}", "id_conta": "={{ $('Info').item.json.id_conta }}", "url_chatwoot": "={{ $('Info').item.json.url_chatwoot }}"}, "matchingColumns": [], "schema": [{"id": "mensagem", "displayName": "mensagem", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string"}, {"id": "id_conta", "displayName": "id_conta", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string"}, {"id": "id_conversa", "displayName": "id_conversa", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string"}, {"id": "url_chatwoot", "displayName": "url_chatwoot", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string"}], "attemptToConvertTypes": false, "convertFieldsToString": false}}, "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "typeVersion": 2.2, "position": [1860, 120], "id": "0a7dd267-8300-4891-bf86-0e27d65a4828", "name": "Enviar agendamento"}, {"parameters": {"resource": "file", "fileId": "={{ $('Receber Mensagem Telegram').item.json.message.voice.file_id }}"}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [1120, -220], "id": "397a199a-157e-49ea-9ad4-a32ce167b30f", "name": "Download áudio", "webhookId": "9b28d599-c57f-4ecf-94a1-6334f7dabf6d", "credentials": {}}], "connections": {"Receber Mensagem Telegram": {"main": [[{"node": "Buscar informações da conta", "type": "main", "index": 0}]]}, "Assistente do escritório interno": {"main": [[{"node": "Responder Telegram", "type": "main", "index": 0}]]}, "Salvar memoria": {"ai_tool": [[{"node": "Assistente do escritório interno", "type": "ai_tool", "index": 0}]]}, "Ler emails": {"ai_tool": [[{"node": "Assistente do escritório interno", "type": "ai_tool", "index": 0}]]}, "Criar tarefa": {"ai_tool": [[{"node": "Assistente do escritório interno", "type": "ai_tool", "index": 0}]]}, "Info": {"main": [[{"node": "Tipo de mensagem", "type": "main", "index": 0}]]}, "Buscar informações da conta": {"main": [[{"node": "Info", "type": "main", "index": 0}]]}, "Postgres Chat Memory": {"ai_memory": [[{"node": "Assistente do escritório interno", "type": "ai_memory", "index": 0}]]}, "Google Gemini Chat Model": {"ai_languageModel": [[{"node": "Assistente do escritório interno", "type": "ai_languageModel", "index": 0}]]}, "MCP Google Calendar": {"ai_tool": [[{"node": "Assistente do escritório interno", "type": "ai_tool", "index": 0}]]}, "Refletir": {"ai_tool": [[{"node": "Assistente do escritório interno", "type": "ai_tool", "index": 0}]]}, "Tipo de mensagem": {"main": [[{"node": "Download áudio", "type": "main", "index": 0}], [{"node": "Set mensagem texto", "type": "main", "index": 0}]]}, "Transcrever áudio": {"main": [[{"node": "Set mensagem áudio", "type": "main", "index": 0}]]}, "Set mensagem texto": {"main": [[{"node": "Assistente do escritório interno", "type": "main", "index": 0}]]}, "Set mensagem áudio": {"main": [[{"node": "Assistente do escritório interno", "type": "main", "index": 0}]]}, "Enviar agendamento": {"ai_tool": [[{"node": "Assistente do escritório interno", "type": "ai_tool", "index": 0}]]}, "Download áudio": {"main": [[{"node": "Transcrever <PERSON>", "type": "main", "index": 0}]]}}, "pinData": {}, "meta": {}}