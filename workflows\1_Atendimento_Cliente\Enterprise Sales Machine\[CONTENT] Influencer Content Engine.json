{"meta": {"instanceId": "INFLUENCER_CONTENT_ENGINE_MULTI_IDENTITY"}, "name": "[CONTENT] Influencer Content Engine", "nodes": [{"parameters": {"rule": {"interval": [{"field": "hours", "hoursInterval": 1}]}}, "id": "schedule_trigger", "name": "TRIGGER: Content Generation", "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1.1, "position": [140, 300]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "-- <PERSON><PERSON> todas as identidades ativas que precisam de conteúdo\nSELECT \n  ai.id,\n  ai.identity_name,\n  ai.display_name,\n  ai.category,\n  ai.bio,\n  ai.personality_traits,\n  ai.expertise_areas,\n  ai.content_style,\n  ai.voice_characteristics,\n  ai.target_audience,\n  ai.content_themes,\n  ai.posting_frequency,\n  ai.optimal_times,\n  ai.platform_focus,\n  ai.content_pillars,\n  ai.kpis,\n  COALESCE(last_content.last_post, '1970-01-01'::timestamptz) as last_content_date,\n  -- Determinar se precisa de conteúdo baseado na frequência\n  CASE ai.posting_frequency\n    WHEN 'twice_daily' THEN COALESCE(last_content.last_post, '1970-01-01'::timestamptz) < NOW() - INTERVAL '12 hours'\n    WHEN 'daily' THEN COALESCE(last_content.last_post, '1970-01-01'::timestamptz) < NOW() - INTERVAL '24 hours'\n    WHEN 'weekly' THEN COALESCE(last_content.last_post, '1970-01-01'::timestamptz) < NOW() - INTERVAL '7 days'\n    ELSE true\n  END as needs_content\nFROM agent.agent_identities ai\nLEFT JOIN (\n  SELECT \n    identity_id,\n    MAX(published_at) as last_post\n  FROM agent.identity_content_history \n  WHERE status = 'published'\n  GROUP BY identity_id\n) last_content ON ai.id = last_content.identity_id\nWHERE ai.status = 'active'\nORDER BY \n  CASE ai.posting_frequency\n    WHEN 'twice_daily' THEN 1\n    WHEN 'daily' THEN 2\n    WHEN 'weekly' THEN 3\n    ELSE 4\n  END,\n  last_content_date ASC;"}, "id": "get_active_identities", "name": "🎭 Get Active Identities", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [360, 300], "credentials": {"postgres": {"id": "postgres-main", "name": "PostgreSQL Main"}}}, {"parameters": {"batchSize": 1}, "id": "loop_identities", "name": "Loop Through Identities", "type": "n8n-nodes-base.splitInBatches", "typeVersion": 2, "position": [580, 300]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "needs_content_check", "leftValue": "={{ $json.needs_content }}", "rightValue": true, "operator": {"type": "boolean", "operation": "equal"}}]}}, "id": "if_needs_content", "name": "IF: Needs Content?", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [800, 300]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "-- Buscar contexto específico desta identidade\nSELECT \n  icc.context_rules,\n  icc.examples,\n  icc.anti_patterns\nFROM agent.identity_content_context icc\nWHERE icc.identity_id = $1 AND icc.context_type = 'brand_voice'\nLIMIT 1;", "options": {"parameters": {"values": ["={{ $json.id }}"]}}}, "id": "get_identity_context", "name": "📋 Get Identity Context", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [1020, 200], "credentials": {"postgres": {"id": "postgres-main", "name": "PostgreSQL Main"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "-- <PERSON>car histórico recente desta identidade para manter consistência\nSELECT \n  content_text,\n  content_type,\n  platform,\n  viral_score,\n  content_tags,\n  published_at\nFROM agent.identity_content_history\nWHERE identity_id = $1\nAND published_at > NOW() - INTERVAL '14 days'\nORDER BY viral_score DESC, published_at DESC\nLIMIT 5;", "options": {"parameters": {"values": ["={{ $('loop_identities').item.json.id }}"]}}}, "id": "get_recent_content", "name": "📊 Get Recent Content", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [1020, 380], "credentials": {"postgres": {"id": "postgres-main", "name": "PostgreSQL Main"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "-- Buscar tendências relevantes para esta identidade\nSELECT \n  th.hashtag,\n  th.platform,\n  th.trend_momentum,\n  th.category\nFROM agent.trending_hashtags th\nWHERE th.status IN ('emerging', 'rising')\nAND th.trend_momentum > 0.3\nAND (\n  th.category = ANY($1::text[]) \n  OR th.platform = ANY($2::text[])\n)\nORDER BY th.trend_momentum DESC\nLIMIT 5;", "options": {"parameters": {"values": ["={{ $('loop_identities').item.json.expertise_areas ? JSON.stringify($('loop_identities').item.json.expertise_areas) : '{}' }}", "={{ $('loop_identities').item.json.platform_focus ? JSON.stringify($('loop_identities').item.json.platform_focus) : '{}' }}"]}}}, "id": "get_relevant_trends", "name": "📈 Get Relevant Trends", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [1240, 200], "credentials": {"postgres": {"id": "postgres-main", "name": "PostgreSQL Main"}}}, {"parameters": {"workflowId": "={{ $env.DISPATCHER_WORKFLOW_ID }}", "data": "={{ {\n  body: {\n    prompt: `IDENTIDADE ESPECÍFICA - ${$('loop_identities').item.json.display_name}\\n\\n**QUEM VOCÊ É:**\\n${$('loop_identities').item.json.bio}\\n\\n**SUA PERSONALIDADE:**\\n${JSON.stringify($('loop_identities').item.json.personality_traits, null, 2)}\\n\\n**SUAS ESPECIALIDADES:**\\n${JSON.stringify($('loop_identities').item.json.expertise_areas)}\\n\\n**SEU ESTILO DE CONTEÚDO:**\\n${JSON.stringify($('loop_identities').item.json.content_style)}\\n\\n**SUA VOZ CARACTERÍSTICA:**\\n${JSON.stringify($('loop_identities').item.json.voice_characteristics)}\\n\\n**SUA AUDIÊNCIA:**\\n${JSON.stringify($('loop_identities').item.json.target_audience)}\\n\\n**SEUS PILARES DE CONTEÚDO:**\\n${JSON.stringify($('loop_identities').item.json.content_pillars)}\\n\\n**REGRAS DE CONTEXTO:**\\n${JSON.stringify($('get_identity_context').item?.json?.context_rules || {})}\\n\\n**EXEMPLOS DO SEU ESTILO:**\\n${JSON.stringify($('get_identity_context').item?.json?.examples || {})}\\n\\n**O QUE VOCÊ NUNCA FARIA:**\\n${JSON.stringify($('get_identity_context').item?.json?.anti_patterns || {})}\\n\\n**SEU CONTEÚDO RECENTE (para consistência):**\\n${$('get_recent_content').all().map(item => `• ${item.json.content_text.substring(0, 100)}... (Score: ${item.json.viral_score || 0})`).join('\\n')}\\n\\n**TRENDS RELEVANTES PARA VOCÊ:**\\n${$('get_relevant_trends').all().map(trend => `• #${trend.json.hashtag} (${trend.json.platform}) - Momentum: ${Math.round(trend.json.trend_momentum * 100)}%`).join('\\n')}\\n\\n**MISSÃO:**\\nCrie 3 ideias de conteúdo autênticas que sejam reconhecidamente SUAS. Mantenha total separação de contexto de outros agentes influenciadores.\\n\\n**INSTRUÇÕES CRÍTICAS:**\\n1. Use APENAS sua voz e personalidade específica\\n2. Foque APENAS em suas áreas de expertise\\n3. Dirija-se APENAS à sua audiência específica\\n4. Mantenha consistência com seu histórico\\n5. NÃO misture estilos de outras identidades\\n6. Use trends apenas se relevantes para sua expertise\\n\\n**PLATAFORMAS PREFERENCIAIS:**\\n${JSON.stringify($('loop_identities').item.json.platform_focus)}\\n\\nResposta em JSON:\\n{\\n  \\\"content_ideas\\\": [\\n    {\\n      \\\"title\\\": \\\"título da ideia\\\",\\n      \\\"content\\\": \\\"conteúdo completo\\\",\\n      \\\"platform\\\": \\\"plataforma ideal\\\",\\n      \\\"content_type\\\": \\\"post/thread/article\\\",\\n      \\\"style_elements\\\": [\\\"elementos do seu estilo\\\"],\\n      \\\"audience_appeal\\\": \\\"como conecta com sua audiência\\\",\\n      \\\"viral_potential\\\": 0.00,\\n      \\\"hashtags\\\": [\\\"hashtags relevantes\\\"]\\n    }\\n  ],\\n  \\\"identity_consistency_score\\\": 0.00,\\n  \\\"context_separation_maintained\\\": true\\n}`,\n    task_type: 'identity_specific_content_batch',\n    calling_workflow_id: $workflow.id,\n    calling_node_id: 'generate_identity_content_batch',\n    identity_context: {\n      identity_id: $('loop_identities').item.json.id,\n      identity_name: $('loop_identities').item.json.identity_name,\n      category: $('loop_identities').item.json.category,\n      last_content_date: $('loop_identities').item.json.last_content_date\n    }\n  }\n} }}", "options": {}}, "id": "generate_identity_content_batch", "name": "🤖 AI: Generate Identity Content Batch", "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 2, "position": [1460, 300]}, {"parameters": {"batchSize": 1}, "id": "loop_content_ideas", "name": "Loop Content Ideas", "type": "n8n-nodes-base.splitInBatches", "typeVersion": 2, "position": [1680, 300]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "-- Validar cada ideia de conteúdo\nSELECT validate_content_context($1, $2, $3) as validation_result;", "options": {"parameters": {"values": ["={{ $('loop_identities').item.json.id }}", "={{ $json.content }}", "={{ $json.platform }}"]}}}, "id": "validate_content_idea", "name": "✅ Validate Content Idea", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [1900, 300], "credentials": {"postgres": {"id": "postgres-main", "name": "PostgreSQL Main"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "validation_passed", "leftValue": "={{ $json.validation_result.recommendation }}", "rightValue": "approved", "operator": {"type": "string", "operation": "equals"}}]}}, "id": "if_content_approved", "name": "IF: Content Approved?", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [2120, 300]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "-- <PERSON><PERSON> con<PERSON>do aprovado no histórico da identidade\nINSERT INTO agent.identity_content_history (\n  identity_id,\n  content_type,\n  content_text,\n  content_metadata,\n  platform,\n  content_tags,\n  status\n)\nVALUES (\n  $1,\n  $2,\n  $3,\n  $4::jsonb,\n  $5,\n  $6::text[],\n  'approved'\n)\nRETURNING *;", "options": {"parameters": {"values": ["={{ $('loop_identities').item.json.id }}", "={{ $('loop_content_ideas').item.json.content_type }}", "={{ $('loop_content_ideas').item.json.content }}", "={{ JSON.stringify({\n                ai_response: $('loop_content_ideas').item.json,\n                validation: $('validate_content_idea').item.json.validation_result,\n                identity_config: {\n                  name: $('loop_identities').item.json.identity_name,\n                  category: $('loop_identities').item.json.category,\n                  display_name: $('loop_identities').item.json.display_name\n                },\n                generation_context: {\n                  batch_id: $('generate_identity_content_batch').item.json.identity_consistency_score,\n                  separation_maintained: $('generate_identity_content_batch').item.json.context_separation_maintained\n                }\n              }) }}", "={{ $('loop_content_ideas').item.json.platform }}", "={{ $('loop_content_ideas').item.json.hashtags ? JSON.stringify($('loop_content_ideas').item.json.hashtags) : '{}' }}"]}}}, "id": "save_approved_content", "name": "💾 Save Approved Content", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [2340, 200], "credentials": {"postgres": {"id": "postgres-main", "name": "PostgreSQL Main"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "-- Determinar timing ótimo para esta identidade e plataforma\nSELECT \n  hour_of_day,\n  day_of_week,\n  engagement_score,\n  confidence_level\nFROM agent.optimal_timing_analysis ota\nWHERE ota.influencer_id = (\n  SELECT ip.id FROM agent.influencer_personas ip \n  WHERE ip.archetype ILIKE '%' || $1 || '%' \n  LIMIT 1\n)\nAND ota.platform = $2\nAND ota.confidence_level > 0.6\nORDER BY ota.engagement_score DESC\nLIMIT 1;", "options": {"parameters": {"values": ["={{ $('loop_identities').item.json.category }}", "={{ $('loop_content_ideas').item.json.platform }}"]}}}, "id": "get_optimal_timing", "name": "⏰ Get Optimal Timing", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [2560, 200], "credentials": {"postgres": {"id": "postgres-main", "name": "PostgreSQL Main"}}}, {"parameters": {"workflowId": "={{ $env.REPURPOSE_ENGINE_WORKFLOW_ID }}", "data": "={{ {\n  body: {\n    source_platform: $('loop_content_ideas').item.json.platform,\n    original_content: $('loop_content_ideas').item.json.content,\n    influencer_id: $('loop_identities').item.json.id,\n    identity_name: $('loop_identities').item.json.identity_name,\n    content_id: $('save_approved_content').item.json.id,\n    auto_schedule: true,\n    optimal_timing: $('get_optimal_timing').item?.json || {}\n  }\n} }}", "options": {}}, "id": "trigger_repurposing", "name": "🔄 Trigger Cross-Platform Repurposing", "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 2, "position": [2780, 200], "continueOnFail": true}, {"parameters": {"method": "POST", "url": "={{ $vars.SLACK_WEBHOOK_URL }}", "sendBody": true, "specifyBody": "json", "jsonBody": "{\n  \"text\": \"❌ Conteúdo rejeitado por violação de contexto\",\n  \"blocks\": [\n    {\n      \"type\": \"section\",\n      \"text\": {\n        \"type\": \"mrkdwn\",\n        \"text\": \"*❌ Content Rejected - Context Violation*\\n\\n*Identidade:* {{ $('loop_identities').item.json.display_name }}\\n*Categoria:* {{ $('loop_identities').item.json.category }}\\n*Plataforma:* {{ $('loop_content_ideas').item.json.platform }}\\n\\n*Validação:* {{ $('validate_content_idea').item.json.validation_result.recommendation }}\\n*Score de Voz:* {{ $('validate_content_idea').item.json.validation_result.voice_compliance_score }}\\n\\n*Conteúdo Rejeitado:*\\n{{ $('loop_content_ideas').item.json.content.substring(0, 300) }}...\\n\\n_Conteúdo não foi salvo para manter integridade da identidade._\"\n      }\n    }\n  ],\n  \"channel\": \"#content-quality\"\n}", "options": {}}, "id": "notify_content_rejection", "name": "🚨 Notify Content Rejection", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [2340, 400], "continueOnFail": true}, {"parameters": {"mode": "combine", "combinationMode": "mergeByPosition", "options": {}}, "id": "merge_content_results", "name": "Merge Content Results", "type": "n8n-nodes-base.merge", "typeVersion": 2.1, "position": [3000, 300]}, {"parameters": {"mode": "combine", "combinationMode": "mergeByPosition", "options": {}}, "id": "merge_identity_results", "name": "Merge Identity Results", "type": "n8n-nodes-base.merge", "typeVersion": 2.1, "position": [3220, 300]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "-- Relatório de execução por identidade\nSELECT \n  ai.identity_name,\n  ai.display_name,\n  ai.category,\n  COUNT(ich.id) as content_generated,\n  COUNT(CASE WHEN ich.status = 'approved' THEN 1 END) as content_approved,\n  COUNT(CASE WHEN ich.created_at > NOW() - INTERVAL '1 hour' THEN 1 END) as content_this_hour,\n  AVG(CASE WHEN ich.created_at > NOW() - INTERVAL '24 hours' THEN \n    (ich.content_metadata->'validation'->>'voice_compliance_score')::decimal \n  END) as avg_voice_compliance\nFROM agent.agent_identities ai\nLEFT JOIN agent.identity_content_history ich ON ai.id = ich.identity_id\nWHERE ai.status = 'active'\nGROUP BY ai.id, ai.identity_name, ai.display_name, ai.category\nORDER BY content_this_hour DESC, avg_voice_compliance DESC;"}, "id": "generate_execution_report", "name": "📊 Generate Execution Report", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [3440, 300], "credentials": {"postgres": {"id": "postgres-main", "name": "PostgreSQL Main"}}}, {"parameters": {"method": "POST", "url": "={{ $vars.SLACK_WEBHOOK_URL }}", "sendBody": true, "specifyBody": "json", "jsonBody": "{\n  \"text\": \"🎭 Multi-Identity Content Engine - Relatório de Execução\",\n  \"blocks\": [\n    {\n      \"type\": \"header\",\n      \"text\": {\n        \"type\": \"plain_text\",\n        \"text\": \"🎭 MULTI-IDENTITY CONTENT REPORT\"\n      }\n    },\n    {\n      \"type\": \"section\",\n      \"fields\": [\n        {\n          \"type\": \"mrkdwn\",\n          \"text\": \"*🔄 Identidades Processadas:*\\n{{ $('get_active_identities').all().length }}\"\n        },\n        {\n          \"type\": \"mrkdwn\",\n          \"text\": \"*✅ Conteúdo Aprovado:*\\n{{ $('save_approved_content').all().length }}\"\n        },\n        {\n          \"type\": \"mrkdwn\",\n          \"text\": \"*🔄 Repurposing Triggered:*\\n{{ $('trigger_repurposing').all().length }}\"\n        },\n        {\n          \"type\": \"mrkdwn\",\n          \"text\": \"*❌ Conteúdo Rejeitado:*\\n{{ $('notify_content_rejection').all().length }}\"\n        }\n      ]\n    },\n    {\n      \"type\": \"section\",\n      \"text\": {\n        \"type\": \"mrkdwn\",\n        \"text\": \"*🎯 Performance por Identidade:*\\n{{ $('generate_execution_report').all().slice(0, 5).map(identity => `• ${identity.json.display_name} (${identity.json.category}): ${identity.json.content_this_hour} conteúdos`).join('\\n') }}\"\n      }\n    },\n    {\n      \"type\": \"section\",\n      \"text\": {\n        \"type\": \"mrkdwn\",\n        \"text\": \"*🔒 Context Separation Status:*\\n✅ Cada identidade mantém seu contexto único\\n✅ Validação automática ativa\\n✅ Histórico por identidade preservado\"\n      }\n    },\n    {\n      \"type\": \"context\",\n      \"elements\": [\n        {\n          \"type\": \"mrkdwn\",\n          \"text\": \"Engine executando a cada hora | Próxima geração: {{ $now.plus({ hours: 1 }).toFormat('dd/MM HH:mm') }}\"\n        }\n      ]\n    }\n  ],\n  \"channel\": \"#content-engine\"\n}", "options": {}}, "id": "notify_execution_summary", "name": "📱 Notify Multi-Identity Summary", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [3660, 300], "continueOnFail": true}], "connections": {"schedule_trigger": {"main": [[{"node": "get_active_identities", "type": "main", "index": 0}]]}, "get_active_identities": {"main": [[{"node": "loop_identities", "type": "main", "index": 0}]]}, "loop_identities": {"main": [[{"node": "if_needs_content", "type": "main", "index": 0}]]}, "if_needs_content": {"main": [[{"node": "get_identity_context", "type": "main", "index": 0}, {"node": "get_recent_content", "type": "main", "index": 0}], [{"node": "merge_identity_results", "type": "main", "index": 0}]]}, "get_identity_context": {"main": [[{"node": "get_relevant_trends", "type": "main", "index": 0}]]}, "get_recent_content": {"main": [[{"node": "get_relevant_trends", "type": "main", "index": 1}]]}, "get_relevant_trends": {"main": [[{"node": "generate_identity_content_batch", "type": "main", "index": 0}]]}, "generate_identity_content_batch": {"main": [[{"node": "loop_content_ideas", "type": "main", "index": 0}]]}, "loop_content_ideas": {"main": [[{"node": "validate_content_idea", "type": "main", "index": 0}]]}, "validate_content_idea": {"main": [[{"node": "if_content_approved", "type": "main", "index": 0}]]}, "if_content_approved": {"main": [[{"node": "save_approved_content", "type": "main", "index": 0}], [{"node": "notify_content_rejection", "type": "main", "index": 0}]]}, "save_approved_content": {"main": [[{"node": "get_optimal_timing", "type": "main", "index": 0}]]}, "get_optimal_timing": {"main": [[{"node": "trigger_repurposing", "type": "main", "index": 0}]]}, "trigger_repurposing": {"main": [[{"node": "merge_content_results", "type": "main", "index": 0}]]}, "notify_content_rejection": {"main": [[{"node": "merge_content_results", "type": "main", "index": 1}]]}, "merge_content_results": {"main": [[{"node": "merge_identity_results", "type": "main", "index": 1}]]}, "merge_identity_results": {"main": [[{"node": "generate_execution_report", "type": "main", "index": 0}]]}, "generate_execution_report": {"main": [[{"node": "notify_execution_summary", "type": "main", "index": 0}]]}}, "settings": {"executionOrder": "v1"}, "staticData": null, "tags": [{"createdAt": "2024-01-15T18:00:00.000Z", "updatedAt": "2024-01-15T18:00:00.000Z", "id": "multi-identity-content", "name": "multi-identity-content"}], "triggerCount": 1, "updatedAt": "2024-01-15T18:00:00.000Z", "versionId": "1"}