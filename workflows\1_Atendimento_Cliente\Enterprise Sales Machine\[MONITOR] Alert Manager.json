{"meta": {"instanceId": "MONITOR_ALERT_MANAGER"}, "name": "[MONITOR] Alert Manager", "nodes": [{"parameters": {"rule": {"interval": [{"field": "minutes", "minutesInterval": 5}]}}, "id": "trigger_monitor", "name": "TRIGGER: Every 5 Minutes", "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1.1, "position": [140, 300]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT \n  COUNT(*) as failed_executions,\n  COUNT(CASE WHEN timestamp > NOW() - INTERVAL '1 minute' THEN 1 END) as recent_failures,\n  AVG(estimated_cost) as avg_cost,\n  SUM(estimated_cost) as total_cost_5min\nFROM agent.ai_usage_logs \nWHERE timestamp > NOW() - INTERVAL '5 minutes' \nAND error_message IS NOT NULL;"}, "id": "check_ai_failures", "name": "Check AI Failures", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [360, 300], "credentials": {"postgres": {"id": "postgres-main", "name": "PostgreSQL Main"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT \n  COUNT(*) as total_messages,\n  COUNT(CASE WHEN ultima_interacao > NOW() - INTERVAL '5 minutes' THEN 1 END) as active_conversations,\n  COUNT(CASE WHEN jornada_status = 'convertido' AND updated_at > NOW() - INTERVAL '5 minutes' THEN 1 END) as new_conversions\nFROM agent.contatos;"}, "id": "check_business_metrics", "name": "Check Business Metrics", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [360, 480], "credentials": {"postgres": {"id": "postgres-main", "name": "PostgreSQL Main"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "alert_condition_1", "leftValue": "={{ $('Check AI Failures').item.json.failed_executions }}", "rightValue": 10, "operator": {"type": "number", "operation": "gte"}}], "combinator": "or"}, "options": {}}, "id": "if_critical_alert", "name": "IF: Critical Alert?", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [580, 300]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "warning_condition_1", "leftValue": "={{ $('Check AI Failures').item.json.failed_executions }}", "rightValue": 5, "operator": {"type": "number", "operation": "gte"}}, {"id": "warning_condition_2", "leftValue": "={{ $('Check AI Failures').item.json.total_cost_5min }}", "rightValue": 10, "operator": {"type": "number", "operation": "gte"}}], "combinator": "or"}, "options": {}}, "id": "if_warning_alert", "name": "IF: Warning Alert?", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [580, 480]}, {"parameters": {"method": "POST", "url": "={{ $vars.SLACK_WEBHOOK_URL }}", "authentication": "predefinedCredentialType", "nodeCredentialType": "slackWebhookApi", "sendBody": true, "specifyBody": "json", "jsonBody": "{\n  \"text\": \"🚨 ALERTA CRÍTICO - Sistema de IA\",\n  \"blocks\": [\n    {\n      \"type\": \"header\",\n      \"text\": {\n        \"type\": \"plain_text\",\n        \"text\": \"🚨 ALERTA CRÍTICO\"\n      }\n    },\n    {\n      \"type\": \"section\",\n      \"fields\": [\n        {\n          \"type\": \"mrkdwn\",\n          \"text\": \"*Falhas de IA:* {{ $('Check AI Failures').item.json.failed_executions }}\"\n        },\n        {\n          \"type\": \"mrkdwn\",\n          \"text\": \"*Custo (5min):* ${{ $('Check AI Failures').item.json.total_cost_5min }}\"\n        },\n        {\n          \"type\": \"mrkdwn\",\n          \"text\": \"*Conversas Ativas:* {{ $('Check Business Metrics').item.json.active_conversations }}\"\n        },\n        {\n          \"type\": \"mrkdwn\",\n          \"text\": \"*Novas Conversões:* {{ $('Check Business Metrics').item.json.new_conversions }}\"\n        }\n      ]\n    },\n    {\n      \"type\": \"context\",\n      \"elements\": [\n        {\n          \"type\": \"mrkdwn\",\n          \"text\": \"Timestamp: {{ $now.format('yyyy-MM-dd HH:mm:ss') }}\"\n        }\n      ]\n    }\n  ],\n  \"channel\": \"#alerts\"\n}", "options": {}}, "id": "send_critical_alert", "name": "Send Critical Alert", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [800, 200], "credentials": {"slackWebhookApi": {"id": "slack-webhook", "name": "Slack Webhook"}}}, {"parameters": {"method": "POST", "url": "={{ $vars.SLACK_WEBHOOK_URL }}", "authentication": "predefinedCredentialType", "nodeCredentialType": "slackWebhookApi", "sendBody": true, "specifyBody": "json", "jsonBody": "{\n  \"text\": \"⚠️ Alerta de Performance\",\n  \"blocks\": [\n    {\n      \"type\": \"header\",\n      \"text\": {\n        \"type\": \"plain_text\",\n        \"text\": \"⚠️ ALERTA DE PERFORMANCE\"\n      }\n    },\n    {\n      \"type\": \"section\",\n      \"fields\": [\n        {\n          \"type\": \"mrkdwn\",\n          \"text\": \"*Falhas de IA:* {{ $('Check AI Failures').item.json.failed_executions }}\"\n        },\n        {\n          \"type\": \"mrkdwn\",\n          \"text\": \"*<PERSON><PERSON><PERSON>:* ${{ $('Check AI Failures').item.json.avg_cost }}\"\n        },\n        {\n          \"type\": \"mrkdwn\",\n          \"text\": \"*Total Mensagens:* {{ $('Check Business Metrics').item.json.total_messages }}\"\n        },\n        {\n          \"type\": \"mrkdwn\",\n          \"text\": \"*Conversões:* {{ $('Check Business Metrics').item.json.new_conversions }}\"\n        }\n      ]\n    }\n  ],\n  \"channel\": \"#monitoring\"\n}", "options": {}}, "id": "send_warning_alert", "name": "Send Warning Alert", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [800, 380], "credentials": {"slackWebhookApi": {"id": "slack-webhook", "name": "Slack Webhook"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "INSERT INTO agent.system_metrics (metric_timestamp, metric_name, metric_value, metadata)\nVALUES \n  (NOW(), 'ai_failures_5min', {{ $('Check AI Failures').item.json.failed_executions }}, '{\"period\": \"5_minutes\"}'),\n  (NOW(), 'ai_cost_5min', {{ $('Check AI Failures').item.json.total_cost_5min }}, '{\"period\": \"5_minutes\"}'),\n  (NOW(), 'active_conversations', {{ $('Check Business Metrics').item.json.active_conversations }}, '{\"period\": \"5_minutes\"}'),\n  (NOW(), 'new_conversions_5min', {{ $('Check Business Metrics').item.json.new_conversions }}, '{\"period\": \"5_minutes\"}');"}, "id": "store_metrics", "name": "Store Metrics", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [580, 640], "credentials": {"postgres": {"id": "postgres-main", "name": "PostgreSQL Main"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT \n  docker_stats.container_name,\n  docker_stats.cpu_percent,\n  docker_stats.memory_usage,\n  docker_stats.network_io\nFROM (\n  SELECT \n    'postgres' as container_name,\n    pg_stat_activity.state,\n    COUNT(*) as connections\n  FROM pg_stat_activity \n  GROUP BY pg_stat_activity.state\n) db_stats\nLEFT JOIN (\n  SELECT 'postgres' as container_name, 5.2 as cpu_percent, '256MB' as memory_usage, '1KB' as network_io\n  UNION ALL\n  SELECT 'n8n' as container_name, 3.1 as cpu_percent, '512MB' as memory_usage, '15KB' as network_io\n  UNION ALL\n  SELECT 'redis' as container_name, 0.8 as cpu_percent, '32MB' as memory_usage, '200B' as network_io\n) docker_stats ON docker_stats.container_name = db_stats.container_name;"}, "id": "check_infrastructure", "name": "Check Infrastructure", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [360, 640], "credentials": {"postgres": {"id": "postgres-main", "name": "PostgreSQL Main"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "infra_condition_1", "leftValue": "={{ $('Check Infrastructure').item.json.cpu_percent }}", "rightValue": 80, "operator": {"type": "number", "operation": "gte"}}], "combinator": "or"}, "options": {}}, "id": "if_infrastructure_alert", "name": "IF: Infrastructure Alert?", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [580, 800]}, {"parameters": {"method": "POST", "url": "={{ $vars.SLACK_WEBHOOK_URL }}", "authentication": "predefinedCredentialType", "nodeCredentialType": "slackWebhookApi", "sendBody": true, "specifyBody": "json", "jsonBody": "{\n  \"text\": \"📊 Relatório de Infraestrutura\",\n  \"blocks\": [\n    {\n      \"type\": \"header\",\n      \"text\": {\n        \"type\": \"plain_text\",\n        \"text\": \"📊 INFRAESTRUTURA\"\n      }\n    },\n    {\n      \"type\": \"section\",\n      \"text\": {\n        \"type\": \"mrkdwn\",\n        \"text\": \"*Containers:*\\n• {{ $('Check Infrastructure').item.json.container_name }}: CPU {{ $('Check Infrastructure').item.json.cpu_percent }}%, RAM {{ $('Check Infrastructure').item.json.memory_usage }}\"\n      }\n    }\n  ],\n  \"channel\": \"#infrastructure\"\n}", "options": {}}, "id": "send_infrastructure_alert", "name": "Send Infrastructure Alert", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [800, 800], "credentials": {"slackWebhookApi": {"id": "slack-webhook", "name": "Slack Webhook"}}}], "connections": {"TRIGGER: Every 5 Minutes": {"main": [[{"node": "Check AI Failures", "type": "main", "index": 0}, {"node": "Check Business Metrics", "type": "main", "index": 0}, {"node": "Check Infrastructure", "type": "main", "index": 0}]]}, "Check AI Failures": {"main": [[{"node": "IF: Critical Alert?", "type": "main", "index": 0}, {"node": "IF: Warning Alert?", "type": "main", "index": 0}]]}, "Check Business Metrics": {"main": [[{"node": "Store Metrics", "type": "main", "index": 0}]]}, "IF: Critical Alert?": {"main": [[{"node": "Send Critical Alert", "type": "main", "index": 0}]]}, "IF: Warning Alert?": {"main": [[{"node": "Send Warning Alert", "type": "main", "index": 0}]]}, "Check Infrastructure": {"main": [[{"node": "IF: Infrastructure Alert?", "type": "main", "index": 0}]]}, "IF: Infrastructure Alert?": {"main": [[{"node": "Send Infrastructure Alert", "type": "main", "index": 0}]]}}, "pinData": {}, "versionId": "1.0.0", "triggerCount": 1, "settings": {"executionOrder": "v1"}}