{"name": "[ESCALATION] 🎯 AI Intelligent Routing v2.0", "nodes": [{"parameters": {"path": "ai-intelligent-routing", "options": {"noResponseBody": false}}, "id": "routing-webhook", "name": "🎯 Routing Request", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [180, 300], "webhookId": "ai-intelligent-routing-webhook"}, {"parameters": {"jsCode": "// Validar e processar dados de entrada para roteamento\nconst inputData = $json;\n\n// Validações básicas\nif (!inputData.escalation_id && !inputData.escalation_data) {\n  throw new Error('escalation_id ou escalation_data é obrigatório');\n}\n\n// Extrair dados da escalação\nconst escalationData = inputData.escalation_data || {};\nconst escalationId = inputData.escalation_id;\n\n// Dados do cliente\nconst customerData = {\n  customer_id: escalationData.customer_id || inputData.customer_id,\n  customer_tier: escalationData.customer_tier || inputData.customer_tier || 'standard',\n  previous_escalations: escalationData.previous_escalations || 0,\n  account_age_days: escalationData.account_age_days || 0,\n  total_spent: escalationData.total_spent || 0,\n  preferred_language: escalationData.preferred_language || 'pt'\n};\n\n// Dados da escalação\nconst escalationInfo = {\n  priority: escalationData.priority || inputData.priority || 'medium',\n  category: escalationData.category || inputData.category || 'general',\n  channel: escalationData.channel || inputData.channel || 'chat',\n  complexity: escalationData.complexity || inputData.complexity || 'medium',\n  sentiment_score: escalationData.sentiment_score || inputData.sentiment_score || 0,\n  urgency_level: escalationData.urgency_level || inputData.urgency_level || 'medium',\n  estimated_resolution_time: escalationData.estimated_resolution_time || 30,\n  requires_specialist: escalationData.requires_specialist || false,\n  technical_issue: escalationData.technical_issue || false\n};\n\n// Contexto adicional\nconst contextData = {\n  current_queue_load: inputData.current_queue_load || {},\n  agent_availability: inputData.agent_availability || {},\n  business_hours: inputData.business_hours !== false,\n  sla_deadline: escalationData.sla_deadline || inputData.sla_deadline,\n  special_requirements: escalationData.special_requirements || []\n};\n\n// Preparar resultado\nconst result = {\n  routing_id: require('crypto').randomUUID(),\n  escalation_id: escalationId,\n  customer_data: customerData,\n  escalation_info: escalationInfo,\n  context_data: contextData,\n  processing_timestamp: new Date().toISOString(),\n  ready_for_routing: true\n};\n\nreturn result;"}, "id": "validate-routing-data", "name": "✅ Validate Routing Data", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [400, 300]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT \n  a.agent_id, a.name, a.email, a.status,\n  a.specializations, a.languages, a.max_concurrent_escalations,\n  a.current_workload, a.performance_score, a.availability_schedule,\n  COUNT(e.escalation_id) as current_escalations,\n  AVG(e.resolution_time_minutes) as avg_resolution_time,\n  AVG(CASE WHEN e.customer_satisfaction_score IS NOT NULL THEN e.customer_satisfaction_score END) as avg_satisfaction\nFROM agent.agents a\nLEFT JOIN agent.escalations e ON a.agent_id = e.assigned_agent_id \n  AND e.status IN ('assigned', 'in_progress')\nWHERE a.status = 'available'\n  AND a.is_active = true\nGROUP BY a.agent_id, a.name, a.email, a.status, a.specializations, \n  a.languages, a.max_concurrent_escalations, a.current_workload, \n  a.performance_score, a.availability_schedule\nORDER BY a.performance_score DESC", "additionalFields": {}}, "id": "get-available-agents", "name": "👥 Get Available Agents", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [620, 300]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT \n  queue_id, name, specialization, max_capacity,\n  current_load, priority_weight, sla_target_minutes,\n  business_hours_only, auto_assignment_enabled\nFROM agent.queues\nWHERE is_active = true\nORDER BY priority_weight DESC", "additionalFields": {}}, "id": "get-active-queues", "name": "📋 Get Active Queues", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [620, 400]}, {"parameters": {"jsCode": "// Algoritmo de roteamento inteligente\nconst routingData = $('validate-routing-data').first().json;\nconst availableAgents = $('get-available-agents').first().json;\nconst activeQueues = $('get-active-queues').first().json;\n\nconst { customer_data, escalation_info, context_data } = routingData;\n\n// Função para calcular score de compatibilidade do agente\nfunction calculateAgentScore(agent, escalation, customer) {\n  let score = 0;\n  let factors = [];\n\n  // 1. Especialização (peso: 30%)\n  const specializations = Array.isArray(agent.specializations) ? agent.specializations : [];\n  if (specializations.includes(escalation.category)) {\n    score += 30;\n    factors.push('specialization_match');\n  }\n\n  // 2. Idioma (peso: 25%)\n  const languages = Array.isArray(agent.languages) ? agent.languages : [];\n  if (languages.includes(customer.preferred_language)) {\n    score += 25;\n    factors.push('language_match');\n  }\n\n  // 3. Carga de trabalho (peso: 20%)\n  const workloadRatio = agent.current_escalations / agent.max_concurrent_escalations;\n  if (workloadRatio < 0.5) {\n    score += 20;\n    factors.push('low_workload');\n  } else if (workloadRatio < 0.8) {\n    score += 10;\n    factors.push('medium_workload');\n  }\n\n  // 4. Performance histórica (peso: 15%)\n  if (agent.performance_score >= 0.9) {\n    score += 15;\n    factors.push('high_performance');\n  } else if (agent.performance_score >= 0.7) {\n    score += 10;\n    factors.push('good_performance');\n  }\n\n  // 5. Tempo médio de resolução (peso: 10%)\n  if (agent.avg_resolution_time && agent.avg_resolution_time < 60) {\n    score += 10;\n    factors.push('fast_resolution');\n  } else if (agent.avg_resolution_time && agent.avg_resolution_time < 120) {\n    score += 5;\n    factors.push('average_resolution');\n  }\n\n  // Penalizações\n  // Cliente VIP precisa de agente experiente\n  if (customer.customer_tier === 'vip' && agent.performance_score < 0.8) {\n    score -= 15;\n    factors.push('vip_penalty');\n  }\n\n  // Escalação técnica precisa de especialista\n  if (escalation.technical_issue && !specializations.includes('technical')) {\n    score -= 20;\n    factors.push('technical_penalty');\n  }\n\n  // Urgência alta precisa de agente disponível\n  if (escalation.urgency_level === 'critical' && workloadRatio > 0.7) {\n    score -= 25;\n    factors.push('urgency_penalty');\n  }\n\n  return {\n    agent_id: agent.agent_id,\n    score: Math.max(0, score),\n    factors: factors,\n    workload_ratio: workloadRatio,\n    can_handle: workloadRatio < 1.0\n  };\n}\n\n// Função para calcular score da fila\nfunction calculateQueueScore(queue, escalation) {\n  let score = 0;\n  let factors = [];\n\n  // 1. Especialização da fila (peso: 40%)\n  if (queue.specialization === escalation.category) {\n    score += 40;\n    factors.push('queue_specialization');\n  }\n\n  // 2. Capacidade disponível (peso: 30%)\n  const loadRatio = queue.current_load / queue.max_capacity;\n  if (loadRatio < 0.5) {\n    score += 30;\n    factors.push('low_queue_load');\n  } else if (loadRatio < 0.8) {\n    score += 15;\n    factors.push('medium_queue_load');\n  }\n\n  // 3. SLA target compatível (peso: 20%)\n  if (queue.sla_target_minutes <= escalation.estimated_resolution_time) {\n    score += 20;\n    factors.push('sla_compatible');\n  }\n\n  // 4. Peso de prioridade (peso: 10%)\n  score += queue.priority_weight * 0.1;\n  factors.push('priority_weight');\n\n  // Penalizações\n  if (!queue.auto_assignment_enabled) {\n    score -= 10;\n    factors.push('manual_assignment_penalty');\n  }\n\n  if (queue.business_hours_only && !context_data.business_hours) {\n    score -= 50;\n    factors.push('business_hours_penalty');\n  }\n\n  return {\n    queue_id: queue.queue_id,\n    score: Math.max(0, score),\n    factors: factors,\n    load_ratio: loadRatio,\n    can_accept: loadRatio < 1.0\n  };\n}\n\n// Calcular scores para todos os agentes\nconst agentScores = availableAgents.map(agent => \n  calculateAgentScore(agent, escalation_info, customer_data)\n).filter(score => score.can_handle)\n.sort((a, b) => b.score - a.score);\n\n// Calcular scores para todas as filas\nconst queueScores = activeQueues.map(queue => \n  calculateQueueScore(queue, escalation_info)\n).filter(score => score.can_accept)\n.sort((a, b) => b.score - a.score);\n\n// Determinar melhor roteamento\nlet routingDecision = {\n  routing_type: 'none',\n  confidence: 0,\n  reason: 'No suitable routing found'\n};\n\n// Priorizar agente direto para casos críticos ou VIP\nif ((escalation_info.urgency_level === 'critical' || customer_data.customer_tier === 'vip') \n    && agentScores.length > 0 && agentScores[0].score >= 50) {\n  \n  const bestAgent = availableAgents.find(a => a.agent_id === agentScores[0].agent_id);\n  routingDecision = {\n    routing_type: 'direct_agent',\n    target_agent_id: bestAgent.agent_id,\n    target_agent_name: bestAgent.name,\n    confidence: agentScores[0].score / 100,\n    reason: 'Direct assignment for critical/VIP case',\n    score_details: agentScores[0]\n  };\n\n} else if (queueScores.length > 0 && queueScores[0].score >= 30) {\n  // Roteamento para fila\n  const bestQueue = activeQueues.find(q => q.queue_id === queueScores[0].queue_id);\n  routingDecision = {\n    routing_type: 'queue',\n    target_queue_id: bestQueue.queue_id,\n    target_queue_name: bestQueue.name,\n    confidence: queueScores[0].score / 100,\n    reason: 'Queue assignment based on specialization and capacity',\n    score_details: queueScores[0],\n    suggested_agents: agentScores.slice(0, 3) // Top 3 agentes sugeridos\n  };\n\n} else if (agentScores.length > 0) {\n  // Fallback para melhor agente disponível\n  const bestAgent = availableAgents.find(a => a.agent_id === agentScores[0].agent_id);\n  routingDecision = {\n    routing_type: 'fallback_agent',\n    target_agent_id: bestAgent.agent_id,\n    target_agent_name: bestAgent.name,\n    confidence: Math.max(0.3, agentScores[0].score / 100),\n    reason: 'Fallback to best available agent',\n    score_details: agentScores[0]\n  };\n}\n\n// Preparar resultado final\nconst routingResult = {\n  routing_id: routingData.routing_id,\n  escalation_id: routingData.escalation_id,\n  \n  // Decisão de roteamento\n  routing_decision: routingDecision,\n  \n  // Análise detalhada\n  analysis: {\n    total_agents_evaluated: availableAgents.length,\n    total_queues_evaluated: activeQueues.length,\n    eligible_agents: agentScores.length,\n    eligible_queues: queueScores.length,\n    top_agent_scores: agentScores.slice(0, 5),\n    top_queue_scores: queueScores.slice(0, 3)\n  },\n  \n  // Contexto da decisão\n  decision_context: {\n    customer_tier: customer_data.customer_tier,\n    escalation_priority: escalation_info.priority,\n    urgency_level: escalation_info.urgency_level,\n    requires_specialist: escalation_info.requires_specialist,\n    business_hours: context_data.business_hours,\n    processing_time_ms: Date.now() - new Date(routingData.processing_timestamp).getTime()\n  },\n  \n  // Metadados\n  created_at: new Date().toISOString(),\n  algorithm_version: '2.0',\n  success: true\n};\n\nreturn routingResult;"}, "id": "calculate-routing-scores", "name": "🧮 Calculate Routing Scores", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [840, 300]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "has-routing", "leftValue": "={{ $json.routing_decision.routing_type }}", "rightValue": "none", "operator": {"type": "string", "operation": "notEquals"}}], "combineOperation": "all"}}, "id": "check-routing-success", "name": "✅ Routing Success?", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [1060, 300]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "UPDATE agent.escalations SET\n  assigned_agent_id = $1,\n  assigned_queue_id = $2,\n  status = CASE \n    WHEN $1 IS NOT NULL THEN 'assigned'\n    WHEN $2 IS NOT NULL THEN 'queued'\n    ELSE status\n  END,\n  routing_algorithm = $3,\n  routing_confidence = $4,\n  routing_reason = $5,\n  assigned_at = CASE \n    WHEN $1 IS NOT NULL OR $2 IS NOT NULL THEN NOW()\n    ELSE assigned_at\n  END,\n  updated_at = NOW()\nWHERE escalation_id = $6\nRETURNING *", "additionalFields": {"values": ["={{ $json.routing_decision.target_agent_id || null }}", "={{ $json.routing_decision.target_queue_id || null }}", "ai_intelligent_routing_v2", "={{ $json.routing_decision.confidence }}", "={{ $json.routing_decision.reason }}", "={{ $json.escalation_id }}"]}}, "id": "update-escalation-assignment", "name": "📝 Update Escalation Assignment", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [1280, 200]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "INSERT INTO agent.escalation_history (\n  escalation_id, previous_status, new_status, \n  previous_agent_id, new_agent_id,\n  previous_queue_id, new_queue_id,\n  change_reason, changed_by, metadata, created_at\n) VALUES (\n  $1, 'pending', $2, NULL, $3, NULL, $4, $5, 'ai_routing_system', $6, NOW()\n)", "additionalFields": {"values": ["={{ $json.escalation_id }}", "={{ $json.routing_decision.routing_type === 'direct_agent' || $json.routing_decision.routing_type === 'fallback_agent' ? 'assigned' : 'queued' }}", "={{ $json.routing_decision.target_agent_id || null }}", "={{ $json.routing_decision.target_queue_id || null }}", "={{ $json.routing_decision.reason }}", "={{ JSON.stringify($json) }}"]}}, "id": "log-routing-history", "name": "📊 Log Routing History", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [1500, 200]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "is-direct-assignment", "leftValue": "={{ $('calculate-routing-scores').first().json.routing_decision.routing_type }}", "rightValue": "direct_agent", "operator": {"type": "string", "operation": "equals"}}, {"id": "is-fallback-assignment", "leftValue": "={{ $('calculate-routing-scores').first().json.routing_decision.routing_type }}", "rightValue": "fallback_agent", "operator": {"type": "string", "operation": "equals"}}], "combineOperation": "any"}}, "id": "check-direct-assignment", "name": "👤 Direct Assignment?", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [1720, 200]}, {"parameters": {"url": "http://localhost:5678/webhook/agent-notification", "sendBody": true, "specifyBody": "json", "jsonBody": "={{ JSON.stringify({\n  notification_type: 'escalation_assigned',\n  agent_id: $('calculate-routing-scores').first().json.routing_decision.target_agent_id,\n  escalation_id: $('calculate-routing-scores').first().json.escalation_id,\n  priority: $('validate-routing-data').first().json.escalation_info.priority,\n  urgency: $('validate-routing-data').first().json.escalation_info.urgency_level,\n  customer_tier: $('validate-routing-data').first().json.customer_data.customer_tier,\n  assignment_reason: $('calculate-routing-scores').first().json.routing_decision.reason,\n  confidence: $('calculate-routing-scores').first().json.routing_decision.confidence\n}) }}", "options": {"timeout": 10000}}, "id": "notify-assigned-agent", "name": "📧 Notify Assigned Agent", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [1940, 100], "continueOnFail": true}, {"parameters": {"url": "http://localhost:5678/webhook/queue-notification", "sendBody": true, "specifyBody": "json", "jsonBody": "={{ JSON.stringify({\n  notification_type: 'escalation_queued',\n  queue_id: $('calculate-routing-scores').first().json.routing_decision.target_queue_id,\n  escalation_id: $('calculate-routing-scores').first().json.escalation_id,\n  priority: $('validate-routing-data').first().json.escalation_info.priority,\n  urgency: $('validate-routing-data').first().json.escalation_info.urgency_level,\n  customer_tier: $('validate-routing-data').first().json.customer_data.customer_tier,\n  suggested_agents: $('calculate-routing-scores').first().json.routing_decision.suggested_agents\n}) }}", "options": {"timeout": 10000}}, "id": "notify-queue-managers", "name": "📋 Notify Queue Managers", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [1940, 300], "continueOnFail": true}, {"parameters": {"jsCode": "// Preparar resposta de roteamento sem sucesso\nconst routingData = $('calculate-routing-scores').first().json;\n\n// Analisar por que o roteamento falhou\nconst analysis = routingData.analysis;\nlet failureReasons = [];\n\nif (analysis.total_agents_evaluated === 0) {\n  failureReasons.push('Nenhum agente disponível');\n} else if (analysis.eligible_agents === 0) {\n  failureReasons.push('Nenhum agente elegível para esta escalação');\n}\n\nif (analysis.total_queues_evaluated === 0) {\n  failureReasons.push('Nenhuma fila ativa');\n} else if (analysis.eligible_queues === 0) {\n  failureReasons.push('Nenhuma fila elegível para esta escalação');\n}\n\n// Sugestões de ação\nlet suggestions = [];\n\nif (analysis.eligible_agents === 0 && analysis.total_agents_evaluated > 0) {\n  suggestions.push('Considere relaxar critérios de especialização');\n  suggestions.push('Verifique disponibilidade de agentes');\n}\n\nif (analysis.eligible_queues === 0 && analysis.total_queues_evaluated > 0) {\n  suggestions.push('Considere criar fila genérica');\n  suggestions.push('Verifique configurações de horário de funcionamento');\n}\n\nif (analysis.total_agents_evaluated === 0) {\n  suggestions.push('Ativar agentes adicionais');\n  suggestions.push('Verificar status dos agentes');\n}\n\n// Preparar resposta de falha\nconst failureResponse = {\n  routing_id: routingData.routing_id,\n  escalation_id: routingData.escalation_id,\n  \n  routing_decision: {\n    routing_type: 'failed',\n    confidence: 0,\n    reason: 'Routing failed: ' + failureReasons.join(', ')\n  },\n  \n  failure_analysis: {\n    reasons: failureReasons,\n    suggestions: suggestions,\n    fallback_options: [\n      'Manual assignment by supervisor',\n      'Queue to general support',\n      'Schedule callback during business hours'\n    ]\n  },\n  \n  analysis: analysis,\n  decision_context: routingData.decision_context,\n  \n  created_at: new Date().toISOString(),\n  algorithm_version: '2.0',\n  success: false\n};\n\nreturn failureResponse;"}, "id": "handle-routing-failure", "name": "❌ Handle Routing Failure", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1280, 400]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "INSERT INTO agent.system_alerts (\n  alert_type, severity, title, description, \n  escalation_id, metadata, created_at\n) VALUES (\n  'routing_failure', 'high', 'Routing Failed', $1, $2, $3, NOW()\n)", "additionalFields": {"values": ["={{ $json.failure_analysis.reasons.join('; ') }}", "={{ $json.escalation_id }}", "={{ JSON.stringify($json) }}"]}}, "id": "create-failure-alert", "name": "🚨 Create Failure Alert", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [1500, 400]}, {"parameters": {"respondWith": "json", "responseBody": "={{ JSON.stringify($json) }}"}, "id": "return-routing-result", "name": "📤 Return Routing Result", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [2160, 200]}, {"parameters": {"respondWith": "json", "responseBody": "={{ JSON.stringify($json) }}"}, "id": "return-failure-result", "name": "❌ Return Failure Result", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1720, 400]}, {"parameters": {"path": "routing-optimization", "options": {"noResponseBody": false}}, "id": "optimization-webhook", "name": "⚡ Routing Optimization", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [180, 700], "webhookId": "routing-optimization-webhook"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "WITH routing_stats AS (\n  SELECT \n    assigned_agent_id,\n    assigned_queue_id,\n    routing_algorithm,\n    AVG(routing_confidence) as avg_confidence,\n    COUNT(*) as total_assignments,\n    AVG(resolution_time_minutes) as avg_resolution_time,\n    AVG(customer_satisfaction_score) as avg_satisfaction,\n    COUNT(CASE WHEN status = 'resolved' THEN 1 END) as resolved_count\n  FROM agent.escalations \n  WHERE created_at >= NOW() - INTERVAL '7 days'\n    AND routing_algorithm = 'ai_intelligent_routing_v2'\n  GROUP BY assigned_agent_id, assigned_queue_id, routing_algorithm\n),\nagent_performance AS (\n  SELECT \n    a.agent_id,\n    a.name,\n    rs.avg_confidence,\n    rs.total_assignments,\n    rs.avg_resolution_time,\n    rs.avg_satisfaction,\n    rs.resolved_count,\n    (rs.resolved_count::float / NULLIF(rs.total_assignments, 0)) as resolution_rate\n  FROM routing_stats rs\n  JOIN agent.agents a ON rs.assigned_agent_id = a.agent_id\n  WHERE rs.assigned_agent_id IS NOT NULL\n),\nqueue_performance AS (\n  SELECT \n    q.queue_id,\n    q.name,\n    rs.avg_confidence,\n    rs.total_assignments,\n    rs.avg_resolution_time,\n    rs.avg_satisfaction,\n    rs.resolved_count,\n    (rs.resolved_count::float / NULLIF(rs.total_assignments, 0)) as resolution_rate\n  FROM routing_stats rs\n  JOIN agent.queues q ON rs.assigned_queue_id = q.queue_id\n  WHERE rs.assigned_queue_id IS NOT NULL\n)\nSELECT \n  'agent_performance' as metric_type,\n  json_agg(agent_performance.*) as data\nFROM agent_performance\nUNION ALL\nSELECT \n  'queue_performance' as metric_type,\n  json_agg(queue_performance.*) as data\nFROM queue_performance", "additionalFields": {}}, "id": "analyze-routing-performance", "name": "📊 Analyze Routing Performance", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [400, 700]}, {"parameters": {"jsCode": "// Analisar performance e gerar recomendações de otimização\nconst performanceData = $input.all();\n\nlet agentPerformance = [];\nlet queuePerformance = [];\n\n// Separar dados por tipo\nperformanceData.forEach(row => {\n  if (row.json.metric_type === 'agent_performance') {\n    agentPerformance = row.json.data || [];\n  } else if (row.json.metric_type === 'queue_performance') {\n    queuePerformance = row.json.data || [];\n  }\n});\n\n// Analisar performance dos agentes\nconst agentAnalysis = {\n  total_agents: agentPerformance.length,\n  top_performers: agentPerformance\n    .filter(a => a.resolution_rate >= 0.8 && a.avg_satisfaction >= 4.0)\n    .sort((a, b) => b.avg_satisfaction - a.avg_satisfaction)\n    .slice(0, 5),\n  underperformers: agentPerformance\n    .filter(a => a.resolution_rate < 0.6 || a.avg_satisfaction < 3.0)\n    .sort((a, b) => a.avg_satisfaction - b.avg_satisfaction),\n  avg_confidence: agentPerformance.reduce((sum, a) => sum + (a.avg_confidence || 0), 0) / agentPerformance.length,\n  avg_resolution_time: agentPerformance.reduce((sum, a) => sum + (a.avg_resolution_time || 0), 0) / agentPerformance.length\n};\n\n// Analisar performance das filas\nconst queueAnalysis = {\n  total_queues: queuePerformance.length,\n  best_queues: queuePerformance\n    .filter(q => q.resolution_rate >= 0.8)\n    .sort((a, b) => b.avg_satisfaction - a.avg_satisfaction)\n    .slice(0, 3),\n  problematic_queues: queuePerformance\n    .filter(q => q.resolution_rate < 0.6 || q.avg_satisfaction < 3.0)\n    .sort((a, b) => a.avg_satisfaction - b.avg_satisfaction),\n  avg_confidence: queuePerformance.reduce((sum, q) => sum + (q.avg_confidence || 0), 0) / queuePerformance.length\n};\n\n// Gerar recomendações\nconst recommendations = [];\n\n// Recomendações para agentes\nif (agentAnalysis.underperformers.length > 0) {\n  recommendations.push({\n    type: 'agent_training',\n    priority: 'high',\n    description: `${agentAnalysis.underperformers.length} agentes precisam de treinamento adicional`,\n    affected_agents: agentAnalysis.underperformers.map(a => a.agent_id),\n    action: 'Providenciar treinamento focado em resolução e satisfação do cliente'\n  });\n}\n\nif (agentAnalysis.avg_confidence < 0.7) {\n  recommendations.push({\n    type: 'routing_algorithm',\n    priority: 'medium',\n    description: 'Confiança média do algoritmo de roteamento está baixa',\n    action: 'Revisar critérios de matching e pesos do algoritmo'\n  });\n}\n\nif (agentAnalysis.avg_resolution_time > 120) {\n  recommendations.push({\n    type: 'process_optimization',\n    priority: 'medium',\n    description: 'Tempo médio de resolução está acima do ideal',\n    action: 'Analisar gargalos no processo de resolução'\n  });\n}\n\n// Recomendações para filas\nif (queueAnalysis.problematic_queues.length > 0) {\n  recommendations.push({\n    type: 'queue_restructuring',\n    priority: 'high',\n    description: `${queueAnalysis.problematic_queues.length} filas com performance baixa`,\n    affected_queues: queueAnalysis.problematic_queues.map(q => q.queue_id),\n    action: 'Reavaliar especialização e capacidade das filas'\n  });\n}\n\n// Identificar oportunidades de melhoria\nconst improvements = [];\n\nif (agentAnalysis.top_performers.length > 0) {\n  improvements.push({\n    type: 'best_practices',\n    description: 'Documentar e compartilhar práticas dos top performers',\n    top_performers: agentAnalysis.top_performers.slice(0, 3)\n  });\n}\n\nif (queueAnalysis.best_queues.length > 0) {\n  improvements.push({\n    type: 'queue_optimization',\n    description: 'Replicar configurações das filas de melhor performance',\n    best_queues: queueAnalysis.best_queues\n  });\n}\n\n// Preparar resultado da otimização\nconst optimizationResult = {\n  optimization_id: require('crypto').randomUUID(),\n  analysis_period: '7_days',\n  \n  performance_analysis: {\n    agents: agentAnalysis,\n    queues: queueAnalysis\n  },\n  \n  recommendations: recommendations,\n  improvements: improvements,\n  \n  summary: {\n    total_recommendations: recommendations.length,\n    high_priority_items: recommendations.filter(r => r.priority === 'high').length,\n    overall_health_score: Math.min(100, Math.max(0, \n      (agentAnalysis.avg_confidence * 50) + \n      ((agentAnalysis.total_agents - agentAnalysis.underperformers.length) / agentAnalysis.total_agents * 30) +\n      ((queueAnalysis.total_queues - queueAnalysis.problematic_queues.length) / queueAnalysis.total_queues * 20)\n    ))\n  },\n  \n  generated_at: new Date().toISOString(),\n  algorithm_version: '2.0'\n};\n\nreturn optimizationResult;"}, "id": "generate-optimization-recommendations", "name": "🎯 Generate Optimization Recommendations", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [620, 700]}, {"parameters": {"respondWith": "json", "responseBody": "={{ JSON.stringify($json) }}"}, "id": "return-optimization-result", "name": "📊 Return Optimization Result", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [840, 700]}], "pinData": {}, "connections": {"routing-webhook": {"main": [[{"node": "validate-routing-data", "type": "main", "index": 0}]]}, "validate-routing-data": {"main": [[{"node": "get-available-agents", "type": "main", "index": 0}, {"node": "get-active-queues", "type": "main", "index": 0}]]}, "get-available-agents": {"main": [[{"node": "calculate-routing-scores", "type": "main", "index": 0}]]}, "get-active-queues": {"main": [[{"node": "calculate-routing-scores", "type": "main", "index": 0}]]}, "calculate-routing-scores": {"main": [[{"node": "check-routing-success", "type": "main", "index": 0}]]}, "check-routing-success": {"main": [[{"node": "update-escalation-assignment", "type": "main", "index": 0}], [{"node": "handle-routing-failure", "type": "main", "index": 0}]]}, "update-escalation-assignment": {"main": [[{"node": "log-routing-history", "type": "main", "index": 0}]]}, "log-routing-history": {"main": [[{"node": "check-direct-assignment", "type": "main", "index": 0}]]}, "check-direct-assignment": {"main": [[{"node": "notify-assigned-agent", "type": "main", "index": 0}], [{"node": "notify-queue-managers", "type": "main", "index": 0}]]}, "notify-assigned-agent": {"main": [[{"node": "return-routing-result", "type": "main", "index": 0}]]}, "notify-queue-managers": {"main": [[{"node": "return-routing-result", "type": "main", "index": 0}]]}, "handle-routing-failure": {"main": [[{"node": "create-failure-alert", "type": "main", "index": 0}]]}, "create-failure-alert": {"main": [[{"node": "return-failure-result", "type": "main", "index": 0}]]}, "optimization-webhook": {"main": [[{"node": "analyze-routing-performance", "type": "main", "index": 0}]]}, "analyze-routing-performance": {"main": [[{"node": "generate-optimization-recommendations", "type": "main", "index": 0}]]}, "generate-optimization-recommendations": {"main": [[{"node": "return-optimization-result", "type": "main", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1", "saveManualExecutions": true, "callerPolicy": "workflowsFromSameOwner", "errorWorkflow": "error-handler-workflow"}, "versionId": "2.0", "meta": {"templateCredsSetupCompleted": true, "instanceId": "escalation-ai-intelligent-routing"}, "id": "escalation-ai-intelligent-routing-v2", "tags": [{"createdAt": "2024-01-15T10:00:00.000Z", "updatedAt": "2024-01-15T10:00:00.000Z", "id": "escalation", "name": "escalation"}, {"createdAt": "2024-01-15T10:00:00.000Z", "updatedAt": "2024-01-15T10:00:00.000Z", "id": "ai", "name": "ai"}, {"createdAt": "2024-01-15T10:00:00.000Z", "updatedAt": "2024-01-15T10:00:00.000Z", "id": "routing", "name": "routing"}]}