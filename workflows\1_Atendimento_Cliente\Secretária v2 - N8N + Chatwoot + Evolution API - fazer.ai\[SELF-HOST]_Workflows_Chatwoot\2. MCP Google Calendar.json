{"nodes": [{"parameters": {"path": "db6bc79d-ba32-41c4-b492-f0f5bbcb8fd3"}, "type": "@n8n/n8n-nodes-langchain.mcpTrigger", "typeVersion": 1, "position": [-220, -100], "id": "8d857f5f-a380-4a75-a765-6714d703fad2", "name": "[MCP Server] Google Calendar", "webhookId": "db6bc79d-ba32-41c4-b492-f0f5bbcb8fd3"}, {"parameters": {"calendar": {"__rl": true, "value": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Calendar', ``, 'string') }}", "mode": "id"}, "start": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Start', ``, 'string') }}", "end": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('End', ``, 'string') }}", "additionalFields": {"description": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Description', ``, 'string') }}", "summary": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Summary', ``, 'string') }}"}}, "type": "n8n-nodes-base.googleCalendarTool", "typeVersion": 1.3, "position": [-320, 120], "id": "fe639381-bf0d-42a7-9414-ca3a374b17b6", "name": "Criar evento", "credentials": {}}, {"parameters": {"operation": "delete", "calendar": {"__rl": true, "value": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Calendar', ``, 'string') }}", "mode": "id"}, "eventId": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Event_ID', ``, 'string') }}", "options": {}}, "type": "n8n-nodes-base.googleCalendarTool", "typeVersion": 1.3, "position": [460, 120], "id": "6bfdc2ca-dd91-45ee-a349-99511cda671f", "name": "Deletar evento", "credentials": {}}, {"parameters": {"operation": "get", "calendar": {"__rl": true, "value": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Calendar', ``, 'string') }}", "mode": "id"}, "eventId": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Event_ID', ``, 'string') }}", "options": {}}, "type": "n8n-nodes-base.googleCalendarTool", "typeVersion": 1.3, "position": [-140, 120], "id": "72654351-0f04-4b2f-8791-5dd9ada09f39", "name": "Buscar evento", "credentials": {}}, {"parameters": {"descriptionType": "manual", "toolDescription": "Retorna eventos em uma período específico.\n\nAs datas devem obrigatoriamente informadas no formato completo.\n\nExemplo para buscar eventos no dia 01/01/2025\n\n- After: \"2025-01-01T00:00:00\"\n- Before: \"2025-01-01T23:59:59\"", "operation": "getAll", "calendar": {"__rl": true, "value": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Calendar', ``, 'string') }}", "mode": "id"}, "returnAll": true, "timeMin": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('After', ``, 'string') }}", "timeMax": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Before', ``, 'string') }}", "options": {}}, "type": "n8n-nodes-base.googleCalendarTool", "typeVersion": 1.3, "position": [60, 120], "id": "e052efac-8996-4f24-b268-d8edbb0265ed", "name": "Buscar todos os eventos", "credentials": {}}, {"parameters": {"operation": "update", "calendar": {"__rl": true, "value": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Calendar', ``, 'string') }}", "mode": "id"}, "eventId": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Event_ID', ``, 'string') }}", "updateFields": {"description": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Description', ``, 'string') }}", "end": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('End', ``, 'string') }}", "start": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Start', ``, 'string') }}", "summary": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Summary', ``, 'string') }}"}}, "type": "n8n-nodes-base.googleCalendarTool", "typeVersion": 1.3, "position": [260, 120], "id": "88f3fb3b-9388-4abf-90e9-cb20726e8e20", "name": "Atualizar evento", "credentials": {}}, {"parameters": {"content": "[![fazer.ai](https://framerusercontent.com/images/HqY9djLTzyutSKnuLLqBr92KbM.png?scale-down-to=256)](https://fazer.ai?utm_source=n8n&utm_campaign=sec-ep2&utm_medium=cw-2)\n\n## Esse é um template faça você mesmo do canal\n## <PERSON> Moreira\n\n### Inscreva-se no nosso canal no YouTube\n[![YouTube Lucas Moreira](https://img.shields.io/youtube/channel/subscribers/UCtmp6SxzLscu0GRTbgM8FTw?style=flat-square&logo=youtube&label=Inscreva-se&color=f00)](https://youtube.com/@eulucassmoreira?si=0lH7hwX9pukjhmPQ)\n\n### Siga nosso GitHub\n[![GitHub fazer.ai](https://img.shields.io/badge/github-%23121011.svg?style=for-the-badge&logo=github&logoColor=white&label)](https://github.com/fazer-ai)\n", "height": 440, "width": 550, "color": 7}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-440, -600], "id": "3d726e75-4dde-43e7-a7ef-500d326acf3b", "name": "Sticky Note10"}, {"parameters": {"content": "## Quer entender como funciona?\n\n\n### Assista o vídeo, deixe um like, e se inscreva no canal para ter acesso a mais workflows como esse!\n\n[![IMAGE ALT TEXT HERE](https://i1.ytimg.com/vi_webp/cvTWGNJGAu4/maxresdefault.webp)](https://www.youtube.com/watch?v=cvTWGNJGAu4)", "height": 440, "width": 500, "color": 7}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [120, -600], "id": "755b7506-7899-4fde-b8a0-70f2e2f1e41b", "name": "Sticky Note13"}, {"parameters": {"content": "![Chatwoot](https://app.chatwoot.com/brand-assets/logo_dark.svg)", "height": 100, "width": 280, "color": 7}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-220, -300], "id": "1934523f-c20a-4970-a779-c9930d3de28d", "name": "Sticky Note23"}, {"parameters": {"content": "## 2. MCP Google Calendar", "height": 80, "width": 540, "color": 5}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-440, -700], "id": "6aad6786-7e4b-433e-a65d-55eb8a44c32b", "name": "Sticky Note30"}], "connections": {"Criar evento": {"ai_tool": [[{"node": "[MCP Server] Google Calendar", "type": "ai_tool", "index": 0}]]}, "Deletar evento": {"ai_tool": [[{"node": "[MCP Server] Google Calendar", "type": "ai_tool", "index": 0}]]}, "Buscar evento": {"ai_tool": [[{"node": "[MCP Server] Google Calendar", "type": "ai_tool", "index": 0}]]}, "Buscar todos os eventos": {"ai_tool": [[{"node": "[MCP Server] Google Calendar", "type": "ai_tool", "index": 0}]]}, "Atualizar evento": {"ai_tool": [[{"node": "[MCP Server] Google Calendar", "type": "ai_tool", "index": 0}]]}}, "pinData": {}, "meta": {}}