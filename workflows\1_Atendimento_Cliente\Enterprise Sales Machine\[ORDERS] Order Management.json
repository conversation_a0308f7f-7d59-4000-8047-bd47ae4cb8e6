{"name": "[ORDERS] Order Management", "nodes": [{"parameters": {"path": "new-order", "options": {}}, "id": "webhook_trigger", "name": "TRIGGER: New Order Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [400, 300], "webhookId": "new-order", "notes": "Um único webhook para todos os marketplaces. Ex: .../webhook/new-order?from=meli"}, {"parameters": {"routing": {"rules": {"values": [{"value1": "={{$json.query.from}}", "value2": "meli"}, {"value1": "={{$json.query.from}}", "value2": "amazon"}]}}}, "id": "switch_source", "name": "SWITCH: Order Source", "type": "n8n-nodes-base.switch", "typeVersion": 2, "position": [620, 300]}, {"parameters": {"method": "GET", "url": "https://api.mercadolibre.com/orders/{{$json.body.resource.split('/').pop()}}", "options": {}}, "id": "get_meli_order", "name": "HTTP: Get Meli Order Details", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [840, 200], "credentials": {"httpHeaderAuth": {"id": "YOUR_MELI_OAUTH_CREDENTIAL_ID"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "WITH ins AS (\n   INSERT INTO agent.orders (marketplace_name, marketplace_order_id, order_status, order_details, total_amount, order_date)\n   VALUES ('Mercado Livre', $1, $2, $3::jsonb, $4, $5::timestamptz)\n   ON CONFLICT (marketplace_order_id) DO NOTHING\n   RETURNING id\n)\nSELECT id FROM ins;", "options": {"parameters": {"values": ["={{$json.id}}", "={{$json.status}}", "={{JSON.stringify($json.order_items)}}", "={{$json.total_amount}}", "={{$json.date_created}}"]}}}, "id": "log_order", "name": "DB: Log Order", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [1060, 200], "credentials": {"postgres": {"id": "YOUR_POSTGRES_CREDENTIAL_ID"}}}, {"parameters": {"functionCode": "const orderItems = $('get_meli_order').item.json.order_items;\nconst skus = [];\nconst quantities = [];\n\nfor (const item of orderItems) {\n  const sku = item.item.id; // Ajuste para o campo SKU correto da sua API\n  const quantity = item.quantity;\n  if (sku && quantity > 0) {\n    skus.push(sku);\n    quantities.push(quantity);\n  }\n}\n\nreturn [{\n  json: {\n    skus: skus,\n    quantities: quantities\n  }\n}];", "options": {}}, "id": "prepare_batch_update", "name": "CODE: Prepare Batch Stock Update", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1260, 200]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "UPDATE agent.products AS p\nSET stock_quantity = p.stock_quantity - u.quantity\nFROM (\n  SELECT\n    unnest($1::text[]) AS sku,\n    unnest($2::int[]) AS quantity\n) AS u\nWHERE p.sku = u.sku;", "options": {"parameters": {"values": ["={{$json.skus}}", "={{$json.quantities}}"]}}}, "id": "decrease_stock_atomic", "name": "DB: Decrease Stock (Atomic)", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [1460, 200], "continueOnFail": false, "notes": "O passo mais crítico: atualiza o estoque na fonte da verdade de forma atômica.", "credentials": {"postgres": {"id": "YOUR_POSTGRES_CREDENTIAL_ID"}}}, {"parameters": {"method": "POST", "url": "={{$credentials.slackWebhook.url}}", "sendBody": true, "body": "={{ ({ \"text\": `🚨 ALERTA CRÍTICO: Falha ao atualizar estoque!\\n\\nNão foi possível deduzir o estoque para o SKU: ${$('loop_items').item.json.item.sku} no pedido ${$('get_meli_order').item.json.id}. INTERVENÇÃO MANUAL NECESSÁRIA.` }) }}", "options": {}}, "id": "slack_notify_error", "name": "SLACK: Notify Stock Error", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [1460, 0], "credentials": {"httpHeaderAuth": {"id": "YOUR_SLACK_WEBHOOK_CREDENTIAL"}}}, {"parameters": {"workflowId": "YOUR_SYNC_WORKFLOW_ID"}, "id": "trigger_sync", "name": "TRIGGER: Sync All Channels", "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 2, "position": [1660, 200], "notes": "Inicia uma sincronização imediata para garantir consistência."}, {"parameters": {"workflowId": "YOUR_SEND_MESSAGE_ID", "parameters": {"values": {"string": [{"name": "recipientId", "value": "={{$json.buyer.phone.area_code + $json.buyer.phone.number}}"}, {"name": "messageContent", "value": "Olá {{$json.buyer.first_name}}! ✅ Recebemos seu pedido #{{$json.id}} do Mercado Livre e já estamos o preparando. Obrigado!"}]}}}, "id": "notify_customer", "name": "SUB: Notify Customer", "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 2, "position": [1860, 200]}], "connections": {"webhook_trigger": {"main": [[{"node": "switch_source", "type": "main", "index": 0}]]}, "switch_source": {"main": [[{"node": "get_meli_order", "type": "main", "index": 0}], []]}, "get_meli_order": {"main": [[{"node": "log_order", "type": "main", "index": 0}]]}, "log_order": {"main": [[{"node": "prepare_batch_update", "type": "main", "index": 0}]]}, "prepare_batch_update": {"main": [[{"node": "decrease_stock_atomic", "type": "main", "index": 0}]]}, "decrease_stock_atomic": {"main": [[{"node": "trigger_sync", "type": "main", "index": 0}]], "error": [[{"node": "slack_notify_error", "type": "error", "index": 0}]]}, "trigger_sync": {"main": [[{"node": "notify_customer", "type": "main", "index": 0}]]}}}