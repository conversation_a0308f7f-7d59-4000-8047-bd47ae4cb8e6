{"name": "[ESCALATION] 💬 Slack Integration Manager v2.0", "nodes": [{"parameters": {"path": "escalation-created", "options": {"noResponseBody": false}}, "id": "escalation-webhook", "name": "🚨 Escalation Created", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [180, 300], "webhookId": "escalation-created-webhook"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT e.*, c.slack_channel_id, c.slack_team_id, c.notification_settings \nFROM escalation.escalations e\nLEFT JOIN escalation.configurations c ON c.key = 'slack_integration'\nWHERE e.id = $1", "additionalFields": {"values": ["={{ $json.escalation_id }}"]}}, "id": "get-escalation-details", "name": "📋 Get Escalation Details", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [400, 300]}, {"parameters": {"jsCode": "// Processar dados da escalação e configurações do Slack\nconst webhookData = $input.first().json;\nconst escalationData = $input.last().json[0] || {};\n\n// Configurações padrão do Slack\nconst defaultConfig = {\n  slack_channel_id: process.env.SLACK_ESCALATION_CHANNEL || '#escalations',\n  slack_team_id: process.env.SLACK_TEAM_ID,\n  notification_settings: {\n    notify_on_create: true,\n    notify_on_assign: true,\n    notify_on_resolve: true,\n    notify_on_sla_breach: true,\n    mention_on_critical: true\n  }\n};\n\n// Mesclar configurações\nconst slackConfig = {\n  ...defaultConfig,\n  ...escalationData\n};\n\n// Determinar emoji e cor baseado na prioridade\nconst priorityConfig = {\n  critical: { emoji: '🔴', color: '#FF0000', urgency: 'CRÍTICA' },\n  high: { emoji: '🟠', color: '#FF8C00', urgency: 'ALTA' },\n  medium: { emoji: '🟡', color: '#FFD700', urgency: 'MÉDIA' },\n  low: { emoji: '🟢', color: '#32CD32', urgency: 'BAIXA' }\n};\n\nconst priority = escalationData.priority || 'medium';\nconst priorityInfo = priorityConfig[priority] || priorityConfig.medium;\n\n// Determinar canal baseado na prioridade\nlet targetChannel = slackConfig.slack_channel_id;\nif (priority === 'critical' && process.env.SLACK_CRITICAL_CHANNEL) {\n  targetChannel = process.env.SLACK_CRITICAL_CHANNEL;\n}\n\n// Calcular tempo restante do SLA\nconst dueAt = new Date(escalationData.due_at);\nconst now = new Date();\nconst timeRemaining = Math.max(0, dueAt - now);\nconst minutesRemaining = Math.floor(timeRemaining / (1000 * 60));\nconst hoursRemaining = Math.floor(minutesRemaining / 60);\nconst remainingMinutes = minutesRemaining % 60;\n\nlet slaText = '';\nif (timeRemaining > 0) {\n  if (hoursRemaining > 0) {\n    slaText = `${hoursRemaining}h ${remainingMinutes}m restantes`;\n  } else {\n    slaText = `${remainingMinutes}m restantes`;\n  }\n} else {\n  slaText = '⚠️ SLA VENCIDO';\n}\n\n// Determinar menções\nlet mentions = '';\nif (priority === 'critical' && slackConfig.notification_settings.mention_on_critical) {\n  mentions = '<!channel> ';\n}\n\n// Preparar dados do cliente\nconst customerInfo = {\n  name: escalationData.customer_name || 'Cliente não identificado',\n  email: escalationData.customer_email || 'N/A',\n  phone: escalationData.customer_phone || 'N/A',\n  id: escalationData.customer_id || 'N/A'\n};\n\n// Preparar informações da fonte\nconst sourceInfo = {\n  channel: escalationData.channel || 'unknown',\n  source: escalationData.source || 'unknown',\n  external_id: escalationData.external_id || 'N/A'\n};\n\n// Preparar análise de IA (se disponível)\nconst aiInfo = {\n  sentiment_score: escalationData.ai_sentiment_score || 0,\n  emotion: escalationData.ai_emotion || 'neutral',\n  urgency_level: escalationData.ai_urgency_level || 'medium',\n  confidence: escalationData.ai_confidence || 0\n};\n\n// Preparar texto do sentimento\nlet sentimentText = '';\nif (aiInfo.sentiment_score < -0.5) {\n  sentimentText = '😡 Muito Negativo';\n} else if (aiInfo.sentiment_score < -0.2) {\n  sentimentText = '😞 Negativo';\n} else if (aiInfo.sentiment_score > 0.5) {\n  sentimentText = '😊 Muito Positivo';\n} else if (aiInfo.sentiment_score > 0.2) {\n  sentimentText = '🙂 Positivo';\n} else {\n  sentimentText = '😐 Neutro';\n}\n\n// Preparar blocos do Slack\nconst slackBlocks = [\n  {\n    \"type\": \"header\",\n    \"text\": {\n      \"type\": \"plain_text\",\n      \"text\": `${priorityInfo.emoji} Nova Escalação - Prioridade ${priorityInfo.urgency}`\n    }\n  },\n  {\n    \"type\": \"section\",\n    \"fields\": [\n      {\n        \"type\": \"mrkdwn\",\n        \"text\": `*ID da Escalação:*\\n\\`${escalationData.id}\\``\n      },\n      {\n        \"type\": \"mrkdwn\",\n        \"text\": `*SLA:*\\n${slaText}`\n      },\n      {\n        \"type\": \"mrkdwn\",\n        \"text\": `*Categoria:*\\n${escalationData.category || 'Geral'}`\n      },\n      {\n        \"type\": \"mrkdwn\",\n        \"text\": `*Canal:*\\n${sourceInfo.channel.toUpperCase()}`\n      }\n    ]\n  },\n  {\n    \"type\": \"section\",\n    \"text\": {\n      \"type\": \"mrkdwn\",\n      \"text\": `*Título:*\\n${escalationData.title || 'Sem título'}`\n    }\n  },\n  {\n    \"type\": \"section\",\n    \"text\": {\n      \"type\": \"mrkdwn\",\n      \"text\": `*Descrição:*\\n${escalationData.description || 'Sem descrição'}`\n    }\n  },\n  {\n    \"type\": \"divider\"\n  },\n  {\n    \"type\": \"section\",\n    \"text\": {\n      \"type\": \"mrkdwn\",\n      \"text\": `*👤 Informações do Cliente*`\n    },\n    \"fields\": [\n      {\n        \"type\": \"mrkdwn\",\n        \"text\": `*Nome:*\\n${customerInfo.name}`\n      },\n      {\n        \"type\": \"mrkdwn\",\n        \"text\": `*Email:*\\n${customerInfo.email}`\n      },\n      {\n        \"type\": \"mrkdwn\",\n        \"text\": `*Telefone:*\\n${customerInfo.phone}`\n      },\n      {\n        \"type\": \"mrkdwn\",\n        \"text\": `*ID Cliente:*\\n${customerInfo.id}`\n      }\n    ]\n  }\n];\n\n// Adicionar análise de IA se disponível\nif (aiInfo.confidence > 0.3) {\n  slackBlocks.push({\n    \"type\": \"divider\"\n  });\n  slackBlocks.push({\n    \"type\": \"section\",\n    \"text\": {\n      \"type\": \"mrkdwn\",\n      \"text\": `*🤖 Análise de IA*`\n    },\n    \"fields\": [\n      {\n        \"type\": \"mrkdwn\",\n        \"text\": `*Sentimento:*\\n${sentimentText}`\n      },\n      {\n        \"type\": \"mrkdwn\",\n        \"text\": `*Emoção:*\\n${aiInfo.emotion}`\n      },\n      {\n        \"type\": \"mrkdwn\",\n        \"text\": `*Urgência IA:*\\n${aiInfo.urgency_level}`\n      },\n      {\n        \"type\": \"mrkdwn\",\n        \"text\": `*Confiança:*\\n${Math.round(aiInfo.confidence * 100)}%`\n      }\n    ]\n  });\n}\n\n// Adicionar botões de ação\nslackBlocks.push({\n  \"type\": \"divider\"\n});\nslackBlocks.push({\n  \"type\": \"actions\",\n  \"elements\": [\n    {\n      \"type\": \"button\",\n      \"text\": {\n        \"type\": \"plain_text\",\n        \"text\": \"🙋‍♂️ Assumir\"\n      },\n      \"style\": \"primary\",\n      \"value\": escalationData.id,\n      \"action_id\": \"assign_escalation\"\n    },\n    {\n      \"type\": \"button\",\n      \"text\": {\n        \"type\": \"plain_text\",\n        \"text\": \"📋 Detalhes\"\n      },\n      \"value\": escalationData.id,\n      \"action_id\": \"view_escalation_details\"\n    },\n    {\n      \"type\": \"button\",\n      \"text\": {\n        \"type\": \"plain_text\",\n        \"text\": \"💬 Responder\"\n      },\n      \"value\": escalationData.id,\n      \"action_id\": \"respond_escalation\"\n    }\n  ]\n});\n\n// Se for crítico, adicionar botão de escalação adicional\nif (priority === 'critical') {\n  slackBlocks[slackBlocks.length - 1].elements.push({\n    \"type\": \"button\",\n    \"text\": {\n      \"type\": \"plain_text\",\n      \"text\": \"🚨 Escalar Supervisor\"\n    },\n    \"style\": \"danger\",\n    \"value\": escalationData.id,\n    \"action_id\": \"escalate_to_supervisor\"\n  });\n}\n\n// Preparar resultado\nconst result = {\n  // Dados da escalação\n  escalation: escalationData,\n  webhook_data: webhookData,\n  \n  // Configurações do Slack\n  slack_config: slackConfig,\n  target_channel: targetChannel,\n  \n  // Dados da mensagem\n  message_data: {\n    channel: targetChannel,\n    text: `${mentions}${priorityInfo.emoji} Nova escalação de prioridade ${priorityInfo.urgency}`,\n    blocks: slackBlocks,\n    attachments: [\n      {\n        color: priorityInfo.color,\n        fields: [\n          {\n            title: \"Score de Urgência\",\n            value: escalationData.urgency_score || 0,\n            short: true\n          },\n          {\n            title: \"Criado em\",\n            value: new Date(escalationData.created_at).toLocaleString('pt-BR'),\n            short: true\n          }\n        ]\n      }\n    ]\n  },\n  \n  // Metadados\n  priority_info: priorityInfo,\n  customer_info: customerInfo,\n  source_info: sourceInfo,\n  ai_info: aiInfo,\n  sla_info: {\n    due_at: escalationData.due_at,\n    minutes_remaining: minutesRemaining,\n    is_overdue: timeRemaining <= 0,\n    sla_text: slaText\n  },\n  \n  // Dados para logging\n  processing_info: {\n    processed_at: new Date().toISOString(),\n    integration_version: '2.0',\n    target_channel: targetChannel,\n    mentions_used: mentions.length > 0\n  }\n};\n\nreturn result;"}, "id": "prepare-slack-message", "name": "📝 Prepare <PERSON>ck Message", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [620, 300]}, {"parameters": {"authentication": "oAuth2", "select": "channel", "channelId": {"__rl": true, "value": "={{ $json.target_channel }}", "mode": "name"}, "text": "={{ $json.message_data.text }}", "otherOptions": {"blocks": "={{ JSON.stringify($json.message_data.blocks) }}", "attachments": "={{ JSON.stringify($json.message_data.attachments) }}"}}, "id": "send-slack-notification", "name": "💬 Send Slack Notification", "type": "n8n-nodes-base.slack", "typeVersion": 2.1, "position": [840, 300]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "UPDATE escalation.escalations SET \n  slack_message_ts = $1,\n  slack_channel_id = $2,\n  updated_at = NOW()\nWHERE id = $3", "additionalFields": {"values": ["={{ $json.ts }}", "={{ $('prepare-slack-message').first().json.target_channel }}", "={{ $('prepare-slack-message').first().json.escalation.id }}"]}}, "id": "update-escalation-slack-info", "name": "🔄 Update Escalation Slack Info", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [1060, 300]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "INSERT INTO agent.integration_logs (\n  integration_name, integration_type, operation, direction,\n  endpoint, http_method, request_data, response_data,\n  status_code, success, processing_time_ms, escalation_id,\n  correlation_id, metadata, created_at\n) VALUES (\n  'slack', 'notification_platform', 'send_notification', 'outbound',\n  '/api/chat.postMessage', 'POST', $1, $2, 200, true,\n  $3, $4, $5, $6, NOW()\n)", "additionalFields": {"values": ["={{ JSON.stringify($('prepare-slack-message').first().json.message_data) }}", "={{ JSON.stringify($json) }}", "={{ Date.now() - new Date($('prepare-slack-message').first().json.processing_info.processed_at).getTime() }}", "={{ $('prepare-slack-message').first().json.escalation.id }}", "={{ 'slack_' + $('prepare-slack-message').first().json.escalation.id }}", "={{ JSON.stringify({\n              notification_sent: true,\n              channel: $('prepare-slack-message').first().json.target_channel,\n              priority: $('prepare-slack-message').first().json.escalation.priority,\n              mentions_used: $('prepare-slack-message').first().json.processing_info.mentions_used,\n              message_ts: $json.ts\n            }) }}"]}}, "id": "log-slack-success", "name": "📝 Log S<PERSON>ck Success", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [1280, 300]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "is-critical-priority", "leftValue": "={{ $('prepare-slack-message').first().json.escalation.priority }}", "rightValue": "critical", "operator": {"type": "string", "operation": "equals"}}], "combineOperation": "all"}}, "id": "check-critical-priority", "name": "🔴 Is Critical?", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [1500, 300]}, {"parameters": {"jsCode": "// Preparar notificação adicional para escalações críticas\nconst escalationData = $('prepare-slack-message').first().json.escalation;\nconst slackResponse = $('send-slack-notification').first().json;\n\n// Preparar lista de supervisores/gerentes para notificar\nconst supervisors = (process.env.SLACK_SUPERVISORS || '').split(',').filter(s => s.trim());\nconst criticalChannel = process.env.SLACK_CRITICAL_CHANNEL || '#critical-escalations';\n\n// Preparar mensagem para supervisores\nconst supervisorMessage = {\n  channel: criticalChannel,\n  text: `🚨 ESCALAÇÃO CRÍTICA REQUER ATENÇÃO IMEDIATA`,\n  blocks: [\n    {\n      \"type\": \"header\",\n      \"text\": {\n        \"type\": \"plain_text\",\n        \"text\": \"🚨 ESCALAÇÃO CRÍTICA - AÇÃO IMEDIATA NECESSÁRIA\"\n      }\n    },\n    {\n      \"type\": \"section\",\n      \"text\": {\n        \"type\": \"mrkdwn\",\n        \"text\": `Uma escalação crítica foi criada e requer atenção imediata dos supervisores.`\n      }\n    },\n    {\n      \"type\": \"section\",\n      \"fields\": [\n        {\n          \"type\": \"mrkdwn\",\n          \"text\": `*ID da Escalação:*\\n\\`${escalationData.id}\\``\n        },\n        {\n          \"type\": \"mrkdwn\",\n          \"text\": `*Cliente:*\\n${escalationData.customer_name}`\n        },\n        {\n          \"type\": \"mrkdwn\",\n          \"text\": `*Score de Urgência:*\\n${escalationData.urgency_score}`\n        },\n        {\n          \"type\": \"mrkdwn\",\n          \"text\": `*SLA:*\\n${escalationData.sla_minutes} minutos`\n        }\n      ]\n    },\n    {\n      \"type\": \"section\",\n      \"text\": {\n        \"type\": \"mrkdwn\",\n        \"text\": `*Link da Mensagem Original:*\\n<https://slack.com/app_redirect?channel=${$('prepare-slack-message').first().json.target_channel}&message_ts=${slackResponse.ts}|Ver Escalação>`\n      }\n    },\n    {\n      \"type\": \"actions\",\n      \"elements\": [\n        {\n          \"type\": \"button\",\n          \"text\": {\n            \"type\": \"plain_text\",\n            \"text\": \"🚨 Assumir Supervisão\"\n          },\n          \"style\": \"danger\",\n          \"value\": escalationData.id,\n          \"action_id\": \"supervisor_assign\"\n        },\n        {\n          \"type\": \"button\",\n          \"text\": {\n            \"type\": \"plain_text\",\n            \"text\": \"📞 Contatar Cliente\"\n          },\n          \"value\": escalationData.id,\n          \"action_id\": \"contact_customer\"\n        }\n      ]\n    }\n  ]\n};\n\n// Preparar mensagens diretas para supervisores\nconst directMessages = supervisors.map(supervisor => ({\n  channel: supervisor.trim(),\n  text: `🚨 ESCALAÇÃO CRÍTICA: ${escalationData.title}`,\n  blocks: [\n    {\n      \"type\": \"section\",\n      \"text\": {\n        \"type\": \"mrkdwn\",\n        \"text\": `🚨 *ESCALAÇÃO CRÍTICA REQUER SUA ATENÇÃO*\\n\\n*ID:* \\`${escalationData.id}\\`\\n*Cliente:* ${escalationData.customer_name}\\n*Urgência:* ${escalationData.urgency_score}/100\\n\\n<https://slack.com/app_redirect?channel=${$('prepare-slack-message').first().json.target_channel}&message_ts=${slackResponse.ts}|🔗 Ver Escalação Completa>`\n      }\n    }\n  ]\n}));\n\nreturn {\n  supervisor_message: supervisorMessage,\n  direct_messages: directMessages,\n  escalation_id: escalationData.id,\n  original_message_ts: slackResponse.ts,\n  supervisors_count: supervisors.length\n};"}, "id": "prepare-critical-notifications", "name": "🚨 Prepare Critical Notifications", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1720, 200]}, {"parameters": {"authentication": "oAuth2", "select": "channel", "channelId": {"__rl": true, "value": "={{ $json.supervisor_message.channel }}", "mode": "name"}, "text": "={{ $json.supervisor_message.text }}", "otherOptions": {"blocks": "={{ JSON.stringify($json.supervisor_message.blocks) }}"}}, "id": "send-supervisor-notification", "name": "👨‍💼 Send Supervisor Notification", "type": "n8n-nodes-base.slack", "typeVersion": 2.1, "position": [1940, 200]}, {"parameters": {"batchSize": 1, "options": {}}, "id": "split-direct-messages", "name": "📤 Split Direct Messages", "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [1720, 400]}, {"parameters": {"authentication": "oAuth2", "select": "channel", "channelId": {"__rl": true, "value": "={{ $json.channel }}", "mode": "name"}, "text": "={{ $json.text }}", "otherOptions": {"blocks": "={{ JSON.stringify($json.blocks) }}"}}, "id": "send-direct-message", "name": "📩 Send Direct Message", "type": "n8n-nodes-base.slack", "typeVersion": 2.1, "position": [1940, 400]}, {"parameters": {"path": "slack-interaction", "options": {"noResponseBody": false}}, "id": "slack-interaction-webhook", "name": "⚡ Slack Interaction", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [180, 600], "webhookId": "slack-interaction-webhook"}, {"parameters": {"jsCode": "// Processar interação do Slack\nconst payload = JSON.parse($json.payload);\nconst action = payload.actions[0];\nconst user = payload.user;\nconst channel = payload.channel;\nconst message = payload.message;\n\n// Extrair dados da ação\nconst actionId = action.action_id;\nconst escalationId = action.value;\nconst userId = user.id;\nconst userName = user.name;\n\n// Determinar tipo de ação\nlet actionType = '';\nlet actionDescription = '';\n\nswitch (actionId) {\n  case 'assign_escalation':\n    actionType = 'assign';\n    actionDescription = 'Escalação assumida';\n    break;\n  case 'view_escalation_details':\n    actionType = 'view_details';\n    actionDescription = 'Detalhes visualizados';\n    break;\n  case 'respond_escalation':\n    actionType = 'respond';\n    actionDescription = 'Resposta iniciada';\n    break;\n  case 'escalate_to_supervisor':\n    actionType = 'escalate_supervisor';\n    actionDescription = 'Escalado para supervisor';\n    break;\n  case 'supervisor_assign':\n    actionType = 'supervisor_assign';\n    actionDescription = 'Supervisão assumida';\n    break;\n  case 'contact_customer':\n    actionType = 'contact_customer';\n    actionDescription = 'Contato com cliente iniciado';\n    break;\n  default:\n    actionType = 'unknown';\n    actionDescription = 'Ação desconhecida';\n}\n\nreturn {\n  escalation_id: escalationId,\n  action_type: actionType,\n  action_description: actionDescription,\n  user_id: userId,\n  user_name: userName,\n  channel_id: channel.id,\n  channel_name: channel.name,\n  message_ts: message.ts,\n  interaction_payload: payload,\n  processed_at: new Date().toISOString()\n};"}, "id": "process-slack-interaction", "name": "⚙️ Process Slack Interaction", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [400, 600]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "is-assign-action", "leftValue": "={{ $json.action_type }}", "rightValue": "assign", "operator": {"type": "string", "operation": "equals"}}], "combineOperation": "all"}}, "id": "check-assign-action", "name": "🙋‍♂️ Is Assign Action?", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [620, 600]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "UPDATE escalation.escalations SET \n  assigned_agent_id = $1,\n  assigned_agent_name = $2,\n  assigned_at = NOW(),\n  status = 'in_progress',\n  updated_at = NOW()\nWHERE id = $3 AND status = 'pending'", "additionalFields": {"values": ["={{ $json.user_id }}", "={{ $json.user_name }}", "={{ $json.escalation_id }}"]}}, "id": "assign-escalation", "name": "👤 Assign Escalation", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [840, 500]}, {"parameters": {"authentication": "oAuth2", "resource": "message", "operation": "update", "channelId": {"__rl": true, "value": "={{ $('process-slack-interaction').first().json.channel_id }}", "mode": "id"}, "timestamp": "={{ $('process-slack-interaction').first().json.message_ts }}", "text": "✅ Escalação assumida por {{ $('process-slack-interaction').first().json.user_name }}", "updateFields": {"attachments": "[{\"color\": \"good\", \"text\": \"👤 Agente: {{ $('process-slack-interaction').first().json.user_name }}\\n⏰ Assumido em: {{ new Date().toLocaleString('pt-BR') }}\"}]"}}, "id": "update-slack-message-assigned", "name": "✅ Update Message - Assigned", "type": "n8n-nodes-base.slack", "typeVersion": 2.1, "position": [1060, 500]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "INSERT INTO agent.integration_logs (\n  integration_name, integration_type, operation, direction,\n  endpoint, http_method, request_data, response_data,\n  status_code, success, escalation_id, correlation_id,\n  metadata, created_at\n) VALUES (\n  'slack', 'notification_platform', 'interaction_received', 'inbound',\n  '/webhook/slack-interaction', 'POST', $1, $2, 200, true,\n  $3, $4, $5, NOW()\n)", "additionalFields": {"values": ["={{ JSON.stringify($('process-slack-interaction').first().json.interaction_payload) }}", "={{ JSON.stringify({\n              action_processed: true,\n              action_type: $('process-slack-interaction').first().json.action_type,\n              user_name: $('process-slack-interaction').first().json.user_name\n            }) }}", "={{ $('process-slack-interaction').first().json.escalation_id }}", "={{ 'slack_interaction_' + $('process-slack-interaction').first().json.escalation_id }}", "={{ JSON.stringify({\n              interaction_processed: true,\n              action_type: $('process-slack-interaction').first().json.action_type,\n              user_id: $('process-slack-interaction').first().json.user_id,\n              channel_id: $('process-slack-interaction').first().json.channel_id\n            }) }}"]}}, "id": "log-slack-interaction", "name": "📝 Log Slack Interaction", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [620, 800]}, {"parameters": {"respondWith": "json", "responseBody": "={\n  \"status\": \"success\",\n  \"message\": \"Slack notification sent successfully\",\n  \"escalation_id\": \"{{ $('prepare-slack-message').first().json.escalation.id }}\",\n  \"slack_message_ts\": \"{{ $('send-slack-notification').first().json.ts }}\",\n  \"channel\": \"{{ $('prepare-slack-message').first().json.target_channel }}\",\n  \"processed_at\": \"{{ new Date().toISOString() }}\"\n}"}, "id": "success-response", "name": "✅ Success Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1500, 500]}, {"parameters": {"respondWith": "json", "responseBody": "={\n  \"status\": \"success\",\n  \"message\": \"Interaction processed\",\n  \"action_type\": \"{{ $('process-slack-interaction').first().json.action_type }}\",\n  \"escalation_id\": \"{{ $('process-slack-interaction').first().json.escalation_id }}\",\n  \"processed_at\": \"{{ new Date().toISOString() }}\"\n}"}, "id": "interaction-response", "name": "⚡ Interaction Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [840, 800]}], "pinData": {}, "connections": {"escalation-webhook": {"main": [[{"node": "get-escalation-details", "type": "main", "index": 0}]]}, "get-escalation-details": {"main": [[{"node": "prepare-slack-message", "type": "main", "index": 0}]]}, "prepare-slack-message": {"main": [[{"node": "send-slack-notification", "type": "main", "index": 0}]]}, "send-slack-notification": {"main": [[{"node": "update-escalation-slack-info", "type": "main", "index": 0}]]}, "update-escalation-slack-info": {"main": [[{"node": "log-slack-success", "type": "main", "index": 0}]]}, "log-slack-success": {"main": [[{"node": "check-critical-priority", "type": "main", "index": 0}]]}, "check-critical-priority": {"main": [[{"node": "prepare-critical-notifications", "type": "main", "index": 0}], [{"node": "success-response", "type": "main", "index": 0}]]}, "prepare-critical-notifications": {"main": [[{"node": "send-supervisor-notification", "type": "main", "index": 0}, {"node": "split-direct-messages", "type": "main", "index": 0}]]}, "send-supervisor-notification": {"main": [[{"node": "success-response", "type": "main", "index": 0}]]}, "split-direct-messages": {"main": [[{"node": "send-direct-message", "type": "main", "index": 0}]]}, "send-direct-message": {"main": [[{"node": "success-response", "type": "main", "index": 0}]]}, "slack-interaction-webhook": {"main": [[{"node": "process-slack-interaction", "type": "main", "index": 0}]]}, "process-slack-interaction": {"main": [[{"node": "check-assign-action", "type": "main", "index": 0}, {"node": "log-slack-interaction", "type": "main", "index": 0}]]}, "check-assign-action": {"main": [[{"node": "assign-escalation", "type": "main", "index": 0}], [{"node": "interaction-response", "type": "main", "index": 0}]]}, "assign-escalation": {"main": [[{"node": "update-slack-message-assigned", "type": "main", "index": 0}]]}, "update-slack-message-assigned": {"main": [[{"node": "interaction-response", "type": "main", "index": 0}]]}, "log-slack-interaction": {"main": [[{"node": "interaction-response", "type": "main", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1", "saveManualExecutions": true, "callerPolicy": "workflowsFromSameOwner", "errorWorkflow": "error-handler-workflow"}, "versionId": "2.0", "meta": {"templateCredsSetupCompleted": true, "instanceId": "escalation-slack-integration"}, "id": "escalation-slack-integration-v2", "tags": [{"createdAt": "2024-01-15T10:00:00.000Z", "updatedAt": "2024-01-15T10:00:00.000Z", "id": "escalation", "name": "escalation"}, {"createdAt": "2024-01-15T10:00:00.000Z", "updatedAt": "2024-01-15T10:00:00.000Z", "id": "slack", "name": "slack"}, {"createdAt": "2024-01-15T10:00:00.000Z", "updatedAt": "2024-01-15T10:00:00.000Z", "id": "integration", "name": "integration"}]}