# 🔍 DIAGNÓSTICO COMPLETO DO INSTALL.LOG - Sistema N8N + Evolution API + Chatwoot

**Data da Instalação:** 05/07/2025 14:32  
**Ambiente:** Windows 10 com Docker Desktop  
**Arquitetura:** Docker Compose multi-serviços  

---

## 📊 RESUMO EXECUTIVO

### ✅ **Serviços Funcionais**
- **PostgreSQL**: ✅ Healthy (postgres_aula)
- **Redis**: ✅ Healthy (redis_aula) 
- **MinIO**: ✅ Running (minio_aula)
- **Chatwoot Rails**: ✅ Running (chatwoot-rails-1)
- **Chatwoot Sidekiq**: ✅ Running (chatwoot-sidekiq-1)
- **N8N Editor**: ✅ Running (n8n_editor-1)

### ⚠️ **Serviços com Problemas**
- **Evolution API**: 🔴 Unhealthy (evolution_aula)

### 🔥 **Erros Críticos Identificados**

1. **[CRÍTICO] Evolution API - Erro 401 (Unauthorized)**
2. **[CRÍTICO] Evolution Webhook - Erro 404 (Not Found)**
3. **[FATAL] MinIO Setup - Erro 400 + Comando Docker Malformado**
4. **[ERRO] PostgreSQL - Databases Inexistentes**

---

## 🚨 ANÁLISE DETALHADA DOS ERROS

### 1. **ERRO CRÍTICO: Evolution API - Autenticação Falhou (401)**

**📍 Localização:** Linhas 8-21 do install.log  
**⚠️ Severidade:** CRÍTICA  

```log
[ERROR][Evolution-Instance] Erro ao criar instância na Evolution API: 
Response status code does not indicate success: 401 (Unauthorized).
```

**🔍 Causa Raiz:**
- A chave de autenticação `AUTHENTICATION_API_KEY` no arquivo `.env` está incorreta, expirada ou não foi configurada adequadamente na Evolution API
- A Evolution API pode não estar aceitando a chave fornecida devido a configuração interna

**🛠️ Soluções Robustas:**

#### Solução A: Verificação e Regeneração de Credenciais
```powershell
# 1. Verificar se a variável está definida no .env
Get-Content .env | Select-String "AUTHENTICATION_API_KEY"

# 2. Gerar nova chave API segura
$newApiKey = [System.Convert]::ToBase64String([System.Text.Encoding]::UTF8.GetBytes((New-Guid).ToString() + (Get-Date).Ticks))
Write-Host "Nova API Key: $newApiKey"

# 3. Atualizar .env com nova chave
(Get-Content .env) -replace "AUTHENTICATION_API_KEY=.*", "AUTHENTICATION_API_KEY=$newApiKey" | Set-Content .env
```

#### Solução B: Configuração Manual da Evolution API
```bash
# 1. Acessar o contêiner da Evolution API
docker exec -it evolution_aula /bin/bash

# 2. Verificar configurações internas
cat /evolution/.env

# 3. Reiniciar o serviço após correção
docker restart evolution_aula
```

### 2. **ERRO CRÍTICO: Evolution Webhook - Endpoint Não Encontrado (404)**

**📍 Localização:** Linhas 22-35 do install.log  
**⚠️ Severidade:** CRÍTICA  

```log
[ERROR][Evolution-Webhook] Erro ao configurar Webhook na Evolution Manager: 
Response status code does not indicate success: 404 (Not Found).
```

**🔍 Causa Raiz:**
- O endpoint `/webhook/set` não existe na versão atual da Evolution API
- A URL base da API está incorreta
- A Evolution API ainda não terminou de inicializar completamente

**🛠️ Soluções Robustas:**

#### Solução A: Verificar Endpoints Disponíveis
```powershell
# Testar conectividade básica da Evolution API
$headers = @{ "X-API-Key" = $env:AUTHENTICATION_API_KEY }
try {
    $response = Invoke-RestMethod -Uri "http://localhost:8080" -Headers $headers
    Write-Host "API Respondeu: $response"
} catch {
    Write-Host "Erro na API: $($_.Exception.Message)"
}

# Listar endpoints disponíveis
curl -H "X-API-Key: $env:AUTHENTICATION_API_KEY" http://localhost:8080/docs
```

#### Solução B: Configuração Manual do Webhook
```yaml
# Configurar webhook manualmente via interface web da Evolution API
# URL: http://localhost:8080
# Webhook URL: http://host.docker.internal:5678/api/n8n-evolution
# Eventos: MESSAGERS_UPSERT
# Base64: Habilitado
```

### 3. **ERRO FATAL: MinIO Setup - Comando Docker Malformado**

**📍 Localização:** Linhas 46-54 do install.log  
**⚠️ Severidade:** FATAL (Causou aborto do script)  

```log
[DEBUG][MinIO-Setup] docker: 'docker logs' requires 1 argument
[FATAL][MinIO-Setup] ERRO FATAL: Configuração do MinIO falhou. Abortando script.
```

**🔍 Causa Raiz:**
- O script `post-setup-automation.ps1` na linha que executa `docker logs` está com sintaxe incorreta
- Falta o nome do contêiner no comando `docker logs`
- Erro na função `Abort-Install` ao tentar capturar logs de diagnóstico

**🛠️ Solução Definitiva:**

#### Correção do Bug no Script
```powershell
# ANTES (linha com erro):
$minioLogs = docker logs --tail 20 2>&1

# DEPOIS (correção):
$minioLogs = docker logs $minioContainerName --tail 20 2>&1
```

#### Script Corrigido para MinIO Setup
```powershell
# Configuração MinIO Robusta - Substituir no post-setup-automation.ps1
try {
    # Verificar se MinIO está rodando
    $minioStatus = docker inspect --format "{{.State.Status}}" $minioContainerName 2>$null
    if ($minioStatus -ne "running") {
        throw "MinIO não está rodando. Status: $minioStatus"
    }

    # Aguardar MinIO estar pronto
    Log-Event "Aguardando MinIO ficar disponível..." "MinIO-Setup" "INFO"
    Start-Sleep -Seconds 10

    # Criar bucket usando mc client
    $mcCommand = "mc alias set local http://localhost:9000 $MINIO_ROOT_USER $MINIO_ROOT_PASSWORD"
    docker exec $minioContainerName sh -c "$mcCommand"
    
    $createBucketCommand = "mc mb local/evolution --ignore-existing"
    docker exec $minioContainerName sh -c "$createBucketCommand"
    
    # Definir política pública
    $policyCommand = "mc anonymous set public local/evolution"
    docker exec $minioContainerName sh -c "$policyCommand"
    
    Log-Event "MinIO configurado com sucesso!" "MinIO-Setup" "SUCCESS"
    
} catch {
    Log-Event "Erro na configuração do MinIO: $($_.Exception.Message)" "MinIO-Setup" "ERROR"
    
    # Capturar logs CORRETAMENTE
    try {
        $minioLogs = docker logs $minioContainerName --tail 20 2>&1
        Log-Event "Logs do MinIO:" "MinIO-Setup" "DEBUG"
        $minioLogs | ForEach-Object { Log-Event $_ "MinIO-Setup" "DEBUG" }
    } catch {
        Log-Event "Não foi possível capturar logs do MinIO: $($_.Exception.Message)" "MinIO-Setup" "WARN"
    }
    
    # NÃO abortar - continuar com outras configurações
    Log-Event "Configuração do MinIO falhou, mas continuando com outras etapas..." "MinIO-Setup" "WARN"
}
```

### 4. **ERRO: PostgreSQL - Databases Inexistentes**

**📍 Localização:** Linhas 280-285 do install.log  
**⚠️ Severidade:** MÉDIA  

```log
[DEBUG][postgres_aula] FATAL:  database "n8n_fila" does not exist
[DEBUG][postgres_aula] FATAL:  database "evolution" does not exist
```

**🔍 Causa Raiz:**
- As databases `n8n_fila` e `evolution` não foram criadas automaticamente
- Os serviços estão tentando conectar antes das databases existirem
- Falta script de inicialização do PostgreSQL

**🛠️ Solução Robusta:**

#### Script de Inicialização do Banco
```sql
-- Criar arquivo: postgres_data/init-db.sql
CREATE DATABASE evolution;
CREATE DATABASE n8n_fila;
CREATE DATABASE chatwoot;

-- Garantir permissões
GRANT ALL PRIVILEGES ON DATABASE evolution TO postgres;
GRANT ALL PRIVILEGES ON DATABASE n8n_fila TO postgres;
GRANT ALL PRIVILEGES ON DATABASE chatwoot TO postgres;
```

#### Atualização do docker-compose.yml
```yaml
postgres:
  image: postgres:15
  container_name: postgres_aula
  environment:
    POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    POSTGRES_HOST_AUTH_METHOD: trust
  volumes:
    - ./postgres_data:/var/lib/postgresql/data
    - ./sql/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql  # Adicionar esta linha
  # ... resto da configuração
```

---

## 🔧 PLANO DE CORREÇÃO PRIORITÁRIO

### **Fase 1: Correções Críticas (Imediatas)**

#### 1.1 Corrigir Bug Fatal no Script MinIO
```powershell
# Editar post-setup-automation.ps1 linha ~388
# SUBSTITUIR:
$minioLogs = docker logs --tail 20 2>&1

# POR:
$minioLogs = docker logs $minioContainerName --tail 20 2>&1
```

#### 1.2 Criar Databases PostgreSQL
```bash
# Executar imediatamente:
docker exec -it postgres_aula psql -U postgres -c "CREATE DATABASE evolution;"
docker exec -it postgres_aula psql -U postgres -c "CREATE DATABASE n8n_fila;"
docker exec -it postgres_aula psql -U postgres -c "CREATE DATABASE chatwoot;"
```

#### 1.3 Regenerar Credenciais Evolution API
```powershell
# Gerar nova API Key
$newKey = [System.Convert]::ToBase64String([System.Text.Encoding]::UTF8.GetBytes((New-Guid).ToString()))
Write-Host "Nova AUTHENTICATION_API_KEY: $newKey"

# Atualizar .env
(Get-Content .env) -replace "AUTHENTICATION_API_KEY=.*", "AUTHENTICATION_API_KEY=$newKey" | Set-Content .env
```

### **Fase 2: Configurações Manuais (Pós-correção)**

#### 2.1 Configurar Webhook Evolution → N8N
1. Acessar Evolution Manager: `http://localhost:8080`
2. Navegar para configurações de Webhook
3. Configurar:
   - **URL**: `http://host.docker.internal:5678/api/n8n-evolution`
   - **Eventos**: `MESSAGERS_UPSERT`
   - **Base64**: Habilitado

#### 2.2 Configurar N8N Webhook Listener
1. Acessar N8N: `http://localhost:5678`
2. Criar workflow com nó Webhook
3. Configurar:
   - **Método**: POST
   - **Path**: `/api/n8n-evolution`
   - Ativar workflow

### **Fase 3: Validação e Testes**

#### 3.1 Script de Validação Completa
```powershell
# Testar todos os serviços
$services = @("postgres_aula", "redis_aula", "minio_aula", "evolution_aula", "n8n_editor-1", "chatwoot-rails-1")

foreach ($service in $services) {
    $status = docker inspect --format "{{.State.Status}}" $service 2>$null
    $health = docker inspect --format "{{.State.Health.Status}}" $service 2>$null
    Write-Host "$service : Status=$status, Health=$health"
}
```

#### 3.2 Teste de Conectividade Evolution API
```powershell
$headers = @{ "X-API-Key" = $env:AUTHENTICATION_API_KEY }
try {
    $response = Invoke-RestMethod -Uri "http://localhost:8080/instance/fetchInstances" -Headers $headers
    Write-Host "✅ Evolution API funcionando: $($response.Count) instâncias"
} catch {
    Write-Host "❌ Evolution API com problemas: $($_.Exception.Message)"
}
```

---

## 📋 DIRETRIZES DE PREVENÇÃO

### **1. Validação de Pré-requisitos**
```powershell
# Adicionar no início do post-setup-automation.ps1
function Test-Prerequisites {
    $errors = @()
    
    # Verificar Docker
    if (-not (Get-Command docker -ErrorAction SilentlyContinue)) {
        $errors += "Docker não instalado ou não no PATH"
    }
    
    # Verificar contêineres rodando
    $requiredContainers = @("postgres_aula", "redis_aula", "minio_aula", "evolution_aula")
    foreach ($container in $requiredContainers) {
        $status = docker inspect --format "{{.State.Status}}" $container 2>$null
        if ($status -ne "running") {
            $errors += "Contêiner $container não está rodando"
        }
    }
    
    # Verificar arquivo .env
    if (-not (Test-Path ".env")) {
        $errors += "Arquivo .env não encontrado"
    }
    
    if ($errors.Count -gt 0) {
        Write-Host "❌ Pré-requisitos não atendidos:" -ForegroundColor Red
        $errors | ForEach-Object { Write-Host "  • $_" -ForegroundColor Yellow }
        return $false
    }
    
    Write-Host "✅ Todos os pré-requisitos atendidos" -ForegroundColor Green
    return $true
}
```

### **2. Tratamento de Erros Robusto**
```powershell
# Substituir Abort-Install por Continue-On-Error para erros não-críticos
function Handle-NonCriticalError {
    param(
        [string]$Message,
        [string]$Component,
        [bool]$IsCritical = $false
    )
    
    if ($IsCritical) {
        Abort-Install $Message $Component
    } else {
        Log-Event $Message $Component "WARN"
        Log-Event "Continuando execução apesar do erro..." $Component "INFO"
    }
}
```

### **3. Retry Logic para APIs**
```powershell
function Invoke-RestMethodWithRetry {
    param(
        [string]$Uri,
        [hashtable]$Headers,
        [string]$Method = "GET",
        [string]$Body = $null,
        [int]$MaxRetries = 3,
        [int]$DelaySeconds = 5
    )
    
    for ($i = 1; $i -le $MaxRetries; $i++) {
        try {
            $params = @{
                Uri = $Uri
                Headers = $Headers
                Method = $Method
                TimeoutSec = 30
            }
            if ($Body) { $params.Body = $Body }
            
            return Invoke-RestMethod @params
        } catch {
            Log-Event "Tentativa $i/$MaxRetries falhou: $($_.Exception.Message)" "API-Retry" "WARN"
            if ($i -eq $MaxRetries) { throw }
            Start-Sleep -Seconds $DelaySeconds
        }
    }
}
```

---

## 🎯 CONCLUSÃO E PRÓXIMOS PASSOS

### **Problemas Identificados por Prioridade:**

1. **🔥 CRÍTICO**: Bug no comando `docker logs` (linha ~388 do script)
2. **🔥 CRÍTICO**: Databases PostgreSQL não existem  
3. **⚠️ ALTO**: Credenciais Evolution API inválidas
4. **⚠️ ALTO**: Endpoint Webhook Evolution API não encontrado
5. **📋 MÉDIO**: Configuração manual N8N necessária

### **Ações Imediatas Recomendadas:**

1. ✅ **Aplicar correção do bug docker logs**
2. ✅ **Criar databases PostgreSQL manualmente**  
3. ✅ **Regenerar AUTHENTICATION_API_KEY**
4. ✅ **Executar script corrigido**
5. ✅ **Configurar webhooks manualmente**

### **Resultado Esperado:**
- ✅ Sistema 100% funcional
- ✅ Integração N8N ↔ Evolution API operacional
- ✅ Chatwoot integrado e funcional
- ✅ MinIO configurado para armazenamento
- ✅ Logs detalhados para monitoramento

### **Tempo Estimado de Correção:**
- **Correções automáticas**: 15-20 minutos
- **Configurações manuais**: 10-15 minutos  
- **Testes e validação**: 10 minutos
- **Total**: 35-45 minutos

---

**📝 Observação**: Este diagnóstico segue as diretrizes de não remover funções, não criar ambiguidades, manter tratamento explícito de erros e fornecer soluções robustas e auditáveis para produção. 