{"name": "[MANAGER] Strategic Coordinator v1.1 - Cost-Aware", "nodes": [{"parameters": {"rule": "cron", "cronTime": "0 7 * * 1"}, "id": "trigger_weekly", "name": "TRIGGER: Weekly (Mon 7am)", "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1.1, "position": [400, 300]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT * FROM agent.market_insights ORDER BY report_date DESC LIMIT 1;", "options": {}}, "id": "db_get_director_report", "name": "DB: Get Last Director's Report", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [640, 300], "notes": "Lê o último relatório do Agente Estrategista/Diretor.", "credentials": {"postgres": {"id": "YOUR_POSTGRES_CREDENTIAL_ID"}}}, {"parameters": {"workflowId": "={{ $env.DISPATCHER_WORKFLOW_ID }}", "data": "={{ { body: { \n    prompt: `Você é um Gerente de Operações de IA. Sua tarefa é traduzir o relatório estratégico do Diretor em um plano de foco para os agentes autônomos na próxima semana. A soma dos pesos deve ser aproximadamente 1.0.\\n\\n**RELATÓRIO DO DIRETOR:**\\n${JSON.stringify($items)}\\n\\n**TAREFA:**\\nDefina o peso (prioridade) para cada área de foco. Um peso de 1.0 significa foco total, 0.0 significa parar. Responda em JSON com a seguinte estrutura:\\n{\\n  \\\"scout_new_products\\\": 0.5, \\n  \\\"create_infoproducts\\\": 0.2,\\n  \\\"prospect_lookalike\\\": 0.3\\n}`,\n    task_type: 'complex_analysis',\n    calling_workflow_id: $workflow.id,\n    calling_node_id: 'call_dispatcher_for_focus'\n} } }}", "options": {}}, "id": "call_dispatcher_for_focus", "name": "Call Dispatcher for Focus", "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 2, "position": [880, 300]}, {"parameters": {"functionCode": "const responseContent = JSON.parse($json.choices[0].message.content);\nconst focusAreas = Object.entries(responseContent).map(([key, value]) => ({ focus_area: key, weight: value }));\nreturn focusAreas;", "options": {}}, "id": "code_format_data", "name": "CODE: Format Focus Data", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1100, 300]}, {"parameters": {"batchSize": 1}, "id": "loop_focus_areas", "name": "Loop Over Focus Areas", "type": "n8n-nodes-base.splitInBatches", "typeVersion": 2, "position": [1300, 300]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "INSERT INTO agent.strategic_focus (focus_area, weight, period_start_date, period_end_date) \nVALUES ($1, $2, CURRENT_DATE, CURRENT_DATE + INTERVAL '7 days')\nON CONFLICT (focus_area) DO UPDATE SET \n  weight = EXCLUDED.weight,\n  period_start_date = EXCLUDED.period_start_date,\n  period_end_date = EXCLUDED.period_end_date,\n  updated_at = NOW();", "options": {"parameters": {"values": ["={{$json.focus_area}}", "={{$json.weight}}"]}}}, "id": "db_update_focus", "name": "DB: Update Strategic Focus", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [1520, 300], "credentials": {"postgres": {"id": "YOUR_POSTGRES_CREDENTIAL_ID"}}}], "connections": {"trigger_weekly": {"main": [[{"node": "db_get_director_report"}]]}, "db_get_director_report": {"main": [[{"node": "call_dispatcher_for_focus"}]]}, "call_dispatcher_for_focus": {"main": [[{"node": "code_format_data"}]]}, "code_format_data": {"main": [[{"node": "loop_focus_areas"}]]}, "loop_focus_areas": {"main": [[{"node": "db_update_focus"}]]}}}