{"name": "[SCOUT] Product Opportunity Finder v1.1 - Cost-Aware", "nodes": [{"parameters": {"rule": "cron", "cronTime": "0 9 1 * *"}, "id": "trigger_monthly", "name": "TRIGGER: Monthly", "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1.1, "position": [400, 300]}, {"parameters": {"path": "run-scout", "options": {}}, "id": "trigger_manual", "name": "TRIGGER: Manual", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [400, 500]}, {"parameters": {}, "id": "merge_triggers", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.merge", "typeVersion": 2, "position": [620, 400]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT content FROM agent.memory_vectors ORDER BY timestamp DESC LIMIT 200;", "options": {}}, "id": "db_get_memories", "name": "DB: Get Recent Memories", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [820, 400], "credentials": {"postgres": {"id": "YOUR_POSTGRES_CREDENTIAL_ID"}}}, {"parameters": {"workflowId": "={{ $env.DISPATCHER_WORKFLOW_ID }}", "data": "={{ { body: { \n    prompt: `<PERSON><PERSON><PERSON> o seguinte extrato de conversas de clientes. Ignore saudações e formalidades. Extraia os 5 principais 'temas de dor' ou 'desejos de produto' mencionados. Seja específico. Formate como um array JSON.\\n\\nCONVERSAS:\\n${$items.map(i => i.json.content).join('\\n---\\n')}\\n\\nExemplo de Saída: [\\\"dificuldade em organizar agenda\\\", \\\"preciso de ajuda com marketing digital\\\", \\\"quero aprender a investir\\\"]`,\n    task_type: 'complex_analysis',\n    calling_workflow_id: $workflow.id,\n    calling_node_id: 'call_dispatcher_for_themes'\n} } }}", "options": {}}, "id": "call_dispatcher_for_themes", "name": "Call Dispatcher for Themes", "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 2, "position": [1040, 400]}, {"parameters": {"functionCode": "const themes = JSON.parse($json.choices[0].message.content);\nreturn themes.map(theme => ({ json: { theme: theme } }));", "options": {}}, "id": "code_prepare_input", "name": "CODE: Prepare Sub-Workflow Input", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1240, 400]}, {"parameters": {"batchSize": 1}, "id": "loop_themes", "name": "Loop Over Themes", "type": "n8n-nodes-base.splitInBatches", "typeVersion": 2, "position": [1440, 400]}, {"parameters": {"workflowId": "={{$env.SUB_SCOUT_THEME_WORKFLOW_ID}}", "options": {"runIn": "background", "parameters": {"values": {"json": [{"name": "data", "value": "={{$json}}"}]}}}}, "id": "execute_sub_scout", "name": "EXECUTE: Scout Theme", "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 2, "position": [1640, 400], "notes": "Dispara um sub-workflow para pesquisar e analisar cada tema de forma independente."}], "connections": {"trigger_monthly": {"main": [[{"node": "merge_triggers"}]]}, "trigger_manual": {"main": [[{"node": "merge_triggers", "type": "main", "index": 1}]]}, "merge_triggers": {"main": [[{"node": "db_get_memories"}]]}, "db_get_memories": {"main": [[{"node": "call_dispatcher_for_themes"}]]}, "call_dispatcher_for_themes": {"main": [[{"node": "code_prepare_input"}]]}, "code_prepare_input": {"main": [[{"node": "loop_themes"}]]}, "loop_themes": {"main": [[{"node": "execute_sub_scout"}]]}}}