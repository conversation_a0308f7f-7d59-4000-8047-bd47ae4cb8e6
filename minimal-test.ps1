# minimal-test.ps1 - <PERSON>e mínimo sem importar módulo

Write-Host "TESTE MINIMO - Verificando estrutura de arquivos" -ForegroundColor Cyan

# Verificar se os arquivos existem
$files = @(
    "Start-Environment.ps1",
    "PowerShellModules\AutomationUtils.psm1",
    "workflows"
)

foreach ($file in $files) {
    if (Test-Path $file) {
        Write-Host "OK: $file existe" -ForegroundColor Green
    } else {
        Write-Host "ERRO: $file nao encontrado" -ForegroundColor Red
    }
}

# Verificar estrutura de workflows
if (Test-Path "workflows") {
    $workflowDirs = Get-ChildItem -Path "workflows" -Directory -Recurse
    Write-Host "`nWorkflows encontrados: $($workflowDirs.Count)" -ForegroundColor Yellow
    
    $workflowsWithSQL = 0
    foreach ($dir in $workflowDirs) {
        $sqlPath = Join-Path $dir.FullName "sql"
        if (Test-Path $sqlPath) {
            $sqlFiles = Get-ChildItem -Path $sqlPath -Filter "*.sql" -ErrorAction SilentlyContinue
            if ($sqlFiles.Count -gt 0) {
                $workflowsWithSQL++
                Write-Host "  $($dir.Name) - $($sqlFiles.Count) arquivos SQL" -ForegroundColor Gray
            }
        }
    }
    
    Write-Host "Workflows com SQL: $workflowsWithSQL" -ForegroundColor Yellow
}

Write-Host "`nTeste estrutural concluido!" -ForegroundColor Green
