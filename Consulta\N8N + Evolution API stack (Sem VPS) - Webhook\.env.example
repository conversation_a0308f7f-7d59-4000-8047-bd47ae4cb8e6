# Variáveis compartilhadas para o Postgres
POSTGRES_USER=<SEU_USER_DO_BANCO>
POSTGRES_PASSWORD=<SUA_SENHA_DO_BANCO>
POSTGRES_DB=app_db

# Variáveis do n8n
N8N_BASIC_AUTH_ACTIVE=true
N8N_BASIC_AUTH_USER=<SEU_USER_DO_N8N>
N8N_BASIC_AUTH_PASSWORD=<SUA_SENHA_DO_N8N>
DB_TYPE=postgresdb
DB_POSTGRESDB_HOST=postgres
DB_POSTGRESDB_PORT=5432
DB_POSTGRESDB_USER=${POSTGRES_USER}
DB_POSTGRESDB_PASSWORD=${POSTGRES_PASSWORD}
DB_POSTGRESDB_DATABASE=${POSTGRES_DB}
N8N_COMMUNITY_PACKAGES_ENABLED=true
GENERIC_TIMEZONE=${GENERIC_TIMEZONE}

# Variáveis do evolution-api
AUTHENTICATION_API_KEY=<SUA_API_KEY_DO_EVOLUTION>
DATABASE_ENABLED=true
DATABASE_PROVIDER=postgresql
DATABASE_CONNECTION_URI=postgresql://${POSTGRES_USER}:${POSTGRES_PASSWORD}@postgres:5432/${POSTGRES_DB}?schema=public
DATABASE_CONNECTION_CLIENT_NAME=evolution_exchange
DATABASE_SAVE_DATA_INSTANCE=true
DATABASE_SAVE_DATA_NEW_MESSAGE=true
DATABASE_SAVE_MESSAGE_UPDATE=true
DATABASE_SAVE_DATA_CONTACTS=true
DATABASE_SAVE_DATA_CHATS=true
DATABASE_SAVE_DATA_LABELS=true
DATABASE_SAVE_DATA_HISTORIC=true

# Variáveis do Redis (evolution-api)
CACHE_REDIS_ENABLED=true
CACHE_REDIS_URI=redis://redis:6379/6
CACHE_REDIS_PREFIX_KEY=evolution
CACHE_REDIS_SAVE_INSTANCES=false
REDIS_PASSWORD=password

# Variáveis do cache local (evolution-api)
CACHE_LOCAL_ENABLED=false