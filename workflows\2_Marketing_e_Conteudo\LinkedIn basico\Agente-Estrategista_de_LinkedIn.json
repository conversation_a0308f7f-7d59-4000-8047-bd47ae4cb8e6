{"name": "Agente Especialista | Estrategista de LinkedIn (v4.1 - Production Stabilized)", "nodes": [{"parameters": {"workflowInputs": {"values": [{"name": "user_id", "type": "string", "value": "user_789"}, {"name": "task", "type": "string", "value": "generate_performance_report"}, {"name": "task_details", "type": "json", "json": "{ \"time_period_days\": 30, \"cache_key\": \"monthly_report\" }"}]}}, "type": "n8n-nodes-base.executeWorkflowTrigger", "typeVersion": 1.1, "position": [-460, 360], "id": "a1b2c3d4-e5f6-a7b8-b9c0-d1e2f3a4b5c6", "name": "Start"}, {"parameters": {"content": "### 👔 Laura v4.1: Produção Estabilizada\n\n**Propósito**: A versão final, otimizada para produção com suporte multi-usuário, renovação de tokens e validação granular.\n\n**Mel<PERSON><PERSON>**:\n1.  **Multi-Usuário**: `user_id` é usado para isolar dados, logs e chaves de cache.\n2.  **Renovação de Token**: O fluxo verifica a expiração de tokens e os renova automaticamente.\n3.  **Roteador de Validação**: Cada tarefa passa por uma validação específica antes de executar.\n4.  **Guia de Infraestrutura**: Uma nota dedicada documenta os requisitos de DB e monitoramento.", "height": 340, "width": 540, "color": 10}, "type": "n8n-nodes-base.stickyNote", "position": [-500, -140], "typeVersion": 1, "id": "c1d2e3f4-a5b6-b7c8-d9e0-f1a2b3c4d5e6", "name": "Nota da Arquitetura v4.1"}, {"parameters": {"routing": {"rules": {"values": [{"output": 0, "operation": "equal", "value1": "={{ $json.task }}", "value2": "generate_performance_report"}, {"output": 1, "operation": "equal", "value1": "={{ $json.task }}", "value2": "create_post"}, {"output": 2, "operation": "equal", "value1": "={{ $json.task }}", "value2": "nurture_network"}]}, "fieldToMatch": "={{ $json.task }}"}}, "id": "2c575a73-3f1b-4394-9b7e-40af9a463994", "name": "Task Router", "type": "n8n-nodes-base.switch", "typeVersion": 2, "position": [240, 360]}, {"parameters": {"values": {"string": []}, "options": {}}, "id": "N3W_N0D3_ID_1", "name": "[TOOL] Get LinkedIn Analytics", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [1380, -160]}, {"parameters": {"agent": "conversationalAgent", "promptType": "define", "text": "Dados para Análise: {{ JSON.stringify($('N3W_N0D3_ID_1').first().json) }}", "options": {"systemMessage": "Sua única tarefa é analisar os dados de performance, logs de ação e metas fornecidos em JSON. Gere um relatório executivo em texto, com seções para 'Resumo', 'Progresso das Metas', 'Análise de Impacto' e 'Recomendações'. Foque em conectar as ações aos resultados."}}, "id": "N3W_N0D3_ID_2", "name": "Agent: Write Report", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "position": [1620, -160]}, {"parameters": {"values": {"string": []}, "options": {"action": "merge"}}, "id": "N3W_N0D3_ID_5", "name": "Merge Point", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [1860, 360]}, {"parameters": {"values": {"string": []}, "options": {}}, "id": "N3W_N0D3_ID_6", "name": "[ERROR] Format Error Message", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [480, 840]}, {"parameters": {"values": {"string": [{"name": "log_entry", "value": "={{ { \"user_id\": $json.user_id, \"timestamp\": $now, \"action\": \"report_generated\", \"details\": $json.task_details, \"success\": true, \"cached\": false } }}"}]}, "options": {}}, "id": "N3W_N0D3_ID_9", "name": "[DB] Save Action Log to Postgres", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [1860, -80]}, {"parameters": {"agent": "conversationalAgent", "promptType": "define", "text": "Detalhes: {{ JSON.stringify($('Start').first().json.task_details) }}", "options": {"systemMessage": "Sua única tarefa é escrever um post para LinkedIn com base no `topic` e `goal` fornecidos. Siga a estrutura: Gancho <PERSON>, Desenvolvimento, CTA e 3-5 Hashtags."}}, "id": "V3_NODE_ID_AGENT_POST", "name": "Agent: Write Post", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "position": [700, 240]}, {"parameters": {"values": {"string": [{"name": "log_entry", "value": "={{ { \"user_id\": $json.user_id, \"timestamp\": $now, \"action\": \"post_created\", \"details\": $json.task_details, \"success\": true } }}"}]}, "options": {}}, "id": "V3_NODE_ID_LOG_POST", "name": "[DB] Save Action Log to Postgres", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [940, 240]}, {"parameters": {"values": {"string": []}, "options": {}}, "id": "V3_NODE_ID_TOOL_NURTURE", "name": "[TOOL] Get Network Updates", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [700, 560]}, {"parameters": {"agent": "conversationalAgent", "promptType": "define", "text": "Sentimento: {{ $('V4_NODE_ID_SENTIMENT').first().json.sentiment }}. <PERSON><PERSON><PERSON><PERSON> do <PERSON>: {{ JSON.stringify($('V3_NODE_ID_TOOL_NURTURE').first().json) }}", "options": {"systemMessage": "Sua única tarefa é analisar as atualizações da rede e o sentimento associado para redigir mensagens de parabéns curtas e genuínas. Adapte o tom da sua mensagem ao sentimento (ex: mais entusiasmado para 'positivo')."}}, "id": "V3_NODE_ID_AGENT_NURTURE", "name": "Agent: Draft Nurture Msgs", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "position": [1180, 560]}, {"parameters": {"values": {"string": []}, "options": {}}, "id": "V3_NODE_ID_LOG_NURTURE", "name": "[TOOL] Send Msgs via Evolution API", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [1420, 560]}, {"parameters": {"values": {"string": []}, "options": {}}, "id": "V3_NODE_ID_OUTPUT", "name": "[OUTPUT] Send Result to User", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [2100, 360]}, {"parameters": {}, "id": "V3_NODE_ID_ERROR_TRIGGER1", "name": "<PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.errorTrigger", "typeVersion": 1, "position": [240, 840]}, {"parameters": {"values": {"string": []}, "options": {}}, "id": "V3_NODE_ID_ERROR_NOTIFY", "name": "[ALERT] Notify Admin via Chat/Email", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [720, 840]}, {"parameters": {"conditions": {"boolean": [{"value1": "={{ $json.cache_hit }}", "operation": "equal", "value2": true}]}}, "id": "V4_NODE_ID_CHECK_CACHE", "name": "[CACHE] Check Memcached for Report", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [480, -80]}, {"parameters": {"values": {"string": []}, "options": {}}, "id": "V4_NODE_ID_AUTH_REDIS_1", "name": "[CACHE] Get <PERSON><PERSON> from Redis", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [700, -160]}, {"parameters": {"values": {"string": []}, "options": {}}, "id": "V4_NODE_ID_AUTH_REDIS_2", "name": "[CACHE] Get <PERSON><PERSON> from Redis", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [480, 240]}, {"parameters": {"values": {"string": []}, "options": {}}, "id": "V4_NODE_ID_AUTH_REDIS_3", "name": "[CACHE] Get <PERSON><PERSON> from Redis", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [480, 560]}, {"parameters": {"values": {"string": [{"name": "sentiment", "value": "positive"}]}, "options": {}}, "id": "V4_NODE_ID_SENTIMENT", "name": "[AI_TOOL] Analyze Sentiment", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [940, 560]}, {"parameters": {"values": {"string": []}, "options": {}}, "id": "V4_NODE_ID_FEEDBACK", "name": "[FEEDBACK] Request Feedback via Chatwoot", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [2340, 360]}, {"parameters": {"content": "#### Requisitos de Infraestrutura (Backend)\n\n**1. Otimização do Postgres**:\n- **Índices**: Criar índices B-Tree nas colunas `user_id`, `action` e `timestamp` da tabela de logs para acelerar as consultas.\n- **Particionamento**: Particionar a tabela de logs por `timestamp` (ex: mensalmente ou trimestralmente) para otimizar a performance de escrita e leitura em grandes volumes.\n\n**2. Monitoramento Centralizado**:\n- **ELK Stack/Prometheus**: Configurar o **Traefik** para encaminhar logs e métricas de todos os contêineres (N8N, Postgres, Redis, etc.) para um sistema de monitoramento centralizado para observabilidade completa.", "height": 380, "width": 420}, "id": "V41_NOTE_ID_INFRA", "name": "Nota de Requisitos de Infraestrutura", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-20, -540]}, {"parameters": {"routing": {"rules": {"values": [{"output": 0, "operation": "equal", "value1": "={{ $json.task }}", "value2": "generate_performance_report"}, {"output": 1, "operation": "equal", "value1": "={{ $json.task }}", "value2": "create_post"}]}, "fieldToMatch": "={{ $json.task }}"}}, "id": "V41_ROUTER_VALIDATE", "name": "Route to Task Validator", "type": "n8n-nodes-base.switch", "typeVersion": 2, "position": [-220, 360]}, {"parameters": {"conditions": {"boolean": [{"value1": "={{ $json.task_details.time_period_days }}", "operation": "isset"}]}}, "id": "V41_VALIDATE_REPORT", "name": "Validate: generate_report", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [0, 240]}, {"parameters": {"conditions": {"boolean": [{"value1": "={{ $json.token_expired }}", "operation": "equal", "value2": true}]}}, "id": "V41_IF_TOKEN_EXPIRED", "name": "Check if Token Expired?", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [940, -160]}, {"parameters": {"values": {"string": []}, "options": {}}, "id": "V41_RENEW_TOKEN", "name": "[AUTH] Renew <PERSON>", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [1160, -260]}], "pinData": {}, "connections": {"Start": {"main": [[{"node": "Route to Task Validator", "type": "main", "index": 0}]]}, "Task Router": {"main": [[{"node": "[CACHE] Check Memcached for Report", "type": "main", "index": 0}], [{"node": "[CACHE] Get <PERSON><PERSON> from Redis", "type": "main", "index": 0}], [{"node": "[CACHE] Get <PERSON><PERSON> from Redis", "type": "main", "index": 0}], [{"node": "[ERROR] Format Error Message", "type": "main", "index": 0}]]}, "[TOOL] Get LinkedIn Analytics": {"main": [[{"node": "Agent: Write Report", "type": "main", "index": 0}]]}, "Agent: Write Report": {"main": [[{"node": "[DB] Save Action Log to Postgres", "type": "main", "index": 0}]]}, "[DB] Save Action Log to Postgres": {"main": [[{"node": "Merge Point", "type": "main", "index": 0}]]}, "Agent: Write Post": {"main": [[{"node": "[DB] Save Action Log to Postgres", "type": "main", "index": 0}]]}, "[TOOL] Get Network Updates": {"main": [[{"node": "[AI_TOOL] Analyze Sentiment", "type": "main", "index": 0}]]}, "Agent: Draft Nurture Msgs": {"main": [[{"node": "[TOOL] Send Msgs via Evolution API", "type": "main", "index": 0}]]}, "[TOOL] Send Msgs via Evolution API": {"main": [[{"node": "Merge Point", "type": "main", "index": 0}]]}, "Error Trigger": {"main": [[{"node": "[ERROR] Format Error Message", "type": "main", "index": 0}]]}, "[ERROR] Format Error Message": {"main": [[{"node": "[ALERT] Notify Admin via Chat/Email", "type": "main", "index": 0}]]}, "[CACHE] Check Memcached for Report": {"main": [[{"node": "Merge Point", "type": "main", "index": 0}], [{"node": "[CACHE] Get <PERSON><PERSON> from Redis", "type": "main", "index": 0}]]}, "[CACHE] Get Auth Token from Redis": {"main": [[{"node": "[TOOL] Get Network Updates", "type": "main", "index": 0}]]}, "[AI_TOOL] Analyze Sentiment": {"main": [[{"node": "Agent: Draft Nurture Msgs", "type": "main", "index": 0}]]}, "Merge Point": {"main": [[{"node": "[OUTPUT] Send Result to User", "type": "main", "index": 0}]]}, "[OUTPUT] Send Result to User": {"main": [[{"node": "[FEEDBACK] Request Feedback via Chatwoot", "type": "main", "index": 0}]]}, "Route to Task Validator": {"main": [[{"node": "Validate: generate_report", "type": "main", "index": 0}], []]}, "Validate: generate_report": {"main": [[{"node": "Task Router", "type": "main", "index": 0}]]}, "Check if Token Expired?": {"main": [[{"node": "[AUTH] Renew <PERSON>", "type": "main", "index": 0}], [{"node": "[TOOL] Get LinkedIn Analytics", "type": "main", "index": 0}]]}, "[AUTH] Renew Token": {"main": [[{"node": "[TOOL] Get LinkedIn Analytics", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1", "errorWorkflow": "V3_NODE_ID_ERROR_TRIGGER1"}, "versionId": "f9f9e0a1-1b2c-3d4e-5f6a-7b8c9d0e1f2g", "meta": {}, "id": "e9f8e7d6-c5b4-a3d2-e1f0-a9a8c7d6e5f5", "tags": []}