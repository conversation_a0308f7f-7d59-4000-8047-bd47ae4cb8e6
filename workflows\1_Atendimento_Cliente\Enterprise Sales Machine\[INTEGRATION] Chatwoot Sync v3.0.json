{"name": "[INTEGRATION] 🔄 Chatwoot Sync v3.0", "nodes": [{"parameters": {"httpMethod": "POST", "path": "chatwoot-bidirectional-sync", "options": {"noResponseBody": false}}, "id": "chatwoot-webhook-trigger", "name": "🎯 Chatwoot Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [240, 300], "webhookId": "chatwoot-bidirectional-sync"}, {"parameters": {"jsCode": "// Enhanced Chatwoot Data Processing with Bidirectional Sync\nconst body = $input.first().json.body || $input.first().json;\n\n// Validate webhook data\nif (!body.event || !body.data) {\n  throw new Error('Invalid Chatwoot webhook: missing event or data');\n}\n\n// Enhanced event mapping with bidirectional support\nconst eventMapping = {\n  'conversation_created': 'new_conversation',\n  'conversation_updated': 'conversation_update',\n  'conversation_status_changed': 'status_change',\n  'conversation_assignee_changed': 'assignee_change',\n  'message_created': 'new_message',\n  'message_updated': 'message_update',\n  'contact_created': 'new_contact',\n  'contact_updated': 'contact_update',\n  'agent_added': 'agent_added',\n  'agent_removed': 'agent_removed'\n};\n\nconst eventType = eventMapping[body.event] || body.event;\nconst eventData = body.data;\n\n// Enhanced data extraction with validation\nconst processedData = {\n  event_type: eventType,\n  original_event: body.event,\n  timestamp: new Date().toISOString(),\n  sync_direction: 'chatwoot_to_system',\n  \n  // Enhanced conversation data\n  conversation: {\n    id: eventData.id,\n    account_id: eventData.account_id,\n    inbox_id: eventData.inbox_id,\n    status: eventData.status,\n    priority: eventData.priority || 'medium',\n    assignee: eventData.assignee ? {\n      id: eventData.assignee.id,\n      name: eventData.assignee.name,\n      email: eventData.assignee.email,\n      role: eventData.assignee.role,\n      availability_status: eventData.assignee.availability_status\n    } : null,\n    team: eventData.team ? {\n      id: eventData.team.id,\n      name: eventData.team.name,\n      description: eventData.team.description\n    } : null,\n    contact: eventData.contact ? {\n      id: eventData.contact.id,\n      name: eventData.contact.name,\n      email: eventData.contact.email,\n      phone: eventData.contact.phone_number,\n      identifier: eventData.contact.identifier,\n      custom_attributes: eventData.contact.custom_attributes || {}\n    } : null,\n    labels: eventData.labels || [],\n    custom_attributes: eventData.custom_attributes || {},\n    messages_count: eventData.messages_count || 0,\n    first_reply_created_at: eventData.first_reply_created_at,\n    last_activity_at: eventData.last_activity_at,\n    created_at: eventData.created_at,\n    updated_at: eventData.updated_at\n  },\n  \n  // Enhanced message data (if applicable)\n  message: body.message ? {\n    id: body.message.id,\n    content: body.message.content,\n    message_type: body.message.message_type,\n    content_type: body.message.content_type,\n    content_attributes: body.message.content_attributes || {},\n    sender: body.message.sender,\n    created_at: body.message.created_at,\n    updated_at: body.message.updated_at,\n    attachments: body.message.attachments || []\n  } : null\n};\n\n// Enhanced escalation criteria with AI-ready analysis\nconst escalationCriteria = checkAdvancedEscalationCriteria(processedData);\nprocessedData.escalation_analysis = escalationCriteria;\n\n// Enhanced escalation criteria function\nfunction checkAdvancedEscalationCriteria(data) {\n  const criteria = {\n    required: false,\n    reasons: [],\n    priority: 'medium',\n    urgency_score: 0,\n    confidence: 0.5,\n    ai_analysis_required: false\n  };\n\n  const conv = data.conversation;\n  const msg = data.message;\n\n  // 1. Label-based escalation (High Priority)\n  const escalationLabels = [\n    'escalation', 'urgent', 'vip', 'complaint', 'technical-issue',\n    'billing-issue', 'refund-request', 'supervisor-request'\n  ];\n  \n  const hasEscalationLabel = conv.labels?.some(label => \n    escalationLabels.some(escLabel => \n      label.title?.toLowerCase().includes(escLabel.toLowerCase())\n    )\n  );\n\n  if (hasEscalationLabel) {\n    criteria.required = true;\n    criteria.reasons.push('escalation_label_detected');\n    criteria.priority = 'high';\n    criteria.urgency_score += 30;\n  }\n\n  // 2. Priority-based escalation\n  if (conv.priority === 'urgent' || conv.priority === 'high') {\n    criteria.required = true;\n    criteria.reasons.push('high_priority_conversation');\n    criteria.priority = 'high';\n    criteria.urgency_score += 25;\n  }\n\n  // 3. Custom attributes escalation\n  if (conv.custom_attributes) {\n    if (conv.custom_attributes.escalation_requested === 'true') {\n      criteria.required = true;\n      criteria.reasons.push('manual_escalation_request');\n      criteria.priority = 'high';\n      criteria.urgency_score += 35;\n    }\n    \n    if (conv.custom_attributes.customer_tier === 'vip' || \n        conv.custom_attributes.customer_tier === 'enterprise') {\n      criteria.required = true;\n      criteria.reasons.push('vip_customer');\n      criteria.priority = 'critical';\n      criteria.urgency_score += 40;\n    }\n\n    if (conv.custom_attributes.issue_severity === 'critical') {\n      criteria.required = true;\n      criteria.reasons.push('critical_issue_severity');\n      criteria.priority = 'critical';\n      criteria.urgency_score += 45;\n    }\n  }\n\n  // 4. Time-based escalation (Enhanced)\n  if (!conv.assignee && conv.created_at) {\n    const createdTime = new Date(conv.created_at);\n    const now = new Date();\n    const hoursSinceCreated = (now - createdTime) / (1000 * 60 * 60);\n    \n    if (hoursSinceCreated > 4) {\n      criteria.required = true;\n      criteria.reasons.push('unassigned_timeout_critical');\n      criteria.priority = 'high';\n      criteria.urgency_score += 30;\n    } else if (hoursSinceCreated > 2) {\n      criteria.required = true;\n      criteria.reasons.push('unassigned_timeout');\n      criteria.priority = 'medium';\n      criteria.urgency_score += 20;\n    }\n  }\n\n  // 5. Message volume escalation\n  if (conv.messages_count > 30) {\n    criteria.required = true;\n    criteria.reasons.push('excessive_message_volume');\n    criteria.priority = 'medium';\n    criteria.urgency_score += 25;\n  } else if (conv.messages_count > 15) {\n    criteria.reasons.push('high_message_volume');\n    criteria.urgency_score += 15;\n  }\n\n  // 6. Content-based escalation (requires AI analysis)\n  if (msg && msg.content) {\n    const content = msg.content.toLowerCase();\n    const escalationKeywords = [\n      'cancel', 'refund', 'supervisor', 'manager', 'complaint',\n      'terrible', 'awful', 'worst', 'hate', 'angry', 'frustrated',\n      'legal', 'lawyer', 'sue', 'court', 'unacceptable'\n    ];\n\n    const hasEscalationKeywords = escalationKeywords.some(keyword => \n      content.includes(keyword)\n    );\n\n    if (hasEscalationKeywords) {\n      criteria.required = true;\n      criteria.reasons.push('escalation_keywords_detected');\n      criteria.priority = 'high';\n      criteria.urgency_score += 35;\n      criteria.ai_analysis_required = true;\n    }\n\n    // Technical issue keywords\n    const technicalKeywords = [\n      'bug', 'error', 'broken', 'not working', 'crash', 'freeze'\n    ];\n\n    if (technicalKeywords.some(keyword => content.includes(keyword))) {\n      criteria.reasons.push('technical_issue_detected');\n      criteria.urgency_score += 20;\n      criteria.ai_analysis_required = true;\n    }\n  }\n\n  // 7. Agent availability check\n  if (conv.assignee && conv.assignee.availability_status === 'offline') {\n    criteria.reasons.push('assigned_agent_offline');\n    criteria.urgency_score += 15;\n  }\n\n  // Calculate final priority and confidence\n  if (criteria.urgency_score >= 70) {\n    criteria.priority = 'critical';\n    criteria.confidence = 0.9;\n  } else if (criteria.urgency_score >= 50) {\n    criteria.priority = 'high';\n    criteria.confidence = 0.8;\n  } else if (criteria.urgency_score >= 30) {\n    criteria.priority = 'medium';\n    criteria.confidence = 0.7;\n  } else {\n    criteria.priority = 'low';\n    criteria.confidence = 0.6;\n  }\n\n  // Auto-escalate if score is high enough\n  if (criteria.urgency_score >= 40) {\n    criteria.required = true;\n  }\n\n  return criteria;\n}\n\nreturn [{ json: processedData }];", "continueOnFail": true}, "id": "process-chatwoot-data", "name": "⚙️ Enhanced Data Processing", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [460, 300]}], "pinData": {}, "connections": {"chatwoot-webhook-trigger": {"main": [[{"node": "process-chatwoot-data", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "3.0", "meta": {"templateCredsSetupCompleted": true, "instanceId": "chatwoot-sync-v3"}, "id": "chatwoot-sync-v3", "tags": [{"createdAt": "2024-01-15T10:00:00.000Z", "updatedAt": "2024-01-15T10:00:00.000Z", "id": "integration", "name": "Integration"}, {"createdAt": "2024-01-15T10:00:00.000Z", "updatedAt": "2024-01-15T10:00:00.000Z", "id": "chatwoot", "name": "Chatwoot"}, {"createdAt": "2024-01-15T10:00:00.000Z", "updatedAt": "2024-01-15T10:00:00.000Z", "id": "escalation", "name": "Escalation"}]}