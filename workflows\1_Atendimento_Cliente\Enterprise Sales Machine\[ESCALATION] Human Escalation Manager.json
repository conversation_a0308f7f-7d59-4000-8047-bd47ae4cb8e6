{
  "name": "[ESCALATION] Human Escalation Manager",
  "nodes": [
    {
      "parameters": {
        "httpMethod": "POST",
        "path": "human-escalation",
        "options": {}
      },
      "id": "f47ac10b-58cc-4372-a567-0e02b2c3d481",
      "name": "🚨 Webhook - Escalation Request",
      "type": "n8n-nodes-base.webhook",
      "typeVersion": 1,
      "position": [240, 300],
      "webhookId": "human-escalation"
    },
    {
      "parameters": {
        "conditions": {
          "options": {
            "caseSensitive": true,
            "leftValue": "",
            "typeValidation": "strict"
          },
          "conditions": [
            {
              "id": "c1",
              "leftValue": "={{ $json.action }}",
              "rightValue": "escalate_content",
              "operator": {
                "type": "string",
                "operation": "equals"
              }
            },
            {
              "id": "c2",
              "leftValue": "={{ $json.action }}",
              "rightValue": "resolve_escalation",
              "operator": {
                "type": "string",
                "operation": "equals"
              }
            },
            {
              "id": "c3",
              "leftValue": "={{ $json.action }}",
              "rightValue": "get_escalations",
              "operator": {
                "type": "string",
                "operation": "equals"
              }
            },
            {
              "id": "c4",
              "leftValue": "={{ $json.action }}",
              "rightValue": "update_escalation",
              "operator": {
                "type": "string",
                "operation": "equals"
              }
            }
          ],
          "combinator": "or"
        },
        "options": {}
      },
      "id": "8f14e45f-ceea-467a-9b87-7df96c6f136a",
      "name": "🔀 Route Action",
      "type": "n8n-nodes-base.switch",
      "typeVersion": 3,
      "position": [460, 300]
    },
    {
      "parameters": {
        "jsCode": "// 🚨 Validar e processar escalação de conteúdo\nconst request = $input.first().json;\n\n// Validações obrigatórias\nconst requiredFields = ['content_id', 'escalation_reason', 'priority'];\nconst missingFields = requiredFields.filter(field => !request[field]);\n\nif (missingFields.length > 0) {\n  throw new Error(`Campos obrigatórios ausentes: ${missingFields.join(', ')}`);\n}\n\n// Validar prioridade\nconst validPriorities = ['low', 'medium', 'high', 'critical'];\nif (!validPriorities.includes(request.priority)) {\n  throw new Error(`Prioridade inválida. Use: ${validPriorities.join(', ')}`);\n}\n\n// Validar razão da escalação\nconst validReasons = [\n  'quality_concern',\n  'brand_alignment',\n  'content_policy',\n  'technical_issue',\n  'performance_concern',\n  'compliance_review',\n  'other'\n];\n\nif (!validReasons.includes(request.escalation_reason)) {\n  throw new Error(`Razão de escalação inválida. Use: ${validReasons.join(', ')}`);\n}\n\n// Calcular deadline baseado na prioridade\nconst now = new Date();\nlet deadline;\n\nswitch (request.priority) {\n  case 'critical':\n    deadline = new Date(now.getTime() + 2 * 60 * 60 * 1000); // 2 horas\n    break;\n  case 'high':\n    deadline = new Date(now.getTime() + 8 * 60 * 60 * 1000); // 8 horas\n    break;\n  case 'medium':\n    deadline = new Date(now.getTime() + 24 * 60 * 60 * 1000); // 24 horas\n    break;\n  case 'low':\n    deadline = new Date(now.getTime() + 72 * 60 * 60 * 1000); // 72 horas\n    break;\n}\n\nreturn [{\n  escalation_data: {\n    content_id: request.content_id,\n    escalation_reason: request.escalation_reason,\n    priority: request.priority,\n    description: request.description || '',\n    escalated_by: request.escalated_by || 'system',\n    assigned_to: request.assigned_to || null,\n    deadline: deadline.toISOString(),\n    metadata: request.metadata || {},\n    status: 'pending',\n    created_at: now.toISOString()\n  }\n}];"
      },
      "id": "8f14e45f-ceea-467a-9b87-7df96c6f136b",
      "name": "✅ Validate Escalation",
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [680, 120]
    },
    {
      "parameters": {
        "operation": "select",
        "schema": {
          "value": "agent"
        },
        "table": {
          "value": "published_content"
        },
        "where": {
          "values": [
            {
              "column": "id",
              "condition": "equal",
              "value": "={{ $('✅ Validate Escalation').first().json.escalation_data.content_id }}"
            }
          ]
        },
        "options": {}
      },
      "id": "6ba7b820-9dad-11d1-80b4-00c04fd430c8",
      "name": "📄 Get Content Details",
      "type": "n8n-nodes-base.postgres",
      "typeVersion": 2.4,
      "position": [900, 80],
      "credentials": {
        "postgres": {
          "id": "postgres-main",
          "name": "PostgreSQL - Main DB"
        }
      }
    },
    {
      "parameters": {
        "operation": "select",
        "schema": {
          "value": "agent"
        },
        "table": {
          "value": "human_escalations"
        },
        "where": {
          "values": [
            {
              "column": "content_id",
              "condition": "equal",
              "value": "={{ $('✅ Validate Escalation').first().json.escalation_data.content_id }}"
            },
            {
              "column": "status",
              "condition": "in",
              "value": "pending,in_progress"
            }
          ]
        },
        "options": {}
      },
      "id": "6ba7b821-9dad-11d1-80b4-00c04fd430c8",
      "name": "🔍 Check Existing Escalations",
      "type": "n8n-nodes-base.postgres",
      "typeVersion": 2.4,
      "position": [900, 160],
      "credentials": {
        "postgres": {
          "id": "postgres-main",
          "name": "PostgreSQL - Main DB"
        }
      }
    },
    {
      "parameters": {
        "jsCode": "// 🔍 Processar verificações e criar escalação\nconst escalationData = $('✅ Validate Escalation').first().json.escalation_data;\nconst contentData = $('📄 Get Content Details').first()?.json;\nconst existingEscalations = $('🔍 Check Existing Escalations').all();\n\n// Verificar se o conteúdo existe\nif (!contentData) {\n  throw new Error(`Conteúdo com ID ${escalationData.content_id} não encontrado`);\n}\n\n// Verificar se já existe escalação ativa\nif (existingEscalations.length > 0) {\n  const activeEscalation = existingEscalations[0].json;\n  return [{\n    escalation_result: {\n      success: false,\n      message: 'Já existe uma escalação ativa para este conteúdo',\n      existing_escalation_id: activeEscalation.id,\n      existing_status: activeEscalation.status,\n      created_at: activeEscalation.created_at\n    }\n  }];\n}\n\n// Determinar assignee baseado na prioridade e razão\nlet assignedTo = escalationData.assigned_to;\n\nif (!assignedTo) {\n  // Auto-assignment baseado em regras\n  switch (escalationData.escalation_reason) {\n    case 'quality_concern':\n    case 'content_policy':\n      assignedTo = 'content_team';\n      break;\n    case 'brand_alignment':\n      assignedTo = 'brand_team';\n      break;\n    case 'technical_issue':\n      assignedTo = 'tech_team';\n      break;\n    case 'compliance_review':\n      assignedTo = 'compliance_team';\n      break;\n    default:\n      assignedTo = 'general_review';\n  }\n  \n  // Escalação crítica sempre vai para supervisor\n  if (escalationData.priority === 'critical') {\n    assignedTo = 'supervisor';\n  }\n}\n\n// Preparar dados para inserção\nconst escalationRecord = {\n  ...escalationData,\n  assigned_to: assignedTo,\n  content_data: {\n    platform: contentData.platform,\n    influencer_id: contentData.influencer_id,\n    content_text: contentData.content_text,\n    visual_url: contentData.visual_url,\n    published_at: contentData.published_at\n  }\n};\n\nreturn [{ escalation_record: escalationRecord }];"
      },
      "id": "8f14e45f-ceea-467a-9b87-7df96c6f136c",
      "name": "🔄 Process Escalation",
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [1120, 120]
    },
    {
      "parameters": {
        "operation": "insert",
        "schema": {
          "value": "agent"
        },
        "table": {
          "value": "human_escalations"
        },
        "columns": {
          "mappingMode": "defineBelow",
          "values": [
            {
              "column": "content_id",
              "value": "={{ $json.escalation_record.content_id }}"
            },
            {
              "column": "escalation_reason",
              "value": "={{ $json.escalation_record.escalation_reason }}"
            },
            {
              "column": "priority",
              "value": "={{ $json.escalation_record.priority }}"
            },
            {
              "column": "description",
              "value": "={{ $json.escalation_record.description }}"
            },
            {
              "column": "escalated_by",
              "value": "={{ $json.escalation_record.escalated_by }}"
            },
            {
              "column": "assigned_to",
              "value": "={{ $json.escalation_record.assigned_to }}"
            },
            {
              "column": "deadline",
              "value": "={{ $json.escalation_record.deadline }}"
            },
            {
              "column": "status",
              "value": "pending"
            },
            {
              "column": "metadata",
              "value": "={{ JSON.stringify($json.escalation_record.metadata) }}"
            },
            {
              "column": "content_snapshot",
              "value": "={{ JSON.stringify($json.escalation_record.content_data) }}"
            }
          ]
        },
        "options": {
          "queryReplacement": "RETURNING id, created_at"
        }
      },
      "id": "6ba7b822-9dad-11d1-80b4-00c04fd430c8",
      "name": "💾 Create Escalation",
      "type": "n8n-nodes-base.postgres",
      "typeVersion": 2.4,
      "position": [1340, 120],
      "credentials": {
        "postgres": {
          "id": "postgres-main",
          "name": "PostgreSQL - Main DB"
        }
      }
    },
    {
      "parameters": {
        "operation": "select",
        "schema": {
          "value": "agent"
        },
        "table": {
          "value": "human_escalations"
        },
        "where": {
          "values": [
            {
              "column": "status",
              "condition": "in",
              "value": "pending,in_progress"
            }
          ]
        },
        "sort": {
          "values": [
            {
              "column": "priority",
              "direction": "ASC"
            },
            {
              "column": "deadline",
              "direction": "ASC"
            }
          ]
        },
        "limit": 50,
        "options": {}
      },
      "id": "6ba7b823-9dad-11d1-80b4-00c04fd430c8",
      "name": "📋 Get Active Escalations",
      "type": "n8n-nodes-base.postgres",
      "typeVersion": 2.4,
      "position": [680, 280],
      "credentials": {
        "postgres": {
          "id": "postgres-main",
          "name": "PostgreSQL - Main DB"
        }
      }
    },
    {
      "parameters": {
        "jsCode": "// 📋 Formatar lista de escalações\nconst escalations = $input.all();\nconst request = $('🚨 Webhook - Escalation Request').first().json;\n\nif (escalations.length === 0) {\n  return [{\n    escalations_list: {\n      total: 0,\n      escalations: [],\n      message: 'Nenhuma escalação ativa encontrada'\n    }\n  }];\n}\n\n// Processar e enriquecer dados\nconst processedEscalations = escalations.map(item => {\n  const data = item.json;\n  const now = new Date();\n  const deadline = new Date(data.deadline);\n  const isOverdue = deadline < now;\n  const timeToDeadline = deadline.getTime() - now.getTime();\n  \n  // Calcular urgência\n  let urgency = 'normal';\n  if (isOverdue) {\n    urgency = 'overdue';\n  } else if (timeToDeadline <= 2 * 60 * 60 * 1000) { // 2 horas\n    urgency = 'urgent';\n  } else if (timeToDeadline <= 8 * 60 * 60 * 1000) { // 8 horas\n    urgency = 'soon';\n  }\n  \n  return {\n    id: data.id,\n    content_id: data.content_id,\n    escalation_reason: data.escalation_reason,\n    priority: data.priority,\n    status: data.status,\n    assigned_to: data.assigned_to,\n    escalated_by: data.escalated_by,\n    description: data.description,\n    deadline: data.deadline,\n    created_at: data.created_at,\n    updated_at: data.updated_at,\n    urgency: urgency,\n    is_overdue: isOverdue,\n    time_remaining: isOverdue ? 0 : Math.max(0, Math.floor(timeToDeadline / (1000 * 60))), // em minutos\n    content_preview: data.content_snapshot ? JSON.parse(data.content_snapshot) : null\n  };\n});\n\n// Agrupar por status e prioridade\nconst groupedByStatus = processedEscalations.reduce((acc, escalation) => {\n  if (!acc[escalation.status]) {\n    acc[escalation.status] = [];\n  }\n  acc[escalation.status].push(escalation);\n  return acc;\n}, {});\n\nconst groupedByPriority = processedEscalations.reduce((acc, escalation) => {\n  if (!acc[escalation.priority]) {\n    acc[escalation.priority] = [];\n  }\n  acc[escalation.priority].push(escalation);\n  return acc;\n}, {});\n\n// Estatísticas\nconst stats = {\n  total: processedEscalations.length,\n  by_status: Object.keys(groupedByStatus).reduce((acc, status) => {\n    acc[status] = groupedByStatus[status].length;\n    return acc;\n  }, {}),\n  by_priority: Object.keys(groupedByPriority).reduce((acc, priority) => {\n    acc[priority] = groupedByPriority[priority].length;\n    return acc;\n  }, {}),\n  overdue: processedEscalations.filter(e => e.is_overdue).length,\n  urgent: processedEscalations.filter(e => e.urgency === 'urgent').length\n};\n\n// Filtros aplicados (se houver)\nlet filteredEscalations = processedEscalations;\n\nif (request.filter_by_priority) {\n  filteredEscalations = filteredEscalations.filter(e => e.priority === request.filter_by_priority);\n}\n\nif (request.filter_by_status) {\n  filteredEscalations = filteredEscalations.filter(e => e.status === request.filter_by_status);\n}\n\nif (request.filter_by_assigned_to) {\n  filteredEscalations = filteredEscalations.filter(e => e.assigned_to === request.filter_by_assigned_to);\n}\n\nif (request.show_overdue_only) {\n  filteredEscalations = filteredEscalations.filter(e => e.is_overdue);\n}\n\nreturn [{\n  escalations_list: {\n    total: filteredEscalations.length,\n    total_unfiltered: processedEscalations.length,\n    escalations: filteredEscalations,\n    statistics: stats,\n    filters_applied: {\n      priority: request.filter_by_priority || null,\n      status: request.filter_by_status || null,\n      assigned_to: request.filter_by_assigned_to || null,\n      overdue_only: request.show_overdue_only || false\n    },\n    last_updated: new Date().toISOString()\n  }\n}];"
      },
      "id": "8f14e45f-ceea-467a-9b87-7df96c6f136d",
      "name": "📊 Format Escalations List",
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [900, 280]
    },
    {
      "parameters": {
        "jsCode": "// ✅ Validar resolução de escalação\nconst request = $input.first().json;\n\n// Validações obrigatórias\nconst requiredFields = ['escalation_id', 'resolution_type'];\nconst missingFields = requiredFields.filter(field => !request[field]);\n\nif (missingFields.length > 0) {\n  throw new Error(`Campos obrigatórios ausentes: ${missingFields.join(', ')}`);\n}\n\n// Validar tipo de resolução\nconst validResolutionTypes = [\n  'approved',\n  'rejected',\n  'modified',\n  'escalated_further',\n  'cancelled'\n];\n\nif (!validResolutionTypes.includes(request.resolution_type)) {\n  throw new Error(`Tipo de resolução inválido. Use: ${validResolutionTypes.join(', ')}`);\n}\n\n// Validações específicas por tipo\nif (request.resolution_type === 'modified' && !request.modifications) {\n  throw new Error('Modificações são obrigatórias quando resolution_type é \'modified\'');\n}\n\nif (request.resolution_type === 'rejected' && !request.rejection_reason) {\n  throw new Error('Razão da rejeição é obrigatória quando resolution_type é \'rejected\'');\n}\n\nreturn [{\n  resolution_data: {\n    escalation_id: request.escalation_id,\n    resolution_type: request.resolution_type,\n    resolution_notes: request.resolution_notes || '',\n    resolved_by: request.resolved_by || 'system',\n    modifications: request.modifications || null,\n    rejection_reason: request.rejection_reason || null,\n    escalate_to: request.escalate_to || null,\n    resolved_at: new Date().toISOString()\n  }\n}];"
      },
      "id": "8f14e45f-ceea-467a-9b87-7df96c6f136e",
      "name": "✅ Validate Resolution",
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [680, 400]
    },
    {
      "parameters": {
        "operation": "select",
        "schema": {
          "value": "agent"
        },
        "table": {
          "value": "human_escalations"
        },
        "where": {
          "values": [
            {
              "column": "id",
              "condition": "equal",
              "value": "={{ $('✅ Validate Resolution').first().json.resolution_data.escalation_id }}"
            }
          ]
        },
        "options": {}
      },
      "id": "6ba7b824-9dad-11d1-80b4-00c04fd430c8",
      "name": "🔍 Get Escalation Details",
      "type": "n8n-nodes-base.postgres",
      "typeVersion": 2.4,
      "position": [900, 360],
      "credentials": {
        "postgres": {
          "id": "postgres-main",
          "name": "PostgreSQL - Main DB"
        }
      }
    },
    {
      "parameters": {
        "jsCode": "// 🔄 Processar resolução\nconst resolutionData = $('✅ Validate Resolution').first().json.resolution_data;\nconst escalationData = $('🔍 Get Escalation Details').first()?.json;\n\nif (!escalationData) {\n  throw new Error(`Escalação com ID ${resolutionData.escalation_id} não encontrada`);\n}\n\n// Verificar se a escalação pode ser resolvida\nif (!['pending', 'in_progress'].includes(escalationData.status)) {\n  throw new Error(`Escalação não pode ser resolvida. Status atual: ${escalationData.status}`);\n}\n\n// Determinar novo status baseado no tipo de resolução\nlet newStatus;\nswitch (resolutionData.resolution_type) {\n  case 'approved':\n    newStatus = 'resolved_approved';\n    break;\n  case 'rejected':\n    newStatus = 'resolved_rejected';\n    break;\n  case 'modified':\n    newStatus = 'resolved_modified';\n    break;\n  case 'escalated_further':\n    newStatus = 'escalated';\n    break;\n  case 'cancelled':\n    newStatus = 'cancelled';\n    break;\n  default:\n    newStatus = 'resolved';\n}\n\n// Preparar dados de resolução\nconst resolutionRecord = {\n  ...resolutionData,\n  new_status: newStatus,\n  original_escalation: escalationData\n};\n\n// Se for escalação adicional, preparar dados para nova escalação\nif (resolutionData.resolution_type === 'escalated_further') {\n  if (!resolutionData.escalate_to) {\n    throw new Error('Campo escalate_to é obrigatório para escalação adicional');\n  }\n  \n  // Aumentar prioridade se possível\n  const priorityLevels = ['low', 'medium', 'high', 'critical'];\n  const currentPriorityIndex = priorityLevels.indexOf(escalationData.priority);\n  const newPriority = currentPriorityIndex < priorityLevels.length - 1 ? \n    priorityLevels[currentPriorityIndex + 1] : escalationData.priority;\n  \n  resolutionRecord.new_escalation = {\n    content_id: escalationData.content_id,\n    escalation_reason: 'escalated_from_previous',\n    priority: newPriority,\n    description: `Escalado de: ${escalationData.escalation_reason}. ${resolutionData.resolution_notes}`,\n    escalated_by: resolutionData.resolved_by,\n    assigned_to: resolutionData.escalate_to,\n    parent_escalation_id: escalationData.id\n  };\n}\n\nreturn [{ resolution_record: resolutionRecord }];"
      },
      "id": "8f14e45f-ceea-467a-9b87-7df96c6f136f",
      "name": "🔄 Process Resolution",
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [1120, 400]
    },
    {
      "parameters": {
        "operation": "update",
        "schema": {
          "value": "agent"
        },
        "table": {
          "value": "human_escalations"
        },
        "where": {
          "values": [
            {
              "column": "id",
              "condition": "equal",
              "value": "={{ $json.resolution_record.escalation_id }}"
            }
          ]
        },
        "columns": {
          "mappingMode": "defineBelow",
          "values": [
            {
              "column": "status",
              "value": "={{ $json.resolution_record.new_status }}"
            },
            {
              "column": "resolution_type",
              "value": "={{ $json.resolution_record.resolution_type }}"
            },
            {
              "column": "resolution_notes",
              "value": "={{ $json.resolution_record.resolution_notes }}"
            },
            {
              "column": "resolved_by",
              "value": "={{ $json.resolution_record.resolved_by }}"
            },
            {
              "column": "resolved_at",
              "value": "={{ $json.resolution_record.resolved_at }}"
            },
            {
              "column": "resolution_metadata",
              "value": "={{ JSON.stringify({modifications: $json.resolution_record.modifications, rejection_reason: $json.resolution_record.rejection_reason}) }}"
            }
          ]
        },
        "options": {}
      },
      "id": "6ba7b825-9dad-11d1-80b4-00c04fd430c8",
      "name": "💾 Update Escalation",
      "type": "n8n-nodes-base.postgres",
      "typeVersion": 2.4,
      "position": [1340, 360],
      "credentials": {
        "postgres": {
          "id": "postgres-main",
          "name": "PostgreSQL - Main DB"
        }
      }
    },
    {
      "parameters": {
        "conditions": {
          "options": {
            "caseSensitive": true,
            "leftValue": "",
            "typeValidation": "strict"
          },
          "conditions": [
            {
              "id": "c1",
              "leftValue": "={{ $json.resolution_record.resolution_type }}",
              "rightValue": "escalated_further",
              "operator": {
                "type": "string",
                "operation": "equals"
              }
            }
          ]
        },
        "options": {}
      },
      "id": "8f14e45f-ceea-467a-9b87-7df96c6f1370",
      "name": "🔀 Check Further Escalation",
      "type": "n8n-nodes-base.switch",
      "typeVersion": 3,
      "position": [1340, 440]
    },
    {
      "parameters": {
        "operation": "insert",
        "schema": {
          "value": "agent"
        },
        "table": {
          "value": "human_escalations"
        },
        "columns": {
          "mappingMode": "defineBelow",
          "values": [
            {
              "column": "content_id",
              "value": "={{ $json.resolution_record.new_escalation.content_id }}"
            },
            {
              "column": "escalation_reason",
              "value": "={{ $json.resolution_record.new_escalation.escalation_reason }}"
            },
            {
              "column": "priority",
              "value": "={{ $json.resolution_record.new_escalation.priority }}"
            },
            {
              "column": "description",
              "value": "={{ $json.resolution_record.new_escalation.description }}"
            },
            {
              "column": "escalated_by",
              "value": "={{ $json.resolution_record.new_escalation.escalated_by }}"
            },
            {
              "column": "assigned_to",
              "value": "={{ $json.resolution_record.new_escalation.assigned_to }}"
            },
            {
              "column": "parent_escalation_id",
              "value": "={{ $json.resolution_record.new_escalation.parent_escalation_id }}"
            },
            {
              "column": "status",
              "value": "pending"
            }
          ]
        },
        "options": {
          "queryReplacement": "RETURNING id, created_at"
        }
      },
      "id": "6ba7b826-9dad-11d1-80b4-00c04fd430c8",
      "name": "🆕 Create New Escalation",
      "type": "n8n-nodes-base.postgres",
      "typeVersion": 2.4,
      "position": [1560, 440],
      "credentials": {
        "postgres": {
          "id": "postgres-main",
          "name": "PostgreSQL - Main DB"
        }
      }
    },
    {
      "parameters": {
        "respondWith": "json",
        "responseBody": "={{ $json }}",
        "options": {}
      },
      "id": "f47ac10b-58cc-4372-a567-0e02b2c3d482",
      "name": "📤 Response",
      "type": "n8n-nodes-base.respondToWebhook",
      "typeVersion": 1,
      "position": [1560, 300]
    },
    {
      "parameters": {
        "jsCode": "// 📤 Formatar resposta final\nconst action = $('🚨 Webhook - Escalation Request').first().json.action;\nconst input = $input.first().json;\n\nlet response = {\n  success: true,\n  action: action,\n  timestamp: new Date().toISOString()\n};\n\nswitch (action) {\n  case 'escalate_content':\n    if (input.escalation_result) {\n      // Caso de escalação duplicada\n      response = {\n        success: false,\n        action: action,\n        ...input.escalation_result,\n        timestamp: new Date().toISOString()\n      };\n    } else {\n      // Escalação criada com sucesso\n      response.message = 'Escalação criada com sucesso';\n      response.data = {\n        escalation_id: input.id,\n        created_at: input.created_at,\n        status: 'pending'\n      };\n    }\n    break;\n    \n  case 'get_escalations':\n    response.data = input.escalations_list;\n    break;\n    \n  case 'resolve_escalation':\n    response.message = 'Escalação resolvida com sucesso';\n    response.data = {\n      escalation_id: input.resolution_record.escalation_id,\n      resolution_type: input.resolution_record.resolution_type,\n      new_status: input.resolution_record.new_status,\n      resolved_at: input.resolution_record.resolved_at\n    };\n    \n    // Se criou nova escalação\n    if (input.id && input.created_at) {\n      response.data.new_escalation = {\n        id: input.id,\n        created_at: input.created_at\n      };\n    }\n    break;\n    \n  case 'update_escalation':\n    response.message = 'Escalação atualizada com sucesso';\n    response.data = input;\n    break;\n}\n\nreturn response;"
      },
      "id": "8f14e45f-ceea-467a-9b87-7df96c6f1371",
      "name": "📋 Format Final Response",
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [1340, 300]
    },
    {
      "parameters": {
        "operation": "insert",
        "schema": {
          "value": "agent"
        },
        "table": {
          "value": "system_logs"
        },
        "columns": {
          "mappingMode": "defineBelow",
          "values": [
            {
              "column": "event_type",
              "value": "escalation_process"
            },
            {
              "column": "event_data",
              "value": "={{ JSON.stringify({action: $('🚨 Webhook - Escalation Request').first().json.action, result: $json}) }}"
            },
            {
              "column": "severity",
              "value": "info"
            }
          ]
        },
        "options": {}
      },
      "id": "6ba7b827-9dad-11d1-80b4-00c04fd430c8",
      "name": "📝 Log Escalation Process",
      "type": "n8n-nodes-base.postgres",
      "typeVersion": 2.4,
      "position": [1560, 240],
      "credentials": {
        "postgres": {
          "id": "postgres-main",
          "name": "PostgreSQL - Main DB"
        }
      }
    },
    {
      "parameters": {
        "jsCode": "// 🚨 Tratamento de erro\nconst error = $input.first().json;\n\nreturn {\n  success: false,\n  error: {\n    message: error.message || 'Erro interno do servidor',\n    code: error.code || 'ESCALATION_ERROR',\n    details: error.details || null\n  },\n  timestamp: new Date().toISOString()\n};"
      },
      "id": "8f14e45f-ceea-467a-9b87-7df96c6f1372",
      "name": "🚨 Error Handler",
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [1340, 600]
    },
    {
      "parameters": {
        "operation": "insert",
        "schema": {
          "value": "agent"
        },
        "table": {
          "value": "system_logs"
        },
        "columns": {
          "mappingMode": "defineBelow",
          "values": [
            {
              "column": "event_type",
              "value": "escalation_error"
            },
            {
              "column": "event_data",
              "value": "={{ JSON.stringify({error: $json.error, action: $('🚨 Webhook - Escalation Request').first().json.action}) }}"
            },
            {
              "column": "severity",
              "value": "error"
            }
          ]
        },
        "options": {}
      },
      "id": "6ba7b828-9dad-11d1-80b4-00c04fd430c8",
      "name": "📝 Log Error",
      "type": "n8n-nodes-base.postgres",
      "typeVersion": 2.4,
      "position": [1560, 600],
      "credentials": {
        "postgres": {
          "id": "postgres-main",
          "name": "PostgreSQL - Main DB"
        }
      }
    }
  ],
  "connections": {
    "🚨 Webhook - Escalation Request": {
      "main": [
        [
          {
            "node": "🔀 Route Action",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "🔀 Route Action": {
      "main": [
        [
          {
            "node": "✅ Validate Escalation",
            "type": "main",
            "index": 0
          }
        ],
        [
          {
            "node": "✅ Validate Resolution",
            "type": "main",
            "index": 0
          }
        ],
        [
          {
            "node": "📋 Get Active Escalations",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "✅ Validate Escalation": {
      "main": [
        [
          {
            "node": "📄 Get Content Details",
            "type": "main",
            "index": 0
          },
          {
            "node": "🔍 Check Existing Escalations",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "📄 Get Content Details": {
      "main": [
        [
          {
            "node": "🔄 Process Escalation",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "🔍 Check Existing Escalations": {
      "main": [
        [
          {
            "node": "🔄 Process Escalation",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "🔄 Process Escalation": {
      "main": [
        [
          {
            "node": "💾 Create Escalation",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "💾 Create Escalation": {
      "main": [
        [
          {
            "node": "📋 Format Final Response",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "📋 Get Active Escalations": {
      "main": [
        [
          {
            "node": "📊 Format Escalations List",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "📊 Format Escalations List": {
      "main": [
        [
          {
            "node": "📋 Format Final Response",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "✅ Validate Resolution": {
      "main": [
        [
          {
            "node": "🔍 Get Escalation Details",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "🔍 Get Escalation Details": {
      "main": [
        [
          {
            "node": "🔄 Process Resolution",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "🔄 Process Resolution": {
      "main": [
        [
          {
            "node": "💾 Update Escalation",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "💾 Update Escalation": {
      "main": [
        [
          {
            "node": "🔀 Check Further Escalation",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "🔀 Check Further Escalation": {
      "main": [
        [
          {
            "node": "🆕 Create New Escalation",
            "type": "main",
            "index": 0
          }
        ],
        [
          {
            "node": "📋 Format Final Response",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "🆕 Create New Escalation": {
      "main": [
        [
          {
            "node": "📋 Format Final Response",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "📋 Format Final Response": {
      "main": [
        [
          {
            "node": "📝 Log Escalation Process",
            "type": "main",
            "index": 0
          },
          {
            "node": "📤 Response",
            "type": "main",
            "index": 0
          }
        ]
      ]
    }
  },
  "pinData": {},
  "settings": {
    "executionOrder": "v1"
  },
  "staticData": null,
  "tags": [
    {
      "createdAt": "2024-01-15T10:00:00.000Z",
      "updatedAt": "2024-01-15T10:00:00.000Z",
      "id": "escalation-human",
      "name": "Escalation & Human"
    }
  ],
  "triggerCount": 0,
  "updatedAt": "2024-01-15T10:00:00.000Z",
  "versionId": "1"
}