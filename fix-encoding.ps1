# fix-encoding.ps1 - <PERSON>ript para corrigir problemas de codificação
Write-Host "Corrigindo problemas de codificação no Start-Environment.ps1..." -ForegroundColor Yellow

# Ler o arquivo
$content = Get-Content "Start-Environment.ps1" -Raw -Encoding UTF8

# Substituições de caracteres problemáticos
$fixes = @{
    # Emojis problemáticos
    'ðŸ"§' = ''
    'ðŸš€' = ''
    'ðŸ"„' = ''
    'ðŸ"…' = ''
    'ðŸ"‹' = ''
    'ðŸŒ' = ''
    'ðŸ"' = ''
    'âš ï¸' = 'ATENÇÃO:'
    'âœ…' = 'OK'
    'âœ"' = 'OK'
    'âŒ' = 'ERRO'
    'ðŸ"Š' = ''
    'ðŸ"±' = ''
    'ðŸ'¬' = ''
    'ðŸ"¦' = ''
    'ðŸŽ¯' = ''
    
    # Caracteres especiais de texto
    'VersÃ£o' = 'Versão'
    'DiagnÃ³sticos' = 'Diagnósticos'
    'AvanÃ§ados' = 'Avançados'
    'inicializaÃ§Ã£o' = 'inicialização'
    'configuraÃ§Ã£o' = 'configuração'
    'automÃ¡tica' = 'automática'
    'avanÃ§ada' = 'avançada'
    'instalaÃ§Ã£o' = 'instalação'
    'nÃ£o' = 'não'
    'estÃ¡' = 'está'
    'jÃ¡' = 'já'
    'necessÃ¡rio' = 'necessário'
    'saudÃ¡vel' = 'saudável'
    'crÃ­ticos' = 'críticos'
    'especÃ­ficos' = 'específicos'
    'pÃºblico' = 'público'
    'tÃºnel' = 'túnel'
    'autenticaÃ§Ã£o' = 'autenticação'
    'migraÃ§Ã£o' = 'migração'
    'concluÃ­da' = 'concluída'
    'usuÃ¡rio' = 'usuário'
    'administrador' = 'administrador'
    'diagnÃ³stico' = 'diagnóstico'
    'serviÃ§os' = 'serviços'
    'instruÃ§Ãµes' = 'instruções'
    'disponÃ­vel' = 'disponível'
    'mÃ³dulo' = 'módulo'
    'encontrado' = 'encontrado'
    'alternativo' = 'alternativo'
    'automÃ¡tica' = 'automática'
    'falhou' = 'falhou'
    'cÃ³digo' = 'código'
    'necessÃ¡ria' = 'necessária'
    'manualmente' = 'manualmente'
    'reconfigurar' = 'reconfigurar'
    'completa' = 'completa'
    'workflows' = 'workflows'
    
    # Caracteres de borda problemáticos
    'â•"â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•—' = '╔══════════════════════════════════════════════════════════╗'
    'â•'      N8N EVOLUTION ENVIRONMENT - INSTALADOR ROBUSTO      â•'' = '║      N8N EVOLUTION ENVIRONMENT - INSTALADOR ROBUSTO      ║'
    'â•šâ•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•' = '╚══════════════════════════════════════════════════════════╝'
    'â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•' = '═══════════════════════════════════════════════════════════'
    'â€¢' = '•'
}

# Aplicar correções
foreach ($old in $fixes.Keys) {
    $new = $fixes[$old]
    $content = $content -replace [regex]::Escape($old), $new
}

# Salvar arquivo corrigido
$content | Out-File "Start-Environment.ps1" -Encoding UTF8 -NoNewline

Write-Host "Correções aplicadas com sucesso!" -ForegroundColor Green
Write-Host "Testando sintaxe..." -ForegroundColor Yellow

# Testar sintaxe
try {
    $null = [System.Management.Automation.PSParser]::Tokenize($content, [ref]$null)
    Write-Host "Sintaxe OK!" -ForegroundColor Green
} catch {
    Write-Host "Ainda há problemas de sintaxe: $($_.Exception.Message)" -ForegroundColor Red
}
