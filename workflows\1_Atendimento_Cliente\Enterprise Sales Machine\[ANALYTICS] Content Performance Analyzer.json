{
  "name": "[ANALYTICS] Content Performance Analyzer",
  "nodes": [
    {
      "parameters": {
        "httpMethod": "POST",
        "path": "content-analytics",
        "options": {}
      },
      "id": "f47ac10b-58cc-4372-a567-0e02b2c3d479",
      "name": "📊 Webhook - Analytics Request",
      "type": "n8n-nodes-base.webhook",
      "typeVersion": 1,
      "position": [240, 300],
      "webhookId": "content-analytics"
    },
    {
      "parameters": {
        "conditions": {
          "options": {
            "caseSensitive": true,
            "leftValue": "",
            "typeValidation": "strict"
          },
          "conditions": [
            {
              "id": "c1",
              "leftValue": "={{ $json.action }}",
              "rightValue": "analyze_performance",
              "operator": {
                "type": "string",
                "operation": "equals"
              }
            },
            {
              "id": "c2",
              "leftValue": "={{ $json.action }}",
              "rightValue": "update_metrics",
              "operator": {
                "type": "string",
                "operation": "equals"
              }
            },
            {
              "id": "c3",
              "leftValue": "={{ $json.action }}",
              "rightValue": "get_insights",
              "operator": {
                "type": "string",
                "operation": "equals"
              }
            },
            {
              "id": "c4",
              "leftValue": "={{ $json.action }}",
              "rightValue": "discover_patterns",
              "operator": {
                "type": "string",
                "operation": "equals"
              }
            }
          ],
          "combinator": "or"
        },
        "options": {}
      },
      "id": "8f14e45f-ceea-467a-9b87-7df96c6f135e",
      "name": "🔀 Route Action",
      "type": "n8n-nodes-base.switch",
      "typeVersion": 3,
      "position": [460, 300]
    },
    {
      "parameters": {
        "operation": "select",
        "schema": {
          "value": "agent"
        },
        "table": {
          "value": "published_content"
        },
        "where": {
          "values": [
            {
              "column": "published_at",
              "condition": "greaterThan",
              "value": "={{ new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString() }}"
            },
            {
              "column": "status",
              "condition": "in",
              "value": "approved,auto_approved"
            }
          ]
        },
        "sort": {
          "values": [
            {
              "column": "published_at",
              "direction": "DESC"
            }
          ]
        },
        "limit": 100,
        "options": {}
      },
      "id": "6ba7b810-9dad-11d1-80b4-00c04fd430c8",
      "name": "📈 Get Recent Content",
      "type": "n8n-nodes-base.postgres",
      "typeVersion": 2.4,
      "position": [680, 120],
      "credentials": {
        "postgres": {
          "id": "postgres-main",
          "name": "PostgreSQL - Main DB"
        }
      }
    },
    {
      "parameters": {
        "jsCode": "// 🤖 Análise inteligente de performance usando IA\nconst contentItems = $input.all();\n\nif (contentItems.length === 0) {\n  return [{\n    analysis_summary: 'Nenhum conteúdo encontrado para análise',\n    recommendations: [],\n    performance_score: 0\n  }];\n}\n\n// Calcular métricas agregadas\nconst totalEngagement = contentItems.reduce((sum, item) => {\n  const data = item.json;\n  return sum + (data.likes_count || 0) + (data.comments_count || 0) + (data.shares_count || 0);\n}, 0);\n\nconst avgEngagement = totalEngagement / contentItems.length;\n\n// Analisar padrões de performance\nconst performanceAnalysis = contentItems.map(item => {\n  const data = item.json;\n  const engagement = (data.likes_count || 0) + (data.comments_count || 0) + (data.shares_count || 0);\n  const hoursOld = (Date.now() - new Date(data.published_at).getTime()) / (1000 * 60 * 60);\n  \n  // Calcular score de performance normalizado\n  const engagementRate = hoursOld > 0 ? engagement / Math.max(hoursOld, 1) : engagement;\n  \n  return {\n    content_id: data.id,\n    influencer_id: data.influencer_id,\n    platform: data.platform,\n    content_preview: data.content_text ? data.content_text.substring(0, 100) + '...' : '',\n    engagement_total: engagement,\n    engagement_rate: engagementRate,\n    hours_old: Math.round(hoursOld * 10) / 10,\n    performance_category: engagementRate > avgEngagement * 1.5 ? 'high' : \n                         engagementRate > avgEngagement * 0.5 ? 'medium' : 'low'\n  };\n});\n\n// Identificar top performers\nconst topPerformers = performanceAnalysis\n  .filter(item => item.performance_category === 'high')\n  .sort((a, b) => b.engagement_rate - a.engagement_rate)\n  .slice(0, 5);\n\n// Identificar underperformers\nconst underPerformers = performanceAnalysis\n  .filter(item => item.performance_category === 'low')\n  .sort((a, b) => a.engagement_rate - b.engagement_rate)\n  .slice(0, 5);\n\n// Análise por plataforma\nconst platformAnalysis = {};\nperformanceAnalysis.forEach(item => {\n  if (!platformAnalysis[item.platform]) {\n    platformAnalysis[item.platform] = {\n      count: 0,\n      total_engagement: 0,\n      avg_engagement: 0\n    };\n  }\n  platformAnalysis[item.platform].count++;\n  platformAnalysis[item.platform].total_engagement += item.engagement_total;\n});\n\nObject.keys(platformAnalysis).forEach(platform => {\n  platformAnalysis[platform].avg_engagement = \n    platformAnalysis[platform].total_engagement / platformAnalysis[platform].count;\n});\n\n// Gerar insights e recomendações\nconst insights = [];\nconst recommendations = [];\n\nif (topPerformers.length > 0) {\n  insights.push(`${topPerformers.length} conteúdos com alta performance identificados`);\n  recommendations.push('Analisar padrões dos top performers para replicar estratégias');\n}\n\nif (underPerformers.length > 0) {\n  insights.push(`${underPerformers.length} conteúdos com baixa performance precisam de atenção`);\n  recommendations.push('Revisar estratégia para conteúdos com baixo engajamento');\n}\n\nconst bestPlatform = Object.keys(platformAnalysis)\n  .reduce((a, b) => platformAnalysis[a].avg_engagement > platformAnalysis[b].avg_engagement ? a : b);\n\nif (bestPlatform) {\n  insights.push(`${bestPlatform} é a plataforma com melhor performance média`);\n  recommendations.push(`Focar mais conteúdo na plataforma ${bestPlatform}`);\n}\n\n// Score geral de performance (0-100)\nconst performanceScore = Math.min(100, Math.round((avgEngagement / 10) * 100));\n\nreturn [{\n  analysis_summary: {\n    total_content: contentItems.length,\n    avg_engagement: Math.round(avgEngagement * 100) / 100,\n    performance_score: performanceScore,\n    analysis_period: '7 dias',\n    top_performers_count: topPerformers.length,\n    underperformers_count: underPerformers.length\n  },\n  detailed_analysis: performanceAnalysis,\n  top_performers: topPerformers,\n  underperformers: underPerformers,\n  platform_analysis: platformAnalysis,\n  insights: insights,\n  recommendations: recommendations,\n  generated_at: new Date().toISOString()\n}];"
      },
      "id": "8f14e45f-ceea-467a-9b87-7df96c6f135f",
      "name": "🤖 AI Performance Analysis",
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [900, 120]
    },
    {
      "parameters": {
        "operation": "insert",
        "schema": {
          "value": "agent"
        },
        "table": {
          "value": "content_performance_analysis"
        },
        "columns": {
          "mappingMode": "defineBelow",
          "values": [
            {
              "column": "analysis_type",
              "value": "weekly_performance"
            },
            {
              "column": "analysis_data",
              "value": "={{ JSON.stringify($json) }}"
            },
            {
              "column": "performance_score",
              "value": "={{ $json.analysis_summary.performance_score }}"
            },
            {
              "column": "insights_count",
              "value": "={{ $json.insights.length }}"
            },
            {
              "column": "recommendations_count",
              "value": "={{ $json.recommendations.length }}"
            },
            {
              "column": "created_by",
              "value": "ai_analytics_engine"
            }
          ]
        },
        "options": {}
      },
      "id": "6ba7b811-9dad-11d1-80b4-00c04fd430c8",
      "name": "💾 Save Analysis",
      "type": "n8n-nodes-base.postgres",
      "typeVersion": 2.4,
      "position": [1120, 120],
      "credentials": {
        "postgres": {
          "id": "postgres-main",
          "name": "PostgreSQL - Main DB"
        }
      }
    },
    {
      "parameters": {
        "jsCode": "// 📊 Validar e processar atualização de métricas\nconst input = $input.first().json;\n\n// Validações básicas\nif (!input.content_id) {\n  throw new Error('content_id é obrigatório');\n}\n\nif (!input.metrics || typeof input.metrics !== 'object') {\n  throw new Error('metrics deve ser um objeto válido');\n}\n\n// Validar métricas numéricas\nconst validMetrics = ['likes_count', 'comments_count', 'shares_count', 'views_count', 'saves_count'];\nconst metricsToUpdate = {};\n\nvalidMetrics.forEach(metric => {\n  if (input.metrics[metric] !== undefined) {\n    const value = parseInt(input.metrics[metric]);\n    if (isNaN(value) || value < 0) {\n      throw new Error(`${metric} deve ser um número não negativo`);\n    }\n    metricsToUpdate[metric] = value;\n  }\n});\n\nif (Object.keys(metricsToUpdate).length === 0) {\n  throw new Error('Pelo menos uma métrica válida deve ser fornecida');\n}\n\n// Calcular engagement total\nconst totalEngagement = (metricsToUpdate.likes_count || 0) + \n                       (metricsToUpdate.comments_count || 0) + \n                       (metricsToUpdate.shares_count || 0);\n\nreturn {\n  content_id: input.content_id,\n  metrics: metricsToUpdate,\n  total_engagement: totalEngagement,\n  updated_at: new Date().toISOString(),\n  source: input.source || 'manual_update'\n};"
      },
      "id": "8f14e45f-ceea-467a-9b87-7df96c6f1360",
      "name": "📊 Validate Metrics Update",
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [680, 240]
    },
    {
      "parameters": {
        "operation": "update",
        "schema": {
          "value": "agent"
        },
        "table": {
          "value": "published_content"
        },
        "where": {
          "values": [
            {
              "column": "id",
              "condition": "equal",
              "value": "={{ $json.content_id }}"
            }
          ]
        },
        "columns": {
          "mappingMode": "defineBelow",
          "values": [
            {
              "column": "likes_count",
              "value": "={{ $json.metrics.likes_count || null }}"
            },
            {
              "column": "comments_count",
              "value": "={{ $json.metrics.comments_count || null }}"
            },
            {
              "column": "shares_count",
              "value": "={{ $json.metrics.shares_count || null }}"
            },
            {
              "column": "views_count",
              "value": "={{ $json.metrics.views_count || null }}"
            },
            {
              "column": "saves_count",
              "value": "={{ $json.metrics.saves_count || null }}"
            },
            {
              "column": "total_engagement",
              "value": "={{ $json.total_engagement }}"
            },
            {
              "column": "metrics_updated_at",
              "value": "={{ $json.updated_at }}"
            }
          ]
        },
        "options": {}
      },
      "id": "6ba7b812-9dad-11d1-80b4-00c04fd430c8",
      "name": "🔄 Update Content Metrics",
      "type": "n8n-nodes-base.postgres",
      "typeVersion": 2.4,
      "position": [900, 240],
      "credentials": {
        "postgres": {
          "id": "postgres-main",
          "name": "PostgreSQL - Main DB"
        }
      }
    },
    {
      "parameters": {
        "operation": "select",
        "schema": {
          "value": "agent"
        },
        "table": {
          "value": "content_performance_analysis"
        },
        "where": {
          "values": [
            {
              "column": "created_at",
              "condition": "greaterThan",
              "value": "={{ new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString() }}"
            }
          ]
        },
        "sort": {
          "values": [
            {
              "column": "created_at",
              "direction": "DESC"
            }
          ]
        },
        "limit": 10,
        "options": {}
      },
      "id": "6ba7b813-9dad-11d1-80b4-00c04fd430c8",
      "name": "📈 Get Recent Insights",
      "type": "n8n-nodes-base.postgres",
      "typeVersion": 2.4,
      "position": [680, 360],
      "credentials": {
        "postgres": {
          "id": "postgres-main",
          "name": "PostgreSQL - Main DB"
        }
      }
    },
    {
      "parameters": {
        "jsCode": "// 💡 Compilar insights e tendências\nconst analyses = $input.all();\n\nif (analyses.length === 0) {\n  return [{\n    insights_summary: 'Nenhuma análise recente encontrada',\n    trends: [],\n    recommendations: []\n  }];\n}\n\n// Extrair dados das análises\nconst allInsights = [];\nconst allRecommendations = [];\nconst performanceScores = [];\n\nanalyses.forEach(analysis => {\n  const data = JSON.parse(analysis.json.analysis_data);\n  \n  if (data.insights) {\n    allInsights.push(...data.insights);\n  }\n  \n  if (data.recommendations) {\n    allRecommendations.push(...data.recommendations);\n  }\n  \n  if (data.analysis_summary && data.analysis_summary.performance_score) {\n    performanceScores.push({\n      score: data.analysis_summary.performance_score,\n      date: analysis.json.created_at\n    });\n  }\n});\n\n// Analisar tendências de performance\nconst trends = [];\nif (performanceScores.length >= 2) {\n  const recent = performanceScores.slice(0, 3);\n  const older = performanceScores.slice(3, 6);\n  \n  if (recent.length > 0 && older.length > 0) {\n    const recentAvg = recent.reduce((sum, item) => sum + item.score, 0) / recent.length;\n    const olderAvg = older.reduce((sum, item) => sum + item.score, 0) / older.length;\n    \n    const change = recentAvg - olderAvg;\n    const changePercent = Math.round((change / olderAvg) * 100);\n    \n    if (Math.abs(changePercent) > 5) {\n      trends.push({\n        type: 'performance_trend',\n        direction: change > 0 ? 'improving' : 'declining',\n        change_percent: changePercent,\n        description: `Performance ${change > 0 ? 'melhorou' : 'declinou'} ${Math.abs(changePercent)}% nas últimas análises`\n      });\n    }\n  }\n}\n\n// Identificar insights mais frequentes\nconst insightFrequency = {};\nallInsights.forEach(insight => {\n  const key = insight.toLowerCase().replace(/\\d+/g, 'X'); // Normalizar números\n  insightFrequency[key] = (insightFrequency[key] || 0) + 1;\n});\n\nconst frequentInsights = Object.entries(insightFrequency)\n  .filter(([_, count]) => count > 1)\n  .sort(([_, a], [__, b]) => b - a)\n  .slice(0, 5)\n  .map(([insight, count]) => ({\n    insight: insight,\n    frequency: count,\n    type: 'recurring_pattern'\n  }));\n\n// Compilar recomendações únicas\nconst uniqueRecommendations = [...new Set(allRecommendations)]\n  .slice(0, 10)\n  .map(rec => ({\n    recommendation: rec,\n    priority: allRecommendations.filter(r => r === rec).length > 1 ? 'high' : 'medium'\n  }));\n\nreturn [{\n  insights_summary: {\n    total_analyses: analyses.length,\n    period_days: 30,\n    avg_performance_score: performanceScores.length > 0 ? \n      Math.round(performanceScores.reduce((sum, item) => sum + item.score, 0) / performanceScores.length) : 0,\n    trends_identified: trends.length,\n    recurring_patterns: frequentInsights.length\n  },\n  performance_trends: trends,\n  recurring_insights: frequentInsights,\n  recommendations: uniqueRecommendations,\n  latest_analysis_date: analyses[0]?.json.created_at,\n  compiled_at: new Date().toISOString()\n}];"
      },
      "id": "8f14e45f-ceea-467a-9b87-7df96c6f1361",
      "name": "💡 Compile Insights",
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [900, 360]
    },
    {
      "parameters": {
        "operation": "select",
        "schema": {
          "value": "agent"
        },
        "table": {
          "value": "published_content"
        },
        "where": {
          "values": [
            {
              "column": "published_at",
              "condition": "greaterThan",
              "value": "={{ new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString() }}"
            },
            {
              "column": "total_engagement",
              "condition": "greaterThan",
              "value": "0"
            }
          ]
        },
        "sort": {
          "values": [
            {
              "column": "total_engagement",
              "direction": "DESC"
            }
          ]
        },
        "limit": 50,
        "options": {}
      },
      "id": "6ba7b814-9dad-11d1-80b4-00c04fd430c8",
      "name": "🔍 Get High-Performing Content",
      "type": "n8n-nodes-base.postgres",
      "typeVersion": 2.4,
      "position": [680, 480],
      "credentials": {
        "postgres": {
          "id": "postgres-main",
          "name": "PostgreSQL - Main DB"
        }
      }
    },
    {
      "parameters": {
        "jsCode": "// 🔍 Descobrir padrões de sucesso usando ML\nconst contentItems = $input.all();\n\nif (contentItems.length < 5) {\n  return [{\n    patterns_found: 0,\n    message: 'Dados insuficientes para análise de padrões (mínimo 5 conteúdos)',\n    recommendations: ['Aguardar mais dados para análise significativa']\n  }];\n}\n\n// Analisar padrões de texto\nconst textPatterns = {};\nconst platformPatterns = {};\nconst timePatterns = {};\nconst lengthPatterns = {};\n\ncontentItems.forEach(item => {\n  const data = item.json;\n  const engagement = data.total_engagement || 0;\n  \n  // Padrões de plataforma\n  if (!platformPatterns[data.platform]) {\n    platformPatterns[data.platform] = { total: 0, count: 0, avg: 0 };\n  }\n  platformPatterns[data.platform].total += engagement;\n  platformPatterns[data.platform].count++;\n  \n  // Padrões de horário\n  const hour = new Date(data.published_at).getHours();\n  const timeSlot = hour < 6 ? 'madrugada' : \n                  hour < 12 ? 'manha' : \n                  hour < 18 ? 'tarde' : 'noite';\n  \n  if (!timePatterns[timeSlot]) {\n    timePatterns[timeSlot] = { total: 0, count: 0, avg: 0 };\n  }\n  timePatterns[timeSlot].total += engagement;\n  timePatterns[timeSlot].count++;\n  \n  // Padrões de comprimento\n  const textLength = data.content_text ? data.content_text.length : 0;\n  const lengthCategory = textLength < 50 ? 'curto' : \n                        textLength < 150 ? 'medio' : 'longo';\n  \n  if (!lengthPatterns[lengthCategory]) {\n    lengthPatterns[lengthCategory] = { total: 0, count: 0, avg: 0 };\n  }\n  lengthPatterns[lengthCategory].total += engagement;\n  lengthPatterns[lengthCategory].count++;\n  \n  // Padrões de palavras-chave (análise simples)\n  if (data.content_text) {\n    const words = data.content_text.toLowerCase()\n      .replace(/[^a-záàâãéèêíìîóòôõúùûç\\s]/g, '')\n      .split(/\\s+/)\n      .filter(word => word.length > 3);\n    \n    words.forEach(word => {\n      if (!textPatterns[word]) {\n        textPatterns[word] = { total: 0, count: 0, avg: 0 };\n      }\n      textPatterns[word].total += engagement;\n      textPatterns[word].count++;\n    });\n  }\n});\n\n// Calcular médias\nObject.keys(platformPatterns).forEach(platform => {\n  platformPatterns[platform].avg = platformPatterns[platform].total / platformPatterns[platform].count;\n});\n\nObject.keys(timePatterns).forEach(time => {\n  timePatterns[time].avg = timePatterns[time].total / timePatterns[time].count;\n});\n\nObject.keys(lengthPatterns).forEach(length => {\n  lengthPatterns[length].avg = lengthPatterns[length].total / lengthPatterns[length].count;\n});\n\nObject.keys(textPatterns).forEach(word => {\n  textPatterns[word].avg = textPatterns[word].total / textPatterns[word].count;\n});\n\n// Identificar padrões de sucesso\nconst successPatterns = [];\n\n// Melhor plataforma\nconst bestPlatform = Object.entries(platformPatterns)\n  .sort(([,a], [,b]) => b.avg - a.avg)[0];\nif (bestPlatform) {\n  successPatterns.push({\n    type: 'platform',\n    pattern: `Plataforma ${bestPlatform[0]}`,\n    avg_engagement: Math.round(bestPlatform[1].avg),\n    confidence: bestPlatform[1].count >= 3 ? 'high' : 'medium',\n    sample_size: bestPlatform[1].count\n  });\n}\n\n// Melhor horário\nconst bestTime = Object.entries(timePatterns)\n  .sort(([,a], [,b]) => b.avg - a.avg)[0];\nif (bestTime) {\n  successPatterns.push({\n    type: 'timing',\n    pattern: `Publicação no período da ${bestTime[0]}`,\n    avg_engagement: Math.round(bestTime[1].avg),\n    confidence: bestTime[1].count >= 3 ? 'high' : 'medium',\n    sample_size: bestTime[1].count\n  });\n}\n\n// Melhor comprimento\nconst bestLength = Object.entries(lengthPatterns)\n  .sort(([,a], [,b]) => b.avg - a.avg)[0];\nif (bestLength) {\n  successPatterns.push({\n    type: 'content_length',\n    pattern: `Textos ${bestLength[0]}s`,\n    avg_engagement: Math.round(bestLength[1].avg),\n    confidence: bestLength[1].count >= 3 ? 'high' : 'medium',\n    sample_size: bestLength[1].count\n  });\n}\n\n// Top palavras-chave\nconst topKeywords = Object.entries(textPatterns)\n  .filter(([word, data]) => data.count >= 2 && data.avg > 0)\n  .sort(([,a], [,b]) => b.avg - a.avg)\n  .slice(0, 5)\n  .map(([word, data]) => ({\n    type: 'keyword',\n    pattern: `Palavra-chave: \"${word}\"",\n    avg_engagement: Math.round(data.avg),\n    confidence: data.count >= 3 ? 'high' : 'medium',\n    sample_size: data.count\n  }));\n\nsuccessPatterns.push(...topKeywords);\n\n// Gerar recomendações baseadas nos padrões\nconst recommendations = [];\n\nif (bestPlatform && bestPlatform[1].count >= 3) {\n  recommendations.push(`Focar mais conteúdo na plataforma ${bestPlatform[0]} (${Math.round(bestPlatform[1].avg)} eng. média)`);\n}\n\nif (bestTime && bestTime[1].count >= 3) {\n  recommendations.push(`Publicar preferencialmente no período da ${bestTime[0]}`);\n}\n\nif (bestLength && bestLength[1].count >= 3) {\n  recommendations.push(`Manter textos ${bestLength[0]}s para melhor engajamento`);\n}\n\nif (topKeywords.length > 0) {\n  recommendations.push(`Incluir palavras-chave de alto engajamento: ${topKeywords.slice(0, 3).map(k => k.pattern.split('\"')[1]).join(', ')}`);\n}\n\nreturn [{\n  patterns_found: successPatterns.length,\n  success_patterns: successPatterns,\n  recommendations: recommendations,\n  analysis_confidence: successPatterns.filter(p => p.confidence === 'high').length / successPatterns.length,\n  sample_size: contentItems.length,\n  discovered_at: new Date().toISOString()\n}];"
      },
      "id": "8f14e45f-ceea-467a-9b87-7df96c6f1362",
      "name": "🔍 Discover Success Patterns",
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [900, 480]
    },
    {
      "parameters": {
        "operation": "insert",
        "schema": {
          "value": "agent"
        },
        "table": {
          "value": "success_patterns"
        },
        "columns": {
          "mappingMode": "defineBelow",
          "values": [
            {
              "column": "pattern_type",
              "value": "ai_discovered"
            },
            {
              "column": "pattern_data",
              "value": "={{ JSON.stringify($json) }}"
            },
            {
              "column": "confidence_score",
              "value": "={{ $json.analysis_confidence || 0 }}"
            },
            {
              "column": "sample_size",
              "value": "={{ $json.sample_size }}"
            },
            {
              "column": "patterns_count",
              "value": "={{ $json.patterns_found }}"
            },
            {
              "column": "created_by",
              "value": "ai_pattern_discovery"
            }
          ]
        },
        "options": {}
      },
      "id": "6ba7b815-9dad-11d1-80b4-00c04fd430c8",
      "name": "💾 Save Success Patterns",
      "type": "n8n-nodes-base.postgres",
      "typeVersion": 2.4,
      "position": [1120, 480],
      "credentials": {
        "postgres": {
          "id": "postgres-main",
          "name": "PostgreSQL - Main DB"
        }
      }
    },
    {
      "parameters": {
        "respondWith": "json",
        "responseBody": "={{ $json }}",
        "options": {}
      },
      "id": "f47ac10b-58cc-4372-a567-0e02b2c3d480",
      "name": "📤 Response",
      "type": "n8n-nodes-base.respondToWebhook",
      "typeVersion": 1,
      "position": [1340, 300]
    },
    {
      "parameters": {
        "jsCode": "// 📤 Formatar resposta final\nconst action = $('📊 Webhook - Analytics Request').first().json.action;\nconst input = $input.first().json;\n\nlet response = {\n  success: true,\n  action: action,\n  timestamp: new Date().toISOString()\n};\n\nswitch (action) {\n  case 'analyze_performance':\n    response.data = {\n      analysis: input,\n      summary: input.analysis_summary,\n      insights_count: input.insights ? input.insights.length : 0,\n      recommendations_count: input.recommendations ? input.recommendations.length : 0\n    };\n    break;\n    \n  case 'update_metrics':\n    response.message = 'Métricas atualizadas com sucesso';\n    response.data = {\n      content_id: input.content_id,\n      total_engagement: input.total_engagement,\n      metrics_updated: Object.keys(input.metrics)\n    };\n    break;\n    \n  case 'get_insights':\n    response.data = {\n      insights: input,\n      summary: input.insights_summary,\n      trends_count: input.performance_trends ? input.performance_trends.length : 0\n    };\n    break;\n    \n  case 'discover_patterns':\n    response.data = {\n      patterns: input,\n      patterns_found: input.patterns_found,\n      confidence: input.analysis_confidence,\n      recommendations_count: input.recommendations ? input.recommendations.length : 0\n    };\n    break;\n}\n\nreturn response;"
      },
      "id": "8f14e45f-ceea-467a-9b87-7df96c6f1363",
      "name": "📋 Format Final Response",
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [1120, 300]
    },
    {
      "parameters": {
        "jsCode": "// 🚨 Tratamento de erro\nconst error = $input.first().json;\n\nreturn {\n  success: false,\n  error: {\n    message: error.message || 'Erro interno do servidor',\n    code: error.code || 'ANALYTICS_ERROR',\n    details: error.details || null\n  },\n  timestamp: new Date().toISOString()\n};"
      },
      "id": "8f14e45f-ceea-467a-9b87-7df96c6f1364",
      "name": "🚨 Error Handler",
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [1120, 540]
    },
    {
      "parameters": {
        "operation": "insert",
        "schema": {
          "value": "agent"
        },
        "table": {
          "value": "system_logs"
        },
        "columns": {
          "mappingMode": "defineBelow",
          "values": [
            {
              "column": "event_type",
              "value": "analytics_error"
            },
            {
              "column": "event_data",
              "value": "={{ JSON.stringify({error: $json.error, action: $('📊 Webhook - Analytics Request').first().json.action}) }}"
            },
            {
              "column": "severity",
              "value": "error"
            }
          ]
        },
        "options": {}
      },
      "id": "6ba7b816-9dad-11d1-80b4-00c04fd430c8",
      "name": "📝 Log Error",
      "type": "n8n-nodes-base.postgres",
      "typeVersion": 2.4,
      "position": [1340, 540],
      "credentials": {
        "postgres": {
          "id": "postgres-main",
          "name": "PostgreSQL - Main DB"
        }
      }
    }
  ],
  "connections": {
    "📊 Webhook - Analytics Request": {
      "main": [
        [
          {
            "node": "🔀 Route Action",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "🔀 Route Action": {
      "main": [
        [
          {
            "node": "📈 Get Recent Content",
            "type": "main",
            "index": 0
          }
        ],
        [
          {
            "node": "📊 Validate Metrics Update",
            "type": "main",
            "index": 0
          }
        ],
        [
          {
            "node": "📈 Get Recent Insights",
            "type": "main",
            "index": 0
          }
        ],
        [
          {
            "node": "🔍 Get High-Performing Content",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "📈 Get Recent Content": {
      "main": [
        [
          {
            "node": "🤖 AI Performance Analysis",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "🤖 AI Performance Analysis": {
      "main": [
        [
          {
            "node": "💾 Save Analysis",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "💾 Save Analysis": {
      "main": [
        [
          {
            "node": "📋 Format Final Response",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "📊 Validate Metrics Update": {
      "main": [
        [
          {
            "node": "🔄 Update Content Metrics",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "🔄 Update Content Metrics": {
      "main": [
        [
          {
            "node": "📋 Format Final Response",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "📈 Get Recent Insights": {
      "main": [
        [
          {
            "node": "💡 Compile Insights",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "💡 Compile Insights": {
      "main": [
        [
          {
            "node": "📋 Format Final Response",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "🔍 Get High-Performing Content": {
      "main": [
        [
          {
            "node": "🔍 Discover Success Patterns",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "🔍 Discover Success Patterns": {
      "main": [
        [
          {
            "node": "💾 Save Success Patterns",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "💾 Save Success Patterns": {
      "main": [
        [
          {
            "node": "📋 Format Final Response",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "📋 Format Final Response": {
      "main": [
        [
          {
            "node": "📤 Response",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "🚨 Error Handler": {
      "main": [
        [
          {
            "node": "📝 Log Error",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "📝 Log Error": {
      "main": [
        [
          {
            "node": "📤 Response",
            "type": "main",
            "index": 0
          }
        ]
      ]
    }
  },
  "pinData": {},
  "settings": {
    "executionOrder": "v1"
  },
  "staticData": null,
  "tags": [
    {
      "createdAt": "2024-12-19T10:00:00.000Z",
      "updatedAt": "2024-12-19T10:00:00.000Z",
      "id": "analytics",
      "name": "analytics"
    },
    {
      "createdAt": "2024-12-19T10:00:00.000Z",
      "updatedAt": "2024-12-19T10:00:00.000Z",
      "id": "performance",
      "name": "performance"
    }
  ],
  "triggerCount": 1,
  "updatedAt": "2024-12-19T10:00:00.000Z",
  "versionId": "1.0.0"
}