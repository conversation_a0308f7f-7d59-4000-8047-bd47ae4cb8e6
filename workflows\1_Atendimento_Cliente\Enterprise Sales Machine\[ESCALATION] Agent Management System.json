{
  "name": "[ESCALATION] Agent Management System",
  "nodes": [
    {
      "parameters": {
        "httpMethod": "POST",
        "path": "/agent-management",
        "options": {
          "noResponseBody": false
        }
      },
      "id": "webhook-trigger",
      "name": "🎯 Webhook: /agent-management",
      "type": "n8n-nodes-base.webhook",
      "typeVersion": 2,
      "position": [240, 300],
      "webhookId": "agent-management-webhook"
    },
    {
      "parameters": {
        "jsCode": "// =====================================================\n// AGENT MANAGEMENT REQUEST VALIDATOR\n// =====================================================\n\nconst Joi = require('joi');\nconst crypto = require('crypto');\n\n// Schema de validação para diferentes operações\nconst schemas = {\n  create_agent: Joi.object({\n    operation: Joi.string().valid('create_agent').required(),\n    agent_data: Joi.object({\n      agent_id: Joi.string().min(3).max(50).required(),\n      name: Joi.string().min(2).max(100).required(),\n      email: Joi.string().email().required(),\n      specializations: Joi.array().items(Joi.string()).min(1).required(),\n      max_concurrent_escalations: Joi.number().integer().min(1).max(20).default(5),\n      skill_level: Joi.string().valid('junior', 'senior', 'expert').default('senior'),\n      languages: Joi.array().items(Joi.string()).default(['pt-BR']),\n      working_hours: Joi.object({\n        timezone: Joi.string().default('America/Sao_Paulo'),\n        schedule: Joi.array().items(\n          Joi.object({\n            day: Joi.string().valid('monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday').required(),\n            start_time: Joi.string().pattern(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/).required(),\n            end_time: Joi.string().pattern(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/).required()\n          })\n        ).min(1).required()\n      }).required(),\n      contact_info: Joi.object({\n        slack_user_id: Joi.string().optional(),\n        phone: Joi.string().optional(),\n        emergency_contact: Joi.string().optional()\n      }).optional()\n    }).required(),\n    metadata: Joi.object().optional()\n  }),\n  \n  update_agent: Joi.object({\n    operation: Joi.string().valid('update_agent').required(),\n    agent_id: Joi.string().required(),\n    updates: Joi.object({\n      name: Joi.string().min(2).max(100).optional(),\n      email: Joi.string().email().optional(),\n      specializations: Joi.array().items(Joi.string()).optional(),\n      max_concurrent_escalations: Joi.number().integer().min(1).max(20).optional(),\n      skill_level: Joi.string().valid('junior', 'senior', 'expert').optional(),\n      languages: Joi.array().items(Joi.string()).optional(),\n      working_hours: Joi.object().optional(),\n      contact_info: Joi.object().optional(),\n      status: Joi.string().valid('available', 'busy', 'offline', 'on_break').optional(),\n      assigned_queue: Joi.string().optional()\n    }).min(1).required(),\n    metadata: Joi.object().optional()\n  }),\n  \n  assign_queue: Joi.object({\n    operation: Joi.string().valid('assign_queue').required(),\n    agent_id: Joi.string().required(),\n    queue_name: Joi.string().required(),\n    priority: Joi.number().integer().min(1).max(10).default(5),\n    metadata: Joi.object().optional()\n  }),\n  \n  update_status: Joi.object({\n    operation: Joi.string().valid('update_status').required(),\n    agent_id: Joi.string().required(),\n    status: Joi.string().valid('available', 'busy', 'offline', 'on_break').required(),\n    reason: Joi.string().max(200).optional(),\n    estimated_return: Joi.string().isoDate().optional(),\n    metadata: Joi.object().optional()\n  }),\n  \n  get_agent: Joi.object({\n    operation: Joi.string().valid('get_agent').required(),\n    agent_id: Joi.string().required()\n  }),\n  \n  list_agents: Joi.object({\n    operation: Joi.string().valid('list_agents').required(),\n    filters: Joi.object({\n      status: Joi.string().valid('available', 'busy', 'offline', 'on_break').optional(),\n      specialization: Joi.string().optional(),\n      queue: Joi.string().optional(),\n      skill_level: Joi.string().valid('junior', 'senior', 'expert').optional()\n    }).optional(),\n    pagination: Joi.object({\n      page: Joi.number().integer().min(1).default(1),\n      limit: Joi.number().integer().min(1).max(100).default(20)\n    }).optional()\n  }),\n  \n  delete_agent: Joi.object({\n    operation: Joi.string().valid('delete_agent').required(),\n    agent_id: Joi.string().required(),\n    reason: Joi.string().max(500).required(),\n    transfer_escalations_to: Joi.string().optional()\n  })\n};\n\ntry {\n  const requestBody = $input.all()[0].json.body || {};\n  const headers = $input.all()[0].json.headers || {};\n  const query = $input.all()[0].json.query || {};\n  \n  // Gerar correlation ID\n  const correlationId = headers['x-correlation-id'] || \n                       query.correlation_id || \n                       crypto.randomUUID();\n  \n  // Validar operação\n  const operation = requestBody.operation;\n  if (!operation) {\n    throw new Error('Campo operation é obrigatório');\n  }\n  \n  const schema = schemas[operation];\n  if (!schema) {\n    throw new Error(`Operação '${operation}' não é suportada. Operações válidas: ${Object.keys(schemas).join(', ')}`);\n  }\n  \n  // Validar dados\n  const { error, value } = schema.validate(requestBody, {\n    abortEarly: false,\n    stripUnknown: true,\n    convert: true\n  });\n  \n  if (error) {\n    const validationErrors = error.details.map(detail => ({\n      field: detail.path.join('.'),\n      message: detail.message,\n      value: detail.context?.value\n    }));\n    \n    throw new Error(`Erro de validação: ${JSON.stringify(validationErrors, null, 2)}`);\n  }\n  \n  // Sanitizar dados\n  const sanitizedData = JSON.parse(JSON.stringify(value));\n  \n  // Gerar hash de contexto para auditoria\n  const contextHash = crypto\n    .createHash('sha256')\n    .update(JSON.stringify({\n      operation,\n      timestamp: new Date().toISOString(),\n      agent_id: sanitizedData.agent_id || 'bulk_operation'\n    }))\n    .digest('hex');\n  \n  console.log('Agent management request validated', {\n    operation,\n    correlationId,\n    contextHash: contextHash.substring(0, 8),\n    agentId: sanitizedData.agent_id || 'N/A'\n  });\n  \n  return [{\n    json: {\n      validation_status: 'success',\n      correlation_id: correlationId,\n      context_hash: contextHash,\n      operation,\n      validated_data: sanitizedData,\n      timestamp: new Date().toISOString(),\n      request_metadata: {\n        user_agent: headers['user-agent'],\n        ip_address: headers['x-forwarded-for'] || headers['x-real-ip'],\n        source: headers['x-source'] || 'unknown'\n      }\n    }\n  }];\n  \n} catch (error) {\n  console.error('Agent management validation failed', {\n    error: error.message,\n    operation: requestBody?.operation || 'unknown'\n  });\n  \n  return [{\n    json: {\n      validation_status: 'error',\n      error_type: 'validation_error',\n      error_message: error.message,\n      timestamp: new Date().toISOString()\n    }\n  }];\n}"
      },
      "id": "request-validator",
      "name": "✅ Validator: Request & Schema",
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [460, 300]
    },
    {
      "parameters": {
        "conditions": {
          "options": {
            "caseSensitive": true,
            "leftValue": "",
            "typeValidation": "strict"
          },
          "conditions": [
            {
              "id": "validation-success",
              "leftValue": "={{ $json.validation_status }}",
              "rightValue": "success",
              "operator": {
                "type": "string",
                "operation": "equals"
              }
            }
          ],
          "combinator": "and"
        },
        "options": {}
      },
      "id": "validation-check",
      "name": "🔍 Check: Validation Status",
      "type": "n8n-nodes-base.if",
      "typeVersion": 2,
      "position": [680, 300]
    },
    {
      "parameters": {
        "conditions": {
          "options": {
            "caseSensitive": true,
            "leftValue": "",
            "typeValidation": "strict"
          },
          "conditions": [
            {
              "id": "create-agent",
              "leftValue": "={{ $json.operation }}",
              "rightValue": "create_agent",
              "operator": {
                "type": "string",
                "operation": "equals"
              }
            }
          ],
          "combinator": "and"
        },
        "options": {}
      },
      "id": "operation-router",
      "name": "🔀 Router: Operation Type",
      "type": "n8n-nodes-base.switch",
      "typeVersion": 3,
      "position": [900, 300]
    },
    {
      "parameters": {
        "operation": "executeQuery",
        "query": "-- Criar novo agente humano\nINSERT INTO agent.human_agents (\n  agent_id, name, email, specializations, max_concurrent_escalations,\n  skill_level, languages, working_hours, contact_info, status,\n  assigned_queue, created_at, updated_at\n) VALUES (\n  $1, $2, $3, $4, $5, $6, $7, $8, $9, 'offline', NULL, NOW(), NOW()\n)\nRETURNING *",
        "options": {
          "queryParameters": "={{ [\n            $json.validated_data.agent_data.agent_id,\n            $json.validated_data.agent_data.name,\n            $json.validated_data.agent_data.email,\n            $json.validated_data.agent_data.specializations,\n            $json.validated_data.agent_data.max_concurrent_escalations,\n            $json.validated_data.agent_data.skill_level,\n            $json.validated_data.agent_data.languages,\n            JSON.stringify($json.validated_data.agent_data.working_hours),\n            JSON.stringify($json.validated_data.agent_data.contact_info || {})\n          ] }}"
        }
      },
      "id": "create-agent-db",
      "name": "👤 DB: Create Agent",
      "type": "n8n-nodes-base.postgres",
      "typeVersion": 2.4,
      "position": [1120, 200],
      "credentials": {
        "postgres": {
          "id": "postgres-main",
          "name": "PostgreSQL - Main Database"
        }
      }
    },
    {
      "parameters": {
        "operation": "executeQuery",
        "query": "-- Atualizar dados do agente\nUPDATE agent.human_agents \nSET \n  name = COALESCE($2, name),\n  email = COALESCE($3, email),\n  specializations = COALESCE($4, specializations),\n  max_concurrent_escalations = COALESCE($5, max_concurrent_escalations),\n  skill_level = COALESCE($6, skill_level),\n  languages = COALESCE($7, languages),\n  working_hours = COALESCE($8, working_hours),\n  contact_info = COALESCE($9, contact_info),\n  status = COALESCE($10, status),\n  assigned_queue = COALESCE($11, assigned_queue),\n  updated_at = NOW()\nWHERE agent_id = $1\nRETURNING *",
        "options": {
          "queryParameters": "={{ [\n            $json.validated_data.agent_id,\n            $json.validated_data.updates.name || null,\n            $json.validated_data.updates.email || null,\n            $json.validated_data.updates.specializations || null,\n            $json.validated_data.updates.max_concurrent_escalations || null,\n            $json.validated_data.updates.skill_level || null,\n            $json.validated_data.updates.languages || null,\n            $json.validated_data.updates.working_hours ? JSON.stringify($json.validated_data.updates.working_hours) : null,\n            $json.validated_data.updates.contact_info ? JSON.stringify($json.validated_data.updates.contact_info) : null,\n            $json.validated_data.updates.status || null,\n            $json.validated_data.updates.assigned_queue || null\n          ] }}"
        }
      },
      "id": "update-agent-db",
      "name": "🔄 DB: Update Agent",
      "type": "n8n-nodes-base.postgres",
      "typeVersion": 2.4,
      "position": [1120, 300],
      "credentials": {
        "postgres": {
          "id": "postgres-main",
          "name": "PostgreSQL - Main Database"
        }
      }
    },
    {
      "parameters": {
        "operation": "executeQuery",
        "query": "-- Atribuir fila ao agente\nUPDATE agent.human_agents \nSET \n  assigned_queue = $2,\n  status = CASE \n    WHEN status = 'offline' THEN 'available'\n    ELSE status\n  END,\n  updated_at = NOW()\nWHERE agent_id = $1\nRETURNING *",
        "options": {
          "queryParameters": "={{ [\n            $json.validated_data.agent_id,\n            $json.validated_data.queue_name\n          ] }}"
        }
      },
      "id": "assign-queue-db",
      "name": "📋 DB: Assign Queue",
      "type": "n8n-nodes-base.postgres",
      "typeVersion": 2.4,
      "position": [1120, 400],
      "credentials": {
        "postgres": {
          "id": "postgres-main",
          "name": "PostgreSQL - Main Database"
        }
      }
    },
    {
      "parameters": {
        "operation": "executeQuery",
        "query": "-- Atualizar status do agente\nUPDATE agent.human_agents \nSET \n  status = $2,\n  last_activity = NOW(),\n  updated_at = NOW()\nWHERE agent_id = $1\nRETURNING *",
        "options": {
          "queryParameters": "={{ [\n            $json.validated_data.agent_id,\n            $json.validated_data.status\n          ] }}"
        }
      },
      "id": "update-status-db",
      "name": "🔄 DB: Update Status",
      "type": "n8n-nodes-base.postgres",
      "typeVersion": 2.4,
      "position": [1120, 500],
      "credentials": {
        "postgres": {
          "id": "postgres-main",
          "name": "PostgreSQL - Main Database"
        }
      }
    },
    {
      "parameters": {
        "operation": "executeQuery",
        "query": "-- Buscar agente específico\nSELECT \n  ha.*,\n  hq.queue_name as queue_details,\n  hq.specialization as queue_specialization,\n  COUNT(ie.escalation_id) as current_escalations,\n  AVG(CASE WHEN ie.status = 'resolved' THEN \n    EXTRACT(EPOCH FROM (ie.resolved_at - ie.created_at))/3600 \n  END) as avg_resolution_hours\nFROM agent.human_agents ha\nLEFT JOIN agent.human_queues hq ON ha.assigned_queue = hq.queue_name\nLEFT JOIN agent.intelligent_escalations ie ON ie.assigned_agent = ha.agent_id \n  AND ie.created_at > NOW() - INTERVAL '30 days'\nWHERE ha.agent_id = $1\nGROUP BY ha.agent_id, hq.queue_name, hq.specialization",
        "options": {
          "queryParameters": "={{ [$json.validated_data.agent_id] }}"
        }
      },
      "id": "get-agent-db",
      "name": "👁️ DB: Get Agent Details",
      "type": "n8n-nodes-base.postgres",
      "typeVersion": 2.4,
      "position": [1120, 600],
      "credentials": {
        "postgres": {
          "id": "postgres-main",
          "name": "PostgreSQL - Main Database"
        }
      }
    },
    {
      "parameters": {
        "operation": "executeQuery",
        "query": "-- Listar agentes com filtros\nSELECT \n  ha.*,\n  hq.queue_name as queue_details,\n  hq.specialization as queue_specialization,\n  COUNT(ie.escalation_id) as current_escalations,\n  AVG(CASE WHEN ie.status = 'resolved' THEN \n    EXTRACT(EPOCH FROM (ie.resolved_at - ie.created_at))/3600 \n  END) as avg_resolution_hours\nFROM agent.human_agents ha\nLEFT JOIN agent.human_queues hq ON ha.assigned_queue = hq.queue_name\nLEFT JOIN agent.intelligent_escalations ie ON ie.assigned_agent = ha.agent_id \n  AND ie.created_at > NOW() - INTERVAL '30 days'\nWHERE \n  ($1::text IS NULL OR ha.status = $1)\n  AND ($2::text IS NULL OR $2 = ANY(ha.specializations))\n  AND ($3::text IS NULL OR ha.assigned_queue = $3)\n  AND ($4::text IS NULL OR ha.skill_level = $4)\nGROUP BY ha.agent_id, hq.queue_name, hq.specialization\nORDER BY ha.created_at DESC\nLIMIT $5 OFFSET $6",
        "options": {
          "queryParameters": "={{ [\n            $json.validated_data.filters?.status || null,\n            $json.validated_data.filters?.specialization || null,\n            $json.validated_data.filters?.queue || null,\n            $json.validated_data.filters?.skill_level || null,\n            $json.validated_data.pagination?.limit || 20,\n            (($json.validated_data.pagination?.page || 1) - 1) * ($json.validated_data.pagination?.limit || 20)\n          ] }}"
        }
      },
      "id": "list-agents-db",
      "name": "📋 DB: List Agents",
      "type": "n8n-nodes-base.postgres",
      "typeVersion": 2.4,
      "position": [1120, 700],
      "credentials": {
        "postgres": {
          "id": "postgres-main",
          "name": "PostgreSQL - Main Database"
        }
      }
    },
    {
      "parameters": {
        "operation": "executeQuery",
        "query": "-- Deletar agente (soft delete)\nUPDATE agent.human_agents \nSET \n  status = 'deleted',\n  deleted_at = NOW(),\n  deleted_reason = $2,\n  updated_at = NOW()\nWHERE agent_id = $1\nRETURNING *",
        "options": {
          "queryParameters": "={{ [\n            $json.validated_data.agent_id,\n            $json.validated_data.reason\n          ] }}"
        }
      },
      "id": "delete-agent-db",
      "name": "🗑️ DB: Delete Agent",
      "type": "n8n-nodes-base.postgres",
      "typeVersion": 2.4,
      "position": [1120, 800],
      "credentials": {
        "postgres": {
          "id": "postgres-main",
          "name": "PostgreSQL - Main Database"
        }
      }
    },
    {
      "parameters": {
        "jsCode": "// =====================================================\n// AGENT OPERATION PROCESSOR\n// =====================================================\n\nconst operation = $json.operation;\nconst dbResult = $input.all()[0].json;\nconst originalData = $json.validated_data;\nconst correlationId = $json.correlation_id;\n\n// Função para processar resultado baseado na operação\nfunction processOperationResult(operation, dbResult, originalData) {\n  const timestamp = new Date().toISOString();\n  \n  switch (operation) {\n    case 'create_agent':\n      return {\n        operation: 'create_agent',\n        status: 'success',\n        message: `Agente ${dbResult[0].name} criado com sucesso`,\n        agent: {\n          agent_id: dbResult[0].agent_id,\n          name: dbResult[0].name,\n          email: dbResult[0].email,\n          specializations: dbResult[0].specializations,\n          status: dbResult[0].status,\n          created_at: dbResult[0].created_at\n        },\n        next_steps: [\n          'Configurar horários de trabalho',\n          'Atribuir fila específica',\n          'Ativar status para disponível',\n          'Configurar notificações Slack'\n        ]\n      };\n      \n    case 'update_agent':\n      return {\n        operation: 'update_agent',\n        status: 'success',\n        message: `Agente ${dbResult[0].name} atualizado com sucesso`,\n        agent: {\n          agent_id: dbResult[0].agent_id,\n          name: dbResult[0].name,\n          email: dbResult[0].email,\n          specializations: dbResult[0].specializations,\n          status: dbResult[0].status,\n          updated_at: dbResult[0].updated_at\n        },\n        changes_applied: Object.keys(originalData.updates)\n      };\n      \n    case 'assign_queue':\n      return {\n        operation: 'assign_queue',\n        status: 'success',\n        message: `Agente ${dbResult[0].name} atribuído à fila ${dbResult[0].assigned_queue}`,\n        agent: {\n          agent_id: dbResult[0].agent_id,\n          name: dbResult[0].name,\n          assigned_queue: dbResult[0].assigned_queue,\n          status: dbResult[0].status,\n          updated_at: dbResult[0].updated_at\n        }\n      };\n      \n    case 'update_status':\n      return {\n        operation: 'update_status',\n        status: 'success',\n        message: `Status do agente ${dbResult[0].name} alterado para ${dbResult[0].status}`,\n        agent: {\n          agent_id: dbResult[0].agent_id,\n          name: dbResult[0].name,\n          status: dbResult[0].status,\n          last_activity: dbResult[0].last_activity,\n          updated_at: dbResult[0].updated_at\n        }\n      };\n      \n    case 'get_agent':\n      const agentDetails = dbResult[0];\n      return {\n        operation: 'get_agent',\n        status: 'success',\n        agent: {\n          ...agentDetails,\n          performance_metrics: {\n            current_escalations: agentDetails.current_escalations || 0,\n            avg_resolution_hours: agentDetails.avg_resolution_hours ? \n              Math.round(agentDetails.avg_resolution_hours * 10) / 10 : null,\n            queue_specialization: agentDetails.queue_specialization\n          }\n        }\n      };\n      \n    case 'list_agents':\n      return {\n        operation: 'list_agents',\n        status: 'success',\n        agents: dbResult.map(agent => ({\n          agent_id: agent.agent_id,\n          name: agent.name,\n          email: agent.email,\n          specializations: agent.specializations,\n          status: agent.status,\n          assigned_queue: agent.assigned_queue,\n          skill_level: agent.skill_level,\n          current_escalations: agent.current_escalations || 0,\n          avg_resolution_hours: agent.avg_resolution_hours ? \n            Math.round(agent.avg_resolution_hours * 10) / 10 : null,\n          last_activity: agent.last_activity,\n          created_at: agent.created_at\n        })),\n        pagination: {\n          page: originalData.pagination?.page || 1,\n          limit: originalData.pagination?.limit || 20,\n          total: dbResult.length\n        },\n        filters_applied: originalData.filters || {}\n      };\n      \n    case 'delete_agent':\n      return {\n        operation: 'delete_agent',\n        status: 'success',\n        message: `Agente ${dbResult[0].name} removido com sucesso`,\n        agent: {\n          agent_id: dbResult[0].agent_id,\n          name: dbResult[0].name,\n          deleted_at: dbResult[0].deleted_at,\n          deleted_reason: dbResult[0].deleted_reason\n        }\n      };\n      \n    default:\n      return {\n        operation,\n        status: 'error',\n        message: `Operação ${operation} não implementada`\n      };\n  }\n}\n\ntry {\n  const result = processOperationResult(operation, dbResult, originalData);\n  \n  console.log('Agent operation completed', {\n    operation,\n    correlationId,\n    agentId: result.agent?.agent_id || 'N/A',\n    status: result.status\n  });\n  \n  return [{\n    json: {\n      ...result,\n      correlation_id: correlationId,\n      timestamp: new Date().toISOString(),\n      processing_time_ms: Date.now() - new Date($json.timestamp).getTime()\n    }\n  }];\n  \n} catch (error) {\n  console.error('Agent operation processing failed', {\n    operation,\n    correlationId,\n    error: error.message\n  });\n  \n  return [{\n    json: {\n      operation,\n      status: 'error',\n      error_type: 'processing_error',\n      error_message: error.message,\n      correlation_id: correlationId,\n      timestamp: new Date().toISOString()\n    }\n  }];\n}"
      },
      "id": "operation-processor",
      "name": "⚙️ Processor: Operation Result",
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [1340, 400]
    },
    {
      "parameters": {
        "operation": "executeQuery",
        "query": "-- Registrar auditoria da operação\nINSERT INTO agent.audit_logs (\n  table_name, operation_type, record_id, old_values, new_values,\n  changed_by, correlation_id, metadata\n) VALUES (\n  'human_agents', $1, $2, $3, $4, $5, $6, $7\n)",
        "options": {
          "queryParameters": "={{ [\n            $json.operation,\n            $json.agent?.agent_id || 'bulk_operation',\n            '{}',\n            JSON.stringify($json.agent || {}),\n            $json.correlation_id,\n            $json.correlation_id,\n            JSON.stringify({\n              operation: $json.operation,\n              status: $json.status,\n              timestamp: $json.timestamp,\n              processing_time_ms: $json.processing_time_ms\n            })\n          ] }}"
        }
      },
      "id": "audit-logger",
      "name": "📝 DB: Audit Log",
      "type": "n8n-nodes-base.postgres",
      "typeVersion": 2.4,
      "position": [1560, 400],
      "credentials": {
        "postgres": {
          "id": "postgres-main",
          "name": "PostgreSQL - Main Database"
        }
      }
    },
    {
      "parameters": {
        "respondWith": "json",
        "responseBody": "={{ $json }}",
        "options": {
          "responseHeaders": {
            "entries": [
              {
                "name": "Content-Type",
                "value": "application/json"
              },
              {
                "name": "X-Correlation-ID",
                "value": "={{ $json.correlation_id }}"
              },
              {
                "name": "X-Processing-Time",
                "value": "={{ $json.processing_time_ms }}ms"
              }
            ]
          }
        }
      },
      "id": "success-response",
      "name": "✅ Response: Success",
      "type": "n8n-nodes-base.respondToWebhook",
      "typeVersion": 1.1,
      "position": [1780, 400]
    },
    {
      "parameters": {
        "respondWith": "json",
        "responseBody": "={{ {\n  status: 'error',\n  error_type: $json.error_type || 'validation_error',\n  error_message: $json.error_message,\n  timestamp: $json.timestamp,\n  correlation_id: $json.correlation_id || 'unknown'\n} }}",
        "options": {
          "responseCode": 400,\n          "responseHeaders": {
            "entries": [
              {
                "name": "Content-Type",
                "value": "application/json"
              }
            ]
          }
        }
      },
      "id": "error-response",
      "name": "❌ Response: Error",
      "type": "n8n-nodes-base.respondToWebhook",
      "typeVersion": 1.1,
      "position": [680, 500]
    }
  ],
  "pinData": {},
  "connections": {
    "webhook-trigger": {
      "main": [
        [
          {
            "node": "request-validator",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "request-validator": {
      "main": [
        [
          {
            "node": "validation-check",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "validation-check": {
      "main": [
        [
          {
            "node": "operation-router",
            "type": "main",
            "index": 0
          }
        ],
        [
          {
            "node": "error-response",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "operation-router": {
      "main": [
        [
          {
            "node": "create-agent-db",
            "type": "main",
            "index": 0
          }
        ],
        [
          {
            "node": "update-agent-db",
            "type": "main",
            "index": 0
          }
        ],
        [
          {
            "node": "assign-queue-db",
            "type": "main",
            "index": 0
          }
        ],
        [
          {
            "node": "update-status-db",
            "type": "main",
            "index": 0
          }
        ],
        [
          {
            "node": "get-agent-db",
            "type": "main",
            "index": 0
          }
        ],
        [
          {
            "node": "list-agents-db",
            "type": "main",
            "index": 0
          }
        ],
        [
          {
            "node": "delete-agent-db",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "create-agent-db": {
      "main": [
        [
          {
            "node": "operation-processor",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "update-agent-db": {
      "main": [
        [
          {
            "node": "operation-processor",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "assign-queue-db": {
      "main": [
        [
          {
            "node": "operation-processor",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "update-status-db": {
      "main": [
        [
          {
            "node": "operation-processor",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "get-agent-db": {
      "main": [
        [
          {
            "node": "operation-processor",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "list-agents-db": {
      "main": [
        [
          {
            "node": "operation-processor",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "delete-agent-db": {
      "main": [
        [
          {
            "node": "operation-processor",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "operation-processor": {
      "main": [
        [
          {
            "node": "audit-logger",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "audit-logger": {
      "main": [
        [
          {
            "node": "success-response",
            "type": "main",
            "index": 0
          }
        ]
      ]
    }
  },
  "active": true,
  "settings": {
    "executionOrder": "v1",
    "saveManualExecutions": false,
    "callerPolicy": "workflowsFromSameOwner",
    "errorWorkflow": "error-handler-workflow"
  },
  "versionId": "1.0.0",
  "meta": {
    "templateCredsSetupCompleted": true,
    "instanceId": "agent-management-v1"
  },
  "id": "agent-management-system",
  "tags": [
    {
      "createdAt": "2024-01-15T10:00:00.000Z",
      "updatedAt": "2024-01-15T10:00:00.000Z",
      "id": "management",
      "name": "management"
    },
    {
      "createdAt": "2024-01-15T10:00:00.000Z",
      "updatedAt": "2024-01-15T10:00:00.000Z",
      "id": "agents",
      "name": "agents"
    },
    {
      "createdAt": "2024-01-15T10:00:00.000Z",
      "updatedAt": "2024-01-15T10:00:00.000Z",
      "id": "enterprise",
      "name": "enterprise"
    }
  ]
}