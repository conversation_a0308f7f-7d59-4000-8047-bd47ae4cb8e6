{"name": "[ENRICHER] Lead Data Miner", "nodes": [{"parameters": {"path": "enrich-lead", "responseMode": "onReceived", "options": {}}, "id": "a1b2c3d4-e5f6-7890-1234-567890abcdef", "name": "Webhook Trigger", "type": "n8n-nodes-base.webhook", "typeVersion": 1.2, "position": [800, 400]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT c.nome, ip.email, c.tags FROM agent.contatos c LEFT JOIN agent.informacoes_pessoais ip ON c.id = ip.id_contato WHERE c.id = {{ $json.body.contact_id }};", "options": {}}, "id": "b2c3d4e5-f6a7-8901-2345-67890abcdef1", "name": "Get Contact Info", "type": "n8n-nodes-base.postgres", "typeVersion": 3.1, "position": [1000, 400], "credentials": {"postgres": {"id": "1", "name": "Postgres"}}}, {"parameters": {"conditions": {"string": [{"value1": "={{ $node[\"Get Contact Info\"].json[\"nome\"] }}", "operation": "isNotEmpty"}]}}, "id": "c3d4e5f6-a7b8-9012-3456-7890abcdef12", "name": "Has Name?", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [1200, 400]}, {"parameters": {"query": "={{ $node[\"Get Contact Info\"].json[\"nome\"] + ' linkedin profile' }}", "options": {}}, "id": "d4e5f6a7-b8c9-0123-4567-890abcdef123", "name": "Search LinkedIn", "type": "n8n-nodes-base.serper", "typeVersion": 1, "position": [1400, 300], "credentials": {"serperApi": {"id": "serper-api", "name": "Serper API"}}}, {"parameters": {"workflowId": "={{ $env.DISPATCHER_WORKFLOW_ID }}", "data": "={{ { body: { \n    prompt: `Você é um analista de dados especialista em enriquecimento de leads. Analise os resultados da busca na web a seguir para o contato '${$('Get Contact Info').item.json.nome}'. Extraia as seguintes informações e retorne APENAS o objeto JSON, sem nenhum texto ou formatação adicional. Se uma informação não for encontrada, deixe o campo como null.\\n\\nInformações a extrair:\\n- Cargo atual (current_title)\\n- Nome da empresa atual (current_company)\\n- Resumo do perfil do LinkedIn (linkedin_summary)\\n- Website da empresa (company_website)\\n- Número de conexões (linkedin_connections), se disponível\\n\\nResultados da Busca:\\n${JSON.stringify($('Search LinkedIn').item.json)}\\n\\nFormato de Saída (JSON):\\n{\\n  \\\"current_title\\\": \\\"<string>\\\",\\n  \\\"current_company\\\": \\\"<string>\\\",\\n  \\\"linkedin_summary\\\": \\\"<string>\\\",\\n  \\\"company_website\\\": \\\"<string>\\\",\\n  \\\"linkedin_connections\\\": <number>\\n}`,\n    task_type: 'complex_analysis',\n    calling_workflow_id: $workflow.id,\n    calling_node_id: 'call_dispatcher_for_enrichment'\n} } }}", "options": {}}, "id": "call_dispatcher_for_enrichment", "name": "Call Dispatcher for Enrichment", "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 2, "position": [1600, 300]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "UPDATE agent.contatos SET enriched_data = '{{ JSON.parse($json.choices[0].message.content) }}'::jsonb WHERE id = {{ $json.body.contact_id }};", "options": {}}, "id": "f6a7b8c9-d0e1-2345-6789-0abcdef12345", "name": "Update Contact in DB", "type": "n8n-nodes-base.postgres", "typeVersion": 3.1, "position": [1800, 300], "credentials": {"postgres": {"id": "1", "name": "Postgres"}}}, {"parameters": {"message": "Enrichment successful for contact_id: {{ $json.body.contact_id }}", "options": {}}, "id": "a7b8c9d0-e1f2-3456-7890-bcdef1234567", "name": "Success", "type": "n8n-nodes-base.noOp", "typeVersion": 1, "position": [2000, 300]}, {"parameters": {"message": "Enrichment skipped for contact_id: {{ $json.body.contact_id }} (No name found)", "options": {}}, "id": "b8c9d0e1-f2a3-4567-8901-cdef12345678", "name": "Skipped", "type": "n8n-nodes-base.noOp", "typeVersion": 1, "position": [1400, 500]}], "connections": {"Webhook Trigger": {"main": [[{"node": "Get Contact Info", "type": "main", "index": 0}]]}, "Get Contact Info": {"main": [[{"node": "Has Name?", "type": "main", "index": 0}]]}, "Has Name?": {"main": [[{"node": "Search LinkedIn", "type": "main", "index": 0}], [{"node": "Skipped", "type": "main", "index": 0}]]}, "Search LinkedIn": {"main": [[{"node": "call_dispatcher_for_enrichment", "type": "main", "index": 0}]]}, "call_dispatcher_for_enrichment": {"main": [[{"node": "Update Contact in DB", "type": "main", "index": 0}]]}, "Update Contact in DB": {"main": [[{"node": "Success", "type": "main", "index": 0}]]}}, "pinData": {}}