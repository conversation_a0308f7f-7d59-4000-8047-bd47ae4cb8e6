# Teste de sintaxe da seção problemática

if ($isFirstInstall) {
    Write-Host-Color "`n🔧 PRIMEIRA INSTALAÇÃO DETECTADA - Executando configuração automática avançada..." "Cyan"
    Log-Event "Primeira instalação detectada. Executando post-setup-automation.ps1 automaticamente..." "Auto-Setup" "INFO"
    
    # Aguardar 10 segundos para garantir que todos os serviços estejam estáveis
    Write-Host-Color "Aguardando estabilizacao dos servicos (10 segundos)..." "Yellow"
    Start-Sleep -Seconds 10
    
    try {
        $postSetupScript = Join-Path $PSScriptRoot "post-setup-automation.ps1"
        if (Test-Path $postSetupScript) {
            Write-Host-Color "🚀 Executando configuração automática..." "Green"
            
            # Executar o script post-setup-automation.ps1
            & $postSetupScript
            
            if ($LASTEXITCODE -eq 0) {
                Write-Host-Color "✅ Configuração automática concluída com sucesso!" "Green"
                Log-Event "post-setup-automation.ps1 executado com sucesso" "Auto-Setup" "SUCCESS"
            } else {
                Write-Host-Color "⚠️ Configuração automática falhou. Execute manualmente: .\post-setup-automation.ps1" "Yellow"
                Log-Event "post-setup-automation.ps1 falhou com código: $LASTEXITCODE" "Auto-Setup" "ERROR"
            }
        } else {
            Write-Host-Color "⚠️ Script post-setup-automation.ps1 não encontrado. Configuração manual necessária." "Yellow"
            Log-Event "post-setup-automation.ps1 não encontrado em: $postSetupScript" "Auto-Setup" "ERROR"
        }
    } catch {
        Write-Host-Color "❌ Erro ao executar configuração automática: $($_.Exception.Message)" "Red"
        Log-Event "Erro ao executar post-setup-automation.ps1: $($_.Exception.Message)" "Auto-Setup" "ERROR"
        Write-Host-Color "📋 Execute manualmente: .\post-setup-automation.ps1" "Yellow"
    }
} else {
    Write-Host-Color "`n🔄 INSTALAÇÃO EXISTENTE DETECTADA" "Cyan"
}

Write-Host "Teste de sintaxe concluído!"