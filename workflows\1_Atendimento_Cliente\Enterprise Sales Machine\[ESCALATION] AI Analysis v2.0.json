{"name": "[ESCALATION] AI Analysis v2.0", "nodes": [{"parameters": {"httpMethod": "POST", "path": "escalation-ai-analysis", "options": {"noResponseBody": false}}, "id": "ai-analysis-trigger", "name": "🧠 AI Analysis Trigger", "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [240, 300], "webhookId": "escalation-ai-analysis"}, {"parameters": {"jsCode": "// Validar e processar dados de entrada para análise AI\nconst body = $input.first().json.body || $input.first().json;\n\n// Validar campos obrigatórios\nif (!body.escalation_id && !body.conversation_data) {\n  throw new Error('Dados insuficientes: escalation_id ou conversation_data são obrigatórios');\n}\n\n// Estruturar dados para análise\nconst analysisData = {\n  escalation_id: body.escalation_id,\n  conversation_data: body.conversation_data || {},\n  customer_data: body.customer_data || {},\n  context_data: body.context_data || {},\n  analysis_type: body.analysis_type || 'comprehensive',\n  timestamp: new Date().toISOString(),\n  source_system: body.source_system || 'unknown'\n};\n\n// Preparar texto para análise\nlet analysisText = '';\n\nif (body.conversation_data) {\n  // Extrair mensagens da conversa\n  if (body.conversation_data.messages) {\n    analysisText = body.conversation_data.messages\n      .map(msg => `${msg.sender || 'User'}: ${msg.content || msg.text || ''}`)\n      .join('\\n');\n  } else if (body.conversation_data.content) {\n    analysisText = body.conversation_data.content;\n  }\n}\n\n// Adicionar contexto adicional\nif (body.description) {\n  analysisText += `\\n\\nDescrição: ${body.description}`;\n}\n\nif (body.title) {\n  analysisText += `\\n\\nTítulo: ${body.title}`;\n}\n\nanalysisData.analysis_text = analysisText;\nanalysisData.text_length = analysisText.length;\n\nreturn [{ json: analysisData }];"}, "id": "validate-analysis-data", "name": "✅ Validate Analysis Data", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [460, 300]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "has-text", "leftValue": "={{ $json.text_length }}", "rightValue": 10, "operator": {"type": "number", "operation": "gt"}}], "combinator": "and"}, "options": {}}, "id": "check-text-availability", "name": "❓ Has Text for Analysis?", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [680, 300]}, {"parameters": {"resource": "text", "operation": "message", "options": {"temperature": 0.3, "maxTokens": 1000}, "messages": {"values": [{"role": "system", "content": "Você é um especialista em análise de sentimentos e escalações de atendimento ao cliente. Analise o texto fornecido e retorne um JSON estruturado com as seguintes informações:\n\n{\n  \"sentiment_analysis\": {\n    \"overall_sentiment\": \"positive|neutral|negative|very_negative\",\n    \"confidence_score\": 0.0-1.0,\n    \"emotional_indicators\": [\"lista de emoções detectadas\"],\n    \"urgency_level\": \"low|medium|high|critical\"\n  },\n  \"escalation_analysis\": {\n    \"escalation_required\": true|false,\n    \"escalation_reasons\": [\"lista de motivos\"],\n    \"suggested_priority\": \"low|medium|high|critical\",\n    \"suggested_category\": \"technical|billing|sales|general|complaint\"\n  },\n  \"content_analysis\": {\n    \"key_topics\": [\"tópicos principais\"],\n    \"customer_intent\": \"descrição da intenção\",\n    \"complexity_level\": \"simple|medium|complex|very_complex\",\n    \"resolution_suggestions\": [\"sugestões de resolução\"]\n  },\n  \"routing_recommendations\": {\n    \"suggested_department\": \"departamento recomendado\",\n    \"required_skills\": [\"habilidades necessárias\"],\n    \"estimated_resolution_time\": \"tempo em minutos\"\n  }\n}\n\nSeja preciso e objetivo na análise."}, {"role": "user", "content": "<PERSON><PERSON>e o seguinte texto de atendimento ao cliente:\n\n{{ $json.analysis_text }}"}]}}, "id": "openai-sentiment-analysis", "name": "🧠 OpenAI Sentiment Analysis", "type": "n8n-nodes-base.openAi", "typeVersion": 1.3, "position": [900, 200], "credentials": {"openAiApi": {"id": "openai-escalation-api", "name": "OpenAI Escalation API"}}}, {"parameters": {"resource": "text", "operation": "message", "options": {"temperature": 0.2, "maxTokens": 800}, "messages": {"values": [{"role": "system", "content": "Você é um especialista em roteamento inteligente de atendimento ao cliente. Com base no texto fornecido, determine o melhor agente ou departamento para resolver a questão. Retorne um JSON estruturado:\n\n{\n  \"routing_analysis\": {\n    \"primary_department\": \"technical|billing|sales|support|management\",\n    \"secondary_department\": \"departamento alternativo\",\n    \"confidence_score\": 0.0-1.0,\n    \"routing_reasons\": [\"motivos para o roteamento\"]\n  },\n  \"agent_requirements\": {\n    \"required_skills\": [\"habilidades necessárias\"],\n    \"experience_level\": \"junior|mid|senior|expert\",\n    \"language_requirements\": [\"idiomas necessários\"],\n    \"specializations\": [\"especializações\"]\n  },\n  \"priority_assessment\": {\n    \"business_impact\": \"low|medium|high|critical\",\n    \"customer_tier\": \"standard|premium|vip|enterprise\",\n    \"sla_requirements\": \"tempo de resposta em minutos\",\n    \"escalation_path\": [\"caminho de escalação\"]\n  }\n}\n\nSeja específico e considere o contexto do negócio."}, {"role": "user", "content": "Analise este caso para roteamento:\n\nTexto: {{ $('validate-analysis-data').item.json.analysis_text }}\n\nContexto adicional:\n- Sistema origem: {{ $('validate-analysis-data').item.json.source_system }}\n- Dados do cliente: {{ JSON.stringify($('validate-analysis-data').item.json.customer_data) }}"}]}}, "id": "openai-routing-analysis", "name": "🎯 OpenAI Routing Analysis", "type": "n8n-nodes-base.openAi", "typeVersion": 1.3, "position": [900, 400], "credentials": {"openAiApi": {"id": "openai-escalation-api", "name": "OpenAI Escalation API"}}}, {"parameters": {"jsCode": "// Processar e combinar resultados das análises AI\nconst inputData = $('validate-analysis-data').item.json;\nconst sentimentResult = $('openai-sentiment-analysis').item.json;\nconst routingResult = $('openai-routing-analysis').item.json;\n\n// Parse dos resultados JSON das análises\nlet sentimentAnalysis = {};\nlet routingAnalysis = {};\n\ntry {\n  // Extrair JSON do resultado do OpenAI\n  const sentimentText = sentimentResult.message?.content || sentimentResult.choices?.[0]?.message?.content || '';\n  const routingText = routingResult.message?.content || routingResult.choices?.[0]?.message?.content || '';\n  \n  // Parse do JSON de sentiment\n  const sentimentMatch = sentimentText.match(/\\{[\\s\\S]*\\}/);\n  if (sentimentMatch) {\n    sentimentAnalysis = JSON.parse(sentimentMatch[0]);\n  }\n  \n  // Parse do JSON de routing\n  const routingMatch = routingText.match(/\\{[\\s\\S]*\\}/);\n  if (routingMatch) {\n    routingAnalysis = JSON.parse(routingMatch[0]);\n  }\n} catch (error) {\n  console.error('Erro ao processar resultados AI:', error);\n  // Fallback para análise básica\n  sentimentAnalysis = {\n    sentiment_analysis: {\n      overall_sentiment: 'neutral',\n      confidence_score: 0.5,\n      urgency_level: 'medium'\n    },\n    escalation_analysis: {\n      escalation_required: true,\n      suggested_priority: 'medium',\n      suggested_category: 'general'\n    }\n  };\n  \n  routingAnalysis = {\n    routing_analysis: {\n      primary_department: 'support',\n      confidence_score: 0.5\n    },\n    agent_requirements: {\n      experience_level: 'mid'\n    }\n  };\n}\n\n// Combinar resultados\nconst combinedAnalysis = {\n  escalation_id: inputData.escalation_id,\n  analysis_timestamp: new Date().toISOString(),\n  source_system: inputData.source_system,\n  \n  // Análise de sentimento\n  sentiment: sentimentAnalysis.sentiment_analysis || {},\n  \n  // Análise de escalação\n  escalation: sentimentAnalysis.escalation_analysis || {},\n  \n  // Análise de conteúdo\n  content: sentimentAnalysis.content_analysis || {},\n  \n  // Análise de roteamento\n  routing: routingAnalysis.routing_analysis || {},\n  \n  // Requisitos do agente\n  agent_requirements: routingAnalysis.agent_requirements || {},\n  \n  // Avaliação de prioridade\n  priority_assessment: routingAnalysis.priority_assessment || {},\n  \n  // Recomendações finais\n  recommendations: {\n    final_priority: routingAnalysis.priority_assessment?.business_impact || \n                   sentimentAnalysis.escalation_analysis?.suggested_priority || 'medium',\n    final_category: sentimentAnalysis.escalation_analysis?.suggested_category || 'general',\n    final_department: routingAnalysis.routing_analysis?.primary_department || 'support',\n    confidence_score: Math.min(\n      sentimentAnalysis.sentiment_analysis?.confidence_score || 0.5,\n      routingAnalysis.routing_analysis?.confidence_score || 0.5\n    ),\n    requires_human_review: (\n      sentimentAnalysis.sentiment_analysis?.overall_sentiment === 'very_negative' ||\n      sentimentAnalysis.sentiment_analysis?.urgency_level === 'critical' ||\n      routingAnalysis.priority_assessment?.business_impact === 'critical'\n    )\n  },\n  \n  // Metadados da análise\n  analysis_metadata: {\n    text_length: inputData.text_length,\n    analysis_type: inputData.analysis_type,\n    processing_time_ms: Date.now() - new Date(inputData.timestamp).getTime(),\n    ai_models_used: ['gpt-3.5-turbo', 'sentiment-analysis', 'routing-analysis']\n  }\n};\n\nreturn [{ json: combinedAnalysis }];"}, "id": "process-ai-results", "name": "⚙️ Process AI Results", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1120, 300]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "-- <PERSON><PERSON> an<PERSON>e AI no banco\nINSERT INTO agent.ai_analysis_results (\n  escalation_id,\n  sentiment_data,\n  routing_data,\n  content_analysis,\n  recommendations,\n  confidence_score,\n  requires_human_review,\n  analysis_metadata,\n  created_at\n) VALUES (\n  $1,\n  $2,\n  $3,\n  $4,\n  $5,\n  $6,\n  $7,\n  $8,\n  NOW()\n)\nRETURNING analysis_id, escalation_id, confidence_score;", "options": {"queryReplacement": "={{ $json.escalation_id }},{{ JSON.stringify($json.sentiment) }},{{ JSON.stringify($json.routing) }},{{ JSON.stringify($json.content) }},{{ JSON.stringify($json.recommendations) }},{{ $json.recommendations.confidence_score }},{{ $json.recommendations.requires_human_review }},{{ JSON.stringify($json.analysis_metadata) }}"}}, "id": "save-ai-analysis", "name": "💾 Save AI Analysis", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [1340, 300], "credentials": {"postgres": {"id": "postgres-escalation-db", "name": "PostgreSQL Escalation DB"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "needs-review", "leftValue": "={{ $json.recommendations.requires_human_review }}", "rightValue": true, "operator": {"type": "boolean", "operation": "equal"}}], "combinator": "and"}, "options": {}}, "id": "check-human-review-needed", "name": "❓ Human Review Needed?", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [1560, 300]}, {"parameters": {"url": "http://localhost:5678/webhook/escalation-priority-alert", "options": {}, "bodyParametersUi": {"parameter": [{"name": "escalation_id", "value": "={{ $json.escalation_id }}"}, {"name": "alert_type", "value": "human_review_required"}, {"name": "priority", "value": "={{ $json.recommendations.final_priority }}"}, {"name": "sentiment", "value": "={{ $json.sentiment.overall_sentiment }}"}, {"name": "confidence_score", "value": "={{ $json.recommendations.confidence_score }}"}, {"name": "analysis_summary", "value": "Análise AI detectou necessidade de revisão humana. Sentimento: {{ $json.sentiment.overall_sentiment }}, Urgência: {{ $json.sentiment.urgency_level }}, Departamento: {{ $json.recommendations.final_department }}"}]}}, "id": "trigger-human-review-alert", "name": "🚨 Trigger Human Review Alert", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1780, 200]}, {"parameters": {"url": "http://localhost:5678/webhook/escalation-auto-route", "options": {}, "bodyParametersUi": {"parameter": [{"name": "escalation_id", "value": "={{ $json.escalation_id }}"}, {"name": "recommended_department", "value": "={{ $json.recommendations.final_department }}"}, {"name": "recommended_priority", "value": "={{ $json.recommendations.final_priority }}"}, {"name": "agent_requirements", "value": "={{ JSON.stringify($json.agent_requirements) }}"}, {"name": "confidence_score", "value": "={{ $json.recommendations.confidence_score }}"}, {"name": "routing_data", "value": "={{ JSON.stringify($json.routing) }}"}]}}, "id": "trigger-auto-routing", "name": "🎯 Trigger Auto Routing", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1780, 400]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "-- Atualizar cache de análise AI\nINSERT INTO agent.ai_analysis_cache (\n  content_hash,\n  analysis_result,\n  confidence_score,\n  created_at,\n  expires_at\n) VALUES (\n  MD5($1),\n  $2,\n  $3,\n  NOW(),\n  NOW() + INTERVAL '24 hours'\n)\nON CONFLICT (content_hash) \nDO UPDATE SET \n  analysis_result = EXCLUDED.analysis_result,\n  confidence_score = EXCLUDED.confidence_score,\n  created_at = NOW(),\n  expires_at = NOW() + INTERVAL '24 hours'\nRETURNING cache_id, content_hash;", "options": {"queryReplacement": "={{ $('validate-analysis-data').item.json.analysis_text }},{{ JSON.stringify($json) }},{{ $json.recommendations.confidence_score }}"}}, "id": "update-ai-cache", "name": "🗄️ Update <PERSON> Cache", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [1560, 500], "credentials": {"postgres": {"id": "postgres-escalation-db", "name": "PostgreSQL Escalation DB"}}}, {"parameters": {"jsCode": "// Resposta para casos sem texto suficiente\nconst inputData = $input.first().json;\n\nreturn [{\n  json: {\n    status: 'insufficient_data',\n    escalation_id: inputData.escalation_id,\n    message: 'Texto insuficiente para análise AI',\n    text_length: inputData.text_length,\n    fallback_analysis: {\n      sentiment: {\n        overall_sentiment: 'neutral',\n        confidence_score: 0.1,\n        urgency_level: 'medium'\n      },\n      recommendations: {\n        final_priority: 'medium',\n        final_category: 'general',\n        final_department: 'support',\n        confidence_score: 0.1,\n        requires_human_review: true\n      }\n    },\n    timestamp: new Date().toISOString(),\n    processing_details: {\n      ai_analysis_performed: false,\n      fallback_used: true,\n      reason: 'insufficient_text_length'\n    }\n  }\n}];"}, "id": "handle-insufficient-data", "name": "⚠️ Handle Insufficient Data", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [680, 500]}, {"parameters": {"jsCode": "// Resposta final da análise AI\nconst analysisData = $input.first().json;\nconst saveResult = $('save-ai-analysis').item?.json;\n\nreturn [{\n  json: {\n    status: 'analysis_completed',\n    analysis_id: saveResult?.analysis_id,\n    escalation_id: analysisData.escalation_id,\n    \n    // Resultados principais\n    sentiment: {\n      overall_sentiment: analysisData.sentiment.overall_sentiment,\n      confidence_score: analysisData.sentiment.confidence_score,\n      urgency_level: analysisData.sentiment.urgency_level\n    },\n    \n    recommendations: {\n      priority: analysisData.recommendations.final_priority,\n      category: analysisData.recommendations.final_category,\n      department: analysisData.recommendations.final_department,\n      confidence_score: analysisData.recommendations.confidence_score,\n      requires_human_review: analysisData.recommendations.requires_human_review\n    },\n    \n    routing: {\n      primary_department: analysisData.routing.primary_department,\n      agent_requirements: analysisData.agent_requirements\n    },\n    \n    // Metadados\n    analysis_metadata: analysisData.analysis_metadata,\n    timestamp: new Date().toISOString(),\n    \n    // Próximos passos\n    next_steps: analysisData.recommendations.requires_human_review ? [\n      'Alerta de revisão humana enviado',\n      'Escalação marcada para revisão prioritária',\n      'Aguardando intervenção de supervisor'\n    ] : [\n      'Roteamento automático iniciado',\n      'Agente será designado automaticamente',\n      'Monitoramento de SLA ativado'\n    ],\n    \n    processing_details: {\n      ai_analysis_performed: true,\n      models_used: analysisData.analysis_metadata.ai_models_used,\n      processing_time_ms: analysisData.analysis_metadata.processing_time_ms,\n      cache_updated: true,\n      database_saved: true\n    }\n  }\n}];"}, "id": "format-analysis-response", "name": "📄 Format Analysis Response", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [2000, 300]}], "pinData": {}, "connections": {"ai-analysis-trigger": {"main": [[{"node": "validate-analysis-data", "type": "main", "index": 0}]]}, "validate-analysis-data": {"main": [[{"node": "check-text-availability", "type": "main", "index": 0}]]}, "check-text-availability": {"main": [[{"node": "openai-sentiment-analysis", "type": "main", "index": 0}, {"node": "openai-routing-analysis", "type": "main", "index": 0}], [{"node": "handle-insufficient-data", "type": "main", "index": 0}]]}, "openai-sentiment-analysis": {"main": [[{"node": "process-ai-results", "type": "main", "index": 0}]]}, "openai-routing-analysis": {"main": [[{"node": "process-ai-results", "type": "main", "index": 0}]]}, "process-ai-results": {"main": [[{"node": "save-ai-analysis", "type": "main", "index": 0}, {"node": "update-ai-cache", "type": "main", "index": 0}]]}, "save-ai-analysis": {"main": [[{"node": "check-human-review-needed", "type": "main", "index": 0}]]}, "check-human-review-needed": {"main": [[{"node": "trigger-human-review-alert", "type": "main", "index": 0}], [{"node": "trigger-auto-routing", "type": "main", "index": 0}]]}, "trigger-human-review-alert": {"main": [[{"node": "format-analysis-response", "type": "main", "index": 0}]]}, "trigger-auto-routing": {"main": [[{"node": "format-analysis-response", "type": "main", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1", "saveManualExecutions": true, "callerPolicy": "workflowsFromSameOwner", "errorWorkflow": "error-handler-workflow"}, "versionId": "ai-analysis-v2.0", "meta": {"templateCredsSetupCompleted": true, "instanceId": "escalation-ai-analysis"}, "id": "ai-analysis-workflow", "tags": [{"createdAt": "2024-01-15T10:00:00.000Z", "updatedAt": "2024-01-15T10:00:00.000Z", "id": "escalation-ai", "name": "escalation-ai"}]}