# .env.example -> renomeie para .env e preencha

# -----------------
# Confi<PERSON><PERSON><PERSON><PERSON><PERSON>
# -----------------
# Domínio principal para acessar os serviços. Ex: agent.meusite.com
# Use um domínio real com DNS apontando para o IP do seu servidor.
DOMAIN=seu-dominio.com
# Email para registro do certificado SSL com Let's Encrypt
ACME_EMAIL=<EMAIL>
# Timezone para todos os contêineres
TZ=America/Sao_Paulo

# -----------------
# PostgreSQL - Banco de Dados Principal
# -----------------
POSTGRES_USER=admin
POSTGRES_PASSWORD=UltraSecretPassword123!
POSTGRES_DB=agent_db
POSTGRES_PORT=5432

# -----------------
# RabbitMQ - <PERSON><PERSON> de <PERSON>
# -----------------
RABBITMQ_DEFAULT_USER=rabbit_user
RABBITMQ_DEFAULT_PASS=RabbitSecretPassword123!

# -----------------
# Evolution API - Gateway WhatsApp
# -----------------
# Crie chaves fortes e aleatórias
EVOLUTION_API_KEY=EvoSecretApiKey
EVOLUTION_GLOBAL_API_KEY=EvoGlobalSecretApiKey

# -----------------
# n8n - Orquestrador de Workflows
# -----------------
# Webhook URL que a Evolution API usará para chamar o n8n
# O n8n precisa saber seu próprio endereço público.
# ATENÇÃO: Use o mesmo domínio definido na variável DOMAIN
N8N_WEBHOOK_URL=https://n8n.${DOMAIN}/

# -----------------
# Chatwoot - Atendimento Humano
# -----------------
# Crie uma chave secreta para o Rails. Use: openssl rand -hex 64
CHATWOOT_SECRET_KEY_BASE=c5a8... # Preencha com o resultado do comando
CHATWOOT_FRONTEND_URL=https://chatwoot.${DOMAIN}
# A senha do banco de dados que o Chatwoot usará (deve ser a mesma do POSTGRES_PASSWORD)
CHATWOOT_DB_PASSWORD=${POSTGRES_PASSWORD}

# -----------------
# Metabase - BI & Dashboards
# -----------------
# Senha para o banco de dados interno que o Metabase usa (pode ser diferente)
METABASE_DB_PASSWORD=MetabaseSecretDbPass123!

# -----------------
# Credenciais de APIs Externas (para o ai_processor)
# -----------------
# Ex: Chave da API da OpenAI, se você for usar o modelo deles via API
OPENAI_API_KEY=sk-...