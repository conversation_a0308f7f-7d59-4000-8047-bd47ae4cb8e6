# quick-test.ps1 - Teste rápido das funções implementadas

# Importar módulo
$modulePath = ".\PowerShellModules\AutomationUtils.psm1"
Import-Module $modulePath -Force

Write-Host "TESTE RAPIDO DAS FUNCOES" -ForegroundColor Cyan

# Teste 1: Verificar se as funções existem
$functions = @('Scan-WorkflowsDirectory', 'Show-WorkflowSelectionMenu', 'Show-CountdownTimer')
foreach ($func in $functions) {
    if (Get-Command $func -ErrorAction SilentlyContinue) {
        Write-Host "OK: $func" -ForegroundColor Green
    } else {
        Write-Host "ERRO: $func nao encontrada" -ForegroundColor Red
    }
}

# Teste 2: Testar escaneamento de workflows
Write-Host "`nTestando escaneamento de workflows..." -ForegroundColor Yellow
try {
    $result = Scan-WorkflowsDirectory -WorkflowsPath "workflows"
    Write-Host "Sucesso: $($result.TotalWorkflows) workflows encontrados" -ForegroundColor Green
    Write-Host "Precisam ajuste: $($result.WorkflowsNeedingAdjustment.Count)" -ForegroundColor Yellow
} catch {
    Write-Host "Erro: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`nTeste concluido!" -ForegroundColor Green
