{"meta": {"instanceId": "MULTI_AGENT_IDENTITY_MANAGER"}, "name": "[IDENTITY] Multi-Agent Identity Manager", "nodes": [{"parameters": {}, "id": "webhook_content_request", "name": "WEBHOOK: Content Request", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [140, 300], "webhookId": "multi-agent-content", "path": "multi-agent-content"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "-- Buscar identidade específica e suas configurações\nSELECT \n  ai.*,\n  acc.context_rules,\n  acc.examples,\n  acc.anti_patterns,\n  isa.platform,\n  isa.account_handle,\n  isa.follower_count,\n  isa.engagement_rate\nFROM agent.agent_identities ai\nLEFT JOIN agent.identity_content_context acc ON ai.id = acc.identity_id AND acc.context_type = 'brand_voice'\nLEFT JOIN agent.identity_social_accounts isa ON ai.id = isa.identity_id AND isa.platform = $2\nWHERE ai.identity_name = $1 AND ai.status = 'active'\nLIMIT 1;", "options": {"parameters": {"values": ["={{ $json.identity_name || 'lucas_tech_efficiency' }}", "={{ $json.target_platform || 'linkedin' }}"]}}}, "id": "get_identity_config", "name": "🎭 Get Identity Configuration", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [360, 300], "credentials": {"postgres": {"id": "postgres-main", "name": "PostgreSQL Main"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "identity_found", "leftValue": "={{ $json.id }}", "rightValue": "", "operator": {"type": "string", "operation": "isNotEmpty"}}]}}, "id": "if_identity_exists", "name": "IF: Identity Exists?", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [580, 300]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "-- <PERSON>car histórico de conteúdo desta identidade para manter consistência\nSELECT \n  content_text,\n  content_type,\n  platform,\n  viral_score,\n  engagement_metrics,\n  content_tags,\n  published_at\nFROM agent.identity_content_history\nWHERE identity_id = $1\nAND published_at > NOW() - INTERVAL '30 days'\nORDER BY viral_score DESC, published_at DESC\nLIMIT 10;", "options": {"parameters": {"values": ["={{ $('get_identity_config').item.json.id }}"]}}}, "id": "get_identity_history", "name": "📊 Get Identity Content History", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [800, 200], "credentials": {"postgres": {"id": "postgres-main", "name": "PostgreSQL Main"}}}, {"parameters": {"workflowId": "={{ $env.DISPATCHER_WORKFLOW_ID }}", "data": "={{ {\n  body: {\n    prompt: `Você é ${$('get_identity_config').item.json.display_name}, ${$('get_identity_config').item.json.bio}\\n\\n**SUA IDENTIDADE ESPECÍFICA:**\\n- Categoria: ${$('get_identity_config').item.json.category}\\n- Especialidades: ${JSON.stringify($('get_identity_config').item.json.expertise_areas)}\\n- Características de Personalidade: ${JSON.stringify($('get_identity_config').item.json.personality_traits)}\\n- Audiência Principal: ${JSON.stringify($('get_identity_config').item.json.target_audience)}\\n- Temas de Conteúdo: ${JSON.stringify($('get_identity_config').item.json.content_themes)}\\n\\n**CONFIGURAÇÃO DE VOZ:**\\n${JSON.stringify($('get_identity_config').item.json.voice_characteristics)}\\n\\n**REGRAS DE CONTEXTO:**\\n${JSON.stringify($('get_identity_config').item.json.context_rules || {})}\\n\\n**EXEMPLOS DO SEU ESTILO:**\\n${JSON.stringify($('get_identity_config').item.json.examples || {})}\\n\\n**O QUE VOCÊ NUNCA FARIA:**\\n${JSON.stringify($('get_identity_config').item.json.anti_patterns || {})}\\n\\n**SEU HISTÓRICO RECENTE (para consistência):**\\n${$('get_identity_history').all().slice(0, 3).map(item => `• ${item.json.content_text.substring(0, 100)}... (Score: ${item.json.viral_score})`).join('\\n')}\\n\\n**SOLICITAÇÃO DE CONTEÚDO:**\\nTópico: ${$('webhook_content_request').item.json.topic || 'N/A'}\\nPlataforma: ${$('webhook_content_request').item.json.target_platform}\\nTipo: ${$('webhook_content_request').item.json.content_type || 'post'}\\nObjetivo: ${$('webhook_content_request').item.json.objective || 'engajar audiência'}\\nContexto adicional: ${$('webhook_content_request').item.json.additional_context || 'N/A'}\\n\\n**INSTRUÇÕES CRÍTICAS:**\\n1. MANTENHA sua voz e personalidade específica\\n2. NÃO saia do seu escopo de expertise\\n3. Use seu estilo característico de comunicação\\n4. Considere seu histórico para manter consistência\\n5. Respeite as regras de contexto da sua identidade\\n6. Foque na sua audiência específica\\n\\nCrie conteúdo autêntico que seja reconhecidamente seu, mantendo total separação de contexto de outros agentes.\\n\\nResposta em JSON:\\n{\\n  \\\"content\\\": \\\"conteúdo criado aqui\\\",\\n  \\\"style_elements\\\": [\\\"elementos do seu estilo\\\"],\\n  \\\"target_audience_appeal\\\": \\\"como isso conecta com sua audiência\\\",\\n  \\\"consistency_score\\\": 0.00,\\n  \\\"context_compliance\\\": true,\\n  \\\"suggested_hashtags\\\": [\\\"hashtags relevantes\\\"],\\n  \\\"optimal_posting_time\\\": \\\"HH:MM\\\"\\n}`,\n    task_type: 'identity_specific_content',\n    calling_workflow_id: $workflow.id,\n    calling_node_id: 'generate_identity_content',\n    identity_context: {\n      identity_id: $('get_identity_config').item.json.id,\n      identity_name: $('get_identity_config').item.json.identity_name,\n      category: $('get_identity_config').item.json.category\n    }\n  }\n} }}", "options": {}}, "id": "generate_identity_content", "name": "🤖 AI: Generate Identity-Specific Content", "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 2, "position": [1020, 200]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "-- Validar contexto do conteúdo gerado\nSELECT validate_content_context($1, $2, $3) as validation_result;", "options": {"parameters": {"values": ["={{ $('get_identity_config').item.json.id }}", "={{ $('generate_identity_content').item.json.content }}", "={{ $('webhook_content_request').item.json.target_platform }}"]}}}, "id": "validate_content_context", "name": "✅ Validate Content Context", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [1240, 200], "credentials": {"postgres": {"id": "postgres-main", "name": "PostgreSQL Main"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "context_approved", "leftValue": "={{ $json.validation_result.recommendation }}", "rightValue": "approved", "operator": {"type": "string", "operation": "equals"}}]}}, "id": "if_context_valid", "name": "IF: Context Valid?", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [1460, 200]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "-- <PERSON><PERSON> conteúdo no histórico da identidade\nINSERT INTO agent.identity_content_history (\n  identity_id,\n  content_type,\n  content_text,\n  content_metadata,\n  platform,\n  content_tags,\n  status\n)\nVALUES (\n  $1,\n  $2,\n  $3,\n  $4::jsonb,\n  $5,\n  $6::text[],\n  'generated'\n)\nRETURNING *;", "options": {"parameters": {"values": ["={{ $('get_identity_config').item.json.id }}", "={{ $('webhook_content_request').item.json.content_type || 'post' }}", "={{ $('generate_identity_content').item.json.content }}", "={{ JSON.stringify({\n                ai_response: $('generate_identity_content').item.json,\n                validation: $('validate_content_context').item.json.validation_result,\n                request_context: $('webhook_content_request').item.json,\n                identity_config: {\n                  name: $('get_identity_config').item.json.identity_name,\n                  category: $('get_identity_config').item.json.category\n                }\n              }) }}", "={{ $('webhook_content_request').item.json.target_platform }}", "={{ $('generate_identity_content').item.json.suggested_hashtags ? JSON.stringify($('generate_identity_content').item.json.suggested_hashtags) : '{}' }}"]}}}, "id": "save_to_identity_history", "name": "💾 Save to Identity History", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [1680, 200], "credentials": {"postgres": {"id": "postgres-main", "name": "PostgreSQL Main"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "should_auto_post", "leftValue": "={{ $('webhook_content_request').item.json.auto_post }}", "rightValue": true, "operator": {"type": "boolean", "operation": "equal"}}, {"id": "has_credentials", "leftValue": "={{ $('get_identity_config').item.json.account_handle }}", "rightValue": "", "operator": {"type": "string", "operation": "isNotEmpty"}}], "combineOperation": "and"}}, "id": "if_should_auto_post", "name": "IF: Should Auto-Post?", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [1900, 200]}, {"parameters": {"workflowId": "={{ $env.CONTENT_ENGINE_WORKFLOW_ID }}", "data": "={{ {\n  body: {\n    action: 'schedule_identity_post',\n    identity_id: $('get_identity_config').item.json.id,\n    identity_name: $('get_identity_config').item.json.identity_name,\n    platform: $('webhook_content_request').item.json.target_platform,\n    account_handle: $('get_identity_config').item.json.account_handle,\n    content: $('generate_identity_content').item.json.content,\n    optimal_time: $('generate_identity_content').item.json.optimal_posting_time,\n    hashtags: $('generate_identity_content').item.json.suggested_hashtags,\n    content_id: $('save_to_identity_history').item.json.id\n  }\n} }}", "options": {}}, "id": "schedule_identity_post", "name": "📅 Schedule Identity Post", "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 2, "position": [2120, 200], "continueOnFail": true}, {"parameters": {"method": "POST", "url": "={{ $vars.SLACK_WEBHOOK_URL }}", "sendBody": true, "specifyBody": "json", "jsonBody": "{\n  \"text\": \"❌ Contexto de identidade violado\",\n  \"blocks\": [\n    {\n      \"type\": \"section\",\n      \"text\": {\n        \"type\": \"mrkdwn\",\n        \"text\": \"*❌ Content Context Violation*\\n\\n*Identidade:* {{ $('get_identity_config').item.json.display_name }}\\n*Categoria:* {{ $('get_identity_config').item.json.category }}\\n*Plataforma:* {{ $('webhook_content_request').item.json.target_platform }}\\n\\n*Problema:* {{ $('validate_content_context').item.json.validation_result.recommendation }}\\n\\n*Score de Voz:* {{ $('validate_content_context').item.json.validation_result.voice_compliance_score }}\\n*Compliance de Tópico:* {{ $('validate_content_context').item.json.validation_result.topic_compliance }}\\n\\n*Conteúdo Rejeitado:*\\n{{ $('generate_identity_content').item.json.content.substring(0, 200) }}...\\n\\n_Conteúdo não foi publicado para manter integridade da identidade._\"\n      }\n    }\n  ],\n  \"channel\": \"#identity-management\"\n}", "options": {}}, "id": "notify_context_violation", "name": "🚨 Notify Context Violation", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [1680, 400], "continueOnFail": true}, {"parameters": {"method": "POST", "url": "={{ $vars.SLACK_WEBHOOK_URL }}", "sendBody": true, "specifyBody": "json", "jsonBody": "{\n  \"text\": \"❌ Identidade não encontrada\",\n  \"blocks\": [\n    {\n      \"type\": \"section\",\n      \"text\": {\n        \"type\": \"mrkdwn\",\n        \"text\": \"*❌ Identity Not Found*\\n\\n*Identidade Solicitada:* {{ $('webhook_content_request').item.json.identity_name }}\\n*Plataforma:* {{ $('webhook_content_request').item.json.target_platform }}\\n\\n*Identidades Disponíveis:*\\n• lucas_tech_efficiency (Tech Analyst)\\n• alex_data_science (Tech Analyst)\\n• bia_conecta_original (Creator Catalyst)\\n• nina_viral_queen (Creator Catalyst)\\n• andre_exec_original (Scale Strategist)\\n• carlos_growth_master (Scale Strategist)\\n• rafael_sales_ninja (Sales Expert)\\n• marina_content_pro (Content Creator)\\n\\n_Solicite conteúdo usando uma identidade válida._\"\n      }\n    }\n  ],\n  \"channel\": \"#identity-management\"\n}", "options": {}}, "id": "notify_identity_not_found", "name": "⚠️ Notify Identity Not Found", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [800, 400], "continueOnFail": true}, {"parameters": {"mode": "combine", "combinationMode": "mergeByPosition", "options": {}}, "id": "merge_responses", "name": "Merge All Responses", "type": "n8n-nodes-base.merge", "typeVersion": 2.1, "position": [2340, 300]}, {"parameters": {"respondWith": "json", "responseBody": "={{ {\n  success: $('save_to_identity_history').item ? true : false,\n  identity: {\n    name: $('get_identity_config').item?.json?.identity_name || 'not_found',\n    display_name: $('get_identity_config').item?.json?.display_name || 'N/A',\n    category: $('get_identity_config').item?.json?.category || 'N/A'\n  },\n  content: {\n    generated: $('generate_identity_content').item?.json?.content || null,\n    validation: $('validate_content_context').item?.json?.validation_result || null,\n    content_id: $('save_to_identity_history').item?.json?.id || null\n  },\n  posting: {\n    auto_posted: $('schedule_identity_post').item ? true : false,\n    platform: $('webhook_content_request').item?.json?.target_platform || null,\n    scheduled_time: $('generate_identity_content').item?.json?.optimal_posting_time || null\n  },\n  metadata: {\n    request_timestamp: $now.toISO(),\n    processing_time_ms: $now.diff($('webhook_content_request').item.json.timestamp || $now).milliseconds,\n    workflow_execution_id: $workflow.executionId\n  }\n} }}", "options": {}}, "id": "respond_to_webhook", "name": "📤 Respond to Webhook", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [2560, 300]}], "connections": {"webhook_content_request": {"main": [[{"node": "get_identity_config", "type": "main", "index": 0}]]}, "get_identity_config": {"main": [[{"node": "if_identity_exists", "type": "main", "index": 0}]]}, "if_identity_exists": {"main": [[{"node": "get_identity_history", "type": "main", "index": 0}], [{"node": "notify_identity_not_found", "type": "main", "index": 0}]]}, "get_identity_history": {"main": [[{"node": "generate_identity_content", "type": "main", "index": 0}]]}, "generate_identity_content": {"main": [[{"node": "validate_content_context", "type": "main", "index": 0}]]}, "validate_content_context": {"main": [[{"node": "if_context_valid", "type": "main", "index": 0}]]}, "if_context_valid": {"main": [[{"node": "save_to_identity_history", "type": "main", "index": 0}], [{"node": "notify_context_violation", "type": "main", "index": 0}]]}, "save_to_identity_history": {"main": [[{"node": "if_should_auto_post", "type": "main", "index": 0}]]}, "if_should_auto_post": {"main": [[{"node": "schedule_identity_post", "type": "main", "index": 0}], [{"node": "merge_responses", "type": "main", "index": 0}]]}, "schedule_identity_post": {"main": [[{"node": "merge_responses", "type": "main", "index": 1}]]}, "notify_context_violation": {"main": [[{"node": "merge_responses", "type": "main", "index": 2}]]}, "notify_identity_not_found": {"main": [[{"node": "merge_responses", "type": "main", "index": 3}]]}, "merge_responses": {"main": [[{"node": "respond_to_webhook", "type": "main", "index": 0}]]}}, "settings": {"executionOrder": "v1"}, "staticData": null, "tags": [{"createdAt": "2024-01-15T16:00:00.000Z", "updatedAt": "2024-01-15T16:00:00.000Z", "id": "identity-management", "name": "identity-management"}], "triggerCount": 1, "updatedAt": "2024-01-15T16:00:00.000Z", "versionId": "1"}