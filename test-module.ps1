# test-module.ps1
# Script de teste para validar o módulo AutomationUtils

Write-Host "=== TESTE DO MÓDULO AUTOMATIONUTILS ===" -ForegroundColor Cyan

# Importar módulo
$utilityModuleName = "AutomationUtils"
$modulePath = Join-Path $PSScriptRoot ".\PowerShellModules\$utilityModuleName.psm1"

if (Test-Path $modulePath) {
    try {
        Import-Module $modulePath -Force -ErrorAction Stop
        Write-Host "✓ Módulo importado com sucesso" -ForegroundColor Green
    } catch {
        Write-Host "✗ Erro ao importar módulo: $($_.Exception.Message)" -ForegroundColor Red
        exit 1
    }
} else {
    Write-Host "✗ Módulo não encontrado em: $modulePath" -ForegroundColor Red
    exit 1
}

# Testar funções essenciais
Write-Host "`nTestando funções do módulo..." -ForegroundColor Yellow

try {
    # Teste Log-Event
    Log-Event "Teste de log funcionando" "TestModule" "INFO"
    Write-Host "✓ Log-Event: OK" -ForegroundColor Green
    
    # Teste Generate-RandomPassword
    $testPassword = Generate-RandomPassword 12
    if ($testPassword.Length -eq 12) {
        Write-Host "✓ Generate-RandomPassword: OK ($testPassword)" -ForegroundColor Green
    } else {
        Write-Host "✗ Generate-RandomPassword: FALHOU" -ForegroundColor Red
    }
    
    # Teste Generate-SecureKey
    $testKey = Generate-SecureKey 16
    if ($testKey.Length -gt 0) {
        Write-Host "✓ Generate-SecureKey: OK (${testKey}...)" -ForegroundColor Green
    } else {
        Write-Host "✗ Generate-SecureKey: FALHOU" -ForegroundColor Red
    }
    
    # Teste Safe-HtmlEncode
    $testHtml = Safe-HtmlEncode "<script>alert('test')</script>"
    if ($testHtml -like "*&lt;script&gt;*") {
        Write-Host "✓ Safe-HtmlEncode: OK" -ForegroundColor Green
    } else {
        Write-Host "✗ Safe-HtmlEncode: FALHOU" -ForegroundColor Red
    }
    
    Log-Event "Todos os testes do módulo concluídos com sucesso!" "TestModule" "SUCCESS"
    Write-Host "`n✓ MÓDULO VALIDADO COM SUCESSO!" -ForegroundColor Green
    
} catch {
    Write-Host "✗ Erro durante os testes: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
} 