{
  "meta": {
    "instanceId": "VIRAL_GROWTH_ENGINE_V2"
  },
  "name": "[GROWTH] Viral Growth Engine v2.0",
  "nodes": [
    {
      "parameters": {
        "rule": {
          "interval": [
            {
              "field": "hours",
              "hoursInterval": 2
            }
          ]
        }
      },
      "id": "trigger_growth_scan",
      "name": "TRIGGER: Growth Scan Every 2H",
      "type": "n8n-nodes-base.scheduleTrigger",
      "typeVersion": 1.1,
      "position": [140, 300]
    },
    {
      "parameters": {
        "operation": "executeQuery",
        "query": "-- Identificar conteúdos virais das últimas 24h\nSELECT \n  pc.id,\n  pc.content_text,\n  pc.engagement_metrics,\n  ip.name as influencer_name,\n  ip.archetype,\n  pc.platform,\n  calculate_viral_score(pc.engagement_metrics) as viral_score,\n  pc.published_at\nFROM agent.published_content pc\nJOIN agent.influencer_personas ip ON pc.influencer_id = ip.id\nWHERE pc.published_at > NOW() - INTERVAL '24 hours'\nAND pc.engagement_metrics IS NOT NULL\nAND calculate_viral_score(pc.engagement_metrics) > 50\nORDER BY viral_score DESC\nLIMIT 10;"
      },
      "id": "analyze_viral_content",
      "name": "Analyze Recent Viral Content",
      "type": "n8n-nodes-base.postgres",
      "typeVersion": 2.4,
      "position": [360, 200],
      "credentials": {
        "postgres": {
          "id": "postgres-main",
          "name": "PostgreSQL Main"
        }
      }
    },
    {
      "parameters": {
        "method": "GET",
        "url": "https://trends.google.com/trends/api/dailytrends?hl=pt-BR&tz=-180&geo=BR&ns=15",
        "options": {
          "headers": {
            "User-Agent": "Mozilla/5.0 (compatible; N8N-TrendBot/2.0)"
          },
          "timeout": 10000
        }
      },
      "id": "fetch_trending_topics",
      "name": "Fetch Google Trends",
      "type": "n8n-nodes-base.httpRequest",
      "typeVersion": 4.1,
      "position": [360, 380],
      "continueOnFail": true
    },
    {
      "parameters": {
        "operation": "executeQuery",
        "query": "-- Analisar hashtags em crescimento\nSELECT \n  th.hashtag,\n  th.platform,\n  th.usage_count,\n  th.engagement_avg,\n  th.trend_momentum,\n  th.status,\n  th.category\nFROM agent.trending_hashtags th\nWHERE th.last_seen > NOW() - INTERVAL '2 hours'\nAND th.status IN ('emerging', 'rising')\nAND th.trend_momentum > 0.3\nORDER BY th.trend_momentum DESC, th.engagement_avg DESC\nLIMIT 15;"
      },
      "id": "trending_hashtags_analysis",
      "name": "Trending Hashtags Analysis",
      "type": "n8n-nodes-base.postgres",
      "typeVersion": 2.4,
      "position": [360, 560],
      "credentials": {
        "postgres": {
          "id": "postgres-main",
          "name": "PostgreSQL Main"
        }
      }
    },
    {
      "parameters": {
        "workflowId": "={{ $env.DISPATCHER_WORKFLOW_ID }}",
        "data": "={{ {\n  body: {\n    prompt: `Você é um especialista em Growth Hacking para influenciadores digitais. Analise os dados abaixo e gere 5 estratégias de crescimento viral específicas.\\n\\n**CONTEÚDOS VIRAIS RECENTES:**\\n${JSON.stringify($('analyze_viral_content').all().slice(0, 5))}\\n\\n**TENDÊNCIAS DO GOOGLE:**\\n${$('fetch_trending_topics').first() ? JSON.stringify($('fetch_trending_topics').first().json) : 'Dados não disponíveis'}\\n\\n**HASHTAGS EM ALTA:**\\n${JSON.stringify($('trending_hashtags_analysis').all())}\\n\\nPara cada estratégia, forneça:\\n1. Nome da estratégia\\n2. Tipo (viral_hook, collaboration, ugc, timing_optimization, trend_surfing)\\n3. Descrição detalhada\\n4. Métricas de sucesso esperadas\\n5. Timeline de implementação\\n6. Score de dificuldade (1-10)\\n7. ROI estimado (1.0-5.0)\\n\\nFormate como JSON:\\n{\\n  \\\"strategies\\\": [\\n    {\\n      \\\"name\\\": \\\"\\\",\\n      \\\"type\\\": \\\"\\\",\\n      \\\"description\\\": \\\"\\\",\\n      \\\"expected_metrics\\\": {\\\"reach_increase\\\": \\\"\\\", \\\"engagement_boost\\\": \\\"\\\"},\\n      \\\"timeline\\\": \\\"\\\",\\n      \\\"difficulty_score\\\": 0,\\n      \\\"roi_estimate\\\": 0.0\\n    }\\n  ]\\n}`,\n    task_type: 'complex_analysis',\n    calling_workflow_id: $workflow.id,\n    calling_node_id: 'generate_growth_strategies'\n  }\n} }}",
        "options": {}
      },
      "id": "generate_growth_strategies",
      "name": "AI: Generate Growth Strategies",
      "type": "n8n-nodes-base.executeWorkflow",
      "typeVersion": 2,
      "position": [580, 380]
    },
    {
      "parameters": {
        "conditions": {
          "options": {
            "caseSensitive": true,
            "leftValue": "",
            "typeValidation": "strict"
          },
          "conditions": [
            {
              "id": "has_strategies",
              "leftValue": "={{ $json.strategies && Array.isArray($json.strategies) && $json.strategies.length > 0 }}",
              "rightValue": true,
              "operator": {
                "type": "boolean",
                "operation": "equal"
              }
            }
          ]
        }
      },
      "id": "if_strategies_generated",
      "name": "IF: Strategies Generated?",
      "type": "n8n-nodes-base.if",
      "typeVersion": 2,
      "position": [800, 380]
    },
    {
      "parameters": {
        "operation": "executeQuery",
        "query": "-- Identificar oportunidades de colaboração baseadas em audiência compartilhada\nSELECT \n  ip1.id as influencer1_id,\n  ip1.name as influencer1_name,\n  ip1.archetype as archetype1,\n  ip2.id as influencer2_id,\n  ip2.name as influencer2_name,\n  ip2.archetype as archetype2,\n  CASE \n    WHEN ip1.archetype ILIKE '%efficiency%' AND ip2.archetype ILIKE '%scale%' THEN 'Tech Leadership Synergy'\n    WHEN ip1.archetype ILIKE '%creator%' AND ip2.archetype ILIKE '%efficiency%' THEN 'Creator x Automation Bridge'\n    WHEN ip1.archetype ILIKE '%creator%' AND ip2.archetype ILIKE '%scale%' THEN 'Creative Strategy Alliance'\n    ELSE 'Cross-Platform Amplification'\n  END as suggested_collab_type,\n  CASE \n    WHEN ip1.archetype ILIKE '%efficiency%' AND ip2.archetype ILIKE '%scale%' THEN 95\n    WHEN ip1.archetype ILIKE '%creator%' THEN 85\n    ELSE 70\n  END as synergy_potential\nFROM agent.influencer_personas ip1\nCROSS JOIN agent.influencer_personas ip2\nWHERE ip1.id < ip2.id\nAND ip1.status = 'active' \nAND ip2.status = 'active'\nAND NOT EXISTS (\n  SELECT 1 FROM agent.influencer_collaborations ic \n  WHERE (ic.influencer1_id = ip1.id AND ic.influencer2_id = ip2.id)\n     OR (ic.influencer1_id = ip2.id AND ic.influencer2_id = ip1.id)\n  AND ic.status IN ('planned', 'executing')\n)\nORDER BY synergy_potential DESC\nLIMIT 3;"
      },
      "id": "plan_collaborations",
      "name": "Plan Smart Collaborations",
      "type": "n8n-nodes-base.postgres",
      "typeVersion": 2.4,
      "position": [1020, 200],
      "credentials": {
        "postgres": {
          "id": "postgres-main",
          "name": "PostgreSQL Main"
        }
      }
    },
    {
      "parameters": {
        "operation": "executeQuery",
        "query": "-- Identificar oportunidades de UGC baseadas no comportamento dos clientes\nSELECT \n  c.id as contact_id,\n  c.nome,\n  c.empresa_atual,\n  c.tags,\n  c.jornada_status,\n  c.engagement_score,\n  CASE \n    WHEN c.tags @> '[\"lead_converted\"]' AND c.engagement_score > 90 THEN 'success_story'\n    WHEN c.tags @> '[\"power_user\"]' THEN 'case_study'\n    WHEN c.engagement_score > 80 THEN 'testimonial'\n    WHEN c.tags @> '[\"community_active\"]' THEN 'community_spotlight'\n    ELSE 'transformation_story'\n  END as opportunity_type,\n  CASE \n    WHEN c.engagement_score > 90 THEN 95\n    WHEN c.engagement_score > 80 THEN 85\n    WHEN c.engagement_score > 70 THEN 75\n    ELSE 60\n  END as priority_score,\n  CASE \n    WHEN c.tags @> '[\"lead_converted\"]' THEN 'Cliente convertido com excelente ROI - ideal para case de sucesso'\n    WHEN c.engagement_score > 80 THEN 'Alto engajamento - perfeito para testimonial autêntico'\n    ELSE 'Jornada interessante - bom para storytelling'\n  END as suggested_approach\nFROM agent.contatos c\nWHERE c.jornada_status IN ('convertido', 'engajado', 'ativo')\nAND c.ultima_interacao > NOW() - INTERVAL '30 days'\nAND c.engagement_score > 60\nAND NOT EXISTS (\n  SELECT 1 FROM agent.ugc_opportunities uo \n  WHERE uo.contact_id = c.id \n  AND uo.status IN ('identified', 'approached', 'creating')\n)\nORDER BY priority_score DESC, c.engagement_score DESC\nLIMIT 5;"
      },
      "id": "identify_ugc_opportunities",
      "name": "Identify UGC Opportunities",
      "type": "n8n-nodes-base.postgres",
      "typeVersion": 2.4,
      "position": [1020, 380],
      "credentials": {
        "postgres": {
          "id": "postgres-main",
          "name": "PostgreSQL Main"
        }
      }
    },
    {
      "parameters": {
        "operation": "executeQuery",
        "query": "-- Analisar timing ótimo baseado em dados históricos das últimas 4 semanas\nWITH timing_analysis AS (\n  SELECT \n    pc.influencer_id,\n    ip.name as influencer_name,\n    pc.platform,\n    EXTRACT(hour FROM pc.published_at) as hour_of_day,\n    EXTRACT(dow FROM pc.published_at) as day_of_week,\n    calculate_viral_score(pc.engagement_metrics) as viral_score,\n    COALESCE((pc.engagement_metrics->>'reach')::int, 0) as reach\n  FROM agent.published_content pc\n  JOIN agent.influencer_personas ip ON pc.influencer_id = ip.id\n  WHERE pc.published_at > NOW() - INTERVAL '4 weeks'\n  AND pc.engagement_metrics IS NOT NULL\n),\ntiming_stats AS (\n  SELECT \n    influencer_id,\n    influencer_name,\n    platform,\n    hour_of_day,\n    day_of_week,\n    CASE day_of_week \n      WHEN 0 THEN 'Domingo'\n      WHEN 1 THEN 'Segunda'\n      WHEN 2 THEN 'Terça'\n      WHEN 3 THEN 'Quarta'\n      WHEN 4 THEN 'Quinta'\n      WHEN 5 THEN 'Sexta'\n      WHEN 6 THEN 'Sábado'\n    END as day_name,\n    AVG(viral_score) as avg_viral_score,\n    AVG(reach) as avg_reach,\n    COUNT(*) as post_count,\n    STDDEV(viral_score) as score_stddev\n  FROM timing_analysis\n  GROUP BY influencer_id, influencer_name, platform, hour_of_day, day_of_week\n  HAVING COUNT(*) >= 2\n)\nSELECT \n  *,\n  CASE \n    WHEN post_count >= 5 THEN 0.9\n    WHEN post_count >= 3 THEN 0.7\n    ELSE 0.5\n  END as confidence_level,\n  RANK() OVER (PARTITION BY influencer_id, platform ORDER BY avg_viral_score DESC) as performance_rank\nFROM timing_stats\nWHERE avg_viral_score > 30\nORDER BY influencer_id, platform, avg_viral_score DESC;"
      },
      "id": "optimal_timing_analysis",
      "name": "Optimal Timing Analysis",
      "type": "n8n-nodes-base.postgres",
      "typeVersion": 2.4,
      "position": [1020, 560],
      "credentials": {
        "postgres": {
          "id": "postgres-main",
          "name": "PostgreSQL Main"
        }
      }
    },
    {
      "parameters": {
        "mode": "combine",
        "combinationMode": "multiplex",
        "options": {}
      },
      "id": "merge_all_insights",
      "name": "Merge All Growth Insights",
      "type": "n8n-nodes-base.merge",
      "typeVersion": 2.1,
      "position": [1240, 380]
    },
    {
      "parameters": {
        "workflowId": "={{ $env.CONTENT_ENGINE_WORKFLOW_ID }}",
        "data": "={{ {\n  body: {\n    action: 'implement_viral_tactics',\n    growth_strategies: $('generate_growth_strategies').first().json.strategies || [],\n    collaboration_opportunities: $('plan_collaborations').all(),\n    ugc_opportunities: $('identify_ugc_opportunities').all(),\n    optimal_timings: $('optimal_timing_analysis').all(),\n    trending_context: {\n      viral_content: $('analyze_viral_content').all(),\n      trending_hashtags: $('trending_hashtags_analysis').all()\n    }\n  }\n} }}",
        "options": {}\n      },\n      \"id\": \"trigger_viral_content_creation\",\n      \"name\": \"Trigger Viral Content Creation\",\n      \"type\": \"n8n-nodes-base.executeWorkflow\",\n      \"typeVersion\": 2,\n      \"position\": [1460, 300],\n      \"continueOnFail\": true\n    },\n    {\n      \"parameters\": {\n        \"batchSize\": 1\n      },\n      \"id\": \"loop_strategies\",\n      \"name\": \"Loop Growth Strategies\",\n      \"type\": \"n8n-nodes-base.splitInBatches\",\n      \"typeVersion\": 2,\n      \"position\": [1460, 480]\n    },\n    {\n      \"parameters\": {\n        \"operation\": \"executeQuery\",\n        \"query\": \"-- Salvar estratégias de crescimento para execução\\nINSERT INTO agent.growth_strategies (\\n  strategy_name,\\n  strategy_type,\\n  strategy_data,\\n  implementation_date,\\n  status,\\n  expected_metrics,\\n  performance_score,\\n  roi_multiplier\\n)\\nVALUES (\\n  $1,\\n  $2,\\n  $3::jsonb,\\n  NOW() + INTERVAL '1 hour',\\n  'pending',\\n  $4::jsonb,\\n  GREATEST(LEAST($5::int, 100), 0),\\n  GREATEST(LEAST($6::decimal, 5.0), 1.0)\\n)\\nON CONFLICT (strategy_name) \\nDO UPDATE SET \\n  strategy_data = EXCLUDED.strategy_data,\\n  expected_metrics = EXCLUDED.expected_metrics,\\n  performance_score = EXCLUDED.performance_score,\\n  roi_multiplier = EXCLUDED.roi_multiplier,\\n  updated_at = NOW()\\nRETURNING *;\",\n        \"options\": {\n          \"parameters\": {\n            \"values\": [\n              \"={{ $json.name }}\",\n              \"={{ $json.type }}\",\n              \"={{ JSON.stringify($json) }}\",\n              \"={{ JSON.stringify($json.expected_metrics || {}) }}\",\n              \"={{ $json.difficulty_score ? (100 - $json.difficulty_score * 10) : 70 }}\",\n              \"={{ $json.roi_estimate || 1.5 }}\"\n            ]\n          }\n        }\n      },\n      \"id\": \"save_growth_strategy\",\n      \"name\": \"Save Growth Strategy\",\n      \"type\": \"n8n-nodes-base.postgres\",\n      \"typeVersion\": 2.4,\n      \"position\": [1680, 480],\n      \"credentials\": {\n        \"postgres\": {\n          \"id\": \"postgres-main\",\n          \"name\": \"PostgreSQL Main\"\n        }\n      }\n    },\n    {\n      \"parameters\": {\n        \"batchSize\": 1\n      },\n      \"id\": \"loop_collaborations\",\n      \"name\": \"Loop Collaborations\",\n      \"type\": \"n8n-nodes-base.splitInBatches\",\n      \"typeVersion\": 2,\n      \"position\": [1460, 660]\n    },\n    {\n      \"parameters\": {\n        \"operation\": \"executeQuery\",\n        \"query\": \"-- Salvar colaborações planejadas\\nINSERT INTO agent.influencer_collaborations (\\n  collaboration_name,\\n  influencer1_id,\\n  influencer2_id,\\n  collaboration_type,\\n  content_brief,\\n  status,\\n  scheduled_date,\\n  synergy_score,\\n  audience_overlap\\n)\\nVALUES (\\n  $1,\\n  $2,\\n  $3,\\n  $4,\\n  $5,\\n  'planned',\\n  NOW() + INTERVAL '24 hours',\\n  $6,\\n  RANDOM() * 0.3 + 0.2\\n)\\nON CONFLICT (collaboration_name) \\nDO UPDATE SET \\n  content_brief = EXCLUDED.content_brief,\\n  synergy_score = EXCLUDED.synergy_score,\\n  scheduled_date = EXCLUDED.scheduled_date\\nRETURNING *;\",\n        \"options\": {\n          \"parameters\": {\n            \"values\": [\n              \"={{ $json.influencer1_name + ' x ' + $json.influencer2_name + ': ' + $json.suggested_collab_type }}\",\n              \"={{ $json.influencer1_id }}\",\n              \"={{ $json.influencer2_id }}\",\n              \"={{ $json.suggested_collab_type.includes('Bridge') ? 'expertise_bridge' : 'cross_mention' }}\",\n              \"={{ 'Colaboração estratégica entre ' + $json.influencer1_name + ' (' + $json.archetype1 + ') e ' + $json.influencer2_name + ' (' + $json.archetype2 + ') para amplificação mútua de audiência e expertise.' }}\",\n              \"={{ $json.synergy_potential }}\"\n            ]\n          }\n        }\n      },\n      \"id\": \"save_collaboration\",\n      \"name\": \"Save Collaboration Plan\",\n      \"type\": \"n8n-nodes-base.postgres\",\n      \"typeVersion\": 2.4,\n      \"position\": [1680, 660],\n      \"credentials\": {\n        \"postgres\": {\n          \"id\": \"postgres-main\",\n          \"name\": \"PostgreSQL Main\"\n        }\n      }\n    },\n    {\n      \"parameters\": {\n        \"batchSize\": 1\n      },\n      \"id\": \"loop_ugc_opportunities\",\n      \"name\": \"Loop UGC Opportunities\",\n      \"type\": \"n8n-nodes-base.splitInBatches\",\n      \"typeVersion\": 2,\n      \"position\": [1460, 840]\n    },\n    {\n      \"parameters\": {\n        \"operation\": \"executeQuery\",\n        \"query\": \"-- Salvar oportunidades de UGC identificadas\\nINSERT INTO agent.ugc_opportunities (\\n  contact_id,\\n  opportunity_type,\\n  content_brief,\\n  suggested_approach,\\n  status,\\n  priority_score,\\n  estimated_reach\\n)\\nVALUES (\\n  $1,\\n  $2,\\n  $3,\\n  $4::jsonb,\\n  'identified',\\n  $5,\\n  CASE \\n    WHEN $5 > 90 THEN 5000\\n    WHEN $5 > 80 THEN 3000\\n    WHEN $5 > 70 THEN 2000\\n    ELSE 1000\\n  END\\n)\\nON CONFLICT (contact_id, opportunity_type) \\nDO UPDATE SET \\n  content_brief = EXCLUDED.content_brief,\\n  suggested_approach = EXCLUDED.suggested_approach,\\n  priority_score = EXCLUDED.priority_score,\\n  estimated_reach = EXCLUDED.estimated_reach\\nRETURNING *;\",\n        \"options\": {\n          \"parameters\": {\n            \"values\": [\n              \"={{ $json.contact_id }}\",\n              \"={{ $json.opportunity_type }}\",\n              \"={{ 'UGC Opportunity para ' + $json.nome + ($json.empresa_atual ? ' da ' + $json.empresa_atual : '') + ': ' + $json.suggested_approach }}\",\n              \"={{ JSON.stringify({\n                approach_type: $json.opportunity_type,\n                contact_info: {\n                  name: $json.nome,\n                  company: $json.empresa_atual,\n                  engagement_level: $json.engagement_score\n                },\n                suggested_content: $json.suggested_approach,\n                best_contact_method: 'whatsapp'\n              }) }}\",\n              \"={{ $json.priority_score }}\"\n            ]\n          }\n        }\n      },\n      \"id\": \"save_ugc_opportunity\",\n      \"name\": \"Save UGC Opportunity\",\n      \"type\": \"n8n-nodes-base.postgres\",\n      \"typeVersion\": 2.4,\n      \"position\": [1680, 840],\n      \"credentials\": {\n        \"postgres\": {\n          \"id\": \"postgres-main\",\n          \"name\": \"PostgreSQL Main\"\n        }\n      }\n    },\n    {\n      \"parameters\": {\n        \"mode\": \"combine\",\n        \"combinationMode\": \"mergeByPosition\",\n        \"options\": {}\n      },\n      \"id\": \"merge_results\",\n      \"name\": \"Merge All Results\",\n      \"type\": \"n8n-nodes-base.merge\",\n      \"typeVersion\": 2.1,\n      \"position\": [1900, 660]\n    },\n    {\n      \"parameters\": {\n        \"method\": \"POST\",\n        \"url\": \"={{ $vars.SLACK_WEBHOOK_URL }}\",\n        \"sendBody\": true,\n        \"specifyBody\": \"json\",\n        \"jsonBody\": \"{\\n  \\\"text\\\": \\\"🚀 Viral Growth Engine v2.0 - Relatório de Execução\\\",\\n  \\\"blocks\\\": [\\n    {\\n      \\\"type\\\": \\\"header\\\",\\n      \\\"text\\\": {\\n        \\\"type\\\": \\\"plain_text\\\",\\n        \\\"text\\\": \\\"🚀 VIRAL GROWTH REPORT v2.0\\\"\\n      }\\n    },\\n    {\\n      \\\"type\\\": \\\"section\\\",\\n      \\\"fields\\\": [\\n        {\\n          \\\"type\\\": \\\"mrkdwn\\\",\\n          \\\"text\\\": \\\"*🔥 Conteúdos Virais Detectados:* {{ $('analyze_viral_content').all().length }}\\\"\\n        },\\n        {\\n          \\\"type\\\": \\\"mrkdwn\\\",\\n          \\\"text\\\": \\\"*🤝 Colaborações Planejadas:* {{ $('plan_collaborations').all().length }}\\\"\\n        },\\n        {\\n          \\\"type\\\": \\\"mrkdwn\\\",\\n          \\\"text\\\": \\\"*👥 Oportunidades UGC:* {{ $('identify_ugc_opportunities').all().length }}\\\"\\n        },\\n        {\\n          \\\"type\\\": \\\"mrkdwn\\\",\\n          \\\"text\\\": \\\"*🎯 Estratégias Geradas:* {{ $('generate_growth_strategies').first().json.strategies ? $('generate_growth_strategies').first().json.strategies.length : 0 }}\\\"\\n        },\\n        {\\n          \\\"type\\\": \\\"mrkdwn\\\",\\n          \\\"text\\\": \\\"*📈 Hashtags Trending:* {{ $('trending_hashtags_analysis').all().length }}\\\"\\n        },\\n        {\\n          \\\"type\\\": \\\"mrkdwn\\\",\\n          \\\"text\\\": \\\"*⏰ Timings Otimizados:* {{ $('optimal_timing_analysis').all().length }}\\\"\\n        }\\n      ]\\n    },\\n    {\\n      \\\"type\\\": \\\"section\\\",\\n      \\\"text\\\": {\\n        \\\"type\\\": \\\"mrkdwn\\\",\\n        \\\"text\\\": \\\"*📊 Top Viral Content:*\\\\n{{ $('analyze_viral_content').all().slice(0, 3).map(item => `• ${item.json.influencer_name}: ${item.json.viral_score} pontos`).join('\\\\n') }}\\\"\\n      }\\n    },\\n    {\\n      \\\"type\\\": \\\"section\\\",\\n      \\\"text\\\": {\\n        \\\"type\\\": \\\"mrkdwn\\\",\\n        \\\"text\\\": \\\"*🔥 Trending Hashtags:*\\\\n{{ $('trending_hashtags_analysis').all().slice(0, 5).map(item => `• #${item.json.hashtag} (${item.json.platform}) - Momentum: ${Math.round(item.json.trend_momentum * 100)}%`).join('\\\\n') }}\\\"\\n      }\\n    },\\n    {\\n      \\\"type\\\": \\\"context\\\",\\n      \\\"elements\\\": [\\n        {\\n          \\\"type\\\": \\\"mrkdwn\\\",\\n          \\\"text\\\": \\\"Sistema executando a cada 2 horas | Próxima análise: {{ $now.plus({ hours: 2 }).toFormat('dd/MM HH:mm') }}\\\"\\n        }\\n      ]\\n    }\\n  ],\\n  \\\"channel\\\": \\\"#growth-hacking\\\"\\n}\",\n        \"options\": {}\n      },\n      \"id\": \"notify_growth_insights\",\n      \"name\": \"📱 Notify Growth Insights\",\n      \"type\": \"n8n-nodes-base.httpRequest\",\n      \"typeVersion\": 4.1,\n      \"position\": [2120, 660],\n      \"continueOnFail\": true\n    },\n    {\n      \"parameters\": {\n        \"operation\": \"executeQuery\",\n        \"query\": \"-- Atualizar timing analysis com novos dados\\nINSERT INTO agent.optimal_timing_analysis (\\n  influencer_id, platform, hour_of_day, day_of_week, \\n  engagement_score, reach_multiplier, post_count, confidence_level\\n)\\nSELECT \\n  $1::int, $2::text, $3::int, $4::int,\\n  $5::decimal, $6::decimal, $7::int, $8::decimal\\nON CONFLICT (influencer_id, platform, hour_of_day, day_of_week)\\nDO UPDATE SET \\n  engagement_score = (agent.optimal_timing_analysis.engagement_score * 0.7) + (EXCLUDED.engagement_score * 0.3),\\n  reach_multiplier = (agent.optimal_timing_analysis.reach_multiplier * 0.7) + (EXCLUDED.reach_multiplier * 0.3),\\n  post_count = agent.optimal_timing_analysis.post_count + EXCLUDED.post_count,\\n  confidence_level = GREATEST(agent.optimal_timing_analysis.confidence_level, EXCLUDED.confidence_level),\\n  last_updated = NOW();\",\n        \"options\": {\n          \"parameters\": {\n            \"values\": [\n              \"={{ $json.influencer_id }}\",\n              \"={{ $json.platform }}\",\n              \"={{ $json.hour_of_day }}\",\n              \"={{ $json.day_of_week }}\",\n              \"={{ $json.avg_viral_score }}\",\n              \"={{ $json.avg_reach ? ($json.avg_reach / 1000) : 1.0 }}\",\n              \"={{ $json.post_count }}\",\n              \"={{ $json.confidence_level }}\"\n            ]\n          }\n        }\n      },\n      \"id\": \"update_timing_analysis\",\n      \"name\": \"📊 Update Timing Analysis\",\n      \"type\": \"n8n-nodes-base.postgres\",\n      \"typeVersion\": 2.4,\n      \"position\": [1680, 1020],\n      \"credentials\": {\n        \"postgres\": {\n          \"id\": \"postgres-main\",\n          \"name\": \"PostgreSQL Main\"\n        }\n      }\n    }\n  ],\n  \"connections\": {\n    \"trigger_growth_scan\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"analyze_viral_content\",\n            \"type\": \"main\",\n            \"index\": 0\n          },\n          {\n            \"node\": \"fetch_trending_topics\",\n            \"type\": \"main\",\n            \"index\": 0\n          },\n          {\n            \"node\": \"trending_hashtags_analysis\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"analyze_viral_content\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"merge_all_insights\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"fetch_trending_topics\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"merge_all_insights\",\n            \"type\": \"main\",\n            \"index\": 1\n          }\n        ]\n      ]\n    },\n    \"trending_hashtags_analysis\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"merge_all_insights\",\n            \"type\": \"main\",\n            \"index\": 2\n          }\n        ]\n      ]\n    },\n    \"merge_all_insights\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"generate_growth_strategies\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"generate_growth_strategies\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"if_strategies_generated\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"if_strategies_generated\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"plan_collaborations\",\n            \"type\": \"main\",\n            \"index\": 0\n          },\n          {\n            \"node\": \"identify_ugc_opportunities\",\n            \"type\": \"main\",\n            \"index\": 0\n          },\n          {\n            \"node\": \"optimal_timing_analysis\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"plan_collaborations\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"loop_collaborations\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"identify_ugc_opportunities\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"loop_ugc_opportunities\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"optimal_timing_analysis\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"trigger_viral_content_creation\",\n            \"type\": \"main\",\n            \"index\": 0\n          },\n          {\n            \"node\": \"loop_strategies\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"loop_strategies\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"save_growth_strategy\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"save_growth_strategy\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"merge_results\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"loop_collaborations\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"save_collaboration\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"save_collaboration\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"merge_results\",\n            \"type\": \"main\",\n            \"index\": 1\n          }\n        ]\n      ]\n    },\n    \"loop_ugc_opportunities\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"save_ugc_opportunity\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"save_ugc_opportunity\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"merge_results\",\n            \"type\": \"main\",\n            \"index\": 2\n          }\n        ]\n      ]\n    },\n    \"merge_results\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"notify_growth_insights\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    }\n  },\n  \"settings\": {\n    \"executionOrder\": \"v1\"\n  },\n  \"staticData\": null,\n  \"tags\": [\n    {\n      \"createdAt\": \"2024-01-15T10:00:00.000Z\",\n      \"updatedAt\": \"2024-01-15T10:00:00.000Z\",\n      \"id\": \"viral-growth\",\n      \"name\": \"viral-growth\"\n    }\n  ],\n  \"triggerCount\": 1,\n  \"updatedAt\": \"2024-01-15T10:00:00.000Z\",\n  \"versionId\": \"1\"\n} 