# Guia de Uso - N8N Evolution Environment

**Versão do Documento: 1.0**

## 1. Introdução

Bem-vindo ao **N8N Evolution Environment**! Este projeto foi desenhado para automatizar completamente a implantação de um ambiente de desenvolvimento e produção robusto, composto por n8n, Evolution API, Chatwoot, MinIO, PostgreSQL e Redis.

A arquitetura do projeto é baseada em um princípio fundamental: a **separação de responsabilidades**. Temos um sistema que provisiona a **infraestrutura base** (os serviços) e um sistema separado para configurar as **aplicações** (os workflows do n8n) sobre essa infraestrutura. Isso garante que o ambiente seja flexível, escalável e fácil de manter.

---

## 2. Pré-requisitos

Antes de começar, garanta que você tenha os seguintes softwares instalados e em execução:

- **Windows** com **PowerShell 5.1** ou superior.
- **Docker Desktop** instalado, em execução e configurado para usar o backend WSL2 (recomendado).

---

## 3. Fluxo de Uso Completo (Passo a Passo)

O uso do ambiente é dividido em três etapas simples e lógicas. Siga-as nesta ordem.

### Etapa 1: Iniciar a Infraestrutura Base

Esta etapa provisiona todos os contêineres Docker, cria as redes, gera senhas seguras e prepara o terreno para os workflows.

1.  Abra um terminal PowerShell na raiz do projeto.
2.  Execute o seguinte comando:

    ```powershell
    pwsh -File .\Start-Environment.ps1
    ```

3.  **O que acontece:**
    - O script verifica se Docker e Docker Compose estão instalados.
    - Se o arquivo `.env` não existir, ele será criado com senhas fortes e aleatórias para todos os serviços.
    - Todos os serviços definidos no `docker-compose.yml` são iniciados.
    - O script aguarda os serviços críticos (como PostgreSQL e Redis) ficarem saudáveis.
    - Um diagnóstico final é executado.

4.  **Resultado:**
    - Um arquivo `install.log` é gerado com o registro detalhado de toda a operação.
    - Um arquivo **`services-dashboard.html`** é criado e aberto automaticamente no seu navegador. Este dashboard é o seu ponto central para verificar o status dos serviços, acessar suas URLs e visualizar as credenciais.

Neste ponto, a infraestrutura está no ar, mas ainda não está pronta para um workflow específico.

### Etapa 2: Configurar um Workflow Específico (Onboarding)

Esta etapa configura o ambiente para as necessidades de um workflow específico, como o `Enterprise Sales Machine`. Ela aplica os esquemas de banco de dados que o workflow espera.

1.  No mesmo terminal, execute o script `Onboard-Workflow.ps1`, especificando o nome do workflow que deseja configurar.

    ```powershell
    # Exemplo para o workflow de vendas
    pwsh -File .\Onboard-Workflow.ps1 -WorkflowName "Enterprise Sales Machine"
    ```

2.  **O que acontece:**
    - O script localiza o diretório do workflow dentro da pasta `workflows/`.
    - Ele procura por uma subpasta `sql/`. Se encontrada, executa todos os arquivos `.sql` em ordem alfabética no banco de dados `n8n_fila`.
    - Ele procura por uma subpasta `config/`. Se encontrada, exibe guias de configuração (como variáveis de ambiente necessárias) para o usuário.


3.  **Resultado:**
    - O banco de dados agora contém todas as tabelas e esquemas que o workflow `Enterprise Sales Machine` precisa para funcionar.
    - Você recebeu instruções claras sobre quais credenciais e variáveis precisa configurar manualmente na interface do n8n.

### Etapa 3: Executar a Automação Pós-Instalação

Esta etapa finaliza a configuração de integrações específicas, como a conexão entre a Evolution API e o MinIO para armazenamento de arquivos.

1.  Execute o script `post-setup-automation.ps1`:

    ```powershell
    pwsh -File .\post-setup-automation.ps1
    ```

2.  **O que acontece:**
    - O script lê as credenciais do arquivo `.env`.
    - Ele se conecta ao MinIO para criar um bucket (`evolution`) e um par de chaves de acesso específico para a API.
    - Ele cria ou atualiza o arquivo `evolution.yml` com as novas chaves do MinIO.
    - Reinicia o contêiner da Evolution API para que ela carregue as novas configurações do `evolution.yml`.

3.  **Resultado:**
    - A Evolution API está agora configurada para usar o MinIO como seu sistema de armazenamento de arquivos (S3), garantindo que mídias e outros arquivos sejam persistidos corretamente.

**Seu ambiente está agora 100% configurado e pronto para uso!**

---

## 4. Estrutura de um Pacote de Workflow

Para adicionar seus próprios workflows de forma modular, siga esta estrutura de diretórios dentro da pasta `workflows/`:

```
workflows/
└── MeuNovoWorkflow/
    ├── meu_workflow_1.json
    ├── meu_workflow_2.json
    ├── sql/
    │   ├── 01_schemas.sql
    │   └── 02_tables.sql
    └── config/
        └── .env.example
```

- **`MeuNovoWorkflow/`**: O diretório raiz do seu pacote de workflow.
- **`*.json`**: Os arquivos de workflow exportados do n8n.
- **`sql/` (Opcional)**: Contém os scripts SQL necessários. Eles são executados em ordem alfabética. É uma boa prática numerá-los (`01_`, `02_`, etc.) para controlar a ordem de execução.
- **`config/` (Opcional)**: Contém arquivos de guia para o usuário. Um `.env.example` é perfeito para listar as variáveis de ambiente que o workflow espera.

Com esta estrutura, basta executar `pwsh -File .\Onboard-Workflow.ps1 -WorkflowName "MeuNovoWorkflow"` para que o ambiente seja preparado automaticamente.

---

## 5. Solução de Problemas (Troubleshooting)

- **Um serviço não está "saudável" no dashboard:**
  - Verifique os logs do contêiner específico com o comando `docker logs <nome_do_container>`. Por exemplo: `docker logs evolution_aula`.

- **Meu workflow falha com erro de "relação não existe" (relation does not exist):**
  - Isso quase sempre significa que o esquema do banco de dados não foi aplicado. Certifique-se de que você executou a **Etapa 2** (`Onboard-Workflow.ps1`) para o workflow correto.

- **Onde estão os logs da instalação?**
  - O arquivo `install.log`, localizado na raiz do projeto, contém um registro detalhado de cada passo executado pelos scripts. É o primeiro lugar para procurar em caso de erro durante a instalação.

- **Como resetar o ambiente?**
  - Para uma reinicialização limpa, execute os seguintes comandos:
    ```powershell
    docker compose down -v # Remove contêineres e volumes
    Remove-Item .\.env, .\install.log, .\services-dashboard.html -ErrorAction SilentlyContinue
    ```
  - Depois, comece novamente pela Etapa 1.

---

## 6. Visão Geral dos Scripts

- **`Start-Environment.ps1`**: O orquestrador da **infraestrutura**. Sua única função é subir e verificar os serviços base.
- **`Onboard-Workflow.ps1`**: O configurador da **aplicação**. Ele prepara a infraestrutura para as necessidades de um workflow específico.
- **`post-setup-automation.ps1`**: O script de **integração**. Ele finaliza a conexão entre serviços que dependem de configurações dinâmicas (como chaves de API).
- **`PowerShellModules/AutomationUtils.psm1`**: A biblioteca de funções compartilhadas. Contém toda a lógica de log, execução de comandos e verificações de saúde, garantindo consistência e robustez em todo o projeto.