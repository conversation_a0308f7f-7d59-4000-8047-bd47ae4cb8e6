# post-setup-automation.ps1
# Script para automatizar a configuração avançada do ambiente pós-instalação
# Versão: 3.0 - Módulo centralizado e instruções robustas

# =====================================================
# IMPORTAR MÓDULO DE UTILITÁRIOS DE AUTOMAÇÃO
# =====================================================

$utilityModuleName = "AutomationUtils"
# Assume que o módulo está na mesma pasta do script PS, ou em um subdiretório
# Se PowerShellModules estiver no mesmo nível que os scripts PS:
$modulePath = Join-Path $PSScriptRoot ".\PowerShellModules\$utilityModuleName.psm1"
# Se PowerShellModules estiver um diretório acima dos scripts PS:
# $modulePath = Join-Path $PSScriptRoot "..\PowerShellModules\$utilityModuleName.psm1"

if (Test-Path $modulePath) {
    try {
        Import-Module $modulePath -Force -ErrorAction Stop
        if (-not (Get-Command Log-Event -ErrorAction SilentlyContinue)) { # Verificação de fallback robusta
            Write-Error "ERRO FATAL: Módulo '$utilityModuleName' importado, mas a função Log-Event não foi encontrada. O módulo pode estar corrompido. Verifique '$modulePath'."
            exit 1
        }
        Log-Event "Módulo '$utilityModuleName' carregado com sucesso." "ModuleLoader" "SUCCESS"
    } catch {
        Write-Error "ERRO FATAL: Falha crítica ao importar o módulo '$utilityModuleName' de '$modulePath'. As funções utilitárias são essenciais. Verifique a instalação do módulo."
        exit 1
    }
} else {
    Write-Error "ERRO FATAL: Módulo de utilidades do PowerShell não encontrado em '$modulePath'. O projeto não pode continuar sem funções de suporte essenciais."
    exit 1
}

# =====================================================
# VARIÁVEL GLOBAL PARA CONTAR ERROS CRÍTICOS
# =====================================================
if (-not (Get-Variable -Name 'CriticalErrors' -Scope Global -ErrorAction SilentlyContinue)) {
    Set-Variable -Name 'CriticalErrors' -Scope Global -Value 0
}

# =====================================================
# FUNÇÃO DE VALIDAÇÃO DE PRÉ-REQUISITOS
# =====================================================

function Test-Prerequisites {
    param()
    
    Log-Event "Validando pré-requisitos do sistema..." "Prerequisites" "INFO"
    $errors = @()
    
    # Verificar Docker
    if (-not (Get-Command docker -ErrorAction SilentlyContinue)) {
        $errors += "Docker não instalado ou não está no PATH do sistema"
    } else {
        try {
            $dockerVersion = docker --version 2>&1
            Log-Event "Docker encontrado: $dockerVersion" "Prerequisites" "SUCCESS"
        } catch {
            $errors += "Docker instalado mas não responde corretamente"
        }
    }
    
    # Verificar contêineres essenciais rodando
    $requiredContainers = @("postgres_aula", "redis_aula", "minio_aula", "evolution_aula")
    foreach ($container in $requiredContainers) {
        try {
            $status = docker inspect --format "{{.State.Status}}" $container 2>$null
            if ($status -ne "running") {
                $errors += "Contêiner '$container' não está rodando (Status: $status)"
            } else {
                Log-Event "Contêiner '$container' está rodando corretamente" "Prerequisites" "SUCCESS"
            }
        } catch {
            $errors += "Não foi possível verificar status do contêiner '$container'"
        }
    }
    
    # Verificar arquivo .env
    $envPath = Join-Path $PSScriptRoot ".env"
    if (-not (Test-Path $envPath)) {
        $errors += "Arquivo .env não encontrado em '$envPath'"
    } else {
        Log-Event "Arquivo .env encontrado e acessível" "Prerequisites" "SUCCESS"
    }
    
    # Verificar docker-compose.yml
    $composePath = Join-Path $PSScriptRoot "docker-compose.yml"
    if (-not (Test-Path $composePath)) {
        $errors += "Arquivo docker-compose.yml não encontrado em '$composePath'"
    } else {
        Log-Event "Arquivo docker-compose.yml encontrado" "Prerequisites" "SUCCESS"
    }
    
    if ($errors.Count -gt 0) {
        Log-Event "❌ Pré-requisitos não atendidos:" "Prerequisites" "ERROR"
        $errors | ForEach-Object { Log-Event "  • $_" "Prerequisites" "ERROR" }
        return $false
    }
    
    Log-Event "✅ Todos os pré-requisitos validados com sucesso" "Prerequisites" "SUCCESS"
    return $true
}

# =====================================================
# FUNÇÃO DE RETRY PARA CHAMADAS DE API
# =====================================================

function Invoke-RestMethodWithRetry {
    param(
        [string]$Uri,
        [hashtable]$Headers,
        [string]$Method = "GET",
        [string]$Body = $null,
        [int]$MaxRetries = 3,
        [int]$DelaySeconds = 5,
        [string]$Component = "API"
    )
    
    for ($i = 1; $i -le $MaxRetries; $i++) {
        try {
            $params = @{
                Uri = $Uri
                Headers = $Headers
                Method = $Method
                TimeoutSec = 30
                ErrorAction = "Stop"
            }
            if ($Body) { 
                $params.Body = $Body 
                $params["ContentType"] = "application/json"
            }
            
            Log-Event "Tentativa $i/$MaxRetries para $Method $Uri" $Component "DEBUG"
            $response = Invoke-RestMethod @params
            Log-Event "Chamada API bem-sucedida na tentativa $i" $Component "SUCCESS"
            return $response
            
        } catch {
            Log-Event "Tentativa $i/$MaxRetries falhou: $($_.Exception.Message)" $Component "WARN"
            if ($i -eq $MaxRetries) { 
                Log-Event "Todas as tentativas falharam para $Uri" $Component "ERROR"
                throw 
            }
            Log-Event "Aguardando $($DelaySeconds)s antes da próxima tentativa..." $Component "INFO"
            Start-Sleep -Seconds $DelaySeconds
        }
    }
}

# =====================================================
# INICIALIZAÇÃO E VALIDAÇÃO
# =====================================================

Log-Event "Iniciando script de automação pós-instalação..."

# Validar pré-requisitos antes de prosseguir
if (-not (Test-Prerequisites)) {
    Log-Event "Não é possível prosseguir sem atender aos pré-requisitos." "FATAL"
    exit 1
}

# Caminho para o arquivo .env
$envFilePath = Join-Path $PSScriptRoot ".env"

# Verificar se o arquivo .env existe
if (-not (Test-Path $envFilePath)) {
    Log-Event "Arquivo .env não encontrado em '$envFilePath'. Não é possível prosseguir sem credenciais." "ERROR"
    exit 1
}

Log-Event "Carregando credenciais do arquivo .env..."

# Carregar variáveis do .env
$envContent = Get-Content $envFilePath -Raw
$envVars = @{}
$envContent -split "`n" | ForEach-Object {
    $line = $_.Trim()
    if ($line -and -not $line.StartsWith("#")) {
        $parts = $line -split "=", 2
        if ($parts.Length -eq 2) {
            $key = $parts[0].Trim()
            $value = $parts[1].Trim()
            # Remover aspas se existirem
            if ($value.StartsWith('"') -and $value.EndsWith('"')) {
                $value = $value.Substring(1, $value.Length - 2)
            }
            $envVars[$key] = $value
        }
    }
}

# Verificar credenciais críticas
$requiredCreds = @("POSTGRES_PASSWORD", "MINIO_ROOT_USER", "MINIO_ROOT_PASSWORD", "AUTHENTICATION_API_KEY", "SECRET_KEY_BASE", "HOST_IP")
$missingCreds = @()

foreach ($cred in $requiredCreds) {
    if (-not $envVars.ContainsKey($cred) -or [string]::IsNullOrWhiteSpace($envVars[$cred])) {
        $missingCreds += $cred
    }
}

if ($missingCreds.Count -gt 0) {
    Log-Event "Credenciais críticas faltando no .env: $($missingCreds -join ', '). Não é possível prosseguir." "ERROR"
    exit 1
}

Log-Event "Credenciais críticas encontradas e carregadas."

# Atribuir credenciais a variáveis para fácil acesso
$POSTGRES_PASSWORD = $envVars["POSTGRES_PASSWORD"]
$MINIO_ROOT_USER = $envVars["MINIO_ROOT_USER"]
$MINIO_ROOT_PASSWORD = $envVars["MINIO_ROOT_PASSWORD"]
$AUTHENTICATION_API_KEY = $envVars["AUTHENTICATION_API_KEY"]
$SECRET_KEY_BASE = $envVars["SECRET_KEY_BASE"]
$HOST_IP = $envVars["HOST_IP"]

# Definir URLs de serviço (usando host.docker.internal para acesso do contêiner ao host)
$evolutionApiUrl = "http://localhost:8080" # Acesso do script (host) à API
$n8nWebhookListenUrl = "http://host.docker.internal:5678/api/n8n-evolution" # URL que Evolution usará para enviar eventos para n8n
$minioConsoleUrl = "http://localhost:9001" # Acesso do script (host) ao console MinIO
$minioApiUrl = "http://localhost:9000" # Acesso do script (host) à API MinIO

# =====================================================
# ETAPA 0: VERIFICAR E CRIAR DATABASES POSTGRESQL
# =====================================================

Log-Event "Verificando e criando databases PostgreSQL necessárias..." "PostgreSQL-Setup" "INFO"

$requiredDatabases = @("evolution", "n8n_fila", "chatwoot")
$postgresContainer = "postgres_aula"

foreach ($dbName in $requiredDatabases) {
    try {
        Log-Event "Verificando se database '$dbName' existe..." "PostgreSQL-Setup" "INFO"
        
        # Verificar existência via consulta SQL idempotente
        $checkDbCommand = "docker exec $postgresContainer psql -U postgres -tAc 'SELECT 1 FROM pg_database WHERE datname=''$dbName'';'"
        $dbExists = $false

        try {
            $checkOutput = Invoke-Expression $checkDbCommand 2>&1
            if ($checkOutput.Trim() -eq '1') { $dbExists = $true }
        } catch {
            Log-Event "Falha ao verificar existência da database '$dbName': $($_.Exception.Message)" "PostgreSQL-Setup" "ERROR"
        }
        
        if ($dbExists) {
            Log-Event "Database '$dbName' já existe" "PostgreSQL-Setup" "SUCCESS"
        } else {
            Log-Event "Criando database '$dbName'..." "PostgreSQL-Setup" "INFO"
            $createDbCommand = "docker exec $postgresContainer psql -U postgres -c 'CREATE DATABASE $dbName;'"
            
            try {
                Invoke-Expression $createDbCommand 2>&1 | ForEach-Object { 
                    Log-Event $_ "PostgreSQL-Setup" "DEBUG" 
                }
                
                if ($LASTEXITCODE -eq 0) {
                    Log-Event "Database '$dbName' criada com sucesso" "PostgreSQL-Setup" "SUCCESS"
                } else {
                    Log-Event "Possível erro ao criar database '$dbName' (código: $LASTEXITCODE)" "PostgreSQL-Setup" "WARN"
                    $global:CriticalErrors++
                }
            } catch {
                Log-Event "Erro ao executar comando de criação da database '$dbName': $($_.Exception.Message)" "PostgreSQL-Setup" "ERROR"
                $global:CriticalErrors++
            }
        }
        
    } catch {
        Log-Event "Erro ao verificar/criar database '$dbName': $($_.Exception.Message)" "PostgreSQL-Setup" "ERROR"
        $global:CriticalErrors++
    }
}

Log-Event "Verificação/criação de databases PostgreSQL concluída" "PostgreSQL-Setup" "INFO"

# --- Etapa 1: Configuração Evolution API - Criar Nova Instância ---
Log-Event "Iniciando configuração da Evolution API: Criar nova instância..."

$instanceName = "agente_A"
$instanceChannel = "Baileys"
# Gerar token aleatório para a instância
$instanceToken = Generate-SecureKey -Length 32 # Exemplo de comprimento
$instanceNumber = "55119" + (Get-Random -Minimum 10000000 -Maximum 99999999) # Número fictício

$newInstanceBody = @{
    instanceName = $instanceName
    token = $instanceToken
    integration = "WHATSAPP-BAILEYS"
} | ConvertTo-Json

$headers = @{
    "apikey" = $AUTHENTICATION_API_KEY
    "Content-Type" = "application/json"
}

$createInstanceEndpoint = "$evolutionApiUrl/instance/create"

try {
    $createInstanceResponse = Invoke-RestMethodWithRetry -Uri $createInstanceEndpoint -Method POST -Headers $headers -Body $newInstanceBody -Component "Evolution-Instance"
    Log-Event "Instância '$instanceName' criada com sucesso na Evolution API." "Evolution-Instance" "SUCCESS"
    # Aqui você pode querer armazenar o token e número gerados se necessário para etapas futuras
    # Por exemplo: $envVars["AGENTE_A_TOKEN"] = $instanceToken
} catch {
    Log-Event "Erro ao criar instância na Evolution API: $($_.Exception.Message)" "Evolution-Instance" "ERROR"
    if ($_.Exception.Response) {
        Log-Event "Status Code: $($_.Exception.Response.StatusCode.value__)" "Evolution-Instance" "ERROR"
        Log-Event "Response: $($_.Exception.Response)" "Evolution-Instance" "DEBUG"
    }
    Log-Event "A configuração da instância falhou, mas continuaremos com outras configurações." "Evolution-Instance" "WARN"
    $global:CriticalErrors++
}

# --- Etapa 2: Configuração Evolution Manager - Configurar Webhook ---
Log-Event "Iniciando configuração do Webhook na Evolution Manager..."

# Endpoint correto para configurar webhook na instância específica (Evolution API 2.2.3)
$webhookConfigEndpoint = "$evolutionApiUrl/instance/$instanceName/webhook/set"

# Corpo da requisição para configurar o webhook
$webhookBody = @{
    enabled = $true
    url = $n8nWebhookListenUrl
    webhookBase64 = $true
    # Assumindo que há um endpoint para configurar eventos separadamente ou que este endpoint aceita eventos
    # Se não, esta parte pode precisar de uma chamada de API diferente ou configuração manual
    events = @("MESSAGERS_UPSERT") # Exemplo, pode precisar de formato diferente (string separada por vírgula, array de strings, etc.)
} | ConvertTo-Json

# Evolution API 2.2.3 não suporta configuração automática de webhook via API
Log-Event "⚠️ CONFIGURAÇÃO MANUAL NECESSÁRIA - Evolution API 2.2.3" "Evolution-Webhook" "WARN"
Log-Event "A Evolution API versão 2.2.3 não possui endpoints públicos para configuração automática de webhooks." "Evolution-Webhook" "WARN"
Log-Event "📋 INSTRUÇÕES PARA CONFIGURAÇÃO MANUAL:" "Evolution-Webhook" "WARN"
Log-Event "1. Acesse: http://localhost:8080/manager" "Evolution-Webhook" "WARN"
Log-Event "2. Use API Key: $AUTHENTICATION_API_KEY" "Evolution-Webhook" "WARN"
Log-Event "3. Selecione instância: $instanceName" "Evolution-Webhook" "WARN"
Log-Event "4. Configure webhook URL: http://host.docker.internal:5678/api/n8n-evolution" "Evolution-Webhook" "WARN"
Log-Event "5. Habilite eventos: MESSAGES_UPSERT" "Evolution-Webhook" "WARN"
Log-Event "6. Ative Webhook Base64: true" "Evolution-Webhook" "WARN"
Log-Event "📚 Consulte: MANUAL_WEBHOOK_CONFIG.md para instruções detalhadas" "Evolution-Webhook" "WARN"

# --- Etapa 3: Configuração n8n - Configurar Webhook para Escutar ---
Log-Event "Iniciando configuração do n8n: Configurar Webhook para escutar eventos da Evolution API..."

# A configuração de um nó Webhook no n8n via API é complexa e depende da versão do n8n e da API disponível.
# A instrução sugere que a URL http://host.docker.internal:5678 é onde a Evolution API enviará os eventos.
# Isso implica que o n8n deve ter um nó Webhook configurado para escutar nesta URL.
# A automação desta etapa via script não é confiável sem documentação específica da API do n8n.

Log-Event "A configuração do Webhook Listener no n8n é manual. Por favor, siga os passos abaixo:" "n8n-Webhook" "WARN"
Log-Event "1. Acesse a UI do n8n (geralmente em http://localhost:5678)." "n8n-Webhook" "WARN"
Log-Event "2. Crie ou edite um workflow." "n8n-Webhook" "WARN"
Log-Event "3. Adicione um nó 'Webhook'." "n8n-Webhook" "WARN"
Log-Event "4. Configure o nó Webhook para o método HTTP 'POST'." "n8n-Webhook" "WARN"
Log-Event "5. IMPORTANTE: Defina o 'Webhook Path' no nó Webhook do n8n. Exemplo recomendado: '/api/n8n-evolution'" "n8n-Webhook" "WARN"
Log-Event "6. A URL COMPLETA que a Evolution API usará será: http://host.docker.internal:5678/api/n8n-evolution" "n8n-Webhook" "WARN"
Log-Event "7. Ative o workflow para que o Webhook comece a escutar." "n8n-Webhook" "WARN"
Log-Event "8. CRÍTICO: Use EXATAMENTE o mesmo path '/api/n8n-evolution' na configuração da Evolution Manager!" "n8n-Webhook" "WARN"
Log-Event "9. LEMBRE-SE: Substitua '/api/n8n-evolution' pelo path específico que você definir no n8n." "n8n-Webhook" "WARN"

Log-Event "Etapa de configuração do n8n Webhook concluída com aviso. A configuração manual é necessária." "n8n-Webhook" "WARN"

# --- Etapa 4: Configuração MinIO - Criar Bucket e Chaves ---

Log-Event "Iniciando configuração do MinIO: Criar bucket e chaves de acesso..."

$minioBucketName = "evolution"
$minioAccessKeyName = "evolution"
$minioContainerName = "minio_aula" # Nome do contêiner MinIO

try {
    # Verificar se MinIO está rodando
    $minioStatus = docker inspect --format "{{.State.Status}}" $minioContainerName 2>$null
    if ($minioStatus -ne "running") {
        throw "MinIO não está rodando. Status: $minioStatus"
    }
    Log-Event "MinIO está rodando corretamente" "MinIO-Setup" "SUCCESS"

    # Aguardar MinIO estar pronto para receber comandos
    Log-Event "Aguardando MinIO ficar disponível para comandos..." "MinIO-Setup" "INFO"
    if (-not (Wait-For-Port "localhost" 9000 120)) {
        throw "MinIO não ficou disponível na porta 9000 dentro do tempo limite."
    }

    # Configurar alias local para mc client (necessário para comandos administrativos)
    Log-Event "Configurando alias mc para MinIO..." "MinIO-Setup" "INFO"
    $mcAliasCommand = "mc alias set local http://localhost:9000 $MINIO_ROOT_USER $MINIO_ROOT_PASSWORD"
    $aliasResult = docker exec $minioContainerName sh -c "$mcAliasCommand" 2>&1
    
    if ($LASTEXITCODE -eq 0) {
        Log-Event "Alias mc configurado com sucesso" "MinIO-Setup" "SUCCESS"
    } else {
        Log-Event "Aviso ao configurar alias mc: $aliasResult" "MinIO-Setup" "WARN"
    }
    
    # Criar bucket (com --ignore-existing para evitar erro se já existir)
    Log-Event "Criando bucket '$minioBucketName'..." "MinIO-Setup" "INFO"
    $createBucketCommand = "mc mb local/$minioBucketName --ignore-existing"
    $bucketResult = docker exec $minioContainerName sh -c "$createBucketCommand" 2>&1
    
    if ($LASTEXITCODE -eq 0) {
        Log-Event "Bucket '$minioBucketName' criado ou já existe: $bucketResult" "MinIO-Setup" "SUCCESS"
    } else {
        Log-Event "Possível erro ao criar bucket: $bucketResult" "MinIO-Setup" "WARN"
    }
    
    # Definir política pública para o bucket
    Log-Event "Definindo política pública para bucket '$minioBucketName'..." "MinIO-Setup" "INFO"
    $policyCommand = "mc anonymous set public local/$minioBucketName"
    $policyResult = docker exec $minioContainerName sh -c "$policyCommand" 2>&1
    
    if ($LASTEXITCODE -eq 0) {
        Log-Event "Política pública definida com sucesso: $policyResult" "MinIO-Setup" "SUCCESS"
    } else {
        Log-Event "Possível erro ao definir política: $policyResult" "MinIO-Setup" "WARN"
    }

    # Gerar credenciais compatíveis (sem caracteres proibidos) – até 3 tentativas
    $attempt = 0
    do {
        $attempt++
        $newMinioAccessKey = Generate-SecureKey -Length 20
        $newMinioSecretKey = Generate-SecureKey -Length 40
    } while ((($newMinioAccessKey -match '[=, :]') -or ($newMinioSecretKey -match '[=, :]')) -and $attempt -lt 3)

    if ($newMinioAccessKey -match '[=, :]' -or $newMinioSecretKey -match '[=, :]') {
        Log-Event "Não foi possível gerar chaves MinIO compatíveis após $attempt tentativas. Usando credenciais root como fallback." "MinIO-Setup" "WARN"
        $MinioS3AccessKey = $MINIO_ROOT_USER
        $MinioS3SecretKey = $MINIO_ROOT_PASSWORD
    } else {
        Log-Event "Criando novo usuário MinIO para Evolution API (tentativa $attempt)..." "MinIO-Setup" "INFO"
        $addUserCommand = "mc admin user add local $newMinioAccessKey $newMinioSecretKey"
        $userResult = docker exec $minioContainerName sh -c "$addUserCommand" 2>&1

        if ($LASTEXITCODE -eq 0) {
            Log-Event "Usuário MinIO criado com sucesso" "MinIO-Setup" "SUCCESS"
            $MinioS3AccessKey = $newMinioAccessKey
            $MinioS3SecretKey = $newMinioSecretKey
        } else {
            Log-Event "Erro ao criar usuário MinIO: $userResult" "MinIO-Setup" "ERROR"
            Log-Event "Usando credenciais root como fallback para MinIO S3" "MinIO-Setup" "WARN"
            $MinioS3AccessKey = $MINIO_ROOT_USER
            $MinioS3SecretKey = $MINIO_ROOT_PASSWORD
        }
    }

    Log-Event "✅ MinIO configurado com sucesso!" "MinIO-Setup" "SUCCESS"
    
} catch {
    Log-Event "Erro na configuração do MinIO: $($_.Exception.Message)" "MinIO-Setup" "ERROR"
    
    # Capturar logs CORRETAMENTE (CORREÇÃO DO BUG CRÍTICO)
    try {
        Log-Event "Capturando logs do MinIO para diagnóstico..." "MinIO-Setup" "INFO"
        $minioLogs = docker logs $minioContainerName --tail 20 2>&1
        Log-Event "Logs recentes do MinIO:" "MinIO-Setup" "DEBUG"
        $minioLogs | ForEach-Object { Log-Event $_ "MinIO-Setup" "DEBUG" }
    } catch {
        Log-Event "Não foi possível capturar logs do MinIO: $($_.Exception.Message)" "MinIO-Setup" "WARN"
    }
    
    # NÃO abortar - continuar com outras configurações usando credenciais root
    Log-Event "Configuração avançada do MinIO falhou. Usando credenciais root como fallback..." "MinIO-Setup" "WARN"
    $MinioS3AccessKey = $MINIO_ROOT_USER
    $MinioS3SecretKey = $MINIO_ROOT_PASSWORD
    $global:CriticalErrors++
}

# Verificar se as chaves MinIO foram obtidas com sucesso
if ([string]::IsNullOrWhiteSpace($MinioS3AccessKey) -or [string]::IsNullOrWhiteSpace($MinioS3SecretKey)) {
    Log-Event "Erro crítico: Não foi possível obter chaves MinIO válidas. Usando credenciais root." "MinIO-Setup" "ERROR"
    $MinioS3AccessKey = $MINIO_ROOT_USER
    $MinioS3SecretKey = $MINIO_ROOT_PASSWORD
    $global:CriticalErrors++
}

    Log-Event "Chaves MinIO finais - AccessKey: $MinioS3AccessKey ($($MinioS3AccessKey.Length) chars)" "MinIO-Setup" "INFO"

# --- Etapa 5: Persistir Configurações no evolution.yml (Robusto) ---
Log-Event "Iniciando persistência de configurações no evolution.yml..."

$evolutionYmlPath = Join-Path $PSScriptRoot "evolution.yml"

try {
    if (-not (Test-Path $evolutionYmlPath)) {
        Log-Event "Arquivo evolution.yml não encontrado em '$evolutionYmlPath'. Criando arquivo com contexto S3 mínimo." "WARN"
        # Create file with minimum content
        $evolutionYmlContent = @"
# Configurações da Evolution API
# Contexto S3 para integração com MinIO
s3:
  enabled: true
  endpoint: http://minio:9000 # Endpoint acessível de dentro do Docker
  bucket: evolution
  accessKey: $MinioS3AccessKey
  secretKey: $MinioS3SecretKey
  forcePathStyle: true
"@
        $evolutionYmlContent | Out-File $evolutionYmlPath -Encoding UTF8
        Log-Event "Arquivo evolution.yml criado com sucesso com credenciais S3." "SUCCESS"
    } else {
        Log-Event "Arquivo evolution.yml encontrado. Atualizando credenciais S3 de forma robusta..." "INFO"
        $ymlLines = Get-Content $evolutionYmlPath
        $updatedLines = @()
        $inS3Block = $false
        $accessKeyUpdated = $false
        $secretKeyUpdated = $false
        $s3BlockIndentation = -1

        for ($i = 0; $i -lt $ymlLines.Length; $i++) {
            $line = $ymlLines[$i]
            $trimmedLine = $line.TrimStart()
            $leadingSpaces = $line.Length - $trimmedLine.Length

            # Check for s3: block
            if ($trimmedLine -match "^s3:$") {
                $inS3Block = $true
                $s3BlockIndentation = $leadingSpaces
                $updatedLines += $line
                continue
            }

            # Check if leaving s3: block
            if ($inS3Block -and $leadingSpaces -le $s3BlockIndentation -and $trimmedLine.Length -gt 0) {
                $inS3Block = $false
            }

            # Update accessKey and secretKey within s3 block
            if ($inS3Block) {
                if ($trimmedLine -match "^accessKey:") {
                    $updatedLines += (" " * $leadingSpaces) + "accessKey: $MinioS3AccessKey"
                    $accessKeyUpdated = $true
                    continue
                }
                if ($trimmedLine -match "^secretKey:") {
                    $updatedLines += (" " * $leadingSpaces) + "secretKey: $MinioS3SecretKey"
                    $secretKeyUpdated = $true
                    continue
                }
            }

            # Add original line if not replaced
            $updatedLines += $line
        }

        # If s3 block or keys were not found/updated, add the s3 block at the end
        if (-not $accessKeyUpdated -or -not $secretKeyUpdated) {
             Log-Event "Bloco S3 ou chaves não encontrados/atualizados. Adicionando bloco S3 ao final do evolution.yml." "WARN"
             $updatedLines += "" # Add a newline for separation
             $updatedLines += "s3:"
             $updatedLines += "  enabled: true"
             $updatedLines += "  endpoint: http://minio:9000"
             $updatedLines += "  bucket: evolution"
             $updatedLines += "  accessKey: $MinioS3AccessKey"
             $updatedLines += "  secretKey: $MinioS3SecretKey"
             $updatedLines += "  forcePathStyle: true"
        }

        $updatedLines | Out-File $evolutionYmlPath -Encoding UTF8
        Log-Event "Credenciais S3 no evolution.yml atualizadas com sucesso de forma robusta." "SUCCESS"
    }
} catch {
    Log-Event "Erro durante a manipulação do arquivo evolution.yml: $($_.Exception.Message)" "ERROR"
    Log-Event "Continuando sem atualizar evolution.yml..." "WARN"
    $global:CriticalErrors++
}

# --- Etapa 6: Reiniciar Contêiner evolution_aula ---
Log-Event "Iniciando reinício do contêiner evolution_aula para aplicar configurações..."

$evolutionContainerName = "evolution_aula" # Nome do contêiner Evolution (verificar no docker-compose.yml)

try {
    Log-Event "Executando comando docker restart $evolutionContainerName..." "Evolution-Restart" "INFO"
    $restartResult = Invoke-Expression "docker restart $evolutionContainerName" 2>&1

    if ($LASTEXITCODE -eq 0) {
        Log-Event "Contêiner '$evolutionContainerName' reiniciado com sucesso." "Evolution-Restart" "SUCCESS"
        
        # Aguardar o contêiner estar saudável novamente usando função do módulo
        Log-Event "Aguardando o contêiner '$evolutionContainerName' ficar saudável..." "Evolution-Restart" "INFO"
        if (-not (Wait-For-Service-Healthy $evolutionContainerName 180)) {
            Log-Event "AVISO: Contêiner '$evolutionContainerName' não ficou saudável após reinício." "Evolution-Restart" "WARN"
            Log-Event "O contêiner pode estar funcionando mesmo sem health-check definido." "Evolution-Restart" "INFO"
        } else {
            Log-Event "Contêiner '$evolutionContainerName' está saudável após reinício." "Evolution-Restart" "SUCCESS"
        }
        
    } else {
        Log-Event "Erro ao reiniciar contêiner '$evolutionContainerName': $($restartResult | Out-String)" "Evolution-Restart" "ERROR"
        Log-Event "Exit Code: $LASTEXITCODE" "Evolution-Restart" "ERROR"
        Log-Event "Continuando apesar do erro de reinício..." "Evolution-Restart" "WARN"
        $global:CriticalErrors++
    }
} catch {
    Log-Event "Exceção ao executar comando docker restart: $($_.Exception.Message)" "Evolution-Restart" "ERROR"
    Log-Event "Continuando apesar do erro de reinício..." "Evolution-Restart" "WARN"
    $global:CriticalErrors++
}

# --- Etapa 7: Validação Final dos Serviços ---
Log-Event "Executando validação final de todos os serviços..." "Final-Validation" "INFO"

$services = @("postgres_aula", "redis_aula", "minio_aula", "evolution_aula", "n8n_editor-1", "chatwoot-rails-1", "chatwoot-sidekiq-1")
$healthyServices = 0
$totalServices = $services.Count

foreach ($service in $services) {
    try {
        $status = docker inspect --format "{{.State.Status}}" $service 2>$null
        $health = docker inspect --format "{{.State.Health.Status}}" $service 2>$null
        
        if ($status -eq "running") {
            if ($health -eq "healthy" -or [string]::IsNullOrEmpty($health)) {
                Log-Event "✅ $service : Status=$status, Health=$health" "Final-Validation" "SUCCESS"
                $healthyServices++
            } else {
                Log-Event "⚠️ $service : Status=$status, Health=$health" "Final-Validation" "WARN"
            }
        } else {
            Log-Event "❌ $service : Status=$status, Health=$health" "Final-Validation" "ERROR"
        }
    } catch {
        Log-Event "❌ $service : Erro ao verificar status" "Final-Validation" "ERROR"
    }
}

Log-Event "Validação final concluída: $healthyServices/$totalServices serviços funcionais" "Final-Validation" "INFO"

# Teste de conectividade da Evolution API
try {
    Log-Event "Testando conectividade da Evolution API..." "API-Test" "INFO"
    $testHeaders = @{ "X-API-Key" = $AUTHENTICATION_API_KEY }
    $testResponse = Invoke-RestMethodWithRetry -Uri "http://localhost:8080" -Headers $testHeaders -MaxRetries 2 -Component "API-Test"
    Log-Event "✅ Evolution API respondendo corretamente" "API-Test" "SUCCESS"
} catch {
    Log-Event "⚠️ Evolution API não respondeu ao teste: $($_.Exception.Message)" "API-Test" "WARN"
    $global:CriticalErrors++
}

# --- Etapa: Verificação Avançada do QR Code da Evolution API ---
Log-Event "Executando verificação avançada do QR Code da Evolution API..." "QRCode-PostSetup" "INFO"

# Verificar se as novas funções do módulo estão disponíveis
if (Get-Command -Name "Test-EvolutionQRCodeReadiness" -ErrorAction SilentlyContinue) {
    try {
        Log-Event "Iniciando diagnóstico completo do QR Code..." "QRCode-PostSetup" "INFO"
        
        # Executar teste de prontidão do QR Code
        $qrCodeReady = Test-EvolutionQRCodeReadiness -EvolutionContainer "evolution_aula" -ApiKey $AUTHENTICATION_API_KEY
        
        if (-not $qrCodeReady) {
            Log-Event "⚠️ Problemas detectados no QR Code da Evolution API. Iniciando reparo automático..." "QRCode-PostSetup" "WARN"
            
            # Executar reparo automático
            $repairResult = Repair-EvolutionQRCode -EvolutionContainer "evolution_aula" -ApiKey $AUTHENTICATION_API_KEY
            
            if ($repairResult) {
                Log-Event "✅ Reparo automático do QR Code concluído com sucesso!" "QRCode-PostSetup" "SUCCESS"
            } else {
                Log-Event "❌ Reparo automático do QR Code falhou. Intervenção manual necessária." "QRCode-PostSetup" "ERROR"
                Log-Event "📋 RECOMENDAÇÃO: Execute 'docker-compose down && docker-compose up -d' para recriar contêineres com CONFIG_SESSION_PHONE_VERSION" "QRCode-PostSetup" "WARN"
                $global:CriticalErrors++
            }
        } else {
            Log-Event "✅ QR Code da Evolution API está funcionando corretamente!" "QRCode-PostSetup" "SUCCESS"
        }
        
        # Verificar se a variável CONFIG_SESSION_PHONE_VERSION está definida
        try {
            $phoneVersionCheck = docker exec evolution_aula env | Select-String -Pattern "CONFIG_SESSION_PHONE_VERSION" 2>$null
            if ($phoneVersionCheck) {
                Log-Event "✅ CONFIG_SESSION_PHONE_VERSION está definida: $phoneVersionCheck" "QRCode-PostSetup" "SUCCESS"
            } else {
                Log-Event "❌ CONFIG_SESSION_PHONE_VERSION não está definida - necessário recriar contêineres" "QRCode-PostSetup" "ERROR"
                Log-Event "📋 AÇÃO NECESSÁRIA: Execute 'docker-compose down && docker-compose up -d' para aplicar novas variáveis de ambiente" "QRCode-PostSetup" "WARN"
                $global:CriticalErrors++
            }
        } catch {
            Log-Event "⚠️ Erro ao verificar CONFIG_SESSION_PHONE_VERSION: $($_.Exception.Message)" "QRCode-PostSetup" "WARN"
        }
        
    } catch {
        Log-Event "❌ Erro durante diagnóstico do QR Code: $($_.Exception.Message)" "QRCode-PostSetup" "ERROR"
        $global:CriticalErrors++
    }
} else {
    Log-Event "⚠️ Funções de diagnóstico do QR Code não disponíveis. Módulo pode não estar carregado corretamente." "QRCode-PostSetup" "WARN"
}

# --- Etapa Final: Abrir services-dashboard.html ---
Log-Event "Abrindo services-dashboard.html no navegador..."

$dashboardHtmlPath = Join-Path $PSScriptRoot "services-dashboard.html"

if (Test-Path $dashboardHtmlPath) {
    try {
        # Usar Start-Process para abrir o arquivo no navegador padrão
        Start-Process $dashboardHtmlPath
        Log-Event "Arquivo '$dashboardHtmlPath' aberto no navegador."
    } catch {
        Log-Event "Erro ao abrir arquivo '$dashboardHtmlPath': $($_.Exception.Message)" "ERROR"
    }
} else {
    Log-Event "Arquivo services-dashboard.html não encontrado em '$dashboardHtmlPath'. Não é possível abrir." "WARN"
}

# =====================================================
# SEÇÃO: PERSISTIR CHAVE API NO DASHBOARD (Credenciais Importantes)
# =====================================================
try {
    $dashboardFile = Join-Path $PSScriptRoot "services-dashboard.html"
    if (Test-Path $dashboardFile) {
        $apiKey = $envVars["AUTHENTICATION_API_KEY"]
        if (-not [string]::IsNullOrWhiteSpace($apiKey)) {
            $marker = "<!-- CREDENCIAIS IMPORTANTES -->"
            $htmlLines = Get-Content $dashboardFile
            if ($htmlLines -notcontains $marker) {
                # Inserir bloco logo após a tag <body>
                $insertIndex = ($htmlLines | Select-String -Pattern "<body" -SimpleMatch).LineNumber
                if ($insertIndex) {
                    $pre  = $htmlLines[0..($insertIndex)]
                    $post = $htmlLines[($insertIndex+1)..($htmlLines.Length-1)]
                    $block = @(
                        $marker,
                        "<div style='background:#FFF4BF;border:2px solid #F59E0B;padding:12px;border-radius:8px;margin:16px 0;'>",
                        "  <strong>Credenciais Importantes</strong><br/>",
                        "  Evolution API Key: <code>$apiKey</code><br/>",
                        "  <em>Apply to .env</em>: Copie esta chave para a variável <code>AUTHENTICATION_API_KEY</code> se precisar redefinir as credenciais.<br/>",
                        "  Documentação completa: <a href='docs/MANUAL_DE_USO.md#credenciais' target='_blank'>MANUAL_DE_USO.md</a>",
                        "</div>"
                    )
                    ($pre + $block + $post) | Out-File $dashboardFile -Encoding UTF8
                    Log-Event "Bloco de credenciais importantes adicionado ao dashboard." "Dashboard" "SUCCESS"
                }
            }
        }
    }
} catch {
    Log-Event "Falha ao inserir chave API no dashboard: $($_.Exception.Message)" "Dashboard" "WARN"
}

# --- Resumo Final ---
Log-Event "=== RESUMO DA EXECUÇÃO ===" "Summary" "INFO"
Log-Event "✅ Pré-requisitos validados" "Summary" "SUCCESS"
Log-Event "✅ Databases PostgreSQL verificadas/criadas" "Summary" "SUCCESS"
Log-Event "⚠️ Evolution API: Configuração manual pode ser necessária" "Summary" "WARN"
Log-Event "⚠️ N8N Webhook: Configuração manual necessária" "Summary" "WARN"
Log-Event "✅ MinIO: Configurado com credenciais válidas" "Summary" "SUCCESS"
Log-Event "✅ Evolution.yml: Atualizado com configurações S3" "Summary" "SUCCESS"
Log-Event "⚠️ Evolution Container: Reiniciado (verificar saúde)" "Summary" "WARN"
Log-Event "✅ Validação final: $healthyServices/$totalServices serviços funcionais" "Summary" "SUCCESS"
Log-Event "✅ QR Code da Evolution API: Diagnóstico e reparo automático executados" "Summary" "SUCCESS"

Log-Event "Script de automação pós-instalação concluído com melhorias robustas."
Log-Event "Consulte o arquivo 'DIAGNOSTICO_COMPLETO_INSTALL_LOG.md' para detalhes completos e próximos passos."

# ----------------- SAÍDA PADRÃO -----------------
if ($global:CriticalErrors -gt 0) {
    Log-Event "Execução concluída com $global:CriticalErrors erro(s) crítico(s). Código de saída 1." "PostSetup" "ERROR"
    exit 1
} else {
    Log-Event "Script pós-instalação concluído sem erros críticos." "PostSetup" "SUCCESS"
    exit 0
}