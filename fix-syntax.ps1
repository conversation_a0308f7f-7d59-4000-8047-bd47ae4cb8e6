# Script para corrigir problemas de sintaxe no Start-Environment.ps1

$scriptPath = "Start-Environment.ps1"
$content = Get-Content $scriptPath -Raw

# Contar chaves de abertura e fechamento
$openBraces = ($content | Select-String -Pattern '\{' -AllMatches).Matches.Count
$closeBraces = ($content | Select-String -Pattern '\}' -AllMatches).Matches.Count

Write-Host "Análise de chaves:"
Write-Host "Chaves de abertura { : $openBraces"
Write-Host "Chaves de fechamento } : $closeBraces"
Write-Host "Diferença: $($openBraces - $closeBraces)"

# Identificar blocos if/else/try/catch não fechados
$lines = Get-Content $scriptPath
$openBlocks = @()
$lineNumber = 0

foreach ($line in $lines) {
    $lineNumber++
    
    # Contar chaves de abertura na linha
    $openCount = ($line | Select-String -Pattern '\{' -AllMatches).Matches.Count
    $closeCount = ($line | Select-String -Pattern '\}' -AllMatches).Matches.Count
    
    # Se há mais aberturas que fechamentos, adicionar à lista
    if ($openCount -gt $closeCount) {
        for ($i = 0; $i -lt ($openCount - $closeCount); $i++) {
            $openBlocks += $lineNumber
        }
    }
    
    # Se há mais fechamentos que aberturas, remover da lista
    if ($closeCount -gt $openCount) {
        for ($i = 0; $i -lt ($closeCount - $openCount); $i++) {
            if ($openBlocks.Count -gt 0) {
                $openBlocks = $openBlocks[0..($openBlocks.Count-2)]
            }
        }
    }
}

Write-Host "`nBlocos não fechados detectados nas linhas:"
$openBlocks | ForEach-Object {
    Write-Host "Linha ${_}: $($lines[$_-1].Trim())"
}

Write-Host "`nTotal de blocos não fechados: $($openBlocks.Count)"

# Corrigir automaticamente adicionando chaves de fechamento
if ($openBlocks.Count -gt 0) {
    Write-Host "`nCorrigindo automaticamente..."
    
    # Ler o conteúdo atual
    $currentContent = Get-Content $scriptPath -Raw
    
    # Adicionar as chaves de fechamento necessárias antes da última linha
    $closingBraces = "`n" + ("}" * $openBlocks.Count) + "`n"
    
    # Encontrar a posição da última linha significativa
    $lastLogEvent = $currentContent.LastIndexOf('Log-Event "OK - Script finalizado com sucesso!"')
    
    if ($lastLogEvent -gt 0) {
        # Inserir as chaves antes da última linha
        $beforeLastLine = $currentContent.Substring(0, $lastLogEvent)
        $lastLine = $currentContent.Substring($lastLogEvent)
        
        $correctedContent = $beforeLastLine + $closingBraces + $lastLine
        
        # Salvar o arquivo corrigido
        $correctedContent | Out-File -FilePath $scriptPath -Encoding UTF8 -NoNewline
        
        Write-Host "✅ Arquivo corrigido! Adicionadas $($openBlocks.Count) chaves de fechamento."
    } else {
        Write-Host "❌ Não foi possível localizar a posição para inserir as correções."
    }
} else {
    Write-Host "✅ Nenhuma correção necessária."
}

Write-Host "`nAnálise concluída."