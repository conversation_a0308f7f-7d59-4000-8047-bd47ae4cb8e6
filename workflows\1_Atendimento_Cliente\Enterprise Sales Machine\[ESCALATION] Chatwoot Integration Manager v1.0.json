{"name": "[ESCALATION] Chatwoot Integration Manager v1.0", "nodes": [{"parameters": {"path": "escalation-chatwoot-sync", "httpMethod": "POST", "responseMode": "responseNode", "options": {"rawBody": true, "allowedOrigins": "*"}}, "id": "webhook-trigger", "name": "🚀 Trigger: Chatwoot Sync Request", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300], "webhookId": "escalation-chatwoot-sync"}, {"parameters": {"jsCode": "// =====================================================\n// CHATWOOT INTEGRATION - REQUEST VALIDATOR\n// =====================================================\n\nconst Joi = require('joi');\nconst crypto = require('crypto');\n\n// Schema de validação para integração Chatwoot\nconst chatwootSyncSchema = Joi.object({\n  escalation_id: Joi.number().integer().positive().required(),\n  action: Joi.string().valid('create_conversation', 'update_status', 'transfer_agent', 'close_conversation').required(),\n  contact_id: Joi.number().integer().positive().required(),\n  chatwoot_conversation_id: Joi.number().integer().positive().optional(),\n  assigned_agent_id: Joi.number().integer().positive().optional(),\n  inbox_id: Joi.number().integer().positive().optional(),\n  priority: Joi.string().valid('low', 'medium', 'high', 'urgent').default('medium'),\n  specialization: Joi.string().valid('technical', 'billing', 'sales', 'support', 'compliance', 'legal', 'product', 'general').optional(),\n  context_data: Joi.object().required(),\n  conversation_history: Joi.array().items(Joi.object()).max(100).optional(),\n  escalation_reason: Joi.string().min(10).max(1000).required(),\n  ai_analysis_score: Joi.number().min(0).max(100).optional(),\n  metadata: Joi.object().optional()\n});\n\nfunction generateCorrelationId() {\n  return crypto.randomUUID();\n}\n\nconst startTime = Date.now();\nconst correlationId = generateCorrelationId();\n\ntry {\n  const requestBody = $input.first().json.body;\n  const headers = $input.first().json.headers;\n  \n  console.log(`[${correlationId}] Chatwoot sync request received`, {\n    timestamp: new Date().toISOString(),\n    action: requestBody.action,\n    escalationId: requestBody.escalation_id\n  });\n  \n  const { error, value: validatedData } = chatwootSyncSchema.validate(requestBody, {\n    abortEarly: false,\n    stripUnknown: true,\n    convert: true\n  });\n  \n  if (error) {\n    const validationErrors = error.details.map(detail => ({\n      field: detail.path.join('.'),\n      message: detail.message,\n      value: detail.context?.value\n    }));\n    \n    return [{\n      json: {\n        success: false,\n        error: 'VALIDATION_ERROR',\n        message: 'Chatwoot sync validation failed',\n        details: validationErrors,\n        correlation_id: correlationId,\n        timestamp: new Date().toISOString()\n      }\n    }];\n  }\n  \n  const processingTime = Date.now() - startTime;\n  \n  const outputData = {\n    ...validatedData,\n    correlation_id: correlationId,\n    validation_time_ms: processingTime,\n    received_at: new Date().toISOString(),\n    user_agent: headers['user-agent'],\n    ip_address: headers['x-forwarded-for'] || headers['x-real-ip'] || 'unknown',\n    validation_status: 'success',\n    next_step: 'chatwoot_action'\n  };\n  \n  console.log(`[${correlationId}] Chatwoot validation successful`, {\n    processingTime,\n    action: validatedData.action,\n    escalationId: validatedData.escalation_id\n  });\n  \n  return [{ json: outputData }];\n  \n} catch (error) {\n  console.error(`[${correlationId}] Chatwoot validation error:`, error);\n  \n  return [{\n    json: {\n      success: false,\n      error: 'INTERNAL_ERROR',\n      message: 'Internal Chatwoot validation error',\n      correlation_id: correlationId,\n      timestamp: new Date().toISOString(),\n      details: {\n        error: error.message,\n        stack: error.stack\n      }\n    }\n  }];\n}"}, "id": "request-validator", "name": "✅ Validator: Chatwoot Request", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [460, 300]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "validation-success", "leftValue": "={{ $json.validation_status }}", "rightValue": "success", "operator": {"type": "string", "operation": "equals"}}], "combinator": "and"}, "options": {}}, "id": "validation-check", "name": "🔍 Check: Validation Status", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [680, 300]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT e.*, ha.chatwoot_agent_id, hq.chatwoot_inbox_id FROM agent.intelligent_escalations e LEFT JOIN agent.human_agents ha ON e.assigned_human = ha.agent_name LEFT JOIN agent.human_queues hq ON e.assigned_queue = hq.queue_name WHERE e.id = $1", "options": {"queryParameters": "={{ [$json.escalation_id] }}"}}, "id": "escalation-lookup", "name": "🔍 Query: Escalation Details", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [900, 200], "credentials": {"postgres": {"id": "postgres-main", "name": "PostgreSQL - Main Database"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "action-create", "leftValue": "={{ $json.action }}", "rightValue": "create_conversation", "operator": {"type": "string", "operation": "equals"}}], "combinator": "and"}, "options": {}}, "id": "action-router", "name": "🔀 Router: Action Type", "type": "n8n-nodes-base.switch", "typeVersion": 3, "position": [1120, 300]}, {"parameters": {"jsCode": "// =====================================================\n// CHATWOOT - CREATE CONVERSATION\n// =====================================================\n\nconst escalationData = $input.first().json;\nconst correlationId = escalationData.correlation_id;\n\ntry {\n  // Mapear urgência para prioridade Chatwoot\n  function mapUrgencyToPriority(urgency) {\n    const mapping = {\n      'low': 'low',\n      'medium': 'medium',\n      'high': 'high',\n      'critical': 'urgent'\n    };\n    return mapping[urgency] || 'medium';\n  }\n  \n  // Determinar inbox baseado na especialização\n  function getInboxBySpecialization(specialization, defaultInboxId) {\n    const inboxMapping = {\n      'technical': process.env.CHATWOOT_TECHNICAL_INBOX_ID || defaultInboxId,\n      'billing': process.env.CHATWOOT_BILLING_INBOX_ID || defaultInboxId,\n      'sales': process.env.CHATWOOT_SALES_INBOX_ID || defaultInboxId,\n      'support': process.env.CHATWOOT_SUPPORT_INBOX_ID || defaultInboxId,\n      'compliance': process.env.CHATWOOT_COMPLIANCE_INBOX_ID || defaultInboxId,\n      'legal': process.env.CHATWOOT_LEGAL_INBOX_ID || defaultInboxId,\n      'product': process.env.CHATWOOT_PRODUCT_INBOX_ID || defaultInboxId\n    };\n    return parseInt(inboxMapping[specialization] || defaultInboxId);\n  }\n  \n  // Preparar payload para Chatwoot\n  const chatwootPayload = {\n    source_id: escalationData.contact_id.toString(),\n    inbox_id: getInboxBySpecialization(\n      escalationData.specialization || escalationData.specialization_required,\n      escalationData.inbox_id || process.env.CHATWOOT_DEFAULT_INBOX_ID\n    ),\n    contact_id: escalationData.contact_id,\n    assignee_id: escalationData.chatwoot_agent_id || escalationData.assigned_agent_id,\n    priority: mapUrgencyToPriority(escalationData.urgency_level || escalationData.priority),\n    status: 'open',\n    custom_attributes: {\n      escalation_id: escalationData.escalation_id,\n      ai_analysis_score: escalationData.ai_analysis_score,\n      escalation_reason: escalationData.escalation_reason,\n      correlation_id: correlationId,\n      escalation_type: 'intelligent_human',\n      created_by_system: 'n8n_escalation_agent'\n    },\n    additional_attributes: {\n      context_data: JSON.stringify(escalationData.context_data),\n      conversation_history_length: escalationData.conversation_history?.length || 0,\n      specialization_hint: escalationData.specialization || 'general'\n    }\n  };\n  \n  // Preparar mensagem inicial\n  const initialMessage = {\n    content: `🚨 **Escalação Inteligente Ativada**\\n\\n` +\n             `**Motivo:** ${escalationData.escalation_reason}\\n` +\n             `**Urgência:** ${escalationData.urgency_level?.toUpperCase() || 'MEDIUM'}\\n` +\n             `**Especialização:** ${escalationData.specialization || 'Geral'}\\n` +\n             `**Score IA:** ${escalationData.ai_analysis_score || 'N/A'}/100\\n\\n` +\n             `**ID de Correlação:** ${correlationId}\\n` +\n             `**Timestamp:** ${new Date().toISOString()}\\n\\n` +\n             `*Esta conversa foi criada automaticamente pelo sistema de escalação inteligente.*`,\n    message_type: 'outgoing',\n    private: false,\n    content_type: 'text'\n  };\n  \n  console.log(`[${correlationId}] Creating Chatwoot conversation`, {\n    inboxId: chatwootPayload.inbox_id,\n    contactId: chatwootPayload.contact_id,\n    priority: chatwootPayload.priority,\n    assigneeId: chatwootPayload.assignee_id\n  });\n  \n  return [{\n    json: {\n      ...escalationData,\n      chatwoot_payload: chatwootPayload,\n      initial_message: initialMessage,\n      action_type: 'create_conversation',\n      processing_status: 'ready_for_api_call'\n    }\n  }];\n  \n} catch (error) {\n  console.error(`[${correlationId}] Error preparing Chatwoot conversation:`, error);\n  \n  return [{\n    json: {\n      ...escalationData,\n      error: 'CHATWOOT_PREPARATION_ERROR',\n      message: 'Failed to prepare Chatwoot conversation data',\n      details: {\n        error: error.message,\n        stack: error.stack\n      },\n      processing_status: 'error'\n    }\n  }];\n}"}, "id": "create-conversation-prep", "name": "📝 Prepare: Chatwoot Conversation", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1340, 200]}, {"parameters": {"url": "={{ process.env.CHATWOOT_BASE_URL }}/api/v1/accounts/{{ process.env.CHATWOOT_ACCOUNT_ID }}/conversations", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendBody": true, "specifyBody": "json", "jsonBody": "={{ $json.chatwoot_payload }}", "options": {"timeout": 30000, "retry": {"enabled": true, "maxTries": 3}}}, "id": "chatwoot-create-api", "name": "🔗 API: Create Chatwoot Conversation", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1560, 200], "credentials": {"httpHeaderAuth": {"id": "chatwoot-api", "name": "Chatwoot API Token"}}}, {"parameters": {"url": "={{ process.env.CHATWOOT_BASE_URL }}/api/v1/accounts/{{ process.env.CHATWOOT_ACCOUNT_ID }}/conversations/{{ $json.id }}/messages", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendBody": true, "specifyBody": "json", "jsonBody": "={{ $('create-conversation-prep').first().json.initial_message }}", "options": {"timeout": 30000}}, "id": "chatwoot-message-api", "name": "💬 API: Send Initial Message", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1780, 200], "credentials": {"httpHeaderAuth": {"id": "chatwoot-api", "name": "Chatwoot API Token"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "UPDATE agent.intelligent_escalations SET chatwoot_conversation_id = $1, status = 'chatwoot_created', updated_at = NOW(), updated_by = 'chatwoot_integration' WHERE id = $2", "options": {"queryParameters": "={{ [$json.id, $('create-conversation-prep').first().json.escalation_id] }}"}}, "id": "update-escalation", "name": "💾 Update: Escalation Status", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [2000, 200], "credentials": {"postgres": {"id": "postgres-main", "name": "PostgreSQL - Main Database"}}}, {"parameters": {"respondWith": "json", "responseBody": "={{ {\n  success: true,\n  message: 'Chatwoot conversation created successfully',\n  data: {\n    escalation_id: $('create-conversation-prep').first().json.escalation_id,\n    chatwoot_conversation_id: $json.id,\n    correlation_id: $('create-conversation-prep').first().json.correlation_id,\n    status: 'chatwoot_created',\n    created_at: new Date().toISOString()\n  }\n} }}"}, "id": "success-response", "name": "✅ Response: Success", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [2220, 200]}, {"parameters": {"respondWith": "json", "responseBody": "={{ $json }}", "options": {"responseCode": 400}}, "id": "error-response", "name": "❌ Response: Error", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [680, 500]}], "connections": {"webhook-trigger": {"main": [[{"node": "request-validator", "type": "main", "index": 0}]]}, "request-validator": {"main": [[{"node": "validation-check", "type": "main", "index": 0}]]}, "validation-check": {"main": [[{"node": "escalation-lookup", "type": "main", "index": 0}], [{"node": "error-response", "type": "main", "index": 0}]]}, "escalation-lookup": {"main": [[{"node": "action-router", "type": "main", "index": 0}]]}, "action-router": {"main": [[{"node": "create-conversation-prep", "type": "main", "index": 0}]]}, "create-conversation-prep": {"main": [[{"node": "chatwoot-create-api", "type": "main", "index": 0}]]}, "chatwoot-create-api": {"main": [[{"node": "chatwoot-message-api", "type": "main", "index": 0}]]}, "chatwoot-message-api": {"main": [[{"node": "update-escalation", "type": "main", "index": 0}]]}, "update-escalation": {"main": [[{"node": "success-response", "type": "main", "index": 0}]]}}, "pinData": {}, "settings": {"executionOrder": "v1"}, "staticData": null, "tags": [{"createdAt": "2024-01-15T10:00:00.000Z", "updatedAt": "2024-01-15T10:00:00.000Z", "id": "escalation", "name": "escalation"}, {"createdAt": "2024-01-15T10:00:00.000Z", "updatedAt": "2024-01-15T10:00:00.000Z", "id": "chatwoot", "name": "chatwoot"}, {"createdAt": "2024-01-15T10:00:00.000Z", "updatedAt": "2024-01-15T10:00:00.000Z", "id": "integration", "name": "integration"}], "triggerCount": 0, "updatedAt": "2024-01-15T10:00:00.000Z", "versionId": "1"}