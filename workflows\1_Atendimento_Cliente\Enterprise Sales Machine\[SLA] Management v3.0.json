{"name": "[SLA] ⏰ Management v3.0", "nodes": [{"parameters": {"httpMethod": "POST", "path": "sla-management", "options": {}}, "id": "sla-webhook-trigger", "name": "🔗 SLA Management Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300], "webhookId": "sla-management"}, {"parameters": {"jsCode": "// Enhanced SLA Processing\nconst slaData = $input.first().json;\n\n// Validate SLA data\nif (!slaData || !slaData.escalation_id) {\n  return [{ json: { error: 'Invalid SLA data - missing escalation_id', processed: false } }];\n}\n\n// Enhanced SLA calculation and monitoring\nconst processSLAData = (data) => {\n  const currentTime = new Date();\n  \n  // Default SLA configurations\n  const slaConfigs = {\n    critical: {\n      first_response_minutes: 15,\n      resolution_hours: 4,\n      escalation_levels: [\n        { level: 1, trigger_minutes: 10, action: 'notify_manager' },\n        { level: 2, trigger_minutes: 30, action: 'notify_director' },\n        { level: 3, trigger_minutes: 60, action: 'emergency_escalation' }\n      ]\n    },\n    high: {\n      first_response_minutes: 60,\n      resolution_hours: 24,\n      escalation_levels: [\n        { level: 1, trigger_minutes: 45, action: 'notify_manager' },\n        { level: 2, trigger_hours: 4, action: 'notify_director' },\n        { level: 3, trigger_hours: 12, action: 'emergency_escalation' }\n      ]\n    },\n    medium: {\n      first_response_minutes: 240,\n      resolution_hours: 72,\n      escalation_levels: [\n        { level: 1, trigger_hours: 3, action: 'notify_manager' },\n        { level: 2, trigger_hours: 24, action: 'notify_director' }\n      ]\n    },\n    low: {\n      first_response_minutes: 480,\n      resolution_hours: 168,\n      escalation_levels: [\n        { level: 1, trigger_hours: 24, action: 'notify_manager' }\n      ]\n    }\n  };\n  \n  // Get priority from data or default to medium\n  const priority = data.priority || 'medium';\n  const slaConfig = slaConfigs[priority] || slaConfigs.medium;\n  \n  // Calculate SLA timestamps\n  const createdAt = new Date(data.created_at || currentTime);\n  const firstResponseDeadline = new Date(createdAt.getTime() + (slaConfig.first_response_minutes * 60 * 1000));\n  const resolutionDeadline = new Date(createdAt.getTime() + (slaConfig.resolution_hours * 60 * 60 * 1000));\n  \n  // Calculate business hours adjustments\n  const adjustForBusinessHours = (deadline) => {\n    const businessStart = 9; // 9 AM\n    const businessEnd = 17; // 5 PM\n    const workDays = [1, 2, 3, 4, 5]; // Monday to Friday\n    \n    let adjustedDeadline = new Date(deadline);\n    \n    // If deadline falls outside business hours, adjust\n    if (!workDays.includes(adjustedDeadline.getDay()) || \n        adjustedDeadline.getHours() < businessStart || \n        adjustedDeadline.getHours() >= businessEnd) {\n      \n      // Move to next business day at business start\n      while (!workDays.includes(adjustedDeadline.getDay())) {\n        adjustedDeadline.setDate(adjustedDeadline.getDate() + 1);\n      }\n      adjustedDeadline.setHours(businessStart, 0, 0, 0);\n    }\n    \n    return adjustedDeadline;\n  };\n  \n  // Apply business hours adjustment for non-critical priorities\n  const adjustedFirstResponse = priority === 'critical' ? firstResponseDeadline : adjustForBusinessHours(firstResponseDeadline);\n  const adjustedResolution = priority === 'critical' ? resolutionDeadline : adjustForBusinessHours(resolutionDeadline);\n  \n  // Calculate time remaining\n  const timeToFirstResponse = Math.max(0, adjustedFirstResponse.getTime() - currentTime.getTime());\n  const timeToResolution = Math.max(0, adjustedResolution.getTime() - currentTime.getTime());\n  \n  // Calculate breach status\n  const isFirstResponseBreached = currentTime > adjustedFirstResponse;\n  const isResolutionBreached = currentTime > adjustedResolution;\n  \n  // Calculate escalation triggers\n  const activeEscalations = [];\n  slaConfig.escalation_levels.forEach(level => {\n    const triggerTime = level.trigger_minutes ? \n      new Date(createdAt.getTime() + (level.trigger_minutes * 60 * 1000)) :\n      new Date(createdAt.getTime() + (level.trigger_hours * 60 * 60 * 1000));\n    \n    if (currentTime >= triggerTime) {\n      activeEscalations.push({\n        ...level,\n        triggered_at: triggerTime,\n        overdue_minutes: Math.floor((currentTime.getTime() - triggerTime.getTime()) / (60 * 1000))\n      });\n    }\n  });\n  \n  // Enhanced SLA metrics\n  const slaMetrics = {\n    escalation_id: data.escalation_id,\n    priority: priority,\n    sla_config: slaConfig,\n    timestamps: {\n      created_at: createdAt.toISOString(),\n      first_response_deadline: adjustedFirstResponse.toISOString(),\n      resolution_deadline: adjustedResolution.toISOString(),\n      current_time: currentTime.toISOString()\n    },\n    time_remaining: {\n      first_response_minutes: Math.floor(timeToFirstResponse / (60 * 1000)),\n      resolution_hours: Math.floor(timeToResolution / (60 * 60 * 1000)),\n      first_response_ms: timeToFirstResponse,\n      resolution_ms: timeToResolution\n    },\n    breach_status: {\n      first_response_breached: isFirstResponseBreached,\n      resolution_breached: isResolutionBreached,\n      any_breach: isFirstResponseBreached || isResolutionBreached,\n      breach_severity: isResolutionBreached ? 'critical' : isFirstResponseBreached ? 'high' : 'none'\n    },\n    escalation_status: {\n      active_escalations: activeEscalations,\n      escalation_count: activeEscalations.length,\n      highest_escalation_level: activeEscalations.length > 0 ? Math.max(...activeEscalations.map(e => e.level)) : 0,\n      requires_immediate_action: activeEscalations.some(e => e.action === 'emergency_escalation')\n    },\n    performance_indicators: {\n      sla_health_score: calculateSLAHealthScore(isFirstResponseBreached, isResolutionBreached, activeEscalations.length),\n      urgency_multiplier: calculateUrgencyMultiplier(priority, isFirstResponseBreached, isResolutionBreached),\n      estimated_resolution_time: estimateResolutionTime(priority, activeEscalations.length, data.complexity || 'medium')\n    }\n  };\n  \n  return slaMetrics;\n};\n\n// Helper function to calculate SLA health score (0-100)\nconst calculateSLAHealthScore = (firstResponseBreached, resolutionBreached, escalationCount) => {\n  let score = 100;\n  \n  if (firstResponseBreached) score -= 30;\n  if (resolutionBreached) score -= 50;\n  score -= (escalationCount * 10);\n  \n  return Math.max(0, score);\n};\n\n// Helper function to calculate urgency multiplier\nconst calculateUrgencyMultiplier = (priority, firstResponseBreached, resolutionBreached) => {\n  const basePriority = { critical: 4, high: 3, medium: 2, low: 1 };\n  let multiplier = basePriority[priority] || 2;\n  \n  if (firstResponseBreached) multiplier *= 1.5;\n  if (resolutionBreached) multiplier *= 2;\n  \n  return Math.min(10, multiplier);\n};\n\n// Helper function to estimate resolution time\nconst estimateResolutionTime = (priority, escalationCount, complexity) => {\n  const baseHours = { critical: 4, high: 24, medium: 72, low: 168 };\n  const complexityMultiplier = { simple: 0.5, medium: 1, complex: 2 };\n  \n  let estimatedHours = baseHours[priority] || 72;\n  estimatedHours *= (complexityMultiplier[complexity] || 1);\n  estimatedHours *= (1 + (escalationCount * 0.2)); // Each escalation adds 20% time\n  \n  return Math.ceil(estimatedHours);\n};\n\n// Process the SLA data\nconst processedSLA = processSLAData(slaData);\n\n// Add processing metadata\nconst enhancedSLAData = {\n  ...processedSLA,\n  processing_metadata: {\n    processed_at: new Date().toISOString(),\n    processing_version: '3.0',\n    data_source: slaData.source || 'escalation_workflow',\n    requires_monitoring: processedSLA.breach_status.any_breach || processedSLA.escalation_status.escalation_count > 0\n  }\n};\n\nreturn [{ json: enhancedSLAData }];"}, "id": "process-sla-data", "name": "⚙️ Process SLA Data", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [460, 300]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "has-breach", "leftValue": "={{ $json.breach_status.any_breach }}", "rightValue": true, "operator": {"type": "boolean", "operation": "equal"}}], "combinator": "and"}, "options": {}}, "id": "check-sla-breach", "name": "🚨 Check SLA Breach", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [680, 300]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "requires-escalation", "leftValue": "={{ $json.escalation_status.escalation_count }}", "rightValue": 0, "operator": {"type": "number", "operation": "gt"}}], "combinator": "and"}, "options": {}}, "id": "check-escalation-required", "name": "⬆️ Check Escalation Required", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [900, 200]}, {"parameters": {"operation": "upsert", "schema": {"value": "agent"}, "table": {"value": "sla_tracking"}, "columns": {"mappingMode": "defineBelow", "values": {"escalation_id": "={{ $json.escalation_id }}", "priority": "={{ $json.priority }}", "first_response_deadline": "={{ $json.timestamps.first_response_deadline }}", "resolution_deadline": "={{ $json.timestamps.resolution_deadline }}", "first_response_breached": "={{ $json.breach_status.first_response_breached }}", "resolution_breached": "={{ $json.breach_status.resolution_breached }}", "escalation_count": "={{ $json.escalation_status.escalation_count }}", "sla_health_score": "={{ $json.performance_indicators.sla_health_score }}", "urgency_multiplier": "={{ $json.performance_indicators.urgency_multiplier }}", "estimated_resolution_hours": "={{ $json.performance_indicators.estimated_resolution_time }}", "last_updated": "={{ $json.processing_metadata.processed_at }}", "sla_data": "={{ JSON.stringify($json) }}"}}, "options": {"upsertColumns": ["escalation_id"]}}, "id": "save-sla-tracking", "name": "💾 Save SLA Tracking", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [1120, 300], "credentials": {"postgres": {"id": "postgres-main", "name": "PostgreSQL Main"}}}, {"parameters": {"jsCode": "// Handle SLA Breach\nconst slaData = $input.first().json;\n\n// Create breach notification data\nconst breachNotification = {\n  escalation_id: slaData.escalation_id,\n  breach_type: slaData.breach_status.breach_severity,\n  breach_details: {\n    first_response_breached: slaData.breach_status.first_response_breached,\n    resolution_breached: slaData.breach_status.resolution_breached,\n    time_overdue: {\n      first_response_minutes: slaData.breach_status.first_response_breached ? \n        Math.abs(slaData.time_remaining.first_response_minutes) : 0,\n      resolution_hours: slaData.breach_status.resolution_breached ? \n        Math.abs(slaData.time_remaining.resolution_hours) : 0\n    }\n  },\n  notifications: [],\n  immediate_actions: []\n};\n\n// Determine notifications based on breach severity\nif (slaData.breach_status.resolution_breached) {\n  // Critical breach - notify all levels\n  breachNotification.notifications.push({\n    type: 'slack',\n    target: '#escalations-critical',\n    priority: 'critical',\n    message: `🚨 RESOLUTION SLA BREACHED for escalation ${slaData.escalation_id}`,\n    slack_data: {\n      channel: '#escalations-critical',\n      text: `🚨 CRITICAL: Resolution SLA breached for escalation ${slaData.escalation_id}`,\n      blocks: [\n        {\n          type: 'section',\n          text: {\n            type: 'mrkdwn',\n            text: `*🚨 RESOLUTION SLA BREACH*\\n*Escalation ID:* ${slaData.escalation_id}\\n*Priority:* ${slaData.priority.toUpperCase()}\\n*Overdue:* ${Math.abs(slaData.time_remaining.resolution_hours)} hours\\n*Health Score:* ${slaData.performance_indicators.sla_health_score}/100`\n          }\n        },\n        {\n          type: 'actions',\n          elements: [\n            {\n              type: 'button',\n              text: { type: 'plain_text', text: 'View Escalation' },\n              style: 'danger',\n              url: `https://your-dashboard.com/escalations/${slaData.escalation_id}`\n            }\n          ]\n        }\n      ]\n    }\n  });\n  \n  breachNotification.notifications.push({\n    type: 'email',\n    target: '<EMAIL>',\n    priority: 'critical',\n    email_data: {\n      to: '<EMAIL>',\n      subject: `🚨 CRITICAL: Resolution SLA Breach - ${slaData.escalation_id}`,\n      body: `Resolution SLA has been breached for escalation ${slaData.escalation_id}. Immediate attention required.`\n    }\n  });\n  \n  breachNotification.immediate_actions.push('emergency_escalation', 'manager_notification', 'priority_reassignment');\n  \n} else if (slaData.breach_status.first_response_breached) {\n  // First response breach\n  breachNotification.notifications.push({\n    type: 'slack',\n    target: '#escalations-alerts',\n    priority: 'high',\n    message: `⚠️ FIRST RESPONSE SLA BREACHED for escalation ${slaData.escalation_id}`,\n    slack_data: {\n      channel: '#escalations-alerts',\n      text: `⚠️ First response SLA breached for escalation ${slaData.escalation_id}`,\n      blocks: [\n        {\n          type: 'section',\n          text: {\n            type: 'mrkdwn',\n            text: `*⚠️ FIRST RESPONSE SLA BREACH*\\n*Escalation ID:* ${slaData.escalation_id}\\n*Priority:* ${slaData.priority.toUpperCase()}\\n*Overdue:* ${Math.abs(slaData.time_remaining.first_response_minutes)} minutes`\n          }\n        }\n      ]\n    }\n  });\n  \n  breachNotification.immediate_actions.push('assign_agent', 'manager_notification');\n}\n\n// Add escalation-based notifications\nslaData.escalation_status.active_escalations.forEach(escalation => {\n  if (escalation.action === 'emergency_escalation') {\n    breachNotification.notifications.push({\n      type: 'sms',\n      target: '+1234567890', // Emergency contact\n      priority: 'critical',\n      sms_data: {\n        to: '+1234567890',\n        message: `EMERGENCY: Escalation ${slaData.escalation_id} requires immediate attention. Level ${escalation.level} breach.`\n      }\n    });\n  }\n});\n\nreturn [{ json: breachNotification }];"}, "id": "handle-sla-breach", "name": "🚨 Handle SLA Breach", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [900, 400]}, {"parameters": {"jsCode": "// Handle Escalation Actions\nconst slaData = $input.first().json;\n\n// Process active escalations\nconst escalationActions = {\n  escalation_id: slaData.escalation_id,\n  active_escalations: slaData.escalation_status.active_escalations,\n  actions_to_execute: [],\n  notifications: []\n};\n\nslaData.escalation_status.active_escalations.forEach(escalation => {\n  switch (escalation.action) {\n    case 'notify_manager':\n      escalationActions.actions_to_execute.push({\n        action_type: 'notification',\n        target: 'manager',\n        escalation_level: escalation.level,\n        urgency: 'high'\n      });\n      \n      escalationActions.notifications.push({\n        type: 'slack',\n        target: '@manager',\n        priority: 'high',\n        message: `Manager notification required for escalation ${slaData.escalation_id}`,\n        slack_data: {\n          channel: '@manager',\n          text: `⚠️ Escalation Level ${escalation.level} triggered for ${slaData.escalation_id}`,\n          blocks: [\n            {\n              type: 'section',\n              text: {\n                type: 'mrkdwn',\n                text: `*⚠️ ESCALATION LEVEL ${escalation.level}*\\n*Escalation ID:* ${slaData.escalation_id}\\n*Overdue:* ${escalation.overdue_minutes} minutes\\n*Action Required:* Manager review and assignment`\n              }\n            }\n          ]\n        }\n      });\n      break;\n      \n    case 'notify_director':\n      escalationActions.actions_to_execute.push({\n        action_type: 'notification',\n        target: 'director',\n        escalation_level: escalation.level,\n        urgency: 'critical'\n      });\n      \n      escalationActions.notifications.push({\n        type: 'email',\n        target: '<EMAIL>',\n        priority: 'critical',\n        email_data: {\n          to: '<EMAIL>',\n          subject: `🚨 ESCALATION Level ${escalation.level} - ${slaData.escalation_id}`,\n          body: `Director attention required for escalation ${slaData.escalation_id}. Level ${escalation.level} has been triggered.`\n        }\n      });\n      break;\n      \n    case 'emergency_escalation':\n      escalationActions.actions_to_execute.push({\n        action_type: 'emergency_protocol',\n        target: 'emergency_team',\n        escalation_level: escalation.level,\n        urgency: 'critical'\n      });\n      \n      escalationActions.notifications.push({\n        type: 'sms',\n        target: '+1234567890',\n        priority: 'critical',\n        sms_data: {\n          to: '+1234567890',\n          message: `EMERGENCY ESCALATION: ${slaData.escalation_id} Level ${escalation.level}. Immediate action required.`\n        }\n      });\n      break;\n  }\n});\n\nreturn [{ json: escalationActions }];"}, "id": "handle-escalation-actions", "name": "⬆️ Handle Escalation Actions", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1120, 200]}, {"parameters": {"url": "http://localhost:5678/webhook/send-notifications", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "bodyParameters": {"parameters": []}, "jsonBody": "={{ JSON.stringify($json) }}", "options": {"retry": {"enabled": true, "maxTries": 3}}}, "id": "send-breach-notifications", "name": "📢 Send Breach Notifications", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1120, 400]}, {"parameters": {"url": "http://localhost:5678/webhook/send-notifications", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "bodyParameters": {"parameters": []}, "jsonBody": "={{ JSON.stringify($json) }}", "options": {"retry": {"enabled": true, "maxTries": 3}}}, "id": "send-escalation-notifications", "name": "📢 Send Escalation Notifications", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1340, 200]}, {"parameters": {"jsCode": "// Monitor SLA Performance\nconst slaData = $input.first().json;\n\n// Create performance monitoring data\nconst performanceData = {\n  escalation_id: slaData.escalation_id,\n  monitoring_timestamp: new Date().toISOString(),\n  sla_metrics: {\n    health_score: slaData.performance_indicators.sla_health_score,\n    urgency_multiplier: slaData.performance_indicators.urgency_multiplier,\n    estimated_resolution_time: slaData.performance_indicators.estimated_resolution_time\n  },\n  time_tracking: {\n    time_to_first_response: slaData.time_remaining.first_response_minutes,\n    time_to_resolution: slaData.time_remaining.resolution_hours,\n    is_within_sla: !slaData.breach_status.any_breach\n  },\n  escalation_tracking: {\n    current_escalation_level: slaData.escalation_status.highest_escalation_level,\n    escalation_count: slaData.escalation_status.escalation_count,\n    requires_immediate_action: slaData.escalation_status.requires_immediate_action\n  },\n  next_monitoring: {\n    next_check_in_minutes: calculateNextCheckInterval(slaData),\n    monitoring_frequency: determineMonitoringFrequency(slaData),\n    alert_thresholds: calculateAlertThresholds(slaData)\n  }\n};\n\n// Helper function to calculate next check interval\nfunction calculateNextCheckInterval(slaData) {\n  if (slaData.breach_status.any_breach) return 5; // Check every 5 minutes if breached\n  if (slaData.escalation_status.escalation_count > 0) return 15; // Check every 15 minutes if escalated\n  if (slaData.priority === 'critical') return 10; // Check every 10 minutes for critical\n  if (slaData.priority === 'high') return 30; // Check every 30 minutes for high\n  return 60; // Check every hour for medium/low\n}\n\n// Helper function to determine monitoring frequency\nfunction determineMonitoringFrequency(slaData) {\n  if (slaData.breach_status.any_breach) return 'continuous';\n  if (slaData.escalation_status.escalation_count > 0) return 'frequent';\n  if (slaData.priority === 'critical') return 'high';\n  return 'standard';\n}\n\n// Helper function to calculate alert thresholds\nfunction calculateAlertThresholds(slaData) {\n  const timeToFirstResponse = slaData.time_remaining.first_response_minutes;\n  const timeToResolution = slaData.time_remaining.resolution_hours;\n  \n  return {\n    first_response_warning: Math.max(5, timeToFirstResponse * 0.2), // 20% of remaining time or 5 minutes\n    resolution_warning: Math.max(1, timeToResolution * 0.1), // 10% of remaining time or 1 hour\n    critical_threshold: Math.max(2, timeToFirstResponse * 0.05) // 5% of remaining time or 2 minutes\n  };\n}\n\nreturn [{ json: performanceData }];"}, "id": "monitor-sla-performance", "name": "📊 Monitor SLA Performance", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1340, 300]}, {"parameters": {"operation": "insert", "schema": {"value": "agent"}, "table": {"value": "sla_monitoring"}, "columns": {"mappingMode": "defineBelow", "values": {"escalation_id": "={{ $json.escalation_id }}", "monitoring_timestamp": "={{ $json.monitoring_timestamp }}", "health_score": "={{ $json.sla_metrics.health_score }}", "urgency_multiplier": "={{ $json.sla_metrics.urgency_multiplier }}", "estimated_resolution_hours": "={{ $json.sla_metrics.estimated_resolution_time }}", "time_to_first_response_minutes": "={{ $json.time_tracking.time_to_first_response }}", "time_to_resolution_hours": "={{ $json.time_tracking.time_to_resolution }}", "is_within_sla": "={{ $json.time_tracking.is_within_sla }}", "escalation_level": "={{ $json.escalation_tracking.current_escalation_level }}", "next_check_minutes": "={{ $json.next_monitoring.next_check_in_minutes }}", "monitoring_frequency": "={{ $json.next_monitoring.monitoring_frequency }}", "performance_data": "={{ JSON.stringify($json) }}"}}, "options": {}}, "id": "save-sla-monitoring", "name": "💾 Save SLA Monitoring", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [1560, 300], "credentials": {"postgres": {"id": "postgres-main", "name": "PostgreSQL Main"}}}, {"parameters": {"jsCode": "// Schedule Next SLA Check\nconst performanceData = $input.first().json;\n\n// Create scheduled check data\nconst scheduledCheck = {\n  escalation_id: performanceData.escalation_id,\n  next_check_time: new Date(Date.now() + (performanceData.next_monitoring.next_check_in_minutes * 60 * 1000)).toISOString(),\n  check_type: 'sla_monitoring',\n  monitoring_config: {\n    frequency: performanceData.next_monitoring.monitoring_frequency,\n    alert_thresholds: performanceData.next_monitoring.alert_thresholds,\n    escalation_level: performanceData.escalation_tracking.current_escalation_level\n  },\n  webhook_payload: {\n    escalation_id: performanceData.escalation_id,\n    check_type: 'scheduled_sla_check',\n    source: 'sla_management_v3'\n  }\n};\n\nreturn [{ json: scheduledCheck }];"}, "id": "schedule-next-check", "name": "⏰ Schedule Next Check", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1780, 300]}, {"parameters": {"operation": "upsert", "schema": {"value": "agent"}, "table": {"value": "scheduled_checks"}, "columns": {"mappingMode": "defineBelow", "values": {"escalation_id": "={{ $json.escalation_id }}", "next_check_time": "={{ $json.next_check_time }}", "check_type": "={{ $json.check_type }}", "monitoring_config": "={{ JSON.stringify($json.monitoring_config) }}", "webhook_payload": "={{ JSON.stringify($json.webhook_payload) }}", "created_at": "={{ new Date().toISOString() }}", "is_active": true}}, "options": {"upsertColumns": ["escalation_id", "check_type"]}}, "id": "save-scheduled-check", "name": "💾 Save Scheduled Check", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [2000, 300], "credentials": {"postgres": {"id": "postgres-main", "name": "PostgreSQL Main"}}}], "connections": {"sla-webhook-trigger": {"main": [[{"node": "process-sla-data", "type": "main", "index": 0}]]}, "process-sla-data": {"main": [[{"node": "check-sla-breach", "type": "main", "index": 0}]]}, "check-sla-breach": {"main": [[{"node": "check-escalation-required", "type": "main", "index": 0}, {"node": "handle-sla-breach", "type": "main", "index": 0}], [{"node": "save-sla-tracking", "type": "main", "index": 0}]]}, "check-escalation-required": {"main": [[{"node": "handle-escalation-actions", "type": "main", "index": 0}], [{"node": "save-sla-tracking", "type": "main", "index": 0}]]}, "save-sla-tracking": {"main": [[{"node": "monitor-sla-performance", "type": "main", "index": 0}]]}, "handle-sla-breach": {"main": [[{"node": "send-breach-notifications", "type": "main", "index": 0}]]}, "handle-escalation-actions": {"main": [[{"node": "send-escalation-notifications", "type": "main", "index": 0}]]}, "send-breach-notifications": {"main": [[{"node": "monitor-sla-performance", "type": "main", "index": 0}]]}, "send-escalation-notifications": {"main": [[{"node": "monitor-sla-performance", "type": "main", "index": 0}]]}, "monitor-sla-performance": {"main": [[{"node": "save-sla-monitoring", "type": "main", "index": 0}]]}, "save-sla-monitoring": {"main": [[{"node": "schedule-next-check", "type": "main", "index": 0}]]}, "schedule-next-check": {"main": [[{"node": "save-scheduled-check", "type": "main", "index": 0}]]}}, "pinData": {}, "settings": {"executionOrder": "v1"}, "staticData": null, "tags": [{"createdAt": "2024-01-15T10:00:00.000Z", "updatedAt": "2024-01-15T10:00:00.000Z", "id": "sla", "name": "SLA"}, {"createdAt": "2024-01-15T10:00:00.000Z", "updatedAt": "2024-01-15T10:00:00.000Z", "id": "management", "name": "Management"}, {"createdAt": "2024-01-15T10:00:00.000Z", "updatedAt": "2024-01-15T10:00:00.000Z", "id": "monitoring", "name": "Monitoring"}]}