{"meta": {"instanceId": "SMART_LEAD_IDENTITY_ROUTER"}, "name": "[ROUTER] Smart Lead Identity Router", "nodes": [{"parameters": {}, "id": "webhook_lead_routing", "name": "WEBHOOK: Lead Routing Request", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [200, 300], "webhookId": "smart-lead-routing", "path": "smart-lead-routing"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT c.id, c.nome, c.tags, c.enriched_data, c.empresa_atual, c.jornada_status, c.engagement_score, c.source, ip.cargo_atual, lir.recommended_identity_id as current_routed_identity, lir.compatibility_score as current_score FROM agent.contatos c LEFT JOIN agent.informacoes_pessoais ip ON c.id = ip.id_contato LEFT JOIN agent.lead_identity_routing lir ON c.id = lir.contact_id WHERE c.id = $1;", "options": {"parameters": {"values": ["={{ $json.contact_id || $json.body.contact_id }}"]}}}, "id": "get_contact_details", "name": "Get Contact Details", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [420, 300], "credentials": {"postgres": {"id": "postgres-main", "name": "PostgreSQL Main"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "force_rerouting_check", "leftValue": "={{ $json.body.force_rerouting || false }}", "rightValue": true, "operator": {"type": "boolean", "operation": "equal"}}, {"id": "no_existing_routing", "leftValue": "={{ $json.current_routed_identity }}", "rightValue": "", "operator": {"type": "string", "operation": "isEmpty"}}], "combineOperation": "any"}}, "id": "check_routing_needed", "name": "Check if Routing Needed", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [640, 300]}, {"parameters": {"workflowId": "={{ $env.DISPATCHER_WORKFLOW_ID }}", "data": "={{ { body: { prompt: `<PERSON><PERSON><PERSON> o perfil do lead e determine qual identidade seria mais adequada:\\n\\nNome: ${$json.nome}\\nEmpresa: ${$json.empresa_atual || 'N/A'}\\nCargo: ${$json.cargo_atual || 'N/A'}\\nTags: ${JSON.stringify($json.tags || [])}\\n\\nIDENTIDADES: Lucas <PERSON> (automação), <PERSON>ia Conecta (creator economy), <PERSON> (estratégia), <PERSON> (vendas B2B)\\n\\nRetorne JSON: {\\\"recommended_identity\\\": \\\"nome\\\", \\\"compatibility_score\\\": 0-100, \\\"confidence_level\\\": 0.0-1.0, \\\"reasoning\\\": {\\\"expertise_match\\\": \\\"...\\\", \\\"audience_fit\\\": \\\"...\\\"}}`, task_type: 'complex_analysis', calling_workflow_id: $workflow.id, calling_node_id: 'ai_analyze_lead_profile' } } }}", "options": {}}, "id": "ai_analyze_lead_profile", "name": "AI Analyze Lead Profile", "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 2, "position": [860, 200]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT id, identity_name, display_name, category FROM agent.agent_identities WHERE identity_name = $1 OR display_name ILIKE '%' || $1 || '%' LIMIT 1;", "options": {"parameters": {"values": ["={{ $json.recommended_identity }}"]}}}, "id": "get_recommended_identity", "name": "Get Recommended Identity", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [1080, 200], "credentials": {"postgres": {"id": "postgres-main", "name": "PostgreSQL Main"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "INSERT INTO agent.lead_identity_routing (contact_id, recommended_identity_id, routing_reason, compatibility_score, routing_confidence) VALUES ($1, $2, $3::jsonb, $4, $5) ON CONFLICT (contact_id) DO UPDATE SET recommended_identity_id = EXCLUDED.recommended_identity_id, routing_reason = EXCLUDED.routing_reason, compatibility_score = EXCLUDED.compatibility_score, routing_confidence = EXCLUDED.routing_confidence, updated_at = NOW() RETURNING *;", "options": {"parameters": {"values": ["={{ $('get_contact_details').item.json.id }}", "={{ $('get_recommended_identity').item.json.id }}", "={{ JSON.stringify($('ai_analyze_lead_profile').item.json.reasoning) }}", "={{ $('ai_analyze_lead_profile').item.json.compatibility_score }}", "={{ $('ai_analyze_lead_profile').item.json.confidence_level }}"]}}}, "id": "save_routing_decision", "name": "Save Routing Decision", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [1300, 200], "credentials": {"postgres": {"id": "postgres-main", "name": "PostgreSQL Main"}}}, {"parameters": {"respondWith": "json", "responseBody": "={{ { status: 'success', message: 'Lead roteado com sucesso', routing_details: { contact_id: $('get_contact_details').item.json.id, contact_name: $('get_contact_details').item.json.nome, recommended_identity: { id: $('get_recommended_identity').item.json.id, name: $('get_recommended_identity').item.json.identity_name, display_name: $('get_recommended_identity').item.json.display_name }, compatibility_score: $('ai_analyze_lead_profile').item.json.compatibility_score, confidence_level: $('ai_analyze_lead_profile').item.json.confidence_level } } }}", "options": {}}, "id": "return_routing_success", "name": "Return Routing Success", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1520, 200]}, {"parameters": {"respondWith": "json", "responseBody": "={{ { status: 'skipped', message: 'Lead j<PERSON> possui roteamento válido', existing_routing: { contact_id: $json.id, contact_name: $json.nome, current_identity_id: $json.current_routed_identity, current_score: $json.current_score } } }}", "options": {}}, "id": "return_existing_routing", "name": "Return Existing Routing", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [640, 440]}], "connections": {"webhook_lead_routing": {"main": [[{"node": "get_contact_details", "type": "main", "index": 0}]]}, "get_contact_details": {"main": [[{"node": "check_routing_needed", "type": "main", "index": 0}]]}, "check_routing_needed": {"main": [[{"node": "ai_analyze_lead_profile", "type": "main", "index": 0}], [{"node": "return_existing_routing", "type": "main", "index": 0}]]}, "ai_analyze_lead_profile": {"main": [[{"node": "get_recommended_identity", "type": "main", "index": 0}]]}, "get_recommended_identity": {"main": [[{"node": "save_routing_decision", "type": "main", "index": 0}]]}, "save_routing_decision": {"main": [[{"node": "return_routing_success", "type": "main", "index": 0}]]}}, "pinData": {}, "settings": {"executionOrder": "v1"}, "staticData": null, "tags": [], "triggerCount": 0, "updatedAt": "2024-01-15T10:00:00.000Z", "versionId": "router-smart-lead-v1.0"}