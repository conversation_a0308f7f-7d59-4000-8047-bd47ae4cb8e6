{
  "name": "[AI] 🧠 Sentiment Analysis v3.0",
  "nodes": [
    {
      "parameters": {
        "httpMethod": "POST",
        "path": "ai-sentiment-analysis",
        "options": {}
      },
      "id": "ai-webhook-trigger",
      "name": "🔗 AI Analysis Webhook",
      "type": "n8n-nodes-base.webhook",
      "typeVersion": 1,
      "position": [240, 300],
      "webhookId": "ai-sentiment-analysis"
    },
    {
      "parameters": {
        "jsCode": "// Enhanced AI Sentiment Analysis Processing\nconst analysisData = $input.first().json.analysis_data || $input.first().json;\n\n// Validate input data\nif (!analysisData || typeof analysisData !== 'object') {\n  return [{ json: { error: 'Invalid analysis data', processed: false } }];\n}\n\n// Extract text for analysis\nconst extractTextForAnalysis = (data) => {\n  const texts = [];\n  \n  // Primary text sources\n  if (data.text_analysis?.primary_text) {\n    texts.push({\n      text: data.text_analysis.primary_text,\n      type: 'primary',\n      weight: 1.0\n    });\n  }\n  \n  // Secondary text sources\n  if (data.text_analysis?.secondary_texts) {\n    data.text_analysis.secondary_texts.forEach(text => {\n      if (text && text.trim()) {\n        texts.push({\n          text: text,\n          type: 'secondary',\n          weight: 0.7\n        });\n      }\n    });\n  }\n  \n  // Context texts\n  if (data.text_analysis?.context_texts) {\n    data.text_analysis.context_texts.forEach(text => {\n      if (text && text.trim()) {\n        texts.push({\n          text: text,\n          type: 'context',\n          weight: 0.3\n        });\n      }\n    });\n  }\n  \n  // Fallback text extraction\n  if (texts.length === 0) {\n    if (data.slack_context?.message?.text) {\n      texts.push({\n        text: data.slack_context.message.text,\n        type: 'fallback',\n        weight: 1.0\n      });\n    } else if (data.chatwoot_context?.message?.content) {\n      texts.push({\n        text: data.chatwoot_context.message.content,\n        type: 'fallback',\n        weight: 1.0\n      });\n    }\n  }\n  \n  return texts;\n};\n\n// Enhanced sentiment analysis\nconst performSentimentAnalysis = (texts) => {\n  const sentimentKeywords = {\n    positive: {\n      keywords: ['good', 'great', 'excellent', 'amazing', 'wonderful', 'fantastic', 'love', 'perfect', 'awesome', 'brilliant', 'outstanding', 'superb', 'pleased', 'satisfied', 'happy', 'delighted', 'impressed', 'thank you', 'thanks', 'appreciate'],\n      score: 1\n    },\n    negative: {\n      keywords: ['bad', 'terrible', 'awful', 'horrible', 'hate', 'worst', 'disgusting', 'pathetic', 'useless', 'broken', 'failed', 'error', 'problem', 'issue', 'bug', 'frustrated', 'angry', 'disappointed', 'upset', 'annoyed', 'furious', 'outraged'],\n      score: -1\n    },\n    urgent: {\n      keywords: ['urgent', 'emergency', 'critical', 'asap', 'immediately', 'now', 'quickly', 'fast', 'rush', 'priority', 'escalate', 'help'],\n      score: 2\n    }\n  };\n  \n  let totalScore = 0;\n  let totalWeight = 0;\n  let sentimentDetails = [];\n  \n  texts.forEach(textObj => {\n    const text = textObj.text.toLowerCase();\n    const weight = textObj.weight;\n    let textScore = 0;\n    let detectedKeywords = [];\n    \n    // Check for sentiment keywords\n    Object.keys(sentimentKeywords).forEach(sentiment => {\n      sentimentKeywords[sentiment].keywords.forEach(keyword => {\n        if (text.includes(keyword)) {\n          textScore += sentimentKeywords[sentiment].score;\n          detectedKeywords.push({ keyword, sentiment, score: sentimentKeywords[sentiment].score });\n        }\n      });\n    });\n    \n    // Apply weight\n    const weightedScore = textScore * weight;\n    totalScore += weightedScore;\n    totalWeight += weight;\n    \n    if (detectedKeywords.length > 0) {\n      sentimentDetails.push({\n        text: textObj.text.substring(0, 100) + (textObj.text.length > 100 ? '...' : ''),\n        type: textObj.type,\n        weight: weight,\n        score: textScore,\n        weighted_score: weightedScore,\n        keywords: detectedKeywords\n      });\n    }\n  });\n  \n  // Calculate final sentiment\n  const averageScore = totalWeight > 0 ? totalScore / totalWeight : 0;\n  \n  let sentiment = 'neutral';\n  let confidence = 0.5;\n  \n  if (averageScore > 1.5) {\n    sentiment = 'very_positive';\n    confidence = Math.min(0.9, 0.6 + (averageScore - 1.5) * 0.2);\n  } else if (averageScore > 0.5) {\n    sentiment = 'positive';\n    confidence = Math.min(0.8, 0.6 + (averageScore - 0.5) * 0.2);\n  } else if (averageScore < -1.5) {\n    sentiment = 'very_negative';\n    confidence = Math.min(0.9, 0.6 + Math.abs(averageScore + 1.5) * 0.2);\n  } else if (averageScore < -0.5) {\n    sentiment = 'negative';\n    confidence = Math.min(0.8, 0.6 + Math.abs(averageScore + 0.5) * 0.2);\n  }\n  \n  return {\n    sentiment,\n    sentiment_score: averageScore,\n    confidence,\n    details: sentimentDetails,\n    total_keywords_found: sentimentDetails.reduce((sum, detail) => sum + detail.keywords.length, 0)\n  };\n};\n\n// Enhanced emotion detection\nconst detectEmotion = (texts) => {\n  const emotionKeywords = {\n    anger: ['angry', 'furious', 'mad', 'rage', 'outraged', 'livid', 'irate', 'annoyed', 'frustrated', 'irritated'],\n    fear: ['scared', 'afraid', 'worried', 'anxious', 'nervous', 'concerned', 'panic', 'terrified'],\n    joy: ['happy', 'joyful', 'excited', 'thrilled', 'delighted', 'cheerful', 'elated', 'ecstatic'],\n    sadness: ['sad', 'depressed', 'disappointed', 'upset', 'down', 'miserable', 'heartbroken'],\n    surprise: ['surprised', 'shocked', 'amazed', 'astonished', 'stunned', 'bewildered'],\n    disgust: ['disgusted', 'revolted', 'appalled', 'sickened', 'repulsed']\n  };\n  \n  const emotionScores = {};\n  let totalEmotionWords = 0;\n  \n  texts.forEach(textObj => {\n    const text = textObj.text.toLowerCase();\n    const weight = textObj.weight;\n    \n    Object.keys(emotionKeywords).forEach(emotion => {\n      emotionKeywords[emotion].forEach(keyword => {\n        if (text.includes(keyword)) {\n          emotionScores[emotion] = (emotionScores[emotion] || 0) + weight;\n          totalEmotionWords++;\n        }\n      });\n    });\n  });\n  \n  // Find dominant emotion\n  let dominantEmotion = 'neutral';\n  let maxScore = 0;\n  let emotionConfidence = 0.3;\n  \n  Object.keys(emotionScores).forEach(emotion => {\n    if (emotionScores[emotion] > maxScore) {\n      maxScore = emotionScores[emotion];\n      dominantEmotion = emotion;\n    }\n  });\n  \n  if (maxScore > 0) {\n    emotionConfidence = Math.min(0.9, 0.5 + (maxScore / (totalEmotionWords || 1)) * 0.4);\n  }\n  \n  return {\n    emotion: dominantEmotion,\n    emotion_confidence: emotionConfidence,\n    emotion_scores: emotionScores,\n    emotion_keywords_found: totalEmotionWords\n  };\n};\n\n// Enhanced urgency assessment\nconst assessUrgency = (texts, contextData) => {\n  const urgencyIndicators = {\n    time_based: ['now', 'immediately', 'asap', 'urgent', 'emergency', 'quickly', 'fast', 'rush', 'deadline'],\n    severity_based: ['critical', 'severe', 'major', 'serious', 'important', 'priority', 'escalate'],\n    impact_based: ['down', 'broken', 'not working', 'failed', 'error', 'outage', 'crash', 'stuck'],\n    business_based: ['customer', 'client', 'revenue', 'sales', 'production', 'live', 'public']\n  };\n  \n  let urgencyScore = 0;\n  let urgencyReasons = [];\n  \n  texts.forEach(textObj => {\n    const text = textObj.text.toLowerCase();\n    const weight = textObj.weight;\n    \n    Object.keys(urgencyIndicators).forEach(category => {\n      urgencyIndicators[category].forEach(indicator => {\n        if (text.includes(indicator)) {\n          const categoryWeight = {\n            time_based: 3,\n            severity_based: 2.5,\n            impact_based: 2,\n            business_based: 1.5\n          }[category];\n          \n          urgencyScore += categoryWeight * weight;\n          urgencyReasons.push({\n            indicator,\n            category,\n            weight: categoryWeight * weight,\n            context: text.substring(Math.max(0, text.indexOf(indicator) - 20), text.indexOf(indicator) + indicator.length + 20)\n          });\n        }\n      });\n    });\n  });\n  \n  // Context-based urgency adjustments\n  if (contextData.temporal_context?.is_business_hours === false) {\n    urgencyScore += 1;\n    urgencyReasons.push({ indicator: 'after_hours', category: 'temporal', weight: 1 });\n  }\n  \n  if (contextData.temporal_context?.is_weekend === true) {\n    urgencyScore += 0.5;\n    urgencyReasons.push({ indicator: 'weekend', category: 'temporal', weight: 0.5 });\n  }\n  \n  // Source-based urgency\n  if (contextData.source === 'slack' && contextData.slack_context?.user?.is_admin) {\n    urgencyScore += 1.5;\n    urgencyReasons.push({ indicator: 'admin_user', category: 'user_role', weight: 1.5 });\n  }\n  \n  if (contextData.source === 'chatwoot' && contextData.chatwoot_context?.conversation?.priority === 'urgent') {\n    urgencyScore += 2;\n    urgencyReasons.push({ indicator: 'urgent_conversation', category: 'conversation_priority', weight: 2 });\n  }\n  \n  // Calculate urgency level\n  let urgencyLevel = 'low';\n  let urgencyConfidence = 0.5;\n  \n  if (urgencyScore >= 8) {\n    urgencyLevel = 'critical';\n    urgencyConfidence = 0.9;\n  } else if (urgencyScore >= 5) {\n    urgencyLevel = 'high';\n    urgencyConfidence = 0.8;\n  } else if (urgencyScore >= 2) {\n    urgencyLevel = 'medium';\n    urgencyConfidence = 0.7;\n  } else if (urgencyScore > 0) {\n    urgencyLevel = 'low';\n    urgencyConfidence = 0.6;\n  }\n  \n  return {\n    urgency_assessment: urgencyLevel,\n    urgency_score: urgencyScore,\n    urgency_confidence: urgencyConfidence,\n    urgency_reasons: urgencyReasons\n  };\n};\n\n// Enhanced intent classification\nconst classifyIntent = (texts) => {\n  const intentPatterns = {\n    question: ['what', 'how', 'when', 'where', 'why', 'who', 'which', '?'],\n    request: ['please', 'can you', 'could you', 'would you', 'need', 'want', 'require'],\n    complaint: ['problem', 'issue', 'wrong', 'error', 'not working', 'broken', 'failed', 'disappointed'],\n    compliment: ['good', 'great', 'excellent', 'thank you', 'thanks', 'appreciate', 'love'],\n    escalation: ['escalate', 'supervisor', 'manager', 'speak to', 'transfer', 'human'],\n    information: ['tell me', 'explain', 'describe', 'information', 'details', 'about'],\n    support: ['help', 'assist', 'support', 'guide', 'show me', 'tutorial']\n  };\n  \n  const intentScores = {};\n  \n  texts.forEach(textObj => {\n    const text = textObj.text.toLowerCase();\n    const weight = textObj.weight;\n    \n    Object.keys(intentPatterns).forEach(intent => {\n      intentPatterns[intent].forEach(pattern => {\n        if (text.includes(pattern)) {\n          intentScores[intent] = (intentScores[intent] || 0) + weight;\n        }\n      });\n    });\n  });\n  \n  // Find dominant intent\n  let dominantIntent = 'unknown';\n  let maxScore = 0;\n  let intentConfidence = 0.3;\n  \n  Object.keys(intentScores).forEach(intent => {\n    if (intentScores[intent] > maxScore) {\n      maxScore = intentScores[intent];\n      dominantIntent = intent;\n    }\n  });\n  \n  if (maxScore > 0) {\n    intentConfidence = Math.min(0.9, 0.4 + (maxScore / texts.length) * 0.5);\n  }\n  \n  return {\n    intent: dominantIntent,\n    intent_confidence: intentConfidence,\n    intent_scores: intentScores\n  };\n};\n\n// Perform comprehensive analysis\nconst texts = extractTextForAnalysis(analysisData);\n\nif (texts.length === 0) {\n  return [{\n    json: {\n      error: 'No text found for analysis',\n      analysis_id: analysisData.analysis_id,\n      processed: false\n    }\n  }];\n}\n\nconst sentimentResult = performSentimentAnalysis(texts);\nconst emotionResult = detectEmotion(texts);\nconst urgencyResult = assessUrgency(texts, analysisData);\nconst intentResult = classifyIntent(texts);\n\n// Enhanced escalation prediction\nconst predictEscalation = () => {\n  let escalationScore = 0;\n  let escalationReasons = [];\n  \n  // Sentiment-based escalation\n  if (sentimentResult.sentiment === 'very_negative') {\n    escalationScore += 40;\n    escalationReasons.push('very_negative_sentiment');\n  } else if (sentimentResult.sentiment === 'negative') {\n    escalationScore += 25;\n    escalationReasons.push('negative_sentiment');\n  }\n  \n  // Emotion-based escalation\n  if (emotionResult.emotion === 'anger') {\n    escalationScore += 35;\n    escalationReasons.push('anger_detected');\n  } else if (emotionResult.emotion === 'fear') {\n    escalationScore += 20;\n    escalationReasons.push('fear_detected');\n  }\n  \n  // Urgency-based escalation\n  if (urgencyResult.urgency_assessment === 'critical') {\n    escalationScore += 50;\n    escalationReasons.push('critical_urgency');\n  } else if (urgencyResult.urgency_assessment === 'high') {\n    escalationScore += 30;\n    escalationReasons.push('high_urgency');\n  }\n  \n  // Intent-based escalation\n  if (intentResult.intent === 'escalation') {\n    escalationScore += 45;\n    escalationReasons.push('explicit_escalation_request');\n  } else if (intentResult.intent === 'complaint') {\n    escalationScore += 25;\n    escalationReasons.push('complaint_detected');\n  }\n  \n  // Context-based escalation\n  if (analysisData.escalation_context?.current_analysis?.required) {\n    escalationScore += 20;\n    escalationReasons.push('pre_analysis_escalation_required');\n  }\n  \n  const escalationPrediction = escalationScore >= 50;\n  const escalationConfidence = Math.min(0.95, escalationScore / 100);\n  \n  return {\n    escalation_prediction: escalationPrediction,\n    escalation_confidence: escalationConfidence,\n    escalation_score: escalationScore,\n    escalation_reasons: escalationReasons\n  };\n};\n\nconst escalationResult = predictEscalation();\n\n// Generate response suggestions\nconst generateResponseSuggestions = () => {\n  const suggestions = [];\n  \n  if (intentResult.intent === 'question') {\n    suggestions.push('Provide informative answer');\n    suggestions.push('Offer additional resources');\n  }\n  \n  if (intentResult.intent === 'complaint') {\n    suggestions.push('Acknowledge the issue');\n    suggestions.push('Apologize for inconvenience');\n    suggestions.push('Offer solution or escalation');\n  }\n  \n  if (sentimentResult.sentiment.includes('negative')) {\n    suggestions.push('Use empathetic language');\n    suggestions.push('Focus on resolution');\n  }\n  \n  if (urgencyResult.urgency_assessment === 'critical' || urgencyResult.urgency_assessment === 'high') {\n    suggestions.push('Prioritize immediate response');\n    suggestions.push('Escalate to human agent');\n  }\n  \n  if (escalationResult.escalation_prediction) {\n    suggestions.push('Prepare for human handoff');\n    suggestions.push('Gather additional context');\n  }\n  \n  return suggestions.length > 0 ? suggestions : ['Provide standard response'];\n};\n\n// Compile final analysis result\nconst analysisResult = {\n  analysis_id: analysisData.analysis_id || `ai_analysis_${Date.now()}`,\n  timestamp: new Date().toISOString(),\n  source: analysisData.source || 'unknown',\n  processing_version: '3.0',\n  \n  // Core analysis results\n  ...sentimentResult,\n  ...emotionResult,\n  ...urgencyResult,\n  ...intentResult,\n  ...escalationResult,\n  \n  // Additional insights\n  key_phrases: texts.flatMap(t => \n    t.text.split(' ')\n      .filter(word => word.length > 4)\n      .slice(0, 5)\n  ).slice(0, 10),\n  \n  topics: [intentResult.intent, emotionResult.emotion, urgencyResult.urgency_assessment].filter(Boolean),\n  \n  language: 'en', // Could be enhanced with language detection\n  \n  toxicity_score: sentimentResult.sentiment === 'very_negative' ? 0.7 : \n                  sentimentResult.sentiment === 'negative' ? 0.4 : 0.1,\n  \n  complexity_score: texts.reduce((sum, t) => sum + t.text.split(' ').length, 0) / texts.length / 20,\n  \n  // Response suggestions\n  suggested_actions: generateResponseSuggestions(),\n  \n  // Processing metadata\n  processing_metadata: {\n    texts_analyzed: texts.length,\n    total_characters: texts.reduce((sum, t) => sum + t.text.length, 0),\n    analysis_duration_ms: Date.now() - (analysisData.timestamp ? new Date(analysisData.timestamp).getTime() : Date.now()),\n    confidence_average: (\n      sentimentResult.confidence + \n      emotionResult.emotion_confidence + \n      urgencyResult.urgency_confidence + \n      intentResult.intent_confidence + \n      escalationResult.escalation_confidence\n    ) / 5\n  }\n};\n\nreturn [{ json: analysisResult }];"}},"id":"ai-analysis-processor","name":"🧠 AI Analysis Processor","type":"n8n-nodes-base.code","typeVersion":2,"position":[460,300]},{"parameters":{"conditions":{"options":{"caseSensitive":true,"leftValue":"","typeValidation":"strict"},"conditions":[{"id":"high-confidence","leftValue":"={{ $json.processing_metadata.confidence_average }}","rightValue":0.7,"operator":{"type":"number","operation":"gte"}}],"combinator":"and"},"options":{}},"id":"check-analysis-confidence","name":"❓ Check Analysis Confidence","type":"n8n-nodes-base.if","typeVersion":2,"position":[680,300]},{"parameters":{"jsCode":"// Cache Analysis Results for Performance\nconst analysisResult = $input.first().json;\n\n// Prepare cache data\nconst cacheData = {\n  cache_key: `ai_analysis_${analysisResult.source}_${Date.now()}`,\n  analysis_id: analysisResult.analysis_id,\n  source: analysisResult.source,\n  cached_at: new Date().toISOString(),\n  expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // 24 hours\n  \n  // Core results for caching\n  cached_results: {\n    sentiment: analysisResult.sentiment,\n    sentiment_score: analysisResult.sentiment_score,\n    emotion: analysisResult.emotion,\n    urgency_assessment: analysisResult.urgency_assessment,\n    intent: analysisResult.intent,\n    escalation_prediction: analysisResult.escalation_prediction,\n    escalation_confidence: analysisResult.escalation_confidence,\n    suggested_actions: analysisResult.suggested_actions\n  },\n  \n  // Metadata\n  cache_metadata: {\n    confidence_average: analysisResult.processing_metadata.confidence_average,\n    processing_version: analysisResult.processing_version,\n    texts_analyzed: analysisResult.processing_metadata.texts_analyzed,\n    cache_hit_count: 0,\n    last_accessed: new Date().toISOString()\n  }\n};\n\nreturn [{\n  json: {\n    ...analysisResult,\n    cache_data: cacheData,\n    cached: true\n  }\n}];"},"id":"cache-analysis-results","name":"💾 Cache Analysis Results","type":"n8n-nodes-base.code","typeVersion":2,"position":[900,200]},{"parameters":{"jsCode":"// Handle Low Confidence Analysis\nconst analysisResult = $input.first().json;\n\n// Enhance low confidence results\nconst enhancedResult = {\n  ...analysisResult,\n  \n  // Add uncertainty flags\n  uncertainty_flags: {\n    low_confidence: true,\n    requires_human_review: true,\n    confidence_threshold_not_met: true,\n    suggested_review_areas: []\n  },\n  \n  // Adjust predictions for safety\n  escalation_prediction: analysisResult.escalation_prediction || analysisResult.urgency_assessment === 'critical',\n  escalation_confidence: Math.max(analysisResult.escalation_confidence, 0.5),\n  \n  // Add conservative suggestions\n  suggested_actions: [\n    ...analysisResult.suggested_actions,\n    'human_review_recommended',\n    'conservative_response_approach',\n    'gather_additional_context'\n  ],\n  \n  // Processing notes\n  processing_notes: [\n    'Low confidence analysis - human review recommended',\n    'Conservative escalation approach applied',\n    'Additional context gathering suggested'\n  ]\n};\n\n// Identify specific low confidence areas\nif (analysisResult.confidence < 0.6) {\n  enhancedResult.uncertainty_flags.suggested_review_areas.push('sentiment_analysis');\n}\nif (analysisResult.emotion_confidence < 0.6) {\n  enhancedResult.uncertainty_flags.suggested_review_areas.push('emotion_detection');\n}\nif (analysisResult.urgency_confidence < 0.6) {\n  enhancedResult.uncertainty_flags.suggested_review_areas.push('urgency_assessment');\n}\nif (analysisResult.intent_confidence < 0.6) {\n  enhancedResult.uncertainty_flags.suggested_review_areas.push('intent_classification');\n}\n\nreturn [{ json: enhancedResult }];"},"id":"handle-low-confidence","name":"⚠️ Handle Low Confidence","type":"n8n-nodes-base.code","typeVersion":2,"position":[900,400]}],"connections":{"ai-webhook-trigger":{"main":[[{"node":"ai-analysis-processor","type":"main","index":0}]]},"ai-analysis-processor":{"main":[[{"node":"check-analysis-confidence","type":"main","index":0}]]},"check-analysis-confidence":{"main":[[{"node":"cache-analysis-results","type":"main","index":0}],[{"node":"handle-low-confidence","type":"main","index":0}]]}},"pinData":{},"settings":{"executionOrder":"v1"},"staticData":null,"tags":[{"createdAt":"2024-01-15T10:00:00.000Z","updatedAt":"2024-01-15T10:00:00.000Z","id":"ai","name":"AI"},{"createdAt":"2024-01-15T10:00:00.000Z","updatedAt":"2024-01-15T10:00:00.000Z","id":"sentiment","name":"Sentiment"},{"createdAt":"2024-01-15T10:00:00.000Z","updatedAt":"2024-01-15T10:00:00.000Z","id":"analysis","name":"Analysis"}]}