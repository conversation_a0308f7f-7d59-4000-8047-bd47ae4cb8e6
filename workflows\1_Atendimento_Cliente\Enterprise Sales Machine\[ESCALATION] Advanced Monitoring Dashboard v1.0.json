{"name": "[ESCALATION] Advanced Monitoring Dashboard v1.0", "nodes": [{"parameters": {"path": "escalation-monitoring", "httpMethod": "GET", "responseMode": "responseNode", "options": {"allowedOrigins": "*"}}, "id": "webhook-trigger", "name": "📊 Trigger: Monitoring Dashboard", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300], "webhookId": "escalation-monitoring"}, {"parameters": {"jsCode": "// =====================================================\n// MONITORING - REQUEST PROCESSOR\n// =====================================================\n\nconst queryParams = $input.first().json.query || {};\nconst correlationId = require('crypto').randomUUID();\n\n// Parâmetros de consulta suportados\nconst supportedMetrics = [\n  'real_time_stats',\n  'performance_metrics',\n  'agent_availability',\n  'queue_status',\n  'escalation_trends',\n  'ai_accuracy',\n  'system_health',\n  'alerts_summary'\n];\n\nconst timeRanges = {\n  '1h': { hours: 1 },\n  '6h': { hours: 6 },\n  '24h': { hours: 24 },\n  '7d': { days: 7 },\n  '30d': { days: 30 }\n};\n\ntry {\n  const requestedMetrics = queryParams.metrics ? \n    queryParams.metrics.split(',').filter(m => supportedMetrics.includes(m)) : \n    supportedMetrics;\n    \n  const timeRange = timeRanges[queryParams.time_range] || timeRanges['24h'];\n  const includeAlerts = queryParams.include_alerts !== 'false';\n  const format = queryParams.format || 'json';\n  \n  // Calcular período de consulta\n  const endTime = new Date();\n  const startTime = new Date();\n  \n  if (timeRange.hours) {\n    startTime.setHours(startTime.getHours() - timeRange.hours);\n  } else if (timeRange.days) {\n    startTime.setDate(startTime.getDate() - timeRange.days);\n  }\n  \n  const monitoringRequest = {\n    correlation_id: correlationId,\n    requested_metrics: requestedMetrics,\n    time_range: {\n      start: startTime.toISOString(),\n      end: endTime.toISOString(),\n      period: queryParams.time_range || '24h'\n    },\n    include_alerts: includeAlerts,\n    format: format,\n    requested_at: new Date().toISOString(),\n    user_agent: $input.first().json.headers['user-agent'] || 'unknown'\n  };\n  \n  console.log(`[${correlationId}] Monitoring request processed`, {\n    metrics: requestedMetrics.length,\n    timeRange: queryParams.time_range || '24h',\n    includeAlerts\n  });\n  \n  return [{ json: monitoringRequest }];\n  \n} catch (error) {\n  console.error(`[${correlationId}] Error processing monitoring request:`, error);\n  \n  return [{\n    json: {\n      error: 'REQUEST_ERROR',\n      message: 'Failed to process monitoring request',\n      correlation_id: correlationId,\n      timestamp: new Date().toISOString()\n    }\n  }];\n}"}, "id": "request-processor", "name": "⚙️ Processor: Request", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [460, 300]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "-- Real-time escalation statistics\nSELECT \n  COUNT(*) as total_escalations,\n  COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_escalations,\n  COUNT(CASE WHEN status = 'in_progress' THEN 1 END) as active_escalations,\n  COUNT(CASE WHEN status = 'resolved' THEN 1 END) as resolved_escalations,\n  COUNT(CASE WHEN urgency = 'high' THEN 1 END) as high_priority,\n  COUNT(CASE WHEN urgency = 'critical' THEN 1 END) as critical_priority,\n  AVG(EXTRACT(EPOCH FROM (resolved_at - created_at))/60) as avg_resolution_time_minutes,\n  AVG(ai_confidence_score) as avg_ai_confidence\nFROM agent.intelligent_escalations \nWHERE created_at >= $1 AND created_at <= $2", "options": {"queryParameters": "={{ [$json.time_range.start, $json.time_range.end] }}"}}, "id": "real-time-stats", "name": "📈 Query: Real-time Stats", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [680, 200], "credentials": {"postgres": {"id": "postgres-main", "name": "PostgreSQL - Main Database"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "-- Agent availability and performance\nSELECT \n  a.agent_id,\n  a.name,\n  a.specialization,\n  a.status,\n  a.current_workload,\n  a.max_concurrent_escalations,\n  COUNT(e.id) as handled_escalations,\n  AVG(EXTRACT(EPOCH FROM (e.resolved_at - e.assigned_at))/60) as avg_handling_time,\n  AVG(e.customer_satisfaction_score) as avg_satisfaction\nFROM agent.agents a\nLEFT JOIN agent.intelligent_escalations e ON a.agent_id = e.assigned_agent_id \n  AND e.assigned_at >= $1 AND e.assigned_at <= $2\nGROUP BY a.agent_id, a.name, a.specialization, a.status, a.current_workload, a.max_concurrent_escalations\nORDER BY a.status DESC, handled_escalations DESC", "options": {"queryParameters": "={{ [$json.time_range.start, $json.time_range.end] }}"}}, "id": "agent-performance", "name": "👥 Query: Agent Performance", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [680, 300], "credentials": {"postgres": {"id": "postgres-main", "name": "PostgreSQL - Main Database"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "-- Queue status and trends\nSELECT \n  specialization,\n  urgency,\n  COUNT(*) as queue_count,\n  AVG(EXTRACT(EPOCH FROM (NOW() - created_at))/60) as avg_wait_time_minutes,\n  MIN(created_at) as oldest_escalation\nFROM agent.intelligent_escalations \nWHERE status = 'pending' \nGROUP BY specialization, urgency\nORDER BY urgency DESC, avg_wait_time_minutes DESC", "options": {}}, "id": "queue-status", "name": "⏳ Query: Queue Status", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [680, 400], "credentials": {"postgres": {"id": "postgres-main", "name": "PostgreSQL - Main Database"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "-- AI accuracy and analysis metrics\nSELECT \n  DATE_TRUNC('hour', created_at) as hour_bucket,\n  COUNT(*) as total_analyses,\n  AVG(confidence_score) as avg_confidence,\n  COUNT(CASE WHEN confidence_score >= 80 THEN 1 END) as high_confidence_count,\n  analysis_type,\n  AVG(EXTRACT(EPOCH FROM (created_at - created_at))*1000) as avg_processing_time_ms\nFROM agent.ai_analysis_results\nWHERE created_at >= $1 AND created_at <= $2\nGROUP BY DATE_TRUNC('hour', created_at), analysis_type\nORDER BY hour_bucket DESC", "options": {"queryParameters": "={{ [$json.time_range.start, $json.time_range.end] }}"}}, "id": "ai-metrics", "name": "🤖 Query: AI Metrics", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [680, 500], "credentials": {"postgres": {"id": "postgres-main", "name": "PostgreSQL - Main Database"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "-- System alerts and notifications\nSELECT \n  alert_type,\n  severity,\n  message,\n  COUNT(*) as occurrence_count,\n  MAX(created_at) as last_occurrence,\n  MIN(created_at) as first_occurrence\nFROM agent.system_alerts \nWHERE created_at >= $1 AND created_at <= $2\n  AND status = 'active'\nGROUP BY alert_type, severity, message\nORDER BY severity DESC, occurrence_count DESC", "options": {"queryParameters": "={{ [$json.time_range.start, $json.time_range.end] }}"}}, "id": "system-alerts", "name": "🚨 Query: System Alerts", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [680, 600], "credentials": {"postgres": {"id": "postgres-main", "name": "PostgreSQL - Main Database"}}}, {"parameters": {"jsCode": "// =====================================================\n// MONITORING - DASHBOARD BUILDER\n// =====================================================\n\nconst monitoringRequest = $input.first().json;\nconst realTimeStats = $input.all()[1]?.json || {};\nconst agentPerformance = $input.all()[2]?.json || [];\nconst queueStatus = $input.all()[3]?.json || [];\nconst aiMetrics = $input.all()[4]?.json || [];\nconst systemAlerts = $input.all()[5]?.json || [];\n\nconst correlationId = monitoringRequest.correlation_id;\n\ntry {\n  // Construir dashboard de monitoramento\n  function buildDashboard() {\n    const dashboard = {\n      metadata: {\n        correlation_id: correlationId,\n        generated_at: new Date().toISOString(),\n        time_range: monitoringRequest.time_range,\n        refresh_interval: '30s',\n        data_freshness: 'real-time'\n      },\n      summary: {\n        system_status: calculateSystemStatus(),\n        total_escalations: realTimeStats.total_escalations || 0,\n        active_escalations: realTimeStats.active_escalations || 0,\n        pending_escalations: realTimeStats.pending_escalations || 0,\n        avg_resolution_time: Math.round(realTimeStats.avg_resolution_time_minutes || 0),\n        ai_confidence: Math.round(realTimeStats.avg_ai_confidence || 0)\n      },\n      metrics: {\n        escalation_stats: buildEscalationStats(),\n        agent_performance: buildAgentMetrics(),\n        queue_analysis: buildQueueAnalysis(),\n        ai_performance: buildAIMetrics(),\n        system_health: buildSystemHealth()\n      },\n      alerts: buildAlertsSection(),\n      trends: buildTrendsAnalysis(),\n      recommendations: generateRecommendations()\n    };\n    \n    return dashboard;\n  }\n  \n  // Calcular status geral do sistema\n  function calculateSystemStatus() {\n    const criticalAlerts = systemAlerts.filter(a => a.severity === 'critical').length;\n    const highPriorityQueue = realTimeStats.critical_priority || 0;\n    const avgResolutionTime = realTimeStats.avg_resolution_time_minutes || 0;\n    \n    if (criticalAlerts > 0 || highPriorityQueue > 10 || avgResolutionTime > 180) {\n      return 'critical';\n    } else if (highPriorityQueue > 5 || avgResolutionTime > 120) {\n      return 'warning';\n    } else {\n      return 'healthy';\n    }\n  }\n  \n  // Construir estatísticas de escalação\n  function buildEscalationStats() {\n    return {\n      total: realTimeStats.total_escalations || 0,\n      by_status: {\n        pending: realTimeStats.pending_escalations || 0,\n        in_progress: realTimeStats.active_escalations || 0,\n        resolved: realTimeStats.resolved_escalations || 0\n      },\n      by_priority: {\n        high: realTimeStats.high_priority || 0,\n        critical: realTimeStats.critical_priority || 0\n      },\n      performance: {\n        avg_resolution_time_minutes: Math.round(realTimeStats.avg_resolution_time_minutes || 0),\n        resolution_rate: calculateResolutionRate()\n      }\n    };\n  }\n  \n  // Construir métricas de agentes\n  function buildAgentMetrics() {\n    const activeAgents = agentPerformance.filter(a => a.status === 'available').length;\n    const totalAgents = agentPerformance.length;\n    const avgSatisfaction = agentPerformance.reduce((sum, a) => sum + (a.avg_satisfaction || 0), 0) / totalAgents;\n    \n    return {\n      total_agents: totalAgents,\n      active_agents: activeAgents,\n      availability_rate: Math.round((activeAgents / totalAgents) * 100),\n      avg_satisfaction_score: Math.round(avgSatisfaction * 10) / 10,\n      top_performers: agentPerformance\n        .sort((a, b) => (b.avg_satisfaction || 0) - (a.avg_satisfaction || 0))\n        .slice(0, 5)\n        .map(a => ({\n          name: a.name,\n          specialization: a.specialization,\n          satisfaction: a.avg_satisfaction,\n          handled_escalations: a.handled_escalations\n        }))\n    };\n  }\n  \n  // Construir análise de filas\n  function buildQueueAnalysis() {\n    const totalInQueue = queueStatus.reduce((sum, q) => sum + q.queue_count, 0);\n    const avgWaitTime = queueStatus.reduce((sum, q) => sum + (q.avg_wait_time_minutes || 0), 0) / queueStatus.length;\n    \n    return {\n      total_in_queue: totalInQueue,\n      avg_wait_time_minutes: Math.round(avgWaitTime),\n      by_specialization: queueStatus.reduce((acc, q) => {\n        if (!acc[q.specialization]) {\n          acc[q.specialization] = { count: 0, avg_wait: 0 };\n        }\n        acc[q.specialization].count += q.queue_count;\n        acc[q.specialization].avg_wait = q.avg_wait_time_minutes;\n        return acc;\n      }, {}),\n      longest_waiting: queueStatus\n        .sort((a, b) => new Date(a.oldest_escalation) - new Date(b.oldest_escalation))\n        .slice(0, 3)\n    };\n  }\n  \n  // Construir métricas de IA\n  function buildAIMetrics() {\n    const totalAnalyses = aiMetrics.reduce((sum, m) => sum + m.total_analyses, 0);\n    const avgConfidence = aiMetrics.reduce((sum, m) => sum + (m.avg_confidence || 0), 0) / aiMetrics.length;\n    const highConfidenceRate = aiMetrics.reduce((sum, m) => sum + m.high_confidence_count, 0) / totalAnalyses;\n    \n    return {\n      total_analyses: totalAnalyses,\n      avg_confidence_score: Math.round(avgConfidence),\n      high_confidence_rate: Math.round(highConfidenceRate * 100),\n      by_type: aiMetrics.reduce((acc, m) => {\n        if (!acc[m.analysis_type]) {\n          acc[m.analysis_type] = { count: 0, confidence: 0 };\n        }\n        acc[m.analysis_type].count += m.total_analyses;\n        acc[m.analysis_type].confidence = m.avg_confidence;\n        return acc;\n      }, {}),\n      performance_trend: aiMetrics\n        .sort((a, b) => new Date(a.hour_bucket) - new Date(b.hour_bucket))\n        .map(m => ({\n          timestamp: m.hour_bucket,\n          confidence: m.avg_confidence,\n          volume: m.total_analyses\n        }))\n    };\n  }\n  \n  // Construir saúde do sistema\n  function buildSystemHealth() {\n    const criticalAlerts = systemAlerts.filter(a => a.severity === 'critical').length;\n    const warningAlerts = systemAlerts.filter(a => a.severity === 'warning').length;\n    \n    return {\n      overall_status: calculateSystemStatus(),\n      active_alerts: {\n        critical: criticalAlerts,\n        warning: warningAlerts,\n        total: systemAlerts.length\n      },\n      uptime_percentage: 99.9, // Placeholder - seria calculado de métricas reais\n      response_time_ms: 150, // Placeholder - seria calculado de métricas reais\n      error_rate_percentage: 0.1 // Placeholder - seria calculado de métricas reais\n    };\n  }\n  \n  // Construir seção de alertas\n  function buildAlertsSection() {\n    return {\n      active_count: systemAlerts.length,\n      by_severity: systemAlerts.reduce((acc, alert) => {\n        acc[alert.severity] = (acc[alert.severity] || 0) + alert.occurrence_count;\n        return acc;\n      }, {}),\n      recent_alerts: systemAlerts\n        .sort((a, b) => new Date(b.last_occurrence) - new Date(a.last_occurrence))\n        .slice(0, 10)\n        .map(alert => ({\n          type: alert.alert_type,\n          severity: alert.severity,\n          message: alert.message,\n          count: alert.occurrence_count,\n          last_seen: alert.last_occurrence\n        }))\n    };\n  }\n  \n  // Construir análise de tendências\n  function buildTrendsAnalysis() {\n    return {\n      escalation_volume_trend: 'stable', // Seria calculado comparando períodos\n      resolution_time_trend: 'improving', // Seria calculado comparando períodos\n      satisfaction_trend: 'stable', // Seria calculado comparando períodos\n      ai_accuracy_trend: 'improving' // Seria calculado comparando períodos\n    };\n  }\n  \n  // Gerar recomendações\n  function generateRecommendations() {\n    const recommendations = [];\n    \n    if (realTimeStats.pending_escalations > 20) {\n      recommendations.push({\n        type: 'capacity',\n        priority: 'high',\n        message: 'High queue volume detected. Consider adding more agents or extending hours.',\n        action: 'scale_agents'\n      });\n    }\n    \n    if (realTimeStats.avg_resolution_time_minutes > 120) {\n      recommendations.push({\n        type: 'performance',\n        priority: 'medium',\n        message: 'Resolution time above target. Review escalation routing and agent training.',\n        action: 'optimize_routing'\n      });\n    }\n    \n    if (realTimeStats.avg_ai_confidence < 70) {\n      recommendations.push({\n        type: 'ai_improvement',\n        priority: 'medium',\n        message: 'AI confidence below threshold. Consider model retraining.',\n        action: 'retrain_model'\n      });\n    }\n    \n    return recommendations;\n  }\n  \n  // Calcular taxa de resolução\n  function calculateResolutionRate() {\n    const total = realTimeStats.total_escalations || 0;\n    const resolved = realTimeStats.resolved_escalations || 0;\n    return total > 0 ? Math.round((resolved / total) * 100) : 0;\n  }\n  \n  const dashboard = buildDashboard();\n  \n  console.log(`[${correlationId}] Monitoring dashboard generated`, {\n    systemStatus: dashboard.summary.system_status,\n    totalEscalations: dashboard.summary.total_escalations,\n    activeAlerts: dashboard.alerts.active_count\n  });\n  \n  return [{ json: dashboard }];\n  \n} catch (error) {\n  console.error(`[${correlationId}] Error building monitoring dashboard:`, error);\n  \n  return [{\n    json: {\n      error: 'DASHBOARD_ERROR',\n      message: 'Failed to build monitoring dashboard',\n      correlation_id: correlationId,\n      timestamp: new Date().toISOString(),\n      details: {\n        error: error.message,\n        stack: error.stack\n      }\n    }\n  }];\n}"}, "id": "dashboard-builder", "name": "📊 Builder: Dashboard", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [900, 350]}, {"parameters": {"respondWith": "json", "responseBody": "={{ $json }}", "options": {"responseHeaders": {"entries": [{"name": "Cache-Control", "value": "no-cache, no-store, must-revalidate"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "Content-Type", "value": "application/json"}]}}}, "id": "dashboard-response", "name": "📈 Response: Dashboard", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1120, 350]}, {"parameters": {"respondWith": "json", "responseBody": "={{ $json }}", "options": {"responseCode": 500}}, "id": "error-response", "name": "❌ Response: Error", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [900, 550]}], "connections": {"webhook-trigger": {"main": [[{"node": "request-processor", "type": "main", "index": 0}]]}, "request-processor": {"main": [[{"node": "real-time-stats", "type": "main", "index": 0}, {"node": "agent-performance", "type": "main", "index": 0}, {"node": "queue-status", "type": "main", "index": 0}, {"node": "ai-metrics", "type": "main", "index": 0}, {"node": "system-alerts", "type": "main", "index": 0}]]}, "real-time-stats": {"main": [[{"node": "dashboard-builder", "type": "main", "index": 0}]]}, "agent-performance": {"main": [[{"node": "dashboard-builder", "type": "main", "index": 0}]]}, "queue-status": {"main": [[{"node": "dashboard-builder", "type": "main", "index": 0}]]}, "ai-metrics": {"main": [[{"node": "dashboard-builder", "type": "main", "index": 0}]]}, "system-alerts": {"main": [[{"node": "dashboard-builder", "type": "main", "index": 0}]]}, "dashboard-builder": {"main": [[{"node": "dashboard-response", "type": "main", "index": 0}]]}}, "pinData": {}, "settings": {"executionOrder": "v1"}, "staticData": null, "tags": [{"createdAt": "2024-01-15T10:00:00.000Z", "updatedAt": "2024-01-15T10:00:00.000Z", "id": "escalation", "name": "escalation"}, {"createdAt": "2024-01-15T10:00:00.000Z", "updatedAt": "2024-01-15T10:00:00.000Z", "id": "monitoring", "name": "monitoring"}, {"createdAt": "2024-01-15T10:00:00.000Z", "updatedAt": "2024-01-15T10:00:00.000Z", "id": "dashboard", "name": "dashboard"}], "triggerCount": 0, "updatedAt": "2024-01-15T10:00:00.000Z", "versionId": "1"}