# Script para verificar encoding de caracteres
$filePath = "c:\Users\<USER>\Desktop\N8N-evolution V5\Start-Environment.ps1"
$content = Get-Content $filePath -Raw
$lines = $content -split "\r?\n"

# Verificar linhas 775-780
for ($i = 775; $i -le 780; $i++) {
    if ($i -le $lines.Count) {
        $line = $lines[$i-1]
        $bytes = [System.Text.Encoding]::UTF8.GetBytes($line)
        $hasNonAscii = $false
        
        for ($j = 0; $j -lt $bytes.Length; $j++) {
            if ($bytes[$j] -gt 127) {
                $hasNonAscii = $true
                Write-Host "Line $i, Position $j - Byte value $($bytes[$j]) (char: $([char]$bytes[$j]))"
            }
        }
        
        if (-not $hasNonAscii) {
            Write-Host "Line $i - OK - No non-ASCII characters"
        }
        
        # Mostrar a linha para inspeção visual
        Write-Host "Line $i content - $line"
        Write-Host "---"
    }
}