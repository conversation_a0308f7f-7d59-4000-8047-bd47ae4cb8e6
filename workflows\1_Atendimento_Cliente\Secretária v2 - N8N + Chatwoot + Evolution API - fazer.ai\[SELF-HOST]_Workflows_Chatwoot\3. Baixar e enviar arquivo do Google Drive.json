{"nodes": [{"parameters": {"workflowInputs": {"values": [{"name": "file_id"}, {"name": "id_conta"}, {"name": "id_conversa"}, {"name": "url_chatwoot"}]}}, "type": "n8n-nodes-base.executeWorkflowTrigger", "typeVersion": 1.1, "position": [140, -40], "id": "aa870e80-10ca-4c6d-a83b-c78451c609a4", "name": "When Executed by Another Workflow"}, {"parameters": {"operation": "download", "fileId": {"__rl": true, "value": "={{ $json.file_id }}", "mode": "id"}, "options": {}}, "type": "n8n-nodes-base.googleDrive", "typeVersion": 3, "position": [360, -40], "id": "2e4f453a-6266-44e9-9544-1806270000bb", "name": "Baixar arquivo", "credentials": {}}, {"parameters": {"assignments": {"assignments": [{"id": "71dda3d7-4505-4d03-b70f-63f5c005c0c3", "name": "status", "value": "={{ $json.status }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [800, -40], "id": "4a544475-3be4-4708-a5f5-d8107453732a", "name": "Output"}, {"parameters": {"content": "[![fazer.ai](https://framerusercontent.com/images/HqY9djLTzyutSKnuLLqBr92KbM.png?scale-down-to=256)](https://fazer.ai?utm_source=n8n&utm_campaign=sec-ep2&utm_medium=cw-3)\n\n## Esse é um template faça você mesmo do canal\n## <PERSON> Moreira\n\n### Inscreva-se no nosso canal no YouTube\n[![YouTube Lucas Moreira](https://img.shields.io/youtube/channel/subscribers/UCtmp6SxzLscu0GRTbgM8FTw?style=flat-square&logo=youtube&label=Inscreva-se&color=f00)](https://youtube.com/@eulucassmoreira?si=0lH7hwX9pukjhmPQ)\n\n### Siga nosso GitHub\n[![GitHub fazer.ai](https://img.shields.io/badge/github-%23121011.svg?style=for-the-badge&logo=github&logoColor=white&label)](https://github.com/fazer-ai)\n", "height": 440, "width": 550, "color": 7}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [0, -600], "id": "757a2aee-ba40-4885-bbb1-a173483286b8", "name": "Sticky Note10"}, {"parameters": {"content": "## Quer entender como funciona?\n\n\n### Assista o vídeo, deixe um like, e se inscreva no canal para ter acesso a mais workflows como esse!\n\n[![IMAGE ALT TEXT HERE](https://i1.ytimg.com/vi_webp/cvTWGNJGAu4/maxresdefault.webp)](https://www.youtube.com/watch?v=cvTWGNJGAu4)", "height": 440, "width": 500, "color": 7}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [560, -600], "id": "86b11f3e-4bea-433c-8c10-810d9c7316ab", "name": "Sticky Note13"}, {"parameters": {"method": "POST", "url": "={{ $('When Executed by Another Workflow').item.json.url_chatwoot }}/api/v1/accounts/{{ $('When Executed by Another Workflow').item.json.id_conta }}/conversations/{{ $('When Executed by Another Workflow').item.json.id_conversa }}/messages", "authentication": "predefinedCredentialType", "nodeCredentialType": "chatwootApi", "sendBody": true, "contentType": "multipart-form-data", "bodyParameters": {"parameters": [{"parameterType": "formBinaryData", "name": "attachments[]", "inputDataFieldName": "data"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [580, -40], "id": "d7c03de1-d7ba-478c-b984-4a111b7537b5", "name": "Enviar arquivo", "credentials": {}}, {"parameters": {"content": "![Chatwoot](https://app.chatwoot.com/brand-assets/logo_dark.svg)", "height": 100, "width": 280, "color": 7}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [220, -300], "id": "20bf15d1-650d-4988-98fa-1b997947463c", "name": "Sticky Note23"}, {"parameters": {"content": "## 3. Baixar e enviar arquivo do Google Drive", "height": 80, "width": 540, "color": 5}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [0, -700], "id": "c20c1d38-4374-428e-8460-fcd4e2689c08", "name": "Sticky Note30"}], "connections": {"When Executed by Another Workflow": {"main": [[{"node": "Baixar arquivo", "type": "main", "index": 0}]]}, "Baixar arquivo": {"main": [[{"node": "Enviar arquivo", "type": "main", "index": 0}]]}, "Enviar arquivo": {"main": [[{"node": "Output", "type": "main", "index": 0}]]}}, "pinData": {}, "meta": {}}