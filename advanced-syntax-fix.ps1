# Script avançado para corrigir problemas de sintaxe no Start-Environment.ps1

$scriptPath = "Start-Environment.ps1"
$lines = Get-Content $scriptPath

Write-Host "Analisando estrutura do arquivo..." -ForegroundColor Cyan

# Analisar estrutura de blocos
$blockStack = @()
$lineNumber = 0
$issues = @()

foreach ($line in $lines) {
    $lineNumber++
    $trimmedLine = $line.Trim()
    
    # Detectar início de blocos
    if ($trimmedLine -match '^\s*(if|else|elseif|try|catch|finally|foreach|for|while|function|switch)\s*\(.*\)\s*\{\s*$' -or
        $trimmedLine -match '^\s*(if|else|elseif|try|catch|finally)\s*\{\s*$' -or
        $trimmedLine -match '^\s*else\s*\{\s*$') {
        
        $blockType = if ($trimmedLine -match '^\s*(\w+)') { $matches[1] } else { "unknown" }
        $blockStack += @{ Type = $blockType; Line = $lineNumber; Content = $trimmedLine }
        Write-Host "Linha $lineNumber`: Início de bloco $blockType" -ForegroundColor Green
    }
    
    # Detectar fechamento de blocos
    if ($trimmedLine -match '^\s*\}\s*$' -or $trimmedLine -match '^\s*\}\s*(else|elseif|catch|finally)') {
        if ($blockStack.Count -gt 0) {
            $closedBlock = $blockStack[-1]
            $blockStack = $blockStack[0..($blockStack.Count-2)]
            Write-Host "Linha $lineNumber`: Fechamento de bloco $($closedBlock.Type) (aberto na linha $($closedBlock.Line))" -ForegroundColor Yellow
        } else {
            $issues += "Linha $lineNumber`: Chave de fechamento sem abertura correspondente: $trimmedLine"
        }
    }
    
    # Detectar linhas com múltiplas chaves
    $openCount = ($line | Select-String -Pattern '\{' -AllMatches).Matches.Count
    $closeCount = ($line | Select-String -Pattern '\}' -AllMatches).Matches.Count
    
    if ($openCount -gt 1 -or $closeCount -gt 1) {
        Write-Host "Linha $lineNumber`: Múltiplas chaves - Abertura: $openCount, Fechamento: $closeCount" -ForegroundColor Magenta
    }
}

Write-Host "`nBlocos não fechados:" -ForegroundColor Red
foreach ($block in $blockStack) {
    Write-Host "  Linha $($block.Line)`: $($block.Type) - $($block.Content)" -ForegroundColor Red
    $issues += "Bloco $($block.Type) não fechado na linha $($block.Line): $($block.Content)"
}

Write-Host "`nProblemas encontrados:" -ForegroundColor Yellow
foreach ($issue in $issues) {
    Write-Host "  $issue" -ForegroundColor Yellow
}

Write-Host "`nTotal de blocos não fechados: $($blockStack.Count)" -ForegroundColor Cyan

# Corrigir automaticamente
if ($blockStack.Count -gt 0) {
    Write-Host "`nCorrigindo automaticamente..." -ForegroundColor Green
    
    # Ler conteúdo atual
    $content = Get-Content $scriptPath -Raw
    
    # Adicionar chaves de fechamento antes da última linha
    $closingBraces = "`n" + ("}" * $blockStack.Count)
    
    # Encontrar posição para inserir
    $lastLogEvent = $content.LastIndexOf('Log-Event "OK - Script finalizado com sucesso!"')
    
    if ($lastLogEvent -gt 0) {
        $beforeLastLine = $content.Substring(0, $lastLogEvent)
        $lastLine = $content.Substring($lastLogEvent)
        
        $correctedContent = $beforeLastLine + $closingBraces + "`n" + $lastLine
        
        # Salvar arquivo corrigido
        $correctedContent | Out-File -FilePath $scriptPath -Encoding UTF8 -NoNewline
        
        Write-Host "✅ Arquivo corrigido! Adicionadas $($blockStack.Count) chaves de fechamento." -ForegroundColor Green
        
        # Verificar resultado
        Write-Host "`nVerificando resultado..." -ForegroundColor Cyan
        try {
            $null = [System.Management.Automation.PSParser]::Tokenize((Get-Content $scriptPath -Raw), [ref]$null)
            Write-Host "✅ Sintaxe corrigida com sucesso!" -ForegroundColor Green
        } catch {
            Write-Host "❌ Ainda há problemas de sintaxe: $($_.Exception.Message)" -ForegroundColor Red
        }
    } else {
        Write-Host "❌ Não foi possível localizar posição para correção." -ForegroundColor Red
    }
} else {
    Write-Host "✅ Nenhuma correção necessária." -ForegroundColor Green
}

Write-Host "`nAnálise concluída." -ForegroundColor Cyan