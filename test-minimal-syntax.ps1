# Teste mínimo das partes problemáticas

# Teste 1: Regex que estava causando problema
$envFile = ".env"
if (Test-Path $envFile) {
    Get-Content $envFile | ForEach-Object {
        if ($_ -match '^([^=]+)=(.*)$') {
            Write-Host "Regex OK: $($matches[1]) = $($matches[2])" -ForegroundColor Green
        }
    }
}

# Teste 2: Estrutura try/catch
try {
    Write-Host "Teste try/catch" -ForegroundColor Green
} catch {
    Write-Host "Erro: $($_.Exception.Message)" -ForegroundColor Red
}

# Teste 3: String simples
$message = "Script finalizado com sucesso!"
Write-Host $message -ForegroundColor Green

Write-Host "Teste de sintaxe básica concluído!" -ForegroundColor Cyan
