{"name": "[NOTIFICATION] 📢 System v3.0", "nodes": [{"parameters": {"httpMethod": "POST", "path": "send-notifications", "options": {}}, "id": "notification-webhook-trigger", "name": "🔗 Notification Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300], "webhookId": "send-notifications"}, {"parameters": {"jsCode": "// Enhanced Notification Processing\nconst notificationData = $input.first().json;\n\n// Validate notification data\nif (!notificationData || !notificationData.notifications) {\n  return [{ json: { error: 'Invalid notification data', processed: false } }];\n}\n\n// Enhanced notification validation and processing\nconst processNotifications = (data) => {\n  const processedNotifications = [];\n  const validationResults = [];\n  \n  data.notifications.forEach((notification, index) => {\n    const validation = {\n      index: index,\n      is_valid: true,\n      errors: [],\n      warnings: []\n    };\n    \n    // Validate required fields\n    if (!notification.type) {\n      validation.errors.push('Missing notification type');\n      validation.is_valid = false;\n    }\n    \n    if (!notification.target) {\n      validation.errors.push('Missing notification target');\n      validation.is_valid = false;\n    }\n    \n    if (!notification.message) {\n      validation.errors.push('Missing notification message');\n      validation.is_valid = false;\n    }\n    \n    // Type-specific validation\n    switch (notification.type) {\n      case 'slack':\n      case 'slack_thread':\n        if (!notification.slack_data) {\n          validation.errors.push('Missing slack_data for Slack notification');\n          validation.is_valid = false;\n        }\n        break;\n        \n      case 'email':\n        if (!notification.email_data) {\n          validation.errors.push('Missing email_data for email notification');\n          validation.is_valid = false;\n        } else {\n          // Email format validation\n          const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n          if (notification.email_data.to && !emailRegex.test(notification.email_data.to)) {\n            validation.warnings.push('Invalid email format');\n          }\n        }\n        break;\n        \n      case 'sms':\n        if (!notification.sms_data) {\n          validation.errors.push('Missing sms_data for SMS notification');\n          validation.is_valid = false;\n        }\n        break;\n        \n      case 'chatwoot_note':\n        if (!notification.chatwoot_data) {\n          validation.errors.push('Missing chatwoot_data for Chatwoot notification');\n          validation.is_valid = false;\n        }\n        break;\n    }\n    \n    validationResults.push(validation);\n    \n    if (validation.is_valid) {\n      // Enhanced notification processing\n      const processedNotification = {\n        ...notification,\n        processing_metadata: {\n          processed_at: new Date().toISOString(),\n          processing_version: '3.0',\n          validation_passed: true,\n          retry_count: 0,\n          max_retries: 3\n        },\n        delivery_tracking: {\n          status: 'pending',\n          attempts: [],\n          delivery_window: {\n            start: new Date().toISOString(),\n            end: new Date(Date.now() + 30 * 60 * 1000).toISOString() // 30 minutes\n          }\n        }\n      };\n      \n      // Add priority-based delivery settings\n      if (data.priority === 'critical') {\n        processedNotification.delivery_tracking.delivery_window.end = new Date(Date.now() + 5 * 60 * 1000).toISOString(); // 5 minutes for critical\n        processedNotification.processing_metadata.max_retries = 5;\n      }\n      \n      processedNotifications.push(processedNotification);\n    }\n  });\n  \n  return {\n    processed_notifications: processedNotifications,\n    validation_results: validationResults,\n    summary: {\n      total_notifications: data.notifications.length,\n      valid_notifications: processedNotifications.length,\n      invalid_notifications: data.notifications.length - processedNotifications.length\n    }\n  };\n};\n\n// Process notifications\nconst processingResult = processNotifications(notificationData);\n\n// Enhanced batch processing metadata\nconst enhancedBatchData = {\n  ...notificationData,\n  processing_result: processingResult,\n  batch_processing: {\n    processed_at: new Date().toISOString(),\n    processing_version: '3.0',\n    batch_id: `batch_${Date.now()}`,\n    total_processing_time_ms: 0 // Will be updated after processing\n  }\n};\n\nreturn [{ json: enhancedBatchData }];"}, "id": "process-notification-batch", "name": "⚙️ Process Notification Batch", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [460, 300]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "has-valid-notifications", "leftValue": "={{ $json.processing_result.summary.valid_notifications }}", "rightValue": 0, "operator": {"type": "number", "operation": "gt"}}], "combinator": "and"}, "options": {}}, "id": "check-valid-notifications", "name": "✅ Check Valid Notifications", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [680, 300]}, {"parameters": {"jsCode": "// Split Notifications by Type\nconst batchData = $input.first().json;\nconst notifications = batchData.processing_result.processed_notifications;\n\n// Group notifications by type\nconst notificationGroups = {\n  slack: [],\n  email: [],\n  sms: [],\n  chatwoot: [],\n  other: []\n};\n\nnotifications.forEach(notification => {\n  switch (notification.type) {\n    case 'slack':\n    case 'slack_thread':\n      notificationGroups.slack.push(notification);\n      break;\n    case 'email':\n      notificationGroups.email.push(notification);\n      break;\n    case 'sms':\n      notificationGroups.sms.push(notification);\n      break;\n    case 'chatwoot_note':\n      notificationGroups.chatwoot.push(notification);\n      break;\n    default:\n      notificationGroups.other.push(notification);\n  }\n});\n\n// Create separate outputs for each notification type\nconst outputs = [];\n\n// Slack notifications\nif (notificationGroups.slack.length > 0) {\n  outputs.push({\n    json: {\n      type: 'slack_batch',\n      escalation_id: batchData.escalation_id,\n      priority: batchData.priority,\n      notifications: notificationGroups.slack,\n      batch_metadata: {\n        ...batchData.batch_metadata,\n        notification_type: 'slack',\n        count: notificationGroups.slack.length\n      }\n    }\n  });\n}\n\n// Email notifications\nif (notificationGroups.email.length > 0) {\n  outputs.push({\n    json: {\n      type: 'email_batch',\n      escalation_id: batchData.escalation_id,\n      priority: batchData.priority,\n      notifications: notificationGroups.email,\n      batch_metadata: {\n        ...batchData.batch_metadata,\n        notification_type: 'email',\n        count: notificationGroups.email.length\n      }\n    }\n  });\n}\n\n// SMS notifications\nif (notificationGroups.sms.length > 0) {\n  outputs.push({\n    json: {\n      type: 'sms_batch',\n      escalation_id: batchData.escalation_id,\n      priority: batchData.priority,\n      notifications: notificationGroups.sms,\n      batch_metadata: {\n        ...batchData.batch_metadata,\n        notification_type: 'sms',\n        count: notificationGroups.sms.length\n      }\n    }\n  });\n}\n\n// Chatwoot notifications\nif (notificationGroups.chatwoot.length > 0) {\n  outputs.push({\n    json: {\n      type: 'chatwoot_batch',\n      escalation_id: batchData.escalation_id,\n      priority: batchData.priority,\n      notifications: notificationGroups.chatwoot,\n      batch_metadata: {\n        ...batchData.batch_metadata,\n        notification_type: 'chatwoot',\n        count: notificationGroups.chatwoot.length\n      }\n    }\n  });\n}\n\n// Other notifications\nif (notificationGroups.other.length > 0) {\n  outputs.push({\n    json: {\n      type: 'other_batch',\n      escalation_id: batchData.escalation_id,\n      priority: batchData.priority,\n      notifications: notificationGroups.other,\n      batch_metadata: {\n        ...batchData.batch_metadata,\n        notification_type: 'other',\n        count: notificationGroups.other.length\n      }\n    }\n  });\n}\n\nreturn outputs.length > 0 ? outputs : [{ json: { error: 'No valid notifications to process' } }];"}, "id": "split-notifications-by-type", "name": "🔀 Split Notifications by Type", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [900, 200]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "is-slack", "leftValue": "={{ $json.type }}", "rightValue": "slack_batch", "operator": {"type": "string", "operation": "equals"}}], "combinator": "and"}, "options": {}}, "id": "route-slack-notifications", "name": "💬 Route Slack Notifications", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [1120, 100]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "is-email", "leftValue": "={{ $json.type }}", "rightValue": "email_batch", "operator": {"type": "string", "operation": "equals"}}], "combinator": "and"}, "options": {}}, "id": "route-email-notifications", "name": "📧 Route Email Notifications", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [1120, 200]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "is-sms", "leftValue": "={{ $json.type }}", "rightValue": "sms_batch", "operator": {"type": "string", "operation": "equals"}}], "combinator": "and"}, "options": {}}, "id": "route-sms-notifications", "name": "📱 Route SMS Notifications", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [1120, 300]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "is-chatwoot", "leftValue": "={{ $json.type }}", "rightValue": "chatwoot_batch", "operator": {"type": "string", "operation": "equals"}}], "combinator": "and"}, "options": {}}, "id": "route-chatwoot-notifications", "name": "💬 Route Chatwoot Notifications", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [1120, 400]}, {"parameters": {"jsCode": "// Process Slack Notifications\nconst slackBatch = $input.first().json;\nconst notifications = slackBatch.notifications;\n\nconst slackResults = [];\n\nnotifications.forEach(notification => {\n  const slackPayload = {\n    channel: notification.slack_data.channel,\n    text: notification.slack_data.text,\n    blocks: notification.slack_data.blocks || [],\n    thread_ts: notification.slack_data.thread_ts || null,\n    username: 'Escalation Bot',\n    icon_emoji: ':warning:',\n    unfurl_links: false,\n    unfurl_media: false\n  };\n  \n  // Add priority-based formatting\n  if (slackBatch.priority === 'critical') {\n    slackPayload.text = `🚨 CRITICAL: ${slackPayload.text}`;\n    slackPayload.icon_emoji = ':rotating_light:';\n  } else if (slackBatch.priority === 'high') {\n    slackPayload.text = `⚠️ HIGH: ${slackPayload.text}`;\n    slackPayload.icon_emoji = ':warning:';\n  }\n  \n  slackResults.push({\n    notification_id: notification.escalation_id + '_' + Date.now(),\n    escalation_id: slackBatch.escalation_id,\n    type: 'slack',\n    target: notification.target,\n    payload: slackPayload,\n    delivery_metadata: {\n      created_at: new Date().toISOString(),\n      priority: slackBatch.priority,\n      retry_count: 0,\n      max_retries: notification.processing_metadata.max_retries\n    }\n  });\n});\n\nreturn [{\n  json: {\n    batch_type: 'slack',\n    escalation_id: slackBatch.escalation_id,\n    slack_notifications: slackResults,\n    batch_summary: {\n      total_notifications: slackResults.length,\n      priority: slackBatch.priority,\n      created_at: new Date().toISOString()\n    }\n  }\n}];"}, "id": "process-slack-notifications", "name": "💬 Process Slack Notifications", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1340, 100]}, {"parameters": {"jsCode": "// Process Email Notifications\nconst emailBatch = $input.first().json;\nconst notifications = emailBatch.notifications;\n\nconst emailResults = [];\n\nnotifications.forEach(notification => {\n  const emailPayload = {\n    to: notification.email_data.to,\n    subject: notification.email_data.subject,\n    html: `\n      <div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;\">\n        <div style=\"background-color: ${emailBatch.priority === 'critical' ? '#ff4444' : '#ff8800'}; color: white; padding: 20px; text-align: center;\">\n          <h1>${emailBatch.priority === 'critical' ? '🚨 CRITICAL ESCALATION' : '⚠️ ESCALATION ALERT'}</h1>\n        </div>\n        <div style=\"padding: 20px; background-color: #f9f9f9;\">\n          <h2>Escalation Details</h2>\n          <p><strong>Message:</strong> ${notification.email_data.body}</p>\n          <p><strong>Escalation ID:</strong> ${emailBatch.escalation_id}</p>\n          <p><strong>Priority:</strong> ${emailBatch.priority.toUpperCase()}</p>\n          <p><strong>Created:</strong> ${new Date().toLocaleString()}</p>\n        </div>\n        <div style=\"padding: 20px; background-color: #e9e9e9; text-align: center;\">\n          <p style=\"margin: 0; color: #666;\">This is an automated escalation notification.</p>\n        </div>\n      </div>\n    `,\n    text: notification.email_data.body\n  };\n  \n  emailResults.push({\n    notification_id: notification.escalation_id + '_email_' + Date.now(),\n    escalation_id: emailBatch.escalation_id,\n    type: 'email',\n    target: notification.target,\n    payload: emailPayload,\n    delivery_metadata: {\n      created_at: new Date().toISOString(),\n      priority: emailBatch.priority,\n      retry_count: 0,\n      max_retries: notification.processing_metadata.max_retries\n    }\n  });\n});\n\nreturn [{\n  json: {\n    batch_type: 'email',\n    escalation_id: emailBatch.escalation_id,\n    email_notifications: emailResults,\n    batch_summary: {\n      total_notifications: emailResults.length,\n      priority: emailBatch.priority,\n      created_at: new Date().toISOString()\n    }\n  }\n}];"}, "id": "process-email-notifications", "name": "📧 Process Email Notifications", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1340, 200]}, {"parameters": {"jsCode": "// Process SMS Notifications\nconst smsBatch = $input.first().json;\nconst notifications = smsBatch.notifications;\n\nconst smsResults = [];\n\nnotifications.forEach(notification => {\n  let smsMessage = notification.sms_data.message;\n  \n  // Add priority prefix\n  if (smsBatch.priority === 'critical') {\n    smsMessage = `🚨 CRITICAL: ${smsMessage}`;\n  } else if (smsBatch.priority === 'high') {\n    smsMessage = `⚠️ HIGH: ${smsMessage}`;\n  }\n  \n  // Ensure SMS length limit (160 characters)\n  if (smsMessage.length > 160) {\n    smsMessage = smsMessage.substring(0, 157) + '...';\n  }\n  \n  const smsPayload = {\n    to: notification.sms_data.to,\n    body: smsMessage,\n    from: '+1234567890' // Configure your SMS service number\n  };\n  \n  smsResults.push({\n    notification_id: notification.escalation_id + '_sms_' + Date.now(),\n    escalation_id: smsBatch.escalation_id,\n    type: 'sms',\n    target: notification.target,\n    payload: smsPayload,\n    delivery_metadata: {\n      created_at: new Date().toISOString(),\n      priority: smsBatch.priority,\n      retry_count: 0,\n      max_retries: notification.processing_metadata.max_retries\n    }\n  });\n});\n\nreturn [{\n  json: {\n    batch_type: 'sms',\n    escalation_id: smsBatch.escalation_id,\n    sms_notifications: smsResults,\n    batch_summary: {\n      total_notifications: smsResults.length,\n      priority: smsBatch.priority,\n      created_at: new Date().toISOString()\n    }\n  }\n}];"}, "id": "process-sms-notifications", "name": "📱 Process SMS Notifications", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1340, 300]}, {"parameters": {"jsCode": "// Process Chatwoot Notifications\nconst chatwootBatch = $input.first().json;\nconst notifications = chatwootBatch.notifications;\n\nconst chatwootResults = [];\n\nnotifications.forEach(notification => {\n  const chatwootPayload = {\n    conversation_id: notification.chatwoot_data.conversation_id,\n    content: notification.chatwoot_data.content,\n    message_type: notification.chatwoot_data.message_type || 'outgoing',\n    private: notification.chatwoot_data.private || false,\n    content_type: 'text',\n    content_attributes: {\n      escalation_id: chatwootBatch.escalation_id,\n      priority: chatwootBatch.priority,\n      automated: true,\n      timestamp: new Date().toISOString()\n    }\n  };\n  \n  // Add priority-based content formatting\n  if (chatwootBatch.priority === 'critical') {\n    chatwootPayload.content = `🚨 CRITICAL ESCALATION: ${chatwootPayload.content}`;\n  } else if (chatwootBatch.priority === 'high') {\n    chatwootPayload.content = `⚠️ HIGH PRIORITY: ${chatwootPayload.content}`;\n  }\n  \n  chatwootResults.push({\n    notification_id: notification.escalation_id + '_chatwoot_' + Date.now(),\n    escalation_id: chatwootBatch.escalation_id,\n    type: 'chatwoot',\n    target: notification.target,\n    payload: chatwootPayload,\n    delivery_metadata: {\n      created_at: new Date().toISOString(),\n      priority: chatwootBatch.priority,\n      retry_count: 0,\n      max_retries: notification.processing_metadata.max_retries\n    }\n  });\n});\n\nreturn [{\n  json: {\n    batch_type: 'chatwoot',\n    escalation_id: chatwootBatch.escalation_id,\n    chatwoot_notifications: chatwootResults,\n    batch_summary: {\n      total_notifications: chatwootResults.length,\n      priority: chatwootBatch.priority,\n      created_at: new Date().toISOString()\n    }\n  }\n}];"}, "id": "process-chatwoot-notifications", "name": "💬 Process Chatwoot Notifications", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1340, 400]}, {"parameters": {"url": "https://slack.com/api/chat.postMessage", "authentication": "predefinedCredentialType", "nodeCredentialType": "slackApi", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}, {"name": "Authorization", "value": "Bearer YOUR_<PERSON><PERSON><PERSON>_BOT_TOKEN"}]}, "sendBody": true, "bodyParameters": {"parameters": []}, "jsonBody": "={{ JSON.stringify($json.slack_notifications[0].payload) }}", "options": {"retry": {"enabled": true, "maxTries": 3}}}, "id": "send-slack-notification", "name": "💬 Send Slack Notification", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1560, 100]}, {"parameters": {"fromEmail": "<EMAIL>", "toEmail": "={{ $json.email_notifications[0].payload.to }}", "subject": "={{ $json.email_notifications[0].payload.subject }}", "emailFormat": "html", "message": "={{ $json.email_notifications[0].payload.html }}", "options": {"allowUnauthorizedCerts": true}}, "id": "send-email-notification", "name": "📧 Send Email Notification", "type": "n8n-nodes-base.emailSend", "typeVersion": 2.1, "position": [1560, 200], "credentials": {"smtp": {"id": "smtp-main", "name": "SMTP Main"}}}, {"parameters": {"url": "https://api.twilio.com/2010-04-01/Accounts/YOUR_ACCOUNT_SID/Messages.json", "authentication": "predefinedCredentialType", "nodeCredentialType": "t<PERSON><PERSON><PERSON><PERSON>", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/x-www-form-urlencoded"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "To", "value": "={{ $json.sms_notifications[0].payload.to }}"}, {"name": "From", "value": "={{ $json.sms_notifications[0].payload.from }}"}, {"name": "Body", "value": "={{ $json.sms_notifications[0].payload.body }}"}]}, "options": {"retry": {"enabled": true, "maxTries": 3}}}, "id": "send-sms-notification", "name": "📱 Send SMS Notification", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1560, 300]}, {"parameters": {"url": "https://your-chatwoot-instance.com/api/v1/accounts/YOUR_ACCOUNT_ID/conversations/{{ $json.chatwoot_notifications[0].payload.conversation_id }}/messages", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}, {"name": "api_access_token", "value": "YOUR_CHATWOOT_API_TOKEN"}]}, "sendBody": true, "bodyParameters": {"parameters": []}, "jsonBody": "={{ JSON.stringify($json.chatwoot_notifications[0].payload) }}", "options": {"retry": {"enabled": true, "maxTries": 3}}}, "id": "send-chatwoot-notification", "name": "💬 Send Chatwoot Notification", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1560, 400]}, {"parameters": {"jsCode": "// Log Notification Results\nconst notificationResult = $input.first().json;\n\n// Determine success/failure\nconst isSuccess = !notificationResult.error && (notificationResult.ok !== false);\nconst statusCode = notificationResult.statusCode || (isSuccess ? 200 : 500);\n\n// Create comprehensive log entry\nconst logEntry = {\n  notification_id: $node['Send Slack Notification']?.json?.slack_notifications?.[0]?.notification_id || \n                   $node['Send Email Notification']?.json?.email_notifications?.[0]?.notification_id ||\n                   $node['Send SMS Notification']?.json?.sms_notifications?.[0]?.notification_id ||\n                   $node['Send Chatwoot Notification']?.json?.chatwoot_notifications?.[0]?.notification_id ||\n                   'unknown',\n  escalation_id: $node['Send Slack Notification']?.json?.escalation_id || \n                 $node['Send Email Notification']?.json?.escalation_id ||\n                 $node['Send SMS Notification']?.json?.escalation_id ||\n                 $node['Send Chatwoot Notification']?.json?.escalation_id ||\n                 'unknown',\n  notification_type: $node['Send Slack Notification'] ? 'slack' :\n                     $node['Send Email Notification'] ? 'email' :\n                     $node['Send SMS Notification'] ? 'sms' :\n                     $node['Send Chatwoot Notification'] ? 'chatwoot' : 'unknown',\n  delivery_status: isSuccess ? 'delivered' : 'failed',\n  status_code: statusCode,\n  response_data: notificationResult,\n  delivered_at: new Date().toISOString(),\n  processing_metadata: {\n    delivery_attempt: 1,\n    processing_time_ms: Date.now() - (notificationResult.timestamp ? new Date(notificationResult.timestamp).getTime() : Date.now()),\n    error_details: notificationResult.error || null\n  }\n};\n\nreturn [{ json: logEntry }];"}, "id": "log-notification-result", "name": "📝 Log Notification Result", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1780, 300]}, {"parameters": {"operation": "insert", "schema": {"value": "agent"}, "table": {"value": "notification_logs"}, "columns": {"mappingMode": "defineBelow", "values": {"notification_id": "={{ $json.notification_id }}", "escalation_id": "={{ $json.escalation_id }}", "notification_type": "={{ $json.notification_type }}", "delivery_status": "={{ $json.delivery_status }}", "status_code": "={{ $json.status_code }}", "response_data": "={{ JSON.stringify($json.response_data) }}", "delivered_at": "={{ $json.delivered_at }}", "processing_metadata": "={{ JSON.stringify($json.processing_metadata) }}"}}, "options": {}}, "id": "save-notification-log", "name": "💾 Save Notification Log", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [2000, 300], "credentials": {"postgres": {"id": "postgres-main", "name": "PostgreSQL Main"}}}, {"parameters": {"jsCode": "// Handle Invalid Notifications\nconst batchData = $input.first().json;\n\nconst errorLog = {\n  batch_id: batchData.batch_metadata?.batch_id || 'unknown',\n  escalation_id: batchData.escalation_id || 'unknown',\n  error_type: 'invalid_notifications',\n  error_details: {\n    total_notifications: batchData.processing_result?.summary?.total_notifications || 0,\n    valid_notifications: batchData.processing_result?.summary?.valid_notifications || 0,\n    invalid_notifications: batchData.processing_result?.summary?.invalid_notifications || 0,\n    validation_errors: batchData.processing_result?.validation_results?.filter(v => !v.is_valid) || []\n  },\n  timestamp: new Date().toISOString(),\n  requires_manual_review: true\n};\n\nreturn [{ json: errorLog }];"}, "id": "handle-invalid-notifications", "name": "❌ Handle Invalid Notifications", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [900, 400]}], "connections": {"notification-webhook-trigger": {"main": [[{"node": "process-notification-batch", "type": "main", "index": 0}]]}, "process-notification-batch": {"main": [[{"node": "check-valid-notifications", "type": "main", "index": 0}]]}, "check-valid-notifications": {"main": [[{"node": "split-notifications-by-type", "type": "main", "index": 0}], [{"node": "handle-invalid-notifications", "type": "main", "index": 0}]]}, "split-notifications-by-type": {"main": [[{"node": "route-slack-notifications", "type": "main", "index": 0}, {"node": "route-email-notifications", "type": "main", "index": 0}, {"node": "route-sms-notifications", "type": "main", "index": 0}, {"node": "route-chatwoot-notifications", "type": "main", "index": 0}]]}, "route-slack-notifications": {"main": [[{"node": "process-slack-notifications", "type": "main", "index": 0}]]}, "route-email-notifications": {"main": [[{"node": "process-email-notifications", "type": "main", "index": 0}]]}, "route-sms-notifications": {"main": [[{"node": "process-sms-notifications", "type": "main", "index": 0}]]}, "route-chatwoot-notifications": {"main": [[{"node": "process-chatwoot-notifications", "type": "main", "index": 0}]]}, "process-slack-notifications": {"main": [[{"node": "send-slack-notification", "type": "main", "index": 0}]]}, "process-email-notifications": {"main": [[{"node": "send-email-notification", "type": "main", "index": 0}]]}, "process-sms-notifications": {"main": [[{"node": "send-sms-notification", "type": "main", "index": 0}]]}, "process-chatwoot-notifications": {"main": [[{"node": "send-chatwoot-notification", "type": "main", "index": 0}]]}, "send-slack-notification": {"main": [[{"node": "log-notification-result", "type": "main", "index": 0}]]}, "send-email-notification": {"main": [[{"node": "log-notification-result", "type": "main", "index": 0}]]}, "send-sms-notification": {"main": [[{"node": "log-notification-result", "type": "main", "index": 0}]]}, "send-chatwoot-notification": {"main": [[{"node": "log-notification-result", "type": "main", "index": 0}]]}, "log-notification-result": {"main": [[{"node": "save-notification-log", "type": "main", "index": 0}]]}}, "pinData": {}, "settings": {"executionOrder": "v1"}, "staticData": null, "tags": [{"createdAt": "2024-01-15T10:00:00.000Z", "updatedAt": "2024-01-15T10:00:00.000Z", "id": "notification", "name": "Notification"}, {"createdAt": "2024-01-15T10:00:00.000Z", "updatedAt": "2024-01-15T10:00:00.000Z", "id": "system", "name": "System"}, {"createdAt": "2024-01-15T10:00:00.000Z", "updatedAt": "2024-01-15T10:00:00.000Z", "id": "communication", "name": "Communication"}]}