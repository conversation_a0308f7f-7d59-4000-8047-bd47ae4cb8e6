{"name": "[SUB] Creator - Write Chapter v1.1 - Cost-Aware", "nodes": [{"parameters": {}, "id": "start_node", "name": "Start", "type": "n8n-nodes-base.start", "typeVersion": 1, "position": [200, 300], "notes": "Recebe de entrada: { infoproduct_id, opportunity_name, chapter_title, chapter_order }"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "INSERT INTO agent.infoproduct_chapters (infoproduct_id, chapter_title, chapter_order, status) VALUES ($1, $2, $3, 'processing') RETURNING id;", "options": {"parameters": {"values": ["={{$json.infoproduct_id}}", "={{$json.chapter_title}}", "={{$json.chapter_order}}"]}}}, "id": "db_log_chapter_start", "name": "DB: Log Chapter Start", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [420, 300], "credentials": {"postgres": {"id": "YOUR_POSTGRES_CREDENTIAL_ID"}}}, {"parameters": {"workflowId": "={{ $env.DISPATCHER_WORKFLOW_ID }}", "data": "={{ { body: { \n    prompt: `Você é um escritor especialista no tópico '${$json.opportunity_name}'.\\n\\nSua tarefa é escrever o conteúdo detalhado para a seguinte seção do nosso infoproduto:\\n\\nSEÇÃO: \\\"${$json.chapter_title}\\\"\\n\\nEscreva um conteúdo aprofundado, claro e prático sobre este tópico. Use o formato Markdown. Comece com um título h2 (##).`,\n    task_type: 'complex_analysis',\n    calling_workflow_id: $workflow.id,\n    calling_node_id: 'call_dispatcher_to_write'\n} } }}", "options": {}}, "id": "call_dispatcher_to_write", "name": "Call Dispatcher to Write", "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 2, "position": [640, 300], "continueOnFail": true}, {"parameters": {"operation": "execute<PERSON>uery", "query": "UPDATE agent.infoproduct_chapters SET chapter_content = $1, status = 'completed' WHERE id = $2;", "options": {"parameters": {"values": ["={{$json.choices[0].message.content}}", "={{$('db_log_chapter_start').item.json.id}}"]}}}, "id": "db_save_content", "name": "DB: Save Chapter Content", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [860, 300], "credentials": {"postgres": {"id": "YOUR_POSTGRES_CREDENTIAL_ID"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "UPDATE agent.infoproduct_chapters SET status = 'failed', error_details = $1 WHERE id = $2;", "options": {"parameters": {"values": ["={{$json.error.message}}", "={{$('db_log_chapter_start').item.json.id}}"]}}}, "id": "db_log_error", "name": "DB: <PERSON><PERSON>", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [640, 500], "credentials": {"postgres": {"id": "YOUR_POSTGRES_CREDENTIAL_ID"}}}, {"parameters": {"workflowId": "={{$env.AGGREGATOR_COMPILE_INFOPRODUCT_WORKFLOW_ID}}", "options": {"runIn": "background", "parameters": {"values": {"json": [{"name": "data", "value": "={{ { 'infoproduct_id': $('start_node').item.json.infoproduct_id } }}"}]}}}}, "id": "trigger_aggregator", "name": "TRIGGER: Aggregator", "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 2, "position": [1080, 300], "notes": "Notifica o agregador para verificar se o infoproduto está completo."}], "connections": {"start_node": {"main": [[{"node": "db_log_chapter_start"}]]}, "db_log_chapter_start": {"main": [[{"node": "call_dispatcher_to_write"}]]}, "call_dispatcher_to_write": {"main": [[{"node": "db_save_content"}]], "error": [[{"node": "db_log_error"}]]}, "db_save_content": {"main": [[{"node": "trigger_aggregator"}]]}, "db_log_error": {"main": [[{"node": "trigger_aggregator"}]]}}}