{
  "nodes": [
    {
      "parameters": {
        "httpMethod": "POST",
        "path": "google-forms-webhook"
      },
      "name": "Webhook",
      "type": "n8n-nodes-base.webhook",
      "typeVersion": 1,
      "position": [240, 300]
      // Recebe dados do Google Forms via webhook
    },
    {
      "parameters": {
        "phoneNumber": "={{$node['Webhook'].json.body.phone}}",
        "message": "Ol<PERSON>, recebemos seu formulário! Entraremos em contato em breve."
      },
      "name": "WhatsApp",
      "type": "n8n-nodes-base.whatsApp",
      "typeVersion": 1,
      "position": [460, 300],
      "credentials": {
        "whatsAppApi": "WhatsApp API Credentials"
      }
      // Envia mensagem via WhatsApp
    }
  ],
  "connections": {
    "Webhook": {
      "main": [
        [
          {
            "node": "WhatsApp",
            "type": "main",
            "index": 0
          }
        ]
      ]
    }
  }
}