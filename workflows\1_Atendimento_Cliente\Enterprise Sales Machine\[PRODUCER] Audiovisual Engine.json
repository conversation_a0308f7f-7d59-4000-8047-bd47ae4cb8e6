{"name": "[PRODUCER] Audiovisual Engine", "nodes": [{"parameters": {"path": "generate-video", "responseMode": "onReceived", "options": {}}, "id": "trigger_webhook", "name": "TRIGGER: Generate Video", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [400, 300], "notes": "Recebe: { \"text_input\": \"...\", \"image_source_url\": \"...\", \"credential_id\": \"did_api_key\" }"}, {"parameters": {"method": "POST", "url": "https://api.d-id.com/talks", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "accept", "value": "application/json"}, {"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "body": "={{ ({ \n  \"script\": { \n    \"type\": \"text\", \n    \"input\": $json.body.text_input \n  },\n  \"source_url\": $json.body.image_source_url,\n  \"config\": {\n    \"result_format\": \"mp4\"\n  }\n}) }}", "options": {}}, "id": "http_generate_video", "name": "HTTP: Generate via D-ID", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [620, 300], "notes": "Usa a API da D-ID como placeholder para a futura API VEO.", "credentials": {"httpHeaderAuth": {"id": "={{$json.body.credential_id}}", "name": "D-ID API Key"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "INSERT INTO agent.generated_media (media_type, source_text, remote_api, remote_id, status) VALUES ('video', $1, 'd-id', $2, 'processing');", "options": {"parameters": {"values": ["={{$node[\"trigger_webhook\"].json.body.text_input}}", "={{$json.id}}"]}}}, "id": "db_log_media", "name": "DB: Log Media Request", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [840, 300], "credentials": {"postgres": {"id": "YOUR_POSTGRES_CREDENTIAL_ID"}}}, {"parameters": {"content": "={{$items}}", "options": {}}, "id": "respond_success", "name": "Respond: Success", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1060, 300]}], "connections": {"trigger_webhook": {"main": [[{"node": "http_generate_video", "type": "main", "index": 0}]]}, "http_generate_video": {"main": [[{"node": "db_log_media", "type": "main", "index": 0}]]}, "db_log_media": {"main": [[{"node": "respond_success", "type": "main", "index": 0}]]}}}