{"name": "[VAULT] Credential Vault & Auto-Configurator v1.0", "nodes": [{"parameters": {"httpMethod": "POST", "path": "vault-config", "options": {"rawBody": true}}, "id": "vault_webhook", "name": "🔐 Vault Config <PERSON>hook", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [380, 300], "webhookId": "credential-vault-config"}, {"parameters": {"functionCode": "// Validar e processar configuração do vault\nconst config = $input.all()[0].json.body;\n\n// Verificar estrutura básica\nif (!config.services || !config.vault_info) {\n  return [{\n    json: {\n      error: 'Configuração inválida - faltam seções obrigatórias',\n      status: 'error'\n    }\n  }];\n}\n\n// Extrair informações básicas\nconst vaultInfo = config.vault_info;\nconst services = config.services;\n\n// Contar total de serviços\nlet totalServices = 0;\nfor (const category in services) {\n  totalServices += Object.keys(services[category]).length;\n}\n\n// Preparar dados para processamento\nreturn [{\n  json: {\n    vault_version: vaultInfo.version,\n    environment: vaultInfo.environment || 'production',\n    total_services: totalServices,\n    deployment_order: config.deployment_order || [],\n    services_config: services,\n    workflows_config: config.workflows || {},\n    environment_vars: config.environment_variables || {},\n    deployment_config: config.deployment_config || {},\n    notification_config: config.notification_config || {},\n    status: 'validated',\n    timestamp: new Date().toISOString()\n  }\n}];"}, "id": "validate_config", "name": "🔍 Validate Configuration", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [600, 300]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "config_valid", "leftValue": "={{ $json.status }}", "rightValue": "validated", "operator": {"type": "string", "operation": "equals"}}], "combineOperation": "and"}}, "id": "if_config_valid", "name": "IF: Config <PERSON>?", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [820, 300]}, {"parameters": {"respondWith": "json", "responseBody": "={{ { \"error\": $json.error, \"status\": \"failed\", \"timestamp\": new Date().toISOString() } }}"}, "id": "return_error", "name": "❌ Return Error", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1040, 420]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "-- Limpar dados existentes e inserir nova configuração\nTRUNCATE agent.credential_vault CASCADE;\nTRUNCATE agent.deployment_log CASCADE;\n\n-- Inserir log de início do deployment\nINSERT INTO agent.deployment_log (service_name, deployment_type, status, details)\nVALUES ('vault_system', 'full_deployment', 'started', $1::jsonb);", "options": {"parameters": {"values": ["={{ JSON.stringify({ vault_version: $json.vault_version, total_services: $json.total_services, timestamp: $json.timestamp }) }}"]}}}, "id": "db_init_deployment", "name": "🗄️ DB: Initialize Deployment", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [1040, 180], "credentials": {"postgres": {"id": "postgres-main"}}}, {"parameters": {"functionCode": "// Processar todos os serviços e preparar para inserção no banco\nconst servicesConfig = $json.services_config;\nconst deploymentOrder = $json.deployment_order;\n\nconst servicesToProcess = [];\nlet priorityCounter = 10;\n\n// Processar por ordem de deployment\nfor (const category of deploymentOrder) {\n  if (servicesConfig[category]) {\n    for (const [serviceName, serviceData] of Object.entries(servicesConfig[category])) {\n      servicesToProcess.push({\n        service_name: serviceName,\n        service_type: serviceData.service_type,\n        credential_data: JSON.stringify(serviceData.credentials || {}),\n        priority: serviceData.priority || priorityCounter,\n        dependencies: JSON.stringify(serviceData.dependencies || []),\n        auto_configure: serviceData.auto_configure !== false,\n        configuration_template: JSON.stringify(serviceData.n8n_setup || {}),\n        validation_endpoint: serviceData.health_check?.endpoint || null,\n        category: category\n      });\n      priorityCounter += 5;\n    }\n  }\n}\n\nreturn servicesToProcess.map(service => ({ json: service }));"}, "id": "process_services", "name": "⚙️ Process Services for DB", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [1260, 180]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "INSERT INTO agent.credential_vault (\n  service_name, service_type, credential_data, priority, dependencies, \n  auto_configure, configuration_template, validation_endpoint\n) VALUES (\n  $1, $2, $3::jsonb, $4, $5::varchar[], $6, $7::jsonb, $8\n) ON CONFLICT (service_name) DO UPDATE SET\n  service_type = EXCLUDED.service_type,\n  credential_data = EXCLUDED.credential_data,\n  priority = EXCLUDED.priority,\n  dependencies = EXCLUDED.dependencies,\n  configuration_template = EXCLUDED.configuration_template,\n  validation_endpoint = EXCLUDED.validation_endpoint,\n  updated_at = NOW();", "options": {"parameters": {"values": ["={{ $json.service_name }}", "={{ $json.service_type }}", "={{ $json.credential_data }}", "={{ $json.priority }}", "={{ $json.dependencies }}", "={{ $json.auto_configure }}", "={{ $json.configuration_template }}", "={{ $json.validation_endpoint }}"]}}}, "id": "db_insert_services", "name": "🗄️ DB: Insert Services", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [1480, 180], "credentials": {"postgres": {"id": "postgres-main"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "-- Obter ordem de deployment baseada em dependências\nSELECT service_name, priority, dependencies \nFROM get_deployment_order() \nORDER BY deployment_order;"}, "id": "db_get_deployment_order", "name": "🗄️ DB: Get Deployment Order", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [1700, 180], "credentials": {"postgres": {"id": "postgres-main"}}}, {"parameters": {"functionCode": "// Processar ordem de deployment e preparar para configuração\nconst deploymentOrder = $input.all();\nconst servicesToConfigure = [];\n\nfor (let i = 0; i < deploymentOrder.length; i++) {\n  const service = deploymentOrder[i].json;\n  servicesToConfigure.push({\n    service_name: service.service_name,\n    priority: service.priority,\n    dependencies: service.dependencies,\n    deployment_index: i + 1,\n    total_services: deploymentOrder.length\n  });\n}\n\nreturn servicesToConfigure.map(service => ({ json: service }));"}, "id": "prepare_deployment", "name": "📋 Prepare Deployment Queue", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [1920, 180]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "-- Aplicar configuração de um serviço específico\nSELECT apply_service_configuration($1);", "options": {"parameters": {"values": ["={{ $json.service_name }}"]}}}, "id": "db_configure_service", "name": "🔧 DB: Configure Service", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [2140, 180], "credentials": {"postgres": {"id": "postgres-main"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "-- Validar credenciais do serviço\nSELECT validate_service_credentials($1) as validation_result;", "options": {"parameters": {"values": ["={{ $json.service_name }}"]}}}, "id": "db_validate_service", "name": "✅ DB: Validate Service", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [2360, 180], "credentials": {"postgres": {"id": "postgres-main"}}}, {"parameters": {"functionCode": "// Processar resultado da validação\nconst serviceData = $('prepare_deployment').item.json;\nconst configResult = $('db_configure_service').item.json;\nconst validationResult = $json.validation_result;\n\n// Parse do resultado JSON se necessário\nlet validation;\ntry {\n  validation = typeof validationResult === 'string' ? JSON.parse(validationResult) : validationResult;\n} catch (e) {\n  validation = { status: 'error', message: 'Erro ao processar validação' };\n}\n\nreturn [{\n  json: {\n    service_name: serviceData.service_name,\n    deployment_index: serviceData.deployment_index,\n    total_services: serviceData.total_services,\n    configuration_status: configResult.status || 'unknown',\n    validation_status: validation.status,\n    validation_message: validation.message,\n    is_successful: validation.status === 'valid',\n    timestamp: new Date().toISOString()\n  }\n}];"}, "id": "process_service_result", "name": "📊 Process Service Result", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [2580, 180]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "is_last_service", "leftValue": "={{ $json.deployment_index }}", "rightValue": "={{ $json.total_services }}", "operator": {"type": "number", "operation": "equal"}}], "combineOperation": "and"}}, "id": "if_last_service", "name": "IF: Last Service?", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [2800, 180]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "-- G<PERSON><PERSON> relat<PERSON>rio final de deployment\nSELECT \n  COUNT(*) as total_services,\n  COUNT(CASE WHEN validation_status = 'valid' THEN 1 END) as successful_services,\n  COUNT(CASE WHEN validation_status = 'invalid' THEN 1 END) as failed_services,\n  COUNT(CASE WHEN validation_status = 'pending' THEN 1 END) as pending_services,\n  ROUND(COUNT(CASE WHEN validation_status = 'valid' THEN 1 END) * 100.0 / COUNT(*), 2) as success_rate\nFROM agent.credential_vault \nWHERE is_active = true;"}, "id": "db_generate_report", "name": "📊 DB: Generate Final Report", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [3020, 80], "credentials": {"postgres": {"id": "postgres-main"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "-- Atualizar log de deployment como concluído\nUPDATE agent.deployment_log \nSET status = 'success', completed_at = NOW(), \n    details = details || $1::jsonb\nWHERE service_name = 'vault_system' \nAND deployment_type = 'full_deployment' \nAND status = 'started';", "options": {"parameters": {"values": ["={{ JSON.stringify({ final_report: $json, timestamp: new Date().toISOString() }) }}"]}}}, "id": "db_finalize_deployment", "name": "🏁 DB: Finalize Deployment", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [3240, 80], "credentials": {"postgres": {"id": "postgres-main"}}}, {"parameters": {"functionCode": "// Preparar notificação para Slack\nconst report = $('db_generate_report').item.json;\nconst timestamp = Math.floor(Date.now() / 1000);\n\nconst message = {\n  text: '🔐 Credential Vault - Deployment Automático Concluído',\n  attachments: [\n    {\n      color: report.success_rate >= 80 ? 'good' : report.success_rate >= 60 ? 'warning' : 'danger',\n      fields: [\n        {\n          title: '✅ Serviços Configurados',\n          value: report.successful_services.toString(),\n          short: true\n        },\n        {\n          title: '❌ Falhas',\n          value: report.failed_services.toString(),\n          short: true\n        },\n        {\n          title: '⏳ Pendentes',\n          value: report.pending_services.toString(),\n          short: true\n        },\n        {\n          title: '📊 Taxa de Sucesso',\n          value: `${report.success_rate}%`,\n          short: true\n        }\n      ],\n      footer: 'Credential Vault Auto-Configurator v1.0',\n      ts: timestamp\n    }\n  ]\n};\n\nreturn [{ json: { slack_message: message, report: report } }];"}, "id": "prepare_slack_notification", "name": "📢 Prepare Slack Notification", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [3460, 80]}, {"parameters": {"authentication": "webhook", "url": "={{ $credentials.slack_webhook.url }}", "sendBody": true, "specifyBody": "json", "jsonBody": "={{ $json.slack_message }}"}, "id": "send_slack_notification", "name": "📱 Send Slack Notification", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [3680, 80], "credentials": {"slack_webhook": {"id": "slack-webhook"}}, "continueOnFail": true}, {"parameters": {"respondWith": "json", "responseBody": "={{ {\n  \"status\": \"success\",\n  \"message\": \"Credential Vault configurado com sucesso\",\n  \"deployment_report\": $('prepare_slack_notification').item.json.report,\n  \"timestamp\": new Date().toISOString(),\n  \"next_steps\": [\n    \"Verificar status de serviços no dashboard\",\n    \"Importar workflows necessários\",\n    \"Executar testes de validação\",\n    \"Configurar monitoramento contínuo\"\n  ]\n} }}"}, "id": "return_success", "name": "✅ Return Success Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [3900, 80]}, {"parameters": {"httpMethod": "GET", "path": "vault-status", "options": {}}, "id": "status_webhook", "name": "📊 Vault Status Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [380, 500], "webhookId": "vault-status"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "-- Obter status completo do vault\nSELECT \n  json_build_object(\n    'vault_status', json_build_object(\n      'total_services', COUNT(*),\n      'active_services', COUNT(CASE WHEN is_active = true THEN 1 END),\n      'valid_services', COUNT(CASE WHEN validation_status = 'valid' THEN 1 END),\n      'invalid_services', COUNT(CASE WHEN validation_status = 'invalid' THEN 1 END),\n      'pending_services', COUNT(CASE WHEN validation_status = 'pending' THEN 1 END),\n      'last_deployment', MAX(updated_at)\n    ),\n    'services_detail', json_agg(\n      json_build_object(\n        'service_name', service_name,\n        'service_type', service_type,\n        'validation_status', validation_status,\n        'is_active', is_active,\n        'last_validated', last_validated_at,\n        'priority', priority\n      ) ORDER BY priority\n    )\n  ) as vault_dashboard\nFROM agent.credential_vault;"}, "id": "db_get_vault_status", "name": "🗄️ DB: Get Vault Status", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [600, 500], "credentials": {"postgres": {"id": "postgres-main"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "-- Obter logs recentes de deployment\nSELECT \n  service_name,\n  deployment_type,\n  status,\n  started_at,\n  completed_at,\n  execution_time_ms,\n  error_message\nFROM agent.deployment_log \nWHERE started_at > NOW() - INTERVAL '24 hours'\nORDER BY started_at DESC\nLIMIT 20;"}, "id": "db_get_recent_logs", "name": "🗄️ DB: Get <PERSON> Logs", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [820, 500], "credentials": {"postgres": {"id": "postgres-main"}}}, {"parameters": {"functionCode": "// Combinar dados do vault status e logs\nconst vaultData = $('db_get_vault_status').item.json.vault_dashboard;\nconst recentLogs = $('db_get_recent_logs').all().map(item => item.json);\n\nconst response = {\n  timestamp: new Date().toISOString(),\n  vault_status: vaultData.vault_status,\n  services: vaultData.services_detail,\n  recent_deployments: recentLogs,\n  health_summary: {\n    overall_health: vaultData.vault_status.valid_services / vaultData.vault_status.total_services >= 0.8 ? 'healthy' : 'degraded',\n    services_needing_attention: vaultData.services_detail.filter(s => s.validation_status !== 'valid').length,\n    last_activity: vaultData.vault_status.last_deployment\n  }\n};\n\nreturn [{ json: response }];"}, "id": "prepare_status_response", "name": "📋 Prepare Status Response", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [1040, 500]}, {"parameters": {"respondWith": "json", "responseBody": "={{ $json }}"}, "id": "return_status", "name": "📊 Return Status", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1260, 500]}, {"parameters": {"httpMethod": "POST", "path": "vault-validate", "options": {}}, "id": "validate_webhook", "name": "🔍 Vault Validate Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [380, 700], "webhookId": "vault-validate"}, {"parameters": {"functionCode": "// Extrair serviço a ser validado do body\nconst body = $input.all()[0].json.body;\nconst serviceName = body.service_name || 'all';\n\nreturn [{\n  json: {\n    service_name: serviceName,\n    validate_all: serviceName === 'all',\n    timestamp: new Date().toISOString()\n  }\n}];"}, "id": "prepare_validation", "name": "⚙️ Prepare Validation", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [600, 700]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "validate_all", "leftValue": "={{ $json.validate_all }}", "rightValue": true, "operator": {"type": "boolean", "operation": "equal"}}], "combineOperation": "and"}}, "id": "if_validate_all", "name": "IF: Validate All?", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [820, 700]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "-- Validar todos os serviços\nSELECT \n  service_name,\n  validate_service_credentials(service_name) as validation_result\nFROM agent.credential_vault \nWHERE is_active = true \nORDER BY priority;"}, "id": "db_validate_all", "name": "✅ DB: Validate All Services", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [1040, 600], "credentials": {"postgres": {"id": "postgres-main"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "-- Validar serviço específico\nSELECT \n  $1 as service_name,\n  validate_service_credentials($1) as validation_result;", "options": {"parameters": {"values": ["={{ $json.service_name }}"]}}}, "id": "db_validate_single", "name": "✅ DB: Validate Single Service", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [1040, 800], "credentials": {"postgres": {"id": "postgres-main"}}}, {"parameters": {"functionCode": "// Processar resultados de validação\nconst validationResults = $input.all().map(item => {\n  const result = item.json;\n  let validation;\n  \n  try {\n    validation = typeof result.validation_result === 'string' \n      ? JSON.parse(result.validation_result) \n      : result.validation_result;\n  } catch (e) {\n    validation = { status: 'error', message: 'Erro ao processar resultado' };\n  }\n  \n  return {\n    service_name: result.service_name,\n    status: validation.status,\n    message: validation.message,\n    timestamp: validation.timestamp || new Date().toISOString()\n  };\n});\n\n// Calcular estatísticas\nconst totalServices = validationResults.length;\nconst validServices = validationResults.filter(r => r.status === 'valid').length;\nconst invalidServices = validationResults.filter(r => r.status === 'invalid').length;\nconst errorServices = validationResults.filter(r => r.status === 'error').length;\n\nconst response = {\n  timestamp: new Date().toISOString(),\n  validation_summary: {\n    total_services: totalServices,\n    valid_services: validServices,\n    invalid_services: invalidServices,\n    error_services: errorServices,\n    success_rate: totalServices > 0 ? (validServices / totalServices * 100).toFixed(2) : 0\n  },\n  validation_results: validationResults,\n  status: validServices === totalServices ? 'all_valid' : 'issues_found'\n};\n\nreturn [{ json: response }];"}, "id": "process_validation_results", "name": "📊 Process Validation Results", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [1260, 700]}, {"parameters": {"respondWith": "json", "responseBody": "={{ $json }}"}, "id": "return_validation_results", "name": "📋 Return Validation Results", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1480, 700]}], "connections": {"vault_webhook": {"main": [[{"node": "validate_config", "type": "main", "index": 0}]]}, "validate_config": {"main": [[{"node": "if_config_valid", "type": "main", "index": 0}]]}, "if_config_valid": {"main": [[{"node": "db_init_deployment", "type": "main", "index": 0}], [{"node": "return_error", "type": "main", "index": 0}]]}, "db_init_deployment": {"main": [[{"node": "process_services", "type": "main", "index": 0}]]}, "process_services": {"main": [[{"node": "db_insert_services", "type": "main", "index": 0}]]}, "db_insert_services": {"main": [[{"node": "db_get_deployment_order", "type": "main", "index": 0}]]}, "db_get_deployment_order": {"main": [[{"node": "prepare_deployment", "type": "main", "index": 0}]]}, "prepare_deployment": {"main": [[{"node": "db_configure_service", "type": "main", "index": 0}]]}, "db_configure_service": {"main": [[{"node": "db_validate_service", "type": "main", "index": 0}]]}, "db_validate_service": {"main": [[{"node": "process_service_result", "type": "main", "index": 0}]]}, "process_service_result": {"main": [[{"node": "if_last_service", "type": "main", "index": 0}]]}, "if_last_service": {"main": [[{"node": "db_generate_report", "type": "main", "index": 0}], []]}, "db_generate_report": {"main": [[{"node": "db_finalize_deployment", "type": "main", "index": 0}]]}, "db_finalize_deployment": {"main": [[{"node": "prepare_slack_notification", "type": "main", "index": 0}]]}, "prepare_slack_notification": {"main": [[{"node": "send_slack_notification", "type": "main", "index": 0}]]}, "send_slack_notification": {"main": [[{"node": "return_success", "type": "main", "index": 0}]]}, "status_webhook": {"main": [[{"node": "db_get_vault_status", "type": "main", "index": 0}]]}, "db_get_vault_status": {"main": [[{"node": "db_get_recent_logs", "type": "main", "index": 0}]]}, "db_get_recent_logs": {"main": [[{"node": "prepare_status_response", "type": "main", "index": 0}]]}, "prepare_status_response": {"main": [[{"node": "return_status", "type": "main", "index": 0}]]}, "validate_webhook": {"main": [[{"node": "prepare_validation", "type": "main", "index": 0}]]}, "prepare_validation": {"main": [[{"node": "if_validate_all", "type": "main", "index": 0}]]}, "if_validate_all": {"main": [[{"node": "db_validate_all", "type": "main", "index": 0}], [{"node": "db_validate_single", "type": "main", "index": 0}]]}, "db_validate_all": {"main": [[{"node": "process_validation_results", "type": "main", "index": 0}]]}, "db_validate_single": {"main": [[{"node": "process_validation_results", "type": "main", "index": 0}]]}, "process_validation_results": {"main": [[{"node": "return_validation_results", "type": "main", "index": 0}]]}}, "pinData": {}, "settings": {"saveManualExecutions": true, "callerPolicy": "workflowsFromSameOwner", "errorWorkflow": "[MONITOR] Alert Manager"}, "staticData": null, "tags": [{"createdAt": "2024-01-20T10:00:00.000Z", "updatedAt": "2024-01-20T10:00:00.000Z", "id": "vault-system", "name": "vault-system"}], "triggerCount": 0, "updatedAt": "2024-01-20T10:00:00.000Z", "versionId": "1.0"}