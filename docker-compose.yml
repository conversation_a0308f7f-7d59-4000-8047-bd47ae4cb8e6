version: '3.8'

services:
  postgres:
    image: postgres:15
    container_name: postgres_aula
    environment:
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_HOST_AUTH_METHOD: trust
    volumes:
      - ./postgres_data:/var/lib/postgresql/data
    networks:
      - app_network
    ports:
      - "5432:5432"
    restart: always
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  redis:
    image: redis:7
    container_name: redis_aula
    command: redis-server --appendonly yes --tcp-keepalive 60 --tcp-backlog 511 --maxclients 10000 --timeout 300
    networks:
      - app_network
    ports:
      - "6379:6379"  # Expor porta para debug/diagnóstico
    volumes:
      - ./redis_data:/data
    restart: always
    sysctls:
      - net.core.somaxconn=1024
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 10s

  minio:
    image: minio/minio:latest
    container_name: minio_aula
    command: server /data --console-address ":9001"
    networks:
      - app_network
    ports:
      - "9000:9000"
      - "9001:9001"
    environment:
      MINIO_ROOT_USER: ${MINIO_ROOT_USER}
      MINIO_ROOT_PASSWORD: ${MINIO_ROOT_PASSWORD}
    volumes:
      - ./minio_data:/data
    restart: always
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/ready"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 20s

  evolution_v2:
    image: atendai/evolution-api:latest
    container_name: evolution_aula
    networks:
      - app_network
    ports:
      - "8080:8080"
    volumes:
      - ./evolution_data:/evolution/instances
    environment:
      - DATABASE_CONNECTION_URI=postgresql://postgres:${POSTGRES_PASSWORD}@postgres:5432/evolution
      - DATABASE_URL=postgresql://postgres:${POSTGRES_PASSWORD}@postgres:5432/evolution
      - DATABASE_PROVIDER=postgresql
      - S3_ACCESS_KEY=${MINIO_ROOT_USER}
      - S3_SECRET_KEY=${MINIO_ROOT_PASSWORD}
      - S3_BUCKET=evolution
      - S3_ENDPOINT=http://minio:9000
      - S3_PORT=9000
      - S3_USE_SSL=false
      - S3_FORCE_PATH_STYLE=true
      - CACHE_REDIS_ENABLED=true
      - CACHE_REDIS_URI=redis://redis:6379/6
      - CACHE_REDIS_PREFIX_KEY=evolution
      - CACHE_REDIS_SAVE_INSTANCES=false
      - CACHE_LOCAL_ENABLED=false
      - REDIS_URI=redis://redis:6379/6
      - REDIS_URL=redis://redis:6379/6
      - AUTHENTICATION_API_KEY=${AUTHENTICATION_API_KEY}
      - LOG_LEVEL=debug
      - LOG_COLOR=true
      - NODE_ENV=production
      - CONFIG_SESSION_PHONE_VERSION=${CONFIG_SESSION_PHONE_VERSION:-2.3000.**********}
      - STORE_MESSAGES=true
      - STORE_MESSAGE_UP=true
      - STORE_CONTACTS=true
      - STORE_CHATS=true
      - WEBHOOK_EVENTS="MESSAGES_UPSERT,MESSAGES_UPDATE,MESSAGES_DELETE,SEND_MESSAGE,CONTACTS_UPDATE,CONTACTS_UPSERT,PRESENCE_UPDATE,CHATS_UPDATE,CHATS_UPSERT,CHATS_DELETE,GROUPS_UPSERT,GROUPS_UPDATE,GROUP_PARTICIPANTS_UPDATE,CONNECTION_UPDATE,CALL"
      - SESSION_TIMEOUT=300
      - SESSION_STORE_TYPE=redis
      - SESSION_STORE_REDIS_URI=redis://redis:6379/6
      - SESSION_STORE_REDIS_PREFIX="evolution:session:"
      - SESSION_STORE_REDIS_TTL=86400
      - RATE_LIMIT_ENABLED=false
      - CORS_ENABLED=true
      - CORS_ORIGIN=*
      - CORS_METHODS=GET,POST,PUT,DELETE,OPTIONS,PATCH
      - CORS_CREDENTIALS=true
      - WEBSOCKET_ENABLED=true
      - WEBSOCKET_GLOBAL_EVENTS=true
      - WEBHOOK_GLOBAL=${WEBHOOK_URL}/webhook/evolution
      - UPLOAD_FILE_ENABLED=true
      - UPLOAD_FILE_PATH=/evolution/instances
      - UPLOAD_FILE_SIZE_LIMIT=100
      - BACKUP_ENABLED=true
      - BACKUP_PATH=/evolution/backups
    restart: always
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      minio:
        condition: service_healthy
    healthcheck:
      test: ["CMD-SHELL", "wget --no-verbose --tries=1 --spider http://localhost:8080 || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  chatwoot-rails:
    image: chatwoot/chatwoot:v4.0.0
    container_name: chatwoot-rails-1
    ports:
      - "3000:3000"
    networks:
      - app_network
    environment:
      - POSTGRES_HOST=postgres
      - POSTGRES_USERNAME=postgres
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_DATABASE=chatwoot
      - REDIS_URL=redis://redis:6379
      - SECRET_KEY_BASE=${SECRET_KEY_BASE}
    command: bundle exec rails s -p 3000 -b 0.0.0.0
    depends_on:
      - postgres
      - redis

  chatwoot-sidekiq:
    image: chatwoot/chatwoot:v4.0.0
    container_name: chatwoot-sidekiq-1
    networks:
      - app_network
    environment:
      - POSTGRES_HOST=postgres
      - POSTGRES_USERNAME=postgres
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_DATABASE=chatwoot
      - REDIS_URL=redis://redis:6379
      - SECRET_KEY_BASE=${SECRET_KEY_BASE}
    command: bundle exec sidekiq -C config/sidekiq.yml
    depends_on:
      chatwoot-rails:
        condition: service_started

  n8n_editor:
    image: n8nio/n8n:latest
    container_name: n8n_editor-1
    ports:
      - "5678:5678"
    networks:
      - app_network
    environment:
      - N8N_ENCRYPTION_KEY=${N8N_ENCRYPTION_KEY}
      - DB_TYPE=postgresdb
      - DB_POSTGRESDB_HOST=postgres
      - DB_POSTGRESDB_PORT=5432
      - DB_POSTGRESDB_DATABASE=n8n_fila
      - DB_POSTGRESDB_USER=postgres
      - N8N_COMMUNITY_PACKAGES_ALLOW_TOOL_USAGE=true
      - DB_POSTGRESDB_PASSWORD=${POSTGRES_PASSWORD}
      - WEBHOOK_URL=${WEBHOOK_URL:-http://${HOST_IP}:5678/}
    volumes:
      - ./n8n_data:/home/<USER>/.n8n
    restart: always
    depends_on:
      - postgres
    user: "0:0"

  # Serviço Ngrok opcional, ativado via profile
  ngrok:
    image: ngrok/ngrok:latest
    container_name: ngrok_tunnel
    command: http n8n_editor:5678 # Expõe o serviço n8n na porta 5678
    environment:
      - NGROK_AUTHTOKEN=${NGROK_AUTHTOKEN}
    ports:
      - "4040:4040" # Porta para a API de inspeção do ngrok
    networks:
      - app_network
    profiles:
      - ngrok # Este serviço só será iniciado com --profile ngrok
    depends_on:
      - n8n_editor

volumes:
  postgres_data:
  redis_data:
  minio_data:
  evolution_data:
  n8n_data:

networks:
  app_network:
    external: true
