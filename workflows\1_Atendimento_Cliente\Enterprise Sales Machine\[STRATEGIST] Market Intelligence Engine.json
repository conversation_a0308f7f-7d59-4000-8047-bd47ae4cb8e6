{"name": "[STRATEGIST] Market Intelligence Engine v1.1 - Cost-Aware", "nodes": [{"parameters": {"rule": "cron", "cronTime": "0 8 * * 1"}, "id": "trigger_weekly", "name": "TRIGGER: Weekly (Mon 8am)", "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1.1, "position": [400, 300]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT content FROM agent.memory_vectors WHERE timestamp > NOW() - INTERVAL '7 days' LIMIT 300;", "options": {}}, "id": "db_get_memories", "name": "DB: Get Last Week's Conversations", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [640, 200], "credentials": {"postgres": {"id": "YOUR_POSTGRES_CREDENTIAL_ID"}}}, {"parameters": {"search_term": "new business ideas and trends in [SEU_NICHO_PRINCIPAL]", "explanation": "Busca tendências de negócio no seu nicho."}, "id": "web_search_trends", "name": "WEB: Search Market Trends", "type": "n8n-nodes-base.webSearch", "typeVersion": 1, "position": [640, 400]}, {"parameters": {}, "id": "merge_data_sources", "name": "Merge Data Sources", "type": "n8n-nodes-base.merge", "typeVersion": 2, "position": [840, 300]}, {"parameters": {"workflowId": "={{ $env.DISPATCHER_WORKFLOW_ID }}", "data": "={{ { body: { \n    prompt: `<PERSON><PERSON><PERSON> <PERSON> o Diretor de Estratégia (CSO). Analise os dados e gere um briefing e um plano de ação em JSON.\\n\\n**DADOS INTERNOS:**\\n${$items(\"DB: Get Last Week's Conversations\").map(i => i.json.content).join('\\n---\\n')}\\n\\n**DADOS EXTERNOS:**\\n${$items(\"WEB: Search Market Trends\").map(i => i.json.snippet).join('\\n---\\n')}\\n\\n**SAÍDA JSON:**\\n{\\n  \"intelligence_report\": {\\n    \"profitable_niches\": [{\\\"niche\\\": \\\"...\\\", \\\"reasoning\\\": \\\"...\\\"}],\\n    \"emerging_trends\": [{\\\"trend\\\": \\\"...\\\", \\\"reasoning\\\": \\\"...\\\"}],\\n    \"key_takeaways\": \\\"...\\\"\\n  },\\n  \"action_plan\": {\\n    \"should_create_campaign\\\": true,\\n    \"suggested_campaign_draft\\\": { ... },\\n    \"should_create_goal\\\": true,\\n    \"suggested_sales_goal\\\": { ... }\\n  }\\n}`,\n    task_type: 'complex_analysis',\n    calling_workflow_id: $workflow.id,\n    calling_node_id: 'call_dispatcher_for_strategy'\n} } }}", "options": {}}, "id": "call_dispatcher_for_strategy", "name": "Call Dispatcher for Strategy", "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 2, "position": [1080, 300]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "INSERT INTO agent.market_insights (report_type, profitable_niches, emerging_trends, new_service_opportunities, key_takeaways) VALUES ('Weekly Strategic Briefing', $1::jsonb, $2::jsonb, $3::jsonb, $4) RETURNING id;", "options": {"parameters": {"values": ["={{JSON.stringify($json.intelligence_report.profitable_niches)}}", "={{JSON.stringify($json.intelligence_report.emerging_trends)}}", "={{JSON.stringify($json.intelligence_report.new_service_opportunities)}}", "={{$json.intelligence_report.key_takeaways}}"]}}}, "id": "db_save_insights", "name": "DB: Save Market Insights", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [1320, 300], "credentials": {"postgres": {"id": "YOUR_POSTGRES_CREDENTIAL_ID"}}}, {"parameters": {"conditions": {"boolean": [{"value1": "={{$json.action_plan.should_create_campaign}}"}]}}, "id": "if_create_campaign", "name": "IF: Create Campaign?", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [1540, 300]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "INSERT INTO agent.campaigns (name, description, base_prompt, status) VALUES ($1, $2, $3, 'draft') RETURNING id;", "options": {"parameters": {"values": ["={{$json.action_plan.suggested_campaign_draft.name}}", "={{$json.action_plan.suggested_campaign_draft.target_description}}", "={{$json.action_plan.suggested_campaign_draft.base_prompt_idea}}"]}}}, "id": "db_create_draft_campaign", "name": "DB: Create Draft Campaign", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [1760, 200], "credentials": {"postgres": {"id": "YOUR_POSTGRES_CREDENTIAL_ID"}}}, {"parameters": {}, "id": "merge_campaign_flow", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.merge", "typeVersion": 2, "position": [1760, 400]}, {"parameters": {"conditions": {"boolean": [{"value1": "={{$json.action_plan.should_create_goal}}"}]}}, "id": "if_create_goal", "name": "IF: Create Goal?", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [1980, 400]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "INSERT INTO agent.sales_goals (name, metric_key, target_value, period_start_date, period_end_date) \nVALUES (\n  $1, \n  $2, \n  (SELECT COALESCE(MAX(target_value), 100) * (1 + $3/100.0) FROM agent.sales_goals WHERE metric_key = $2), \n  CURRENT_DATE, \n  CURRENT_DATE + INTERVAL '30 days'\n)\nRETURNING id;", "options": {"parameters": {"values": ["={{$json.action_plan.suggested_sales_goal.name}}", "={{$json.action_plan.suggested_sales_goal.metric_key}}", "={{$json.action_plan.suggested_sales_goal.target_value_increase_percentage}}"]}}}, "id": "db_create_draft_goal", "name": "DB: Create Draft Goal", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [2200, 300], "credentials": {"postgres": {"id": "YOUR_POSTGRES_CREDENTIAL_ID"}}}, {"parameters": {}, "id": "merge_goal_flow", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.merge", "typeVersion": 2, "position": [2200, 500]}, {"parameters": {"method": "POST", "url": "={{$credentials.slackWebhook.url}}", "body": "={{ ({ 'text': `*📈 Relatório e Plano de Ação do Diretor*\\n\\nO Diretor de Estratégia concluiu a análise da semana. O relatório está disponível no banco de dados.\\n\\n*Principais Recomendações:*\\n${$json.intelligence_report.key_takeaways}\\n\\n*Ações Iniciadas:* Uma campanha e uma meta foram pré-rascunhadas para sua revisão.` }) }}"}, "id": "slack_notify_manager", "name": "SLACK: Notify Manager", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [2420, 400], "credentials": {"httpHeaderAuth": {"id": "YOUR_SLACK_WEBHOOK_CREDENTIAL"}}}], "connections": {"trigger_weekly": {"main": [[{"node": "db_get_memories"}, {"node": "web_search_trends"}]]}, "db_get_memories": {"main": [[{"node": "merge_data_sources"}]]}, "web_search_trends": {"main": [[{"node": "merge_data_sources", "type": "main", "index": 1}]]}, "merge_data_sources": {"main": [[{"node": "call_dispatcher_for_strategy"}]]}, "call_dispatcher_for_strategy": {"main": [[{"node": "db_save_insights"}]]}, "db_save_insights": {"main": [[{"node": "if_create_campaign"}]]}, "if_create_campaign": {"main": [[{"node": "db_create_draft_campaign"}], [{"node": "merge_campaign_flow"}]]}, "db_create_draft_campaign": {"main": [[{"node": "merge_campaign_flow"}]]}, "merge_campaign_flow": {"main": [[{"node": "if_create_goal"}]]}, "if_create_goal": {"main": [[{"node": "db_create_draft_goal"}], [{"node": "merge_goal_flow"}]]}, "db_create_draft_goal": {"main": [[{"node": "merge_goal_flow"}]]}, "merge_goal_flow": {"main": [[{"node": "slack_notify_manager"}]]}}}