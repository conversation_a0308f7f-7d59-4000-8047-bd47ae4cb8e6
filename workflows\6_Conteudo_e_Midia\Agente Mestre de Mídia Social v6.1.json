{
  "name": "Agente Mestre de Mídia Social v6.2 (A Edição Ultra-Resiliente e Consciente - Totalmente Expandida)",
  "nodes": [
    {
      "parameters": {
        "workflowInputs": {
          "values": [
            {
              "name": "user_id",
              "type": "string",
              "value": "user_v6.2_ultimate_test_001"
            },
            {
              "name": "network",
              "type": "string",
              "value": "X"
            },
            {
              "name": "task",
              "type": "string",
              "value": "create_post"
            },
            {
              "name": "task_details",
              "type": "json",
              "json": "{ \"topic\": \"N8N Workflow Automation Summit! #n8n #automation #AI\", \"cache_key\": \"x_post_n8n_summit_v62_001\", \"retrieved_context\": [] }"
            }
          ]
        }
      },
      "type": "n8n-nodes-base.executeWorkflowTrigger",
      "typeVersion": 1.1,
      "position": [ -2600, 600 ],
      "id": "START_NODE",
      "name": "Start"
    },
    {
      "parameters": {
        "content": "### 🚀 Agente Mestre de Mídia Social v6.2 (A Edição Ultra-Resiliente e Consciente - Totalmente Expandida)\n\n**Propósito**: Implementação exaustiva e TOTALMENTE FUNCIONAL de API de IA Backup para todas as tarefas/redes, fallback para Redis com cache local (simulado com SQLite/DB), TTL para cache tradicional, hashing, criptografia, monitoramento e testes automatizados. Esta versão expande todas as 15 combinações de rede/tarefa com seus agentes primários específicos e lógica de fluxo.\n\n**Arquitetura (v6.2)**: \n1.  **Entrada Global (`START_NODE`, `V51_SCHEDULE_TRIGGER`, `V62_TEST_AUTOMATE`)**\n2.  **Preparação de Chaves e Segurança (`V61_PREPARE_REDIS_KEYS`)**: Gera `user_id_hashed` (SHA-256), chaves de contexto, e carrega `encryption_key` (das credenciais n8n).\n3.  **Gerenciamento de Contexto (Redis c/ Fallback SQLite e Criptografia AES)**:\n    *   `V61_REDIS_RETRIEVE_CONTEXT_ENCRYPTED`: Tenta buscar do Redis.\n    *   `V62_DECRYPT_REDIS_CONTEXT`: Decriptografa.\n    *   `V62_CHECK_REDIS_RETRIEVE_FAIL` + `V62_SQLITE_RETRIEVE_FALLBACK_ENCRYPTED` + `V62_DECRYPT_LOCAL_CACHE_CONTEXT`: Se Redis/decriptografia falhar, busca do SQLite e decriptografa.\n    *   `V61_INJECT_RETRIEVED_CONTEXT`: Unifica e injeta contexto (últimas 5 interações) em `task_details`.\n4.  **Validação e Análise Inicial (`V55_VALIDATE_INFO`, `IF_INVALID_GLOBAL_INPUT`, `V55_ANALYZE_MEDIA`)**\n5.  **Roteamento por Rede (`V5_ROUTE_NETWORK`)**\n6.  **Módulos de Tarefa por Rede (5 Redes x 3 Tarefas = 15 Fluxos Detalhados)**:\n    *   `V54_ROUTE_<REDE>_TASK`: Roteador interno da tarefa.\n    *   Para CADA tarefa:\n        *   `V62_VALIDATE_<REDE>_<TAREFA>` (Validação Específica)\n        *   `V62_AUTH_<REDE>` (Autenticação, pode ser compartilhada na rede)\n        *   `V62_CHECK_TOKEN_<REDE>` e `V62_RENEW_TOKEN_<REDE>`\n        *   `V62_SENTIMENT_<REDE>_<TAREFA>` (Análise de Sentimento)\n        *   `V62_CACHE_CHECK_<REDE>_<TAREFA>` (Cache Tradicional Check)\n        *   Se Cache Miss:\n            *   `V62_AGENT_<REDE>_<TAREFA>` (Agente Primário: Langchain com prompt específico)\n            *   Conexão com `V62_CHECK_PRIMARY_API_FAIL_GENERIC` -> `V62_CALL_BACKUP_API_GENERIC` -> `V62_CHECK_BACKUP_API_FAIL_GENERIC` -> `V62_MERGE_API_RESPONSES_GENERIC`\n            *   `V62_CACHE_SAVE_<REDE>_<TAREFA>` (Cache Tradicional Save com `ttl_seconds: 604800`)\n        *   `V62_TOOL_<REDE>_<TAREFA>` (Ferramenta simulada da Rede)\n        *   `V62_LOG_<REDE>_<TAREFA>` (Log Detalhado)\n7.  **Salvamento de Contexto (Redis c/ Fallback SQLite, TTL e Criptografia AES)**:\n    *   Todos os fluxos de tarefa convergem para `V62_ENCRYPT_CONTEXT_FOR_STORAGE`.\n    *   `V62_ENCRYPT_CONTEXT_FOR_STORAGE` -> `V61_REDIS_SAVE_CONTEXT`\n    *   `V62_CHECK_REDIS_SAVE_FAIL` + `V62_SQLITE_SAVE_FALLBACK`\n    *   `V62_MERGE_SAVE_STATUSES`\n8.  **Saída e Monitoramento**: `V5_MERGE_FINAL` -> `V55_EVOLUTION_API_NOTIFY` -> `V5_OUTPUT_USER` -> `V62_AGGREGATE_API_METRICS` -> `V5_FEEDBACK_CHATWOOT`.\n9.  **Tratamento de Erro Global**: `V5_ERROR_TRIGGER`.\n\n**Instruções de Teste Automatizado (`V62_TEST_AUTOMATE`)**: \n- Simula execuções para as 15 combinações de rede/tarefa.\n- Verifica se `final_agent_output` (ou `log_entry.success` para relatórios) é gerado.\n- Falhas acionam `V5_ERROR_TRIGGER`.\n\n**Nota sobre Credenciais**: Todas as chaves API, segredos e a chave de criptografia AES devem ser configuradas nas credenciais do n8n.",
        "height": 1300, "width": 800, "color": 10
      },
      "type": "n8n-nodes-base.stickyNote",
      "position": [ -2640, -1000 ],
      "typeVersion": 1,
      "id": "V5_NOTE_ARCH",
      "name": "Nota da Arquitetura v6.2 (Ultra-Resiliente e Expandida)"
    },
    {
      "parameters": {
        "functionCode": "const crypto = require('crypto');\nconst userIdOriginal = items[0].json.user_id || $('START_NODE').first().json.user_id;\n\nif (!userIdOriginal) {\n  throw new Error('user_id is missing from input.');\n}\n\nitems[0].json.user_id_original = userIdOriginal;\nitems[0].json.user_id_hashed = crypto.createHash('sha256').update(userIdOriginal).digest('hex');\nitems[0].json.redis_key_context = `user_ctx:${items[0].json.user_id_hashed}`;\nitems[0].json.local_cache_key_context = `local_ctx_${items[0].json.user_id_hashed}`;\n\nconst creds = $credentials.DefaultAESKey; \nif (!creds || !creds.key || creds.key.length !== 32) {\n  throw new Error('AES Encryption Key (32 bytes) not found or invalid in n8n credentials DefaultAESKey.key');\n}\nitems[0].json.encryption_key = creds.key;\nitems[0].json.encryption_iv_length = 16;\n\nreturn items;"
      },
      "id": "V61_PREPARE_REDIS_KEYS",
      "name": "V6.2 🔑 Prepare Keys & Crypto Vars",
      "type": "n8n-nodes-base.function",
      "typeVersion": 1,
      "position": [ -2380, 600 ]
    },
    {
      "parameters": {
        "url": "https://mock-redis-api.n8n.example/get",
        "options": { "responseData": "={{ $json.body }}", "timeout": 3000 },
        "bodyParameters": { "parameters": [ { "name": "key", "value": "={{ $('V61_PREPARE_REDIS_KEYS').first().json.redis_key_context }}" } ] },
        "authentication": "predefinedCredential",
        "nodeCredentialType": "redisApiBasic", 
        "sendHeaders": true,
        "headerParameters": { "parameters": [ { "name": "Content-Type", "value": "application/json" } ] }
      },
      "id": "V61_REDIS_RETRIEVE_CONTEXT_ENCRYPTED",
      "name": "🚀 [Redis] Retrieve Encrypted Context",
      "type": "n8n-nodes-base.httpRequest",
      "typeVersion": 4.1,
      "position": [ -2160, 480 ],
      "continueOnFail": true,
      "credentials": { "redisApiBasic": { "id": "yourRedisAuthCredentialID", "name": "DefaultRedisAuth"} }
    },
    {
        "parameters": {
            "functionCode": "const crypto = require('crypto');\nconst encryptedContext = items[0].json.value;\nconst encryptionKey = $('V61_PREPARE_REDIS_KEYS').first().json.encryption_key;\n\nitems[0].json.decrypted_context_data = null;\n\nif (encryptedContext && typeof encryptedContext === 'string' && encryptedContext.includes(':')) {\n  try {\n    const parts = encryptedContext.split(':');\n    const iv = Buffer.from(parts.shift(), 'hex');\n    const encryptedText = Buffer.from(parts.join(':'), 'hex');\n    const decipher = crypto.createDecipheriv('aes-256-cbc', Buffer.from(encryptionKey), iv);\n    let decrypted = decipher.update(encryptedText);\n    decrypted = Buffer.concat([decrypted, decipher.final()]);\n    items[0].json.decrypted_context_data = JSON.parse(decrypted.toString());\n  } catch (e) {\n    console.error('Redis decryption error:', e.message);\n    items[0].json.decryption_error = e.message;\n  }\n} else if (encryptedContext) {\n    items[0].json.decryption_error = 'Invalid or missing encrypted data format from Redis.';\n}\n\nreturn items;"
        },
        "id": "V62_DECRYPT_REDIS_CONTEXT",
        "name": "🔑 Decrypt Redis Context",
        "type": "n8n-nodes-base.function",
        "typeVersion": 1,
        "position": [ -2160, 600 ],
        "continueOnFail": true
    },
    {
        "parameters": { "conditions": { "boolean": [ { "value1": "={{ $('V61_REDIS_RETRIEVE_CONTEXT_ENCRYPTED').execution.error || $('V62_DECRYPT_REDIS_CONTEXT').execution.error || !$('V62_DECRYPT_REDIS_CONTEXT').first().json.decrypted_context_data }}", "operation": "equal", "value2": true } ] } },
        "id": "V62_CHECK_REDIS_RETRIEVE_FAIL",
        "name": "🛠️ Check Redis Retrieve/Decrypt Fail?",
        "type": "n8n-nodes-base.if",
        "typeVersion": 1,
        "position": [ -2160, 720 ]
    },
    {
        "parameters": {
            "databaseType": "sqlite",
            "operation": "executeQuery",
            "query": "SELECT value FROM user_context_cache WHERE key = '{{ $('V61_PREPARE_REDIS_KEYS').first().json.local_cache_key_context }}' LIMIT 1;"
        },
        "id": "V62_SQLITE_RETRIEVE_FALLBACK_ENCRYPTED",
        "name": "🛠️ [SQLite] Retrieve Encrypted Fallback",
        "type": "n8n-nodes-base.database",
        "typeVersion": 1.1,
        "position": [ -2160, 840 ],
        "continueOnFail": true,
        "credentials": { "sqlite": { "id": "yourSQLiteCredentialID", "name": "DefaultSQLiteDB" } }
    },
    {
        "parameters": {
            "functionCode": "const crypto = require('crypto');\nconst queryResult = items[0].json;\nconst encryptedContext = queryResult && queryResult[0] ? queryResult[0].value : null;\nconst encryptionKey = $('V61_PREPARE_REDIS_KEYS').first().json.encryption_key;\n\nitems[0].json.decrypted_local_data = null;\n\nif (encryptedContext && typeof encryptedContext === 'string' && encryptedContext.includes(':')) {\n  try {\n    const parts = encryptedContext.split(':');\n    const iv = Buffer.from(parts.shift(), 'hex');\n    const encryptedText = Buffer.from(parts.join(':'), 'hex');\n    const decipher = crypto.createDecipheriv('aes-256-cbc', Buffer.from(encryptionKey), iv);\n    let decrypted = decipher.update(encryptedText);\n    decrypted = Buffer.concat([decrypted, decipher.final()]);\n    items[0].json.decrypted_local_data = JSON.parse(decrypted.toString());\n  } catch (e) {\n    console.error('Local Cache decryption error:', e.message);\n    items[0].json.local_decryption_error = e.message;\n  }\n} else if (encryptedContext) {\n    items[0].json.local_decryption_error = 'Invalid or missing local encrypted data format from SQLite.';\n}\nreturn items;"
        },
        "id": "V62_DECRYPT_LOCAL_CACHE_CONTEXT",
        "name": "🔑 Decrypt Local Cache Context",
        "type": "n8n-nodes-base.function",
        "typeVersion": 1,
        "position": [ -2160, 960 ],
        "continueOnFail": true
    },
    {
        "parameters": {
            "values": {
                "string": [
                    { "name": "retrieved_context_raw_data", "value": "={{ $('V62_CHECK_REDIS_RETRIEVE_FAIL').first().json.branch === 0 ? ($('V62_SQLITE_RETRIEVE_FALLBACK_ENCRYPTED').execution.error || $('V62_DECRYPT_LOCAL_CACHE_CONTEXT').execution.error || !$('V62_DECRYPT_LOCAL_CACHE_CONTEXT').first().json.decrypted_local_data ? null : $('V62_DECRYPT_LOCAL_CACHE_CONTEXT').first().json.decrypted_local_data) : $('V62_DECRYPT_REDIS_CONTEXT').first().json.decrypted_context_data }}"},
                    { "name": "retrieved_context", "value": "={{ $json.retrieved_context_raw_data && $json.retrieved_context_raw_data.tasks ? $json.retrieved_context_raw_data.tasks.slice(-5) : [] }}" },
                    { "name": "task_details_with_context", "value": "={{ { ...($('START_NODE').first().json.task_details || items[0].json.task_details || {}), 'retrieved_context': $json.retrieved_context } }}" },
                    { "name": "redis_retrieval_status", "value": "={{ $('V61_REDIS_RETRIEVE_CONTEXT_ENCRYPTED').execution.error || $('V62_DECRYPT_REDIS_CONTEXT').execution.error || !$('V62_DECRYPT_REDIS_CONTEXT').first().json.decrypted_context_data ? 'failed' : 'success' }}"},
                    { "name": "local_cache_retrieval_status", "value": "={{ $('V62_CHECK_REDIS_RETRIEVE_FAIL').first().json.branch === 0 ? ($('V62_SQLITE_RETRIEVE_FALLBACK_ENCRYPTED').execution.error || $('V62_DECRYPT_LOCAL_CACHE_CONTEXT').execution.error || !$('V62_DECRYPT_LOCAL_CACHE_CONTEXT').first().json.decrypted_local_data ? 'failed' : 'success') : 'not_attempted' }}"}
                ]
            },
            "options": { "keepOnlySet": false, "include": "all" }
        },
        "id": "V61_INJECT_RETRIEVED_CONTEXT",
        "name": "V6.2 Inject Context (Redis/SQLite) to Task",
        "type": "n8n-nodes-base.set",
        "typeVersion": 4.1,
        "position": [ -1940, 720 ]
    },
    {
      "parameters": {
        "values": {
            "boolean": [
                {"name": "is_valid_network", "value": "={{ ['LinkedIn', 'Instagram', 'X', 'TikTok', 'Facebook'].includes($json.network || $('START_NODE').first().json.network) }}" },
                {"name": "is_valid_task", "value": "={{ ['create_post', 'generate_performance_report', 'respond_to_comment'].includes($json.task || $('START_NODE').first().json.task) }}" }
            ]
        },
        "options": {"keepOnlySet": false}
      },
      "id": "V55_VALIDATE_INFO", "name": "V6.2 Validate Global Inputs", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [ -1740, 720 ]
    },
    {
        "parameters": {"conditions": {"boolean": [{"value1":"={{!$json.is_valid_network || !$json.is_valid_task}}", "operation":"equal", "value2":true}]}},
        "id": "IF_INVALID_GLOBAL_INPUT",
        "name": "If Invalid Global Input?",
        "type":"n8n-nodes-base.if",
        "typeVersion":1,
        "position":[-1740, 840]
    },
    {
      "parameters": { 
          "model": "openaiMultiModal", 
          "imagePropertyName":"{{ ($json.task_details_with_context && $json.task_details_with_context.media_url) || ($json.task_details && $json.task_details.media_url) ? 'media_url_property_placeholder' : '' }}", 
          "text":"Analyze this media (if any) for content and safety. {{ ($json.task_details_with_context && $json.task_details_with_context.media_url) || ($json.task_details && $json.task_details.media_url) ? (($json.task_details_with_context && $json.task_details_with_context.media_url) || ($json.task_details && $json.task_details.media_url)) : 'No media provided.' }}", 
          "options":{} 
      },
      "id": "V55_ANALYZE_MEDIA", "name": "[AI_TOOL] Analyze Media (Vision)", "type": "@n8n/n8n-nodes-langchain.vision", "typeVersion": 1, "position": [ -1740, 960 ],
      "credentials": { "openAiApi": { "id": "yourOpenAICredentialID", "name": "DefaultOpenAI"}}, "continueOnFail": true
    },
    {
      "parameters": { "routing": { "rules": { "values": [ 
          { "output": 0, "operation": "equal", "value1": "={{ $json.network || $('START_NODE').first().json.network }}", "value2": "LinkedIn" }, 
          { "output": 1, "operation": "equal", "value1": "={{ $json.network || $('START_NODE').first().json.network }}", "value2": "Instagram" }, 
          { "output": 2, "operation": "equal", "value1": "={{ $json.network || $('START_NODE').first().json.network }}", "value2": "X" }, 
          { "output": 3, "operation": "equal", "value1": "={{ $json.network || $('START_NODE').first().json.network }}", "value2": "TikTok" }, 
          { "output": 4, "operation": "equal", "value1": "={{ $json.network || $('START_NODE').first().json.network }}", "value2": "Facebook" } 
        ] }, 
        "fieldToMatch": "={{ $json.network || $('START_NODE').first().json.network }}" } },
      "id": "V5_ROUTE_NETWORK", "name": "Route by Network", "type": "n8n-nodes-base.switch", "typeVersion": 2, "position": [ -1520, 840 ]
    },

    // --- Task Routers for each Network ---
    { "parameters": { "routing": { "rules": { "values": [ { "output": 0, "operation": "equal", "value1": "={{ $json.task || $('START_NODE').first().json.task }}", "value2": "create_post" }, { "output": 1, "operation": "equal", "value1": "={{ $json.task || $('START_NODE').first().json.task }}", "value2": "generate_performance_report" }, { "output": 2, "operation": "equal", "value1": "={{ $json.task || $('START_NODE').first().json.task }}", "value2": "respond_to_comment" } ] }, "fieldToMatch": "={{ $json.task || $('START_NODE').first().json.task }}" } }, "id": "V54_ROUTE_LINKEDIN_TASK", "name": "Route LinkedIn Task", "type": "n8n-nodes-base.switch", "typeVersion": 2, "position": [ -1280, -220 ] },
    { "parameters": { "routing": { "rules": { "values": [ { "output": 0, "operation": "equal", "value1": "={{ $json.task || $('START_NODE').first().json.task }}", "value2": "create_post" }, { "output": 1, "operation": "equal", "value1": "={{ $json.task || $('START_NODE').first().json.task }}", "value2": "generate_performance_report" }, { "output": 2, "operation": "equal", "value1": "={{ $json.task || $('START_NODE').first().json.task }}", "value2": "respond_to_comment" } ] }, "fieldToMatch": "={{ $json.task || $('START_NODE').first().json.task }}" } }, "id": "V54_ROUTE_INSTAGRAM_TASK", "name": "Route Instagram Task", "type": "n8n-nodes-base.switch", "typeVersion": 2, "position": [ -1280, 420 ] },
    { "parameters": { "routing": { "rules": { "values": [ { "output": 0, "operation": "equal", "value1": "={{ $json.task || $('START_NODE').first().json.task }}", "value2": "create_post" }, { "output": 1, "operation": "equal", "value1": "={{ $json.task || $('START_NODE').first().json.task }}", "value2": "generate_performance_report" }, { "output": 2, "operation": "equal", "value1": "={{ $json.task || $('START_NODE').first().json.task }}", "value2": "respond_to_comment" } ] }, "fieldToMatch": "={{ $json.task || $('START_NODE').first().json.task }}" } }, "id": "V54_ROUTE_X_TASK", "name": "Route X Task", "type": "n8n-nodes-base.switch", "typeVersion": 2, "position": [ -1280, 1060 ] },
    { "parameters": { "routing": { "rules": { "values": [ { "output": 0, "operation": "equal", "value1": "={{ $json.task || $('START_NODE').first().json.task }}", "value2": "create_post" }, { "output": 1, "operation": "equal", "value1": "={{ $json.task || $('START_NODE').first().json.task }}", "value2": "generate_performance_report" }, { "output": 2, "operation": "equal", "value1": "={{ $json.task || $('START_NODE').first().json.task }}", "value2": "respond_to_comment" } ] }, "fieldToMatch": "={{ $json.task || $('START_NODE').first().json.task }}" } }, "id": "V54_ROUTE_TIKTOK_TASK", "name": "Route TikTok Task", "type": "n8n-nodes-base.switch", "typeVersion": 2, "position": [ -1280, 1700 ] },
    { "parameters": { "routing": { "rules": { "values": [ { "output": 0, "operation": "equal", "value1": "={{ $json.task || $('START_NODE').first().json.task }}", "value2": "create_post" }, { "output": 1, "operation": "equal", "value1": "={{ $json.task || $('START_NODE').first().json.task }}", "value2": "generate_performance_report" }, { "output": 2, "operation": "equal", "value1": "={{ $json.task || $('START_NODE').first().json.task }}", "value2": "respond_to_comment" } ] }, "fieldToMatch": "={{ $json.task || $('START_NODE').first().json.task }}" } }, "id": "V54_ROUTE_FACEBOOK_TASK", "name": "Route Facebook Task", "type": "n8n-nodes-base.switch", "typeVersion": 2, "position": [ -1280, 2340 ] },

    // --- SHARED AUTH NODES PER NETWORK (MOCKED) ---
    // LinkedIn Auth Flow (Example)
    { "parameters": { "values": {"string": [{"name": "linkedin_token", "value": "mock_linkedin_token_ok"}]}, "options": {} }, "id": "V62_AUTH_LINKEDIN", "name": "[CACHE] Get LinkedIn Token", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [ -800, -300 ] },
    { "parameters": { "conditions": { "boolean": [ { "value1": "={{ $json.linkedin_token === 'mock_linkedin_token_expired' }}", "operation": "equal", "value2": true } ] } }, "id": "V62_CHECK_TOKEN_LINKEDIN", "name": "Check LinkedIn Token Expired?", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [ -600, -300 ] },
    { "parameters": { "values": {"string": [{"name": "linkedin_token", "value": "mock_linkedin_token_renewed_ok"}]}, "options": {} }, "id": "V62_RENEW_TOKEN_LINKEDIN", "name": "[AUTH] Renew LinkedIn Token", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [ -400, -360 ] },
    // Instagram Auth Flow
    { "parameters": { "values": {"string": [{"name": "instagram_token", "value": "mock_instagram_token_ok"}]}, "options": {} }, "id": "V62_AUTH_INSTAGRAM", "name": "[CACHE] Get Instagram Token", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [ -800, 340 ] },
    { "parameters": { "conditions": { "boolean": [ { "value1": "={{ $json.instagram_token === 'mock_instagram_token_expired' }}", "operation": "equal", "value2": true } ] } }, "id": "V62_CHECK_TOKEN_INSTAGRAM", "name": "Check Instagram Token Expired?", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [ -600, 340 ] },
    { "parameters": { "values": {"string": [{"name": "instagram_token", "value": "mock_instagram_token_renewed_ok"}]}, "options": {} }, "id": "V62_RENEW_TOKEN_INSTAGRAM", "name": "[AUTH] Renew Instagram Token", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [ -400, 280 ] },
    // X Auth Flow
    { "parameters": { "values": {"string": [{"name": "x_token", "value": "mock_x_token_ok"}]}, "options": {} }, "id": "V62_AUTH_X", "name": "[CACHE] Get X Token", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [ -800, 980 ] },
    { "parameters": { "conditions": { "boolean": [ { "value1": "={{ $json.x_token === 'mock_x_token_expired' }}", "operation": "equal", "value2": true } ] } }, "id": "V62_CHECK_TOKEN_X", "name": "Check X Token Expired?", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [ -600, 980 ] },
    { "parameters": { "values": {"string": [{"name": "x_token", "value": "mock_x_token_renewed_ok"}]}, "options": {} }, "id": "V62_RENEW_TOKEN_X", "name": "[AUTH] Renew X Token", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [ -400, 920 ] },
    // TikTok Auth Flow
    { "parameters": { "values": {"string": [{"name": "tiktok_token", "value": "mock_tiktok_token_ok"}]}, "options": {} }, "id": "V62_AUTH_TIKTOK", "name": "[CACHE] Get TikTok Token", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [ -800, 1620 ] },
    { "parameters": { "conditions": { "boolean": [ { "value1": "={{ $json.tiktok_token === 'mock_tiktok_token_expired' }}", "operation": "equal", "value2": true } ] } }, "id": "V62_CHECK_TOKEN_TIKTOK", "name": "Check TikTok Token Expired?", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [ -600, 1620 ] },
    { "parameters": { "values": {"string": [{"name": "tiktok_token", "value": "mock_tiktok_token_renewed_ok"}]}, "options": {} }, "id": "V62_RENEW_TOKEN_TIKTOK", "name": "[AUTH] Renew TikTok Token", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [ -400, 1560 ] },
    // Facebook Auth Flow
    { "parameters": { "values": {"string": [{"name": "facebook_token", "value": "mock_facebook_token_ok"}]}, "options": {} }, "id": "V62_AUTH_FACEBOOK", "name": "[CACHE] Get Facebook Token", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [ -800, 2260 ] },
    { "parameters": { "conditions": { "boolean": [ { "value1": "={{ $json.facebook_token === 'mock_facebook_token_expired' }}", "operation": "equal", "value2": true } ] } }, "id": "V62_CHECK_TOKEN_FACEBOOK", "name": "Check Facebook Token Expired?", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [ -600, 2260 ] },
    { "parameters": { "values": {"string": [{"name": "facebook_token", "value": "mock_facebook_token_renewed_ok"}]}, "options": {} }, "id": "V62_RENEW_TOKEN_FACEBOOK", "name": "[AUTH] Renew Facebook Token", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [ -400, 2200 ] },

    // --- LINKEDIN TASKS ---
    // LinkedIn - Create Post
    { "parameters": { "conditions": { "boolean": [ { "value1": "={{ $json.task_details_with_context && ($json.task_details_with_context.topic || ($json.task_details && $json.task_details.topic)) }}", "operation": "isset" } ] } }, "id": "V62_VALIDATE_LINKEDIN_CREATE_POST", "name": "Validate: LI Post", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [ -1040, -340 ] },
    { "parameters": { "values": { "string": [ { "name": "sentiment", "value": "professional" } ] }, "options": {} }, "id": "V62_SENTIMENT_LINKEDIN_CREATE_POST", "name": "[AI] Sentiment LI Post", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [ -200, -340 ] },
    { "parameters": { "conditions": { "boolean": [ { "value1": "={{ $json.cache_hit_placeholder }}", "operation": "equal", "value2": true } ] } }, "id": "V62_CACHE_CHECK_LINKEDIN_CREATE_POST", "name": "[CACHE] Check LI Post", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [ 0, -340 ] },
    { "parameters": { "agent": "conversationalAgent", "model": "gpt-3.5-turbo", "promptType": "define", "text": "Task Details: {{ JSON.stringify($json.task_details_with_context) }}\nSentiment: {{ $json.sentiment }}\nRetrieved Context: {{JSON.stringify($json.retrieved_context)}}", "options": { "systemMessage": "Você é um especialista em LinkedIn. Crie um post profissional sobre o `topic`. Use um tom formal e inclua 3-5 hashtags relevantes. Ajuste ao sentimento. Max 3000 chars." } }, "id": "V62_AGENT_LINKEDIN_CREATE_POST", "name": "Agent: Create LI Post", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "credentials": {"openAiApi":{"id":"yourOpenAICredentialID", "name":"DefaultOpenAI"}}, "position": [ 240, -340 ] },
    { "parameters": { "values": { "string": [ {"name": "post_content_cached", "value": "={{ $json.final_agent_output }}"}, {"name":"ttl_seconds", "value": 604800} ] }, "options": {} }, "id": "V62_CACHE_SAVE_LINKEDIN_CREATE_POST", "name": "[CACHE] Save LI Post", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [ 900, -340 ] },
    { "parameters": { "values": { "string": [ {"name": "linkedin_post_api_response", "value": "Mock LinkedIn Post API Success for: {{ $json.final_agent_output ? $json.final_agent_output.substring(0,50) : 'N/A' }}..."} ] }, "options": {} }, "id": "V62_TOOL_LINKEDIN_CREATE_POST", "name": "[TOOL] Post to LI API", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [ 1100, -340 ] },
    { "parameters": { "values": { "string": [ { "name": "log_entry", "value": "={{ { \"user_id\": $json.user_id_original, \"network\": \"LinkedIn\", \"task\": \"create_post\", \"timestamp\": $now.toISO(), \"details\": $json.task_details_with_context, \"api_used\": $json.api_used, \"primary_failed\": $json.api_primary_failed_count > 0, \"backup_used\": $json.api_backup_used_count > 0, \"output_preview\": ($json.final_agent_output || '').substring(0,100), \"success\": $json.final_agent_output ? true : false, \"cached_result_used\": $json.cache_hit_placeholder === true } }}" } ] }, "options": {} }, "id": "V62_LOG_LINKEDIN_CREATE_POST", "name": "[DB] Log LI Post", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [ 1300, -340 ] },
    // LinkedIn - Generate Report
    { "parameters": { "conditions": { "boolean": [ { "value1": "={{ ($json.task_details_with_context && $json.task_details_with_context.time_period_days) || ($json.task_details && $json.task_details.time_period_days) }}", "operation": "isset" } ] } }, "id": "V62_VALIDATE_LINKEDIN_GENERATE_REPORT", "name": "Validate: LI Report", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [ -1040, -180 ] },
    { "parameters": { "values": { "string": [ { "name": "sentiment", "value": "analytical" } ] }, "options": {} }, "id": "V62_SENTIMENT_LINKEDIN_GENERATE_REPORT", "name": "[AI] Sentiment LI Report", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [ -200, -180 ] },
    { "parameters": { "conditions": { "boolean": [ { "value1": "={{ $json.cache_hit_placeholder }}", "operation": "equal", "value2": true } ] } }, "id": "V62_CACHE_CHECK_LINKEDIN_GENERATE_REPORT", "name": "[CACHE] Check LI Report", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [ 0, -180 ] },
    { "parameters": { "values": {"string": [{"name": "mock_analytics_data", "value": "{'impressions': 1000, 'clicks': 50, 'engagement_rate': '5%'}"}]}, "options": {} } , "id": "V62_GET_ANALYTICS_LINKEDIN_REPORT", "name": "[TOOL] Get LI Analytics", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [ 200, -240 ] },
    { "parameters": { "agent": "conversationalAgent", "model": "gpt-3.5-turbo", "promptType": "define", "text": "Dados Analíticos: {{ $json.mock_analytics_data }}\nTask Details: {{ JSON.stringify($json.task_details_with_context) }}\nRetrieved Context: {{JSON.stringify($json.retrieved_context)}}", "options": { "systemMessage": "Você é um analista de dados do LinkedIn. Analise os dados fornecidos e gere um relatório de performance executivo para o período especificado. Destaque KPIs importantes e forneça insights." } }, "id": "V62_AGENT_LINKEDIN_GENERATE_REPORT", "name": "Agent: Write LI Report", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "credentials": {"openAiApi":{"id":"yourOpenAICredentialID", "name":"DefaultOpenAI"}}, "position": [ 440, -180 ] },
    { "parameters": { "values": { "string": [ {"name": "report_content_cached", "value": "={{ $json.final_agent_output }}"}, {"name":"ttl_seconds", "value": 604800} ] }, "options": {} }, "id": "V62_CACHE_SAVE_LINKEDIN_GENERATE_REPORT", "name": "[CACHE] Save LI Report", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [ 900, -180 ] },
    { "parameters": { "values": { "string": [ {"name": "linkedin_report_tool_response", "value": "Mock LinkedIn Report Generation Success"} ] }, "options": {} }, "id": "V62_TOOL_LINKEDIN_GENERATE_REPORT", "name": "[TOOL] Process LI Report", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [ 1100, -180 ] },
    { "parameters": { "values": { "string": [ { "name": "log_entry", "value": "={{ { \"user_id\": $json.user_id_original, \"network\": \"LinkedIn\", \"task\": \"generate_performance_report\", \"timestamp\": $now.toISO(), \"details\": $json.task_details_with_context, \"api_used\": $json.api_used, \"primary_failed\": $json.api_primary_failed_count > 0, \"backup_used\": $json.api_backup_used_count > 0, \"output_preview\": ($json.final_agent_output || '').substring(0,100), \"success\": $json.final_agent_output ? true : false, \"cached_result_used\": $json.cache_hit_placeholder === true } }}" } ] }, "options": {} }, "id": "V62_LOG_LINKEDIN_GENERATE_REPORT", "name": "[DB] Log LI Report", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [ 1300, -180 ] },
    // LinkedIn - Respond Comment
    { "parameters": { "conditions": { "boolean": [ { "value1": "={{ ($json.task_details_with_context && $json.task_details_with_context.comment_id) || ($json.task_details && $json.task_details.comment_id) }}", "operation": "isset" } ] } }, "id": "V62_VALIDATE_LINKEDIN_RESPOND_COMMENT", "name": "Validate: LI Comment", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [ -1040, -20 ] },
    { "parameters": { "values": { "string": [ { "name": "sentiment", "value": "helpful" } ] }, "options": {} }, "id": "V62_SENTIMENT_LINKEDIN_RESPOND_COMMENT", "name": "[AI] Sentiment LI Comment", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [ -200, -20 ] },
    { "parameters": { "conditions": { "boolean": [ { "value1": "={{ $json.cache_hit_placeholder }}", "operation": "equal", "value2": true } ] } }, "id": "V62_CACHE_CHECK_LINKEDIN_RESPOND_COMMENT", "name": "[CACHE] Check LI Comment", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [ 0, -20 ] },
    { "parameters": { "agent": "conversationalAgent", "model": "gpt-3.5-turbo", "promptType": "define", "text": "Comment Text: {{ ($json.task_details_with_context && $json.task_details_with_context.comment_text) || ($json.task_details && $json.task_details.comment_text) }}\nTask Details: {{ JSON.stringify($json.task_details_with_context) }}\nSentiment: {{ $json.sentiment }}\nRetrieved Context: {{JSON.stringify($json.retrieved_context)}}", "options": { "systemMessage": "Você é um especialista em LinkedIn. Responda a este comentário de forma profissional e útil, alinhado ao sentimento." } }, "id": "V62_AGENT_LINKEDIN_RESPOND_COMMENT", "name": "Agent: Respond LI Comment", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "credentials": {"openAiApi":{"id":"yourOpenAICredentialID", "name":"DefaultOpenAI"}}, "position": [ 240, -20 ] },
    { "parameters": { "values": { "string": [ {"name": "comment_reply_cached", "value": "={{ $json.final_agent_output }}"}, {"name":"ttl_seconds", "value": 604800} ] }, "options": {} }, "id": "V62_CACHE_SAVE_LINKEDIN_RESPOND_COMMENT", "name": "[CACHE] Save LI Comment", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [ 900, -20 ] },
    { "parameters": { "values": { "string": [ {"name": "linkedin_comment_api_response", "value": "Mock LinkedIn Comment API Success for: {{ $json.final_agent_output ? $json.final_agent_output.substring(0,50) : 'N/A' }}..."} ] }, "options": {} }, "id": "V62_TOOL_LINKEDIN_RESPOND_COMMENT", "name": "[TOOL] Post LI Comment API", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [ 1100, -20 ] },
    { "parameters": { "values": { "string": [ { "name": "log_entry", "value": "={{ { \"user_id\": $json.user_id_original, \"network\": \"LinkedIn\", \"task\": \"respond_to_comment\", \"timestamp\": $now.toISO(), \"details\": $json.task_details_with_context, \"api_used\": $json.api_used, \"primary_failed\": $json.api_primary_failed_count > 0, \"backup_used\": $json.api_backup_used_count > 0, \"output_preview\": ($json.final_agent_output || '').substring(0,100), \"success\": $json.final_agent_output ? true : false, \"cached_result_used\": $json.cache_hit_placeholder === true } }}" } ] }, "options": {} }, "id": "V62_LOG_LINKEDIN_RESPOND_COMMENT", "name": "[DB] Log LI Comment", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [ 1300, -20 ] },

    // --- INSTAGRAM TASKS ---
    // Instagram - Create Post
    { "parameters": { "conditions": { "boolean": [ { "value1": "={{ ($json.task_details_with_context && $json.task_details_with_context.topic) || ($json.task_details && $json.task_details.topic) }}", "operation": "isset" } ] } }, "id": "V62_VALIDATE_INSTAGRAM_CREATE_POST", "name": "Validate: IG Post", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [ -1040, 300 ] },
    { "parameters": { "values": { "string": [ { "name": "sentiment", "value": "positive_engaging" } ] }, "options": {} }, "id": "V62_SENTIMENT_INSTAGRAM_CREATE_POST", "name": "[AI] Sentiment IG Post", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [ -200, 300 ] },
    { "parameters": { "conditions": { "boolean": [ { "value1": "={{ $json.cache_hit_placeholder }}", "operation": "equal", "value2": true } ] } }, "id": "V62_CACHE_CHECK_INSTAGRAM_CREATE_POST", "name": "[CACHE] Check IG Post", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [ 0, 300 ] },
    { "parameters": { "agent": "conversationalAgent", "model": "gpt-3.5-turbo", "promptType": "define", "text": "Task Details: {{ JSON.stringify($json.task_details_with_context) }}\nSentiment: {{ $json.sentiment }}\nRetrieved Context: {{JSON.stringify($json.retrieved_context)}}", "options": { "systemMessage": "Você é um criador de conteúdo para Instagram. Sua tarefa é gerar uma ideia para um post sobre o `topic`. Descreva a imagem/vídeo, escreva uma legenda envolvente com CTA, e sugira 7-10 hashtags. Ajuste o tom da legenda ao sentimento detectado (e.g., positivo_engaging, inspirational, fun)." } }, "id": "V62_AGENT_INSTAGRAM_CREATE_POST", "name": "Agent: Create IG Post", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "credentials": {"openAiApi":{"id":"yourOpenAICredentialID", "name":"DefaultOpenAI"}}, "position": [ 240, 300 ] },
    { "parameters": { "values": { "string": [ {"name": "post_content_cached", "value": "={{ $json.final_agent_output }}"}, {"name":"ttl_seconds", "value": 604800} ] }, "options": {} }, "id": "V62_CACHE_SAVE_INSTAGRAM_CREATE_POST", "name": "[CACHE] Save IG Post", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [ 900, 300 ] },
    { "parameters": { "values": { "string": [ {"name": "instagram_post_api_response", "value": "Mock Instagram Post API Success for: {{ $json.final_agent_output ? $json.final_agent_output.substring(0,50) : 'N/A' }}..."} ] }, "options": {} }, "id": "V62_TOOL_INSTAGRAM_CREATE_POST", "name": "[TOOL] Post to IG API", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [ 1100, 300 ] },
    { "parameters": { "values": { "string": [ { "name": "log_entry", "value": "={{ { \"user_id\": $json.user_id_original, \"network\": \"Instagram\", \"task\": \"create_post\", \"timestamp\": $now.toISO(), \"details\": $json.task_details_with_context, \"api_used\": $json.api_used, \"primary_failed\": $json.api_primary_failed_count > 0, \"backup_used\": $json.api_backup_used_count > 0, \"output_preview\": ($json.final_agent_output || '').substring(0,100), \"success\": $json.final_agent_output ? true : false, \"cached_result_used\": $json.cache_hit_placeholder === true } }}" } ] }, "options": {} }, "id": "V62_LOG_INSTAGRAM_CREATE_POST", "name": "[DB] Log IG Post", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [ 1300, 300 ] },
    // Instagram - Generate Report
    { "parameters": { "conditions": { "boolean": [ { "value1": "={{ ($json.task_details_with_context && $json.task_details_with_context.time_period_days) || ($json.task_details && $json.task_details.time_period_days) }}", "operation": "isset" } ] } }, "id": "V62_VALIDATE_INSTAGRAM_GENERATE_REPORT", "name": "Validate: IG Report", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [ -1040, 460 ] },
    { "parameters": { "values": { "string": [ { "name": "sentiment", "value": "analytical_visual" } ] }, "options": {} }, "id": "V62_SENTIMENT_INSTAGRAM_GENERATE_REPORT", "name": "[AI] Sentiment IG Report", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [ -200, 460 ] },
    { "parameters": { "conditions": { "boolean": [ { "value1": "={{ $json.cache_hit_placeholder }}", "operation": "equal", "value2": true } ] } }, "id": "V62_CACHE_CHECK_INSTAGRAM_GENERATE_REPORT", "name": "[CACHE] Check IG Report", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [ 0, 460 ] },
    { "parameters": { "values": {"string": [{"name": "mock_analytics_data", "value": "{'followers': 5000, 'reach': 15000, 'story_views': 800}"}]}, "options": {} } , "id": "V62_GET_ANALYTICS_INSTAGRAM_REPORT", "name": "[TOOL] Get IG Analytics", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [ 200, 400 ] },
    { "parameters": { "agent": "conversationalAgent", "model": "gpt-3.5-turbo", "promptType": "define", "text": "Dados Analíticos: {{ $json.mock_analytics_data }}\nTask Details: {{ JSON.stringify($json.task_details_with_context) }}\nRetrieved Context: {{JSON.stringify($json.retrieved_context)}}", "options": { "systemMessage": "Você é um analista de dados do Instagram. Gere um relatório de performance, focando em engajamento, alcance e crescimento de seguidores. Destaque os posts de melhor performance." } }, "id": "V62_AGENT_INSTAGRAM_GENERATE_REPORT", "name": "Agent: Write IG Report", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "credentials": {"openAiApi":{"id":"yourOpenAICredentialID", "name":"DefaultOpenAI"}}, "position": [ 440, 460 ] },
    { "parameters": { "values": { "string": [ {"name": "report_content_cached", "value": "={{ $json.final_agent_output }}"}, {"name":"ttl_seconds", "value": 604800} ] }, "options": {} }, "id": "V62_CACHE_SAVE_INSTAGRAM_GENERATE_REPORT", "name": "[CACHE] Save IG Report", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [ 900, 460 ] },
    { "parameters": { "values": { "string": [ {"name": "instagram_report_tool_response", "value": "Mock Instagram Report Generation Success"} ] }, "options": {} }, "id": "V62_TOOL_INSTAGRAM_GENERATE_REPORT", "name": "[TOOL] Process IG Report", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [ 1100, 460 ] },
    { "parameters": { "values": { "string": [ { "name": "log_entry", "value": "={{ { \"user_id\": $json.user_id_original, \"network\": \"Instagram\", \"task\": \"generate_performance_report\", \"timestamp\": $now.toISO(), \"details\": $json.task_details_with_context, \"api_used\": $json.api_used, \"primary_failed\": $json.api_primary_failed_count > 0, \"backup_used\": $json.api_backup_used_count > 0, \"output_preview\": ($json.final_agent_output || '').substring(0,100), \"success\": $json.final_agent_output ? true : false, \"cached_result_used\": $json.cache_hit_placeholder === true } }}" } ] }, "options": {} }, "id": "V62_LOG_INSTAGRAM_GENERATE_REPORT", "name": "[DB] Log IG Report", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [ 1300, 460 ] },
    // Instagram - Respond Comment
    { "parameters": { "conditions": { "boolean": [ { "value1": "={{ ($json.task_details_with_context && $json.task_details_with_context.comment_id) || ($json.task_details && $json.task_details.comment_id) }}", "operation": "isset" } ] } }, "id": "V62_VALIDATE_INSTAGRAM_RESPOND_COMMENT", "name": "Validate: IG Comment", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [ -1040, 620 ] },
    { "parameters": { "values": { "string": [ { "name": "sentiment", "value": "friendly_positive" } ] }, "options": {} }, "id": "V62_SENTIMENT_INSTAGRAM_RESPOND_COMMENT", "name": "[AI] Sentiment IG Comment", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [ -200, 620 ] },
    { "parameters": { "conditions": { "boolean": [ { "value1": "={{ $json.cache_hit_placeholder }}", "operation": "equal", "value2": true } ] } }, "id": "V62_CACHE_CHECK_INSTAGRAM_RESPOND_COMMENT", "name": "[CACHE] Check IG Comment", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [ 0, 620 ] },
    { "parameters": { "agent": "conversationalAgent", "model": "gpt-3.5-turbo", "promptType": "define", "text": "Comment Text: {{ ($json.task_details_with_context && $json.task_details_with_context.comment_text) || ($json.task_details && $json.task_details.comment_text) }}\nTask Details: {{ JSON.stringify($json.task_details_with_context) }}\nSentiment: {{ $json.sentiment }}\nRetrieved Context: {{JSON.stringify($json.retrieved_context)}}", "options": { "systemMessage": "Você é um especialista em Instagram. Responda ao comentário de forma envolvente e alinhada ao tom do sentimento (e.g., friendly_positive, appreciative, supportive). Use emojis apropriados." } }, "id": "V62_AGENT_INSTAGRAM_RESPOND_COMMENT", "name": "Agent: Respond IG Comment", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "credentials": {"openAiApi":{"id":"yourOpenAICredentialID", "name":"DefaultOpenAI"}}, "position": [ 240, 620 ] },
    { "parameters": { "values": { "string": [ {"name": "comment_reply_cached", "value": "={{ $json.final_agent_output }}"}, {"name":"ttl_seconds", "value": 604800} ] }, "options": {} }, "id": "V62_CACHE_SAVE_INSTAGRAM_RESPOND_COMMENT", "name": "[CACHE] Save IG Comment", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [ 900, 620 ] },
    { "parameters": { "values": { "string": [ {"name": "instagram_comment_api_response", "value": "Mock Instagram Comment API Success for: {{ $json.final_agent_output ? $json.final_agent_output.substring(0,50) : 'N/A' }}..."} ] }, "options": {} }, "id": "V62_TOOL_INSTAGRAM_RESPOND_COMMENT", "name": "[TOOL] Post IG Comment API", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [ 1100, 620 ] },
    { "parameters": { "values": { "string": [ { "name": "log_entry", "value": "={{ { \"user_id\": $json.user_id_original, \"network\": \"Instagram\", \"task\": \"respond_to_comment\", \"timestamp\": $now.toISO(), \"details\": $json.task_details_with_context, \"api_used\": $json.api_used, \"primary_failed\": $json.api_primary_failed_count > 0, \"backup_used\": $json.api_backup_used_count > 0, \"output_preview\": ($json.final_agent_output || '').substring(0,100), \"success\": $json.final_agent_output ? true : false, \"cached_result_used\": $json.cache_hit_placeholder === true } }}" } ] }, "options": {} }, "id": "V62_LOG_INSTAGRAM_RESPOND_COMMENT", "name": "[DB] Log IG Comment", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [ 1300, 620 ] },

    // --- X (TWITTER) TASKS ---
    // X - Create Post
    { "parameters": { "conditions": { "boolean": [ { "value1": "={{ ($json.task_details_with_context && $json.task_details_with_context.topic) || ($json.task_details && $json.task_details.topic) }}", "operation": "isset" } ] } }, "id": "V62_VALIDATE_X_CREATE_POST", "name": "Validate: X Post", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [ -1040, 940 ] },
    { "parameters": { "values": { "string": [ { "name": "sentiment", "value": "concise_impactful" } ] }, "options": {} }, "id": "V62_SENTIMENT_X_CREATE_POST", "name": "[AI] Sentiment X Post", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [ -200, 940 ] },
    { "parameters": { "conditions": { "boolean": [ { "value1": "={{ $json.cache_hit_placeholder }}", "operation": "equal", "value2": true } ] } }, "id": "V62_CACHE_CHECK_X_CREATE_POST", "name": "[CACHE] Check X Post", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [ 0, 940 ] },
    { "parameters": { "agent": "conversationalAgent", "model": "gpt-3.5-turbo", "promptType": "define", "text": "Task Details: {{ JSON.stringify($json.task_details_with_context) }}\nSentiment: {{ $json.sentiment }}\nRetrieved Context: {{JSON.stringify($json.retrieved_context)}}", "options": { "systemMessage": "Você é um especialista em X. Crie um tweet sobre o `topic` com até 280 caracteres, 1-2 hashtags relevantes e um CTA, se apropriado. Ajuste ao sentimento (e.g., concise_impactful, witty, informative)." } }, "id": "V62_AGENT_X_CREATE_POST", "name": "Agent: Create X Post", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "credentials": {"openAiApi":{"id":"yourOpenAICredentialID", "name":"DefaultOpenAI"}}, "position": [ 240, 940 ] },
    { "parameters": { "values": { "string": [ {"name": "post_content_cached", "value": "={{ $json.final_agent_output }}"}, {"name":"ttl_seconds", "value": 604800} ] }, "options": {} }, "id": "V62_CACHE_SAVE_X_CREATE_POST", "name": "[CACHE] Save X Post", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [ 900, 940 ] },
    { "parameters": { "values": { "string": [ {"name": "x_post_api_response", "value": "Mock X Post API Success for: {{ $json.final_agent_output ? $json.final_agent_output.substring(0,50) : 'N/A' }}..."} ] }, "options": {} }, "id": "V62_TOOL_X_CREATE_POST", "name": "[TOOL] Post to X API", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [ 1100, 940 ] },
    { "parameters": { "values": { "string": [ { "name": "log_entry", "value": "={{ { \"user_id\": $json.user_id_original, \"network\": \"X\", \"task\": \"create_post\", \"timestamp\": $now.toISO(), \"details\": $json.task_details_with_context, \"api_used\": $json.api_used, \"primary_failed\": $json.api_primary_failed_count > 0, \"backup_used\": $json.api_backup_used_count > 0, \"output_preview\": ($json.final_agent_output || '').substring(0,100), \"success\": $json.final_agent_output ? true : false, \"cached_result_used\": $json.cache_hit_placeholder === true } }}" } ] }, "options": {} }, "id": "V62_LOG_X_CREATE_POST", "name": "[DB] Log X Post", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [ 1300, 940 ] },
    // X - Generate Report
    { "parameters": { "conditions": { "boolean": [ { "value1": "={{ ($json.task_details_with_context && $json.task_details_with_context.time_period_days) || ($json.task_details && $json.task_details.time_period_days) }}", "operation": "isset" } ] } }, "id": "V62_VALIDATE_X_GENERATE_REPORT", "name": "Validate: X Report", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [ -1040, 1100 ] },
    { "parameters": { "values": { "string": [ { "name": "sentiment", "value": "data_driven_concise" } ] }, "options": {} }, "id": "V62_SENTIMENT_X_GENERATE_REPORT", "name": "[AI] Sentiment X Report", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [ -200, 1100 ] },
    { "parameters": { "conditions": { "boolean": [ { "value1": "={{ $json.cache_hit_placeholder }}", "operation": "equal", "value2": true } ] } }, "id": "V62_CACHE_CHECK_X_GENERATE_REPORT", "name": "[CACHE] Check X Report", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [ 0, 1100 ] },
    { "parameters": { "values": {"string": [{"name": "mock_analytics_data", "value": "{'retweets': 200, 'likes': 1200, 'impressions': 25000}"}]}, "options": {} } , "id": "V62_GET_ANALYTICS_X_REPORT", "name": "[TOOL] Get X Analytics", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [ 200, 1040 ] },
    { "parameters": { "agent": "conversationalAgent", "model": "gpt-3.5-turbo", "promptType": "define", "text": "Dados Analíticos: {{ $json.mock_analytics_data }}\nTask Details: {{ JSON.stringify($json.task_details_with_context) }}\nRetrieved Context: {{JSON.stringify($json.retrieved_context)}}", "options": { "systemMessage": "Você é um analista de dados do X. Gere um relatório de performance conciso, focando em impressões, engajamento (retweets, likes) e principais tweets." } }, "id": "V62_AGENT_X_GENERATE_REPORT", "name": "Agent: Write X Report", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "credentials": {"openAiApi":{"id":"yourOpenAICredentialID", "name":"DefaultOpenAI"}}, "position": [ 440, 1100 ] },
    { "parameters": { "values": { "string": [ {"name": "report_content_cached", "value": "={{ $json.final_agent_output }}"}, {"name":"ttl_seconds", "value": 604800} ] }, "options": {} }, "id": "V62_CACHE_SAVE_X_GENERATE_REPORT", "name": "[CACHE] Save X Report", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [ 900, 1100 ] },
    { "parameters": { "values": { "string": [ {"name": "x_report_tool_response", "value": "Mock X Report Generation Success"} ] }, "options": {} }, "id": "V62_TOOL_X_GENERATE_REPORT", "name": "[TOOL] Process X Report", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [ 1100, 1100 ] },
    { "parameters": { "values": { "string": [ { "name": "log_entry", "value": "={{ { \"user_id\": $json.user_id_original, \"network\": \"X\", \"task\": \"generate_performance_report\", \"timestamp\": $now.toISO(), \"details\": $json.task_details_with_context, \"api_used\": $json.api_used, \"primary_failed\": $json.api_primary_failed_count > 0, \"backup_used\": $json.api_backup_used_count > 0, \"output_preview\": ($json.final_agent_output || '').substring(0,100), \"success\": $json.final_agent_output ? true : false, \"cached_result_used\": $json.cache_hit_placeholder === true } }}" } ] }, "options": {} }, "id": "V62_LOG_X_GENERATE_REPORT", "name": "[DB] Log X Report", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [ 1300, 1100 ] },
    // X - Respond Comment
    { "parameters": { "conditions": { "boolean": [ { "value1": "={{ ($json.task_details_with_context && $json.task_details_with_context.comment_id) || ($json.task_details && $json.task_details.comment_id) }}", "operation": "isset" } ] } }, "id": "V62_VALIDATE_X_RESPOND_COMMENT", "name": "Validate: X Comment", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [ -1040, 1260 ] },
    { "parameters": { "values": { "string": [ { "name": "sentiment", "value": "quick_witty" } ] }, "options": {} }, "id": "V62_SENTIMENT_X_RESPOND_COMMENT", "name": "[AI] Sentiment X Comment", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [ -200, 1260 ] },
    { "parameters": { "conditions": { "boolean": [ { "value1": "={{ $json.cache_hit_placeholder }}", "operation": "equal", "value2": true } ] } }, "id": "V62_CACHE_CHECK_X_RESPOND_COMMENT", "name": "[CACHE] Check X Comment", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [ 0, 1260 ] },
    { "parameters": { "agent": "conversationalAgent", "model": "gpt-3.5-turbo", "promptType": "define", "text": "Comment Text: {{ ($json.task_details_with_context && $json.task_details_with_context.comment_text) || ($json.task_details && $json.task_details.comment_text) }}\nTask Details: {{ JSON.stringify($json.task_details_with_context) }}\nSentiment: {{ $json.sentiment }}\nRetrieved Context: {{JSON.stringify($json.retrieved_context)}}", "options": { "systemMessage": "Você é um especialista em X. Responda ao comentário de forma rápida e divertida (ou conforme o sentimento detectado: quick_witty, direct, helpful). Mantenha a resposta curta." } }, "id": "V62_AGENT_X_RESPOND_COMMENT", "name": "Agent: Respond X Comment", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "credentials": {"openAiApi":{"id":"yourOpenAICredentialID", "name":"DefaultOpenAI"}}, "position": [ 240, 1260 ] },
    { "parameters": { "values": { "string": [ {"name": "comment_reply_cached", "value": "={{ $json.final_agent_output }}"}, {"name":"ttl_seconds", "value": 604800} ] }, "options": {} }, "id": "V62_CACHE_SAVE_X_RESPOND_COMMENT", "name": "[CACHE] Save X Comment", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [ 900, 1260 ] },
    { "parameters": { "values": { "string": [ {"name": "x_comment_api_response", "value": "Mock X Comment API Success for: {{ $json.final_agent_output ? $json.final_agent_output.substring(0,50) : 'N/A' }}..."} ] }, "options": {} }, "id": "V62_TOOL_X_RESPOND_COMMENT", "name": "[TOOL] Post X Comment API", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [ 1100, 1260 ] },
    { "parameters": { "values": { "string": [ { "name": "log_entry", "value": "={{ { \"user_id\": $json.user_id_original, \"network\": \"X\", \"task\": \"respond_to_comment\", \"timestamp\": $now.toISO(), \"details\": $json.task_details_with_context, \"api_used\": $json.api_used, \"primary_failed\": $json.api_primary_failed_count > 0, \"backup_used\": $json.api_backup_used_count > 0, \"output_preview\": ($json.final_agent_output || '').substring(0,100), \"success\": $json.final_agent_output ? true : false, \"cached_result_used\": $json.cache_hit_placeholder === true } }}" } ] }, "options": {} }, "id": "V62_LOG_X_RESPOND_COMMENT", "name": "[DB] Log X Comment", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [ 1300, 1260 ] },

    // --- TIKTOK TASKS ---
    // TikTok - Create Post
    { "parameters": { "conditions": { "boolean": [ { "value1": "={{ ($json.task_details_with_context && $json.task_details_with_context.topic) || ($json.task_details && $json.task_details.topic) }}", "operation": "isset" } ] } }, "id": "V62_VALIDATE_TIKTOK_CREATE_POST", "name": "Validate: TikTok Post", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [ -1040, 1580 ] },
    { "parameters": { "values": { "string": [ { "name": "sentiment", "value": "trendy_fun" } ] }, "options": {} }, "id": "V62_SENTIMENT_TIKTOK_CREATE_POST", "name": "[AI] Sentiment TikTok Post", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [ -200, 1580 ] },
    { "parameters": { "conditions": { "boolean": [ { "value1": "={{ $json.cache_hit_placeholder }}", "operation": "equal", "value2": true } ] } }, "id": "V62_CACHE_CHECK_TIKTOK_CREATE_POST", "name": "[CACHE] Check TikTok Post", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [ 0, 1580 ] },
    { "parameters": { "agent": "conversationalAgent", "model": "gpt-3.5-turbo", "promptType": "define", "text": "Task Details: {{ JSON.stringify($json.task_details_with_context) }}\nSentiment: {{ $json.sentiment }}\nRetrieved Context: {{JSON.stringify($json.retrieved_context)}}", "options": { "systemMessage": "Você é um criador de conteúdo para TikTok. Crie um conceito de vídeo ou post sobre o `topic`. Descreva o conceito do vídeo (cenas, música), sugira uma legenda curta e impactante, e 3-5 hashtags em alta. Adapte ao público jovem e ao sentimento detectado (e.g., trendy_fun, educational_quick, challenge_idea)." } }, "id": "V62_AGENT_TIKTOK_CREATE_POST", "name": "Agent: Create TikTok Post", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "credentials": {"openAiApi":{"id":"yourOpenAICredentialID", "name":"DefaultOpenAI"}}, "position": [ 240, 1580 ] },
    { "parameters": { "values": { "string": [ {"name": "post_content_cached", "value": "={{ $json.final_agent_output }}"}, {"name":"ttl_seconds", "value": 604800} ] }, "options": {} }, "id": "V62_CACHE_SAVE_TIKTOK_CREATE_POST", "name": "[CACHE] Save TikTok Post", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [ 900, 1580 ] },
    { "parameters": { "values": { "string": [ {"name": "tiktok_post_api_response", "value": "Mock TikTok Post API Success for: {{ $json.final_agent_output ? $json.final_agent_output.substring(0,50) : 'N/A' }}..."} ] }, "options": {} }, "id": "V62_TOOL_TIKTOK_CREATE_POST", "name": "[TOOL] Post to TikTok API", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [ 1100, 1580 ] },
    { "parameters": { "values": { "string": [ { "name": "log_entry", "value": "={{ { \"user_id\": $json.user_id_original, \"network\": \"TikTok\", \"task\": \"create_post\", \"timestamp\": $now.toISO(), \"details\": $json.task_details_with_context, \"api_used\": $json.api_used, \"primary_failed\": $json.api_primary_failed_count > 0, \"backup_used\": $json.api_backup_used_count > 0, \"output_preview\": ($json.final_agent_output || '').substring(0,100), \"success\": $json.final_agent_output ? true : false, \"cached_result_used\": $json.cache_hit_placeholder === true } }}" } ] }, "options": {} }, "id": "V62_LOG_TIKTOK_CREATE_POST", "name": "[DB] Log TikTok Post", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [ 1300, 1580 ] },
    // TikTok - Generate Report
    { "parameters": { "conditions": { "boolean": [ { "value1": "={{ ($json.task_details_with_context && $json.task_details_with_context.time_period_days) || ($json.task_details && $json.task_details.time_period_days) }}", "operation": "isset" } ] } }, "id": "V62_VALIDATE_TIKTOK_GENERATE_REPORT", "name": "Validate: TikTok Report", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [ -1040, 1740 ] },
    { "parameters": { "values": { "string": [ { "name": "sentiment", "value": "viral_metrics" } ] }, "options": {} }, "id": "V62_SENTIMENT_TIKTOK_GENERATE_REPORT", "name": "[AI] Sentiment TikTok Report", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [ -200, 1740 ] },
    { "parameters": { "conditions": { "boolean": [ { "value1": "={{ $json.cache_hit_placeholder }}", "operation": "equal", "value2": true } ] } }, "id": "V62_CACHE_CHECK_TIKTOK_GENERATE_REPORT", "name": "[CACHE] Check TikTok Report", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [ 0, 1740 ] },
    { "parameters": { "values": {"string": [{"name": "mock_analytics_data", "value": "{'views': 1000000, 'likes': 50000, 'shares': 5000}"}]}, "options": {} } , "id": "V62_GET_ANALYTICS_TIKTOK_REPORT", "name": "[TOOL] Get TikTok Analytics", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [ 200, 1680 ] },
    { "parameters": { "agent": "conversationalAgent", "model": "gpt-3.5-turbo", "promptType": "define", "text": "Dados Analíticos: {{ $json.mock_analytics_data }}\nTask Details: {{ JSON.stringify($json.task_details_with_context) }}\nRetrieved Context: {{JSON.stringify($json.retrieved_context)}}", "options": { "systemMessage": "Você é um analista de dados do TikTok. Gere um relatório de performance com foco em visualizações, likes, compartilhamentos e taxa de conclusão de vídeo. Identifique tendências de viralização." } }, "id": "V62_AGENT_TIKTOK_GENERATE_REPORT", "name": "Agent: Write TikTok Report", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "credentials": {"openAiApi":{"id":"yourOpenAICredentialID", "name":"DefaultOpenAI"}}, "position": [ 440, 1740 ] },
    { "parameters": { "values": { "string": [ {"name": "report_content_cached", "value": "={{ $json.final_agent_output }}"}, {"name":"ttl_seconds", "value": 604800} ] }, "options": {} }, "id": "V62_CACHE_SAVE_TIKTOK_GENERATE_REPORT", "name": "[CACHE] Save TikTok Report", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [ 900, 1740 ] },
    { "parameters": { "values": { "string": [ {"name": "tiktok_report_tool_response", "value": "Mock TikTok Report Generation Success"} ] }, "options": {} }, "id": "V62_TOOL_TIKTOK_GENERATE_REPORT", "name": "[TOOL] Process TikTok Report", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [ 1100, 1740 ] },
    { "parameters": { "values": { "string": [ { "name": "log_entry", "value": "={{ { \"user_id\": $json.user_id_original, \"network\": \"TikTok\", \"task\": \"generate_performance_report\", \"timestamp\": $now.toISO(), \"details\": $json.task_details_with_context, \"api_used\": $json.api_used, \"primary_failed\": $json.api_primary_failed_count > 0, \"backup_used\": $json.api_backup_used_count > 0, \"output_preview\": ($json.final_agent_output || '').substring(0,100), \"success\": $json.final_agent_output ? true : false, \"cached_result_used\": $json.cache_hit_placeholder === true } }}" } ] }, "options": {} }, "id": "V62_LOG_TIKTOK_GENERATE_REPORT", "name": "[DB] Log TikTok Report", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [ 1300, 1740 ] },
    // TikTok - Respond Comment
    { "parameters": { "conditions": { "boolean": [ { "value1": "={{ ($json.task_details_with_context && $json.task_details_with_context.comment_id) || ($json.task_details && $json.task_details.comment_id) }}", "operation": "isset" } ] } }, "id": "V62_VALIDATE_TIKTOK_RESPOND_COMMENT", "name": "Validate: TikTok Comment", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [ -1040, 1900 ] },
    { "parameters": { "values": { "string": [ { "name": "sentiment", "value": "engaging_fun" } ] }, "options": {} }, "id": "V62_SENTIMENT_TIKTOK_RESPOND_COMMENT", "name": "[AI] Sentiment TikTok Comment", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [ -200, 1900 ] },
    { "parameters": { "conditions": { "boolean": [ { "value1": "={{ $json.cache_hit_placeholder }}", "operation": "equal", "value2": true } ] } }, "id": "V62_CACHE_CHECK_TIKTOK_RESPOND_COMMENT", "name": "[CACHE] Check TikTok Comment", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [ 0, 1900 ] },
    { "parameters": { "agent": "conversationalAgent", "model": "gpt-3.5-turbo", "promptType": "define", "text": "Comment Text: {{ ($json.task_details_with_context && $json.task_details_with_context.comment_text) || ($json.task_details && $json.task_details.comment_text) }}\nTask Details: {{ JSON.stringify($json.task_details_with_context) }}\nSentiment: {{ $json.sentiment }}\nRetrieved Context: {{JSON.stringify($json.retrieved_context)}}", "options": { "systemMessage": "Você é um especialista em TikTok. Responda ao comentário de forma criativa, divertida e alinhada com a vibe do TikTok. Use gírias e emojis populares, se apropriado e conforme o sentimento (e.g., engaging_fun, supportive_cool, challenge_response)." } }, "id": "V62_AGENT_TIKTOK_RESPOND_COMMENT", "name": "Agent: Respond TikTok Comment", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "credentials": {"openAiApi":{"id":"yourOpenAICredentialID", "name":"DefaultOpenAI"}}, "position": [ 240, 1900 ] },
    { "parameters": { "values": { "string": [ {"name": "comment_reply_cached", "value": "={{ $json.final_agent_output }}"}, {"name":"ttl_seconds", "value": 604800} ] }, "options": {} }, "id": "V62_CACHE_SAVE_TIKTOK_RESPOND_COMMENT", "name": "[CACHE] Save TikTok Comment", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [ 900, 1900 ] },
    { "parameters": { "values": { "string": [ {"name": "tiktok_comment_api_response", "value": "Mock TikTok Comment API Success for: {{ $json.final_agent_output ? $json.final_agent_output.substring(0,50) : 'N/A' }}..."} ] }, "options": {} }, "id": "V62_TOOL_TIKTOK_RESPOND_COMMENT", "name": "[TOOL] Post TikTok Comment API", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [ 1100, 1900 ] },
    { "parameters": { "values": { "string": [ { "name": "log_entry", "value": "={{ { \"user_id\": $json.user_id_original, \"network\": \"TikTok\", \"task\": \"respond_to_comment\", \"timestamp\": $now.toISO(), \"details\": $json.task_details_with_context, \"api_used\": $json.api_used, \"primary_failed\": $json.api_primary_failed_count > 0, \"backup_used\": $json.api_backup_used_count > 0, \"output_preview\": ($json.final_agent_output || '').substring(0,100), \"success\": $json.final_agent_output ? true : false, \"cached_result_used\": $json.cache_hit_placeholder === true } }}" } ] }, "options": {} }, "id": "V62_LOG_TIKTOK_RESPOND_COMMENT", "name": "[DB] Log TikTok Comment", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [ 1300, 1900 ] },

    // --- FACEBOOK TASKS ---
    // Facebook - Create Post
    { "parameters": { "conditions": { "boolean": [ { "value1": "={{ ($json.task_details_with_context && ($json.task_details_with_context.topic || $json.task_details_with_context.audience)) || ($json.task_details && ($json.task_details.topic || $json.task_details.audience)) }}", "operation": "isset" } ] } }, "id": "V62_VALIDATE_FACEBOOK_CREATE_POST", "name": "Validate: FB Post", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [ -1040, 2220 ] },
    { "parameters": { "values": { "string": [ { "name": "sentiment", "value": "community_focused" } ] }, "options": {} }, "id": "V62_SENTIMENT_FACEBOOK_CREATE_POST", "name": "[AI] Sentiment FB Post", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [ -200, 2220 ] },
    { "parameters": { "conditions": { "boolean": [ { "value1": "={{ $json.cache_hit_placeholder }}", "operation": "equal", "value2": true } ] } }, "id": "V62_CACHE_CHECK_FACEBOOK_CREATE_POST", "name": "[CACHE] Check FB Post", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [ 0, 2220 ] },
    { "parameters": { "agent": "conversationalAgent", "model": "gpt-3.5-turbo", "promptType": "define", "text": "Task Details: {{ JSON.stringify($json.task_details_with_context) }}\nAudience: {{ ($json.task_details_with_context && $json.task_details_with_context.audience) || ($json.task_details && $json.task_details.audience) || 'general' }}\nSentiment: {{ $json.sentiment }}\nRetrieved Context: {{JSON.stringify($json.retrieved_context)}}", "options": { "systemMessage": "Você é um gerente de mídias sociais para o Facebook. Crie um post para uma página sobre o `topic`. Adapte o tom para o público (`audience`) e o sentimento (e.g., community_focused, informative_detailed, event_promo). Incentive comentários, compartilhamentos e interações." } }, "id": "V62_AGENT_FACEBOOK_CREATE_POST", "name": "Agent: Create FB Post", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "credentials": {"openAiApi":{"id":"yourOpenAICredentialID", "name":"DefaultOpenAI"}}, "position": [ 240, 2220 ] },
    { "parameters": { "values": { "string": [ {"name": "post_content_cached", "value": "={{ $json.final_agent_output }}"}, {"name":"ttl_seconds", "value": 604800} ] }, "options": {} }, "id": "V62_CACHE_SAVE_FACEBOOK_CREATE_POST", "name": "[CACHE] Save FB Post", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [ 900, 2220 ] },
    { "parameters": { "values": { "string": [ {"name": "facebook_post_api_response", "value": "Mock Facebook Post API Success for: {{ $json.final_agent_output ? $json.final_agent_output.substring(0,50) : 'N/A' }}..."} ] }, "options": {} }, "id": "V62_TOOL_FACEBOOK_CREATE_POST", "name": "[TOOL] Post to FB API", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [ 1100, 2220 ] },
    { "parameters": { "values": { "string": [ { "name": "log_entry", "value": "={{ { \"user_id\": $json.user_id_original, \"network\": \"Facebook\", \"task\": \"create_post\", \"timestamp\": $now.toISO(), \"details\": $json.task_details_with_context, \"api_used\": $json.api_used, \"primary_failed\": $json.api_primary_failed_count > 0, \"backup_used\": $json.api_backup_used_count > 0, \"output_preview\": ($json.final_agent_output || '').substring(0,100), \"success\": $json.final_agent_output ? true : false, \"cached_result_used\": $json.cache_hit_placeholder === true } }}" } ] }, "options": {} }, "id": "V62_LOG_FACEBOOK_CREATE_POST", "name": "[DB] Log FB Post", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [ 1300, 2220 ] },
    // Facebook - Generate Report
    { "parameters": { "conditions": { "boolean": [ { "value1": "={{ ($json.task_details_with_context && $json.task_details_with_context.time_period_days) || ($json.task_details && $json.task_details.time_period_days) }}", "operation": "isset" } ] } }, "id": "V62_VALIDATE_FACEBOOK_GENERATE_REPORT", "name": "Validate: FB Report", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [ -1040, 2380 ] },
    { "parameters": { "values": { "string": [ { "name": "sentiment", "value": "comprehensive_insights" } ] }, "options": {} }, "id": "V62_SENTIMENT_FACEBOOK_GENERATE_REPORT", "name": "[AI] Sentiment FB Report", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [ -200, 2380 ] },
    { "parameters": { "conditions": { "boolean": [ { "value1": "={{ $json.cache_hit_placeholder }}", "operation": "equal", "value2": true } ] } }, "id": "V62_CACHE_CHECK_FACEBOOK_GENERATE_REPORT", "name": "[CACHE] Check FB Report", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [ 0, 2380 ] },
    { "parameters": { "values": {"string": [{"name": "mock_analytics_data", "value": "{'page_likes': 10000, 'post_reach': 30000, 'engagement': 2500}"}]}, "options": {} } , "id": "V62_GET_ANALYTICS_FACEBOOK_REPORT", "name": "[TOOL] Get FB Analytics", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [ 200, 2320 ] },
    { "parameters": { "agent": "conversationalAgent", "model": "gpt-3.5-turbo", "promptType": "define", "text": "Dados Analíticos: {{ $json.mock_analytics_data }}\nTask Details: {{ JSON.stringify($json.task_details_with_context) }}\nRetrieved Context: {{JSON.stringify($json.retrieved_context)}}", "options": { "systemMessage": "Você é um analista de dados do Facebook. Gere um relatório de performance abrangente, cobrindo crescimento da página, alcance, engajamento e dados demográficos do público (se disponíveis)." } }, "id": "V62_AGENT_FACEBOOK_GENERATE_REPORT", "name": "Agent: Write FB Report", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "credentials": {"openAiApi":{"id":"yourOpenAICredentialID", "name":"DefaultOpenAI"}}, "position": [ 440, 2380 ] },
    { "parameters": { "values": { "string": [ {"name": "report_content_cached", "value": "={{ $json.final_agent_output }}"}, {"name":"ttl_seconds", "value": 604800} ] }, "options": {} }, "id": "V62_CACHE_SAVE_FACEBOOK_GENERATE_REPORT", "name": "[CACHE] Save FB Report", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [ 900, 2380 ] },
    { "parameters": { "values": { "string": [ {"name": "facebook_report_tool_response", "value": "Mock Facebook Report Generation Success"} ] }, "options": {} }, "id": "V62_TOOL_FACEBOOK_GENERATE_REPORT", "name": "[TOOL] Process FB Report", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [ 1100, 2380 ] },
    { "parameters": { "values": { "string": [ { "name": "log_entry", "value": "={{ { \"user_id\": $json.user_id_original, \"network\": \"Facebook\", \"task\": \"generate_performance_report\", \"timestamp\": $now.toISO(), \"details\": $json.task_details_with_context, \"api_used\": $json.api_used, \"primary_failed\": $json.api_primary_failed_count > 0, \"backup_used\": $json.api_backup_used_count > 0, \"output_preview\": ($json.final_agent_output || '').substring(0,100), \"success\": $json.final_agent_output ? true : false, \"cached_result_used\": $json.cache_hit_placeholder === true } }}" } ] }, "options": {} }, "id": "V62_LOG_FACEBOOK_GENERATE_REPORT", "name": "[DB] Log FB Report", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [ 1300, 2380 ] },
    // Facebook - Respond Comment
    { "parameters": { "conditions": { "boolean": [ { "value1": "={{ ($json.task_details_with_context && $json.task_details_with_context.comment_id) || ($json.task_details && $json.task_details.comment_id) }}", "operation": "isset" } ] } }, "id": "V62_VALIDATE_FACEBOOK_RESPOND_COMMENT", "name": "Validate: FB Comment", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [ -1040, 2540 ] },
    { "parameters": { "values": { "string": [ { "name": "sentiment", "value": "helpful_friendly" } ] }, "options": {} }, "id": "V62_SENTIMENT_FACEBOOK_RESPOND_COMMENT", "name": "[AI] Sentiment FB Comment", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [ -200, 2540 ] },
    { "parameters": { "conditions": { "boolean": [ { "value1": "={{ $json.cache_hit_placeholder }}", "operation": "equal", "value2": true } ] } }, "id": "V62_CACHE_CHECK_FACEBOOK_RESPOND_COMMENT", "name": "[CACHE] Check FB Comment", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [ 0, 2540 ] },
    { "parameters": { "agent": "conversationalAgent", "model": "gpt-3.5-turbo", "promptType": "define", "text": "Comment Text: {{ ($json.task_details_with_context && $json.task_details_with_context.comment_text) || ($json.task_details && $json.task_details.comment_text) }}\nTask Details: {{ JSON.stringify($json.task_details_with_context) }}\nSentiment: {{ $json.sentiment }}\nRetrieved Context: {{JSON.stringify($json.retrieved_context)}}", "options": { "systemMessage": "Você é um especialista em Facebook. Responda ao comentário de forma amigável, útil e que incentive mais engajamento. Adapte-se ao sentimento (e.g., helpful_friendly, empathetic, conversational)." } }, "id": "V62_AGENT_FACEBOOK_RESPOND_COMMENT", "name": "Agent: Respond FB Comment", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "credentials": {"openAiApi":{"id":"yourOpenAICredentialID", "name":"DefaultOpenAI"}}, "position": [ 240, 2540 ] },
    { "parameters": { "values": { "string": [ {"name": "comment_reply_cached", "value": "={{ $json.final_agent_output }}"}, {"name":"ttl_seconds", "value": 604800} ] }, "options": {} }, "id": "V62_CACHE_SAVE_FACEBOOK_RESPOND_COMMENT", "name": "[CACHE] Save FB Comment", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [ 900, 2540 ] },
    { "parameters": { "values": { "string": [ {"name": "facebook_comment_api_response", "value": "Mock Facebook Comment API Success for: {{ $json.final_agent_output ? $json.final_agent_output.substring(0,50) : 'N/A' }}..."} ] }, "options": {} }, "id": "V62_TOOL_FACEBOOK_RESPOND_COMMENT", "name": "[TOOL] Post FB Comment API", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [ 1100, 2540 ] },
    { "parameters": { "values": { "string": [ { "name": "log_entry", "value": "={{ { \"user_id\": $json.user_id_original, \"network\": \"Facebook\", \"task\": \"respond_to_comment\", \"timestamp\": $now.toISO(), \"details\": $json.task_details_with_context, \"api_used\": $json.api_used, \"primary_failed\": $json.api_primary_failed_count > 0, \"backup_used\": $json.api_backup_used_count > 0, \"output_preview\": ($json.final_agent_output || '').substring(0,100), \"success\": $json.final_agent_output ? true : false, \"cached_result_used\": $json.cache_hit_placeholder === true } }}" } ] }, "options": {} }, "id": "V62_LOG_FACEBOOK_RESPOND_COMMENT", "name": "[DB] Log FB Comment", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [ 1300, 2540 ] },
    
    // --- GENERIC API FAILOVER MECHANISM (SHARED BY ALL PRIMARY AGENTS) ---
    {
      "parameters": { "conditions": { "boolean": [ { "value1": "={{ $json.execution && ($json.execution.error || ($response && ($response.statusCode >= 400 || $response.statusCode === undefined)) || ($json.execution.duration > 15000 && !$json.data && !($input.item.json.data && $input.item.json.data.text)) ) }}", "operation": "equal", "value2": true } ] } },
      "id": "V62_CHECK_PRIMARY_API_FAIL_GENERIC", "name": "🚀 V6.2 Check Primary API Fail (Generic)", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [ 480, -600 ] 
    },
    {
      "parameters": { "url": "https://api.backup-ai.n8n.example/v1/chat/completions", "method": "POST", "options": { "bodyFormat": "json", "timeout":15000 }, "sendBody": true, "contentType":"json", "jsonBody": "={{ { model: 'backup-model-chat', messages: [ {role: 'system', content: ($input.item.json.options && $input.item.json.options.systemMessage) || ($json.options && $json.options.systemMessage) || 'You are a helpful AI social media assistant.'}, {role: 'user', content: `PREVIOUS CONTEXT (if any): ${JSON.stringify($('V61_INJECT_RETRIEVED_CONTEXT').item.json.retrieved_context)}. CURRENT TASK: ${($input.item.json.text || $json.text || 'Unavailable')}. TASK DETAILS: ${JSON.stringify($('V61_INJECT_RETRIEVED_CONTEXT').item.json.task_details_with_context)}. SENTIMENT: ${($input.item.json.sentiment || $json.sentiment || 'neutral')}. Please provide a response for this social media task.` } ], temperature: 0.7 } }}", "authentication": "predefinedCredential", "nodeCredentialType": "httpHeaderAuth", "sendHeaders": true, "headerParameters": { "parameters": [ {"name":"Authorization", "value":"Bearer {{ $credentials.DefaultBackupAIAuth.apiKey }}"} ] } },
      "id": "V62_CALL_BACKUP_API_GENERIC", "name": "🚀 V6.2 Call Backup AI (Generic)", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [ 480, -460 ], "continueOnFail": true,
      "credentials": {"httpHeaderAuth":{"id":"yourBackupAICredentialID", "name":"DefaultBackupAIAuth"}}
    },
    {
      "parameters": { "conditions": { "boolean": [ { "value1": "={{ $('V62_CALL_BACKUP_API_GENERIC').execution.error || ($response && $response.statusCode >= 400) || !($json.data && $json.data.choices && $json.data.choices[0] && $json.data.choices[0].message && $json.data.choices[0].message.content) }}", "operation": "equal", "value2": true } ] } },
      "id": "V62_CHECK_BACKUP_API_FAIL_GENERIC", "name": "🚀 V6.2 Check Backup API Fail (Generic)", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [ 480, -320 ]
    },
    {
        "parameters": {
            "values": {
                "string": [
                    { "name": "final_agent_output", "value": "={{ $('V62_CHECK_PRIMARY_API_FAIL_GENERIC').first(0).json.branch === 0 ? ($('V62_CALL_BACKUP_API_GENERIC').first(0).json.data && $('V62_CALL_BACKUP_API_GENERIC').first(0).json.data.choices && $('V62_CALL_BACKUP_API_GENERIC').first(0).json.data.choices[0] ? $('V62_CALL_BACKUP_API_GENERIC').first(0).json.data.choices[0].message.content : 'Backup API failed to return content.') : ($input.item.json.data.text || $input.item.json.data || $json.data.text || $json.data) }}"},
                    { "name": "api_used", "value": "={{ $('V62_CHECK_PRIMARY_API_FAIL_GENERIC').first(0).json.branch === 0 ? 'backup' : 'primary' }}"},
                    { "name": "api_primary_failed_count", "value":"={{ $('V62_CHECK_PRIMARY_API_FAIL_GENERIC').first(0).json.branch === 0 ? 1 : 0 }}"},
                    { "name": "api_backup_used_count", "value":"={{ ($('V62_CHECK_PRIMARY_API_FAIL_GENERIC').first(0).json.branch === 0 && !$('V62_CHECK_BACKUP_API_FAIL_GENERIC').first(0).json.branch === 1) ? 1 : 0 }}"}
                ]
            },
            "options": {"keepOnlySet": false, "include": "all"}
        },
        "id": "V62_MERGE_API_RESPONSES_GENERIC", "name": "V6.2 Merge API Responses (Generic)", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [ 700, -600 ] 
    },

    // --- CONTEXT SAVING MECHANISM (SHARED) ---
    {
      "parameters": {
        "functionCode": "const crypto = require('crypto');\nlet currentContextTasks = ($('V61_INJECT_RETRIEVED_CONTEXT').first(0).json.retrieved_context || []);\nif (!Array.isArray(currentContextTasks)) currentContextTasks = [];\n\nconst dataToEncrypt = {\n  tasks: currentContextTasks.concat([\n    {\n      task: $('V61_INJECT_RETRIEVED_CONTEXT').first(0).json.task_details_with_context.task || items[0].json.task || $('START_NODE').first(0).json.task,\n      network: $('V61_INJECT_RETRIEVED_CONTEXT').first(0).json.task_details_with_context.network || items[0].json.network || $('START_NODE').first(0).json.network,\n      timestamp: $now.toISO(),\n      details: $('V61_INJECT_RETRIEVED_CONTEXT').first(0).json.task_details_with_context,\n      response: $json.final_agent_output || ($json.log_entry ? $json.log_entry.output_preview : null)\n    }\n  ]).slice(-5)\n};\nconst encryptionKey = $('V61_PREPARE_REDIS_KEYS').first(0).json.encryption_key;\nconst iv = crypto.randomBytes($('V61_PREPARE_REDIS_KEYS').first(0).json.encryption_iv_length);\nconst cipher = crypto.createCipheriv('aes-256-cbc', Buffer.from(encryptionKey), iv);\nlet encrypted = cipher.update(JSON.stringify(dataToEncrypt), 'utf8', 'hex');\nencrypted += cipher.final('hex');\nitems[0].json.encrypted_payload_for_storage = iv.toString('hex') + ':' + encrypted;\nreturn items;"
      },
      "id": "V62_ENCRYPT_CONTEXT_FOR_STORAGE",
      "name": "🔑 Encrypt Context for Storage",
      "type": "n8n-nodes-base.function",
      "typeVersion": 1,
      "position": [ 1600, 1100 ], // Central point after all logs
      "continueOnFail": true
    },
    {
      "parameters": { "url": "https://mock-redis-api.n8n.example/set", "method":"POST", "options": { "timeout": 5000 }, "sendHeaders": true, "headerParameters": { "parameters": [ { "name": "Content-Type", "value": "application/json" }, {"name": "X-API-Key", "value":"sample_redis_api_key"} ] }, "sendBody": true, "contentType": "json", "jsonBody": "={{ { \"key\": $('V61_PREPARE_REDIS_KEYS').first().json.redis_key_context, \"value\": $('V62_ENCRYPT_CONTEXT_FOR_STORAGE').first().json.encrypted_payload_for_storage, \"ttl_seconds\": 2592000 } }}" }, // TTL 30 days for context
      "id": "V61_REDIS_SAVE_CONTEXT",
      "name": "🚀 [Redis] Save Encrypted Context",
      "type": "n8n-nodes-base.httpRequest", 
      "typeVersion": 4.1,
      "position": [ 1600, 1220 ],
      "continueOnFail": true,
      "credentials": { "redisApiBasic": { "id": "yourRedisAuthCredentialID", "name": "DefaultRedisAuth"} }
    },
    {
        "parameters": { "conditions": { "boolean": [ { "value1": "={{ $('V61_REDIS_SAVE_CONTEXT').execution.error || $('V62_ENCRYPT_CONTEXT_FOR_STORAGE').execution.error }}", "operation": "equal", "value2": true } ] } },
        "id": "V62_CHECK_REDIS_SAVE_FAIL",
        "name": "🛠️ Check Redis Save/Encrypt Fail?",
        "type": "n8n-nodes-base.if",
        "typeVersion": 1,
        "position": [ 1600, 1340 ]
    },
    {
        "parameters": {
            "databaseType":"sqlite",
            "operation":"executeQuery",
            "query":"INSERT OR REPLACE INTO user_context_cache (key, value, updated_at) VALUES ('{{ $('V61_PREPARE_REDIS_KEYS').first().json.local_cache_key_context }}', '{{ $('V62_ENCRYPT_CONTEXT_FOR_STORAGE').first().json.encrypted_payload_for_storage }}', datetime('now'));"
        },
        "id": "V62_SQLITE_SAVE_FALLBACK",
        "name": "🛠️ [SQLite] Save Encrypted Fallback",
        "type": "n8n-nodes-base.database",
        "typeVersion": 1.1,
        "position": [ 1600, 1460 ],
        "continueOnFail": true,
        "credentials": { "sqlite": { "id": "yourSQLiteCredentialID", "name": "DefaultSQLiteDB" } }
    },
    {
        "parameters": {
            "values": {
                "string": [
                    { "name": "redis_save_status", "value": "={{ $('V61_REDIS_SAVE_CONTEXT').execution.error || $('V62_ENCRYPT_CONTEXT_FOR_STORAGE').execution.error ? 'failed' : 'success' }}"},
                    { "name": "local_cache_save_status", "value": "={{ $('V62_CHECK_REDIS_SAVE_FAIL').first().json.branch === 0 ? ($('V62_SQLITE_SAVE_FALLBACK').execution.error ? 'failed' : 'success') : 'not_attempted' }}"}
                ]
            },
            "options": {"keepOnlySet": false, "include": "all" }
        },
        "id": "V62_MERGE_SAVE_STATUSES",
        "name": "Merge Save Statuses",
        "type": "n8n-nodes-base.set",
        "typeVersion": 4.1,
        "position": [ 1860, 1340 ]
    },

    // --- FINAL OUTPUT & MONITORING (SHARED) ---
    {
      "parameters": { "mode": "mergeByIndex" },
      "id": "V5_MERGE_FINAL", "name": "Final Merge Point", "type": "n8n-nodes-base.merge", "typeVersion": 1.1, "position": [ 2080, 1340 ]
    },
     {
      "parameters": { "values": {"string": [{"name": "evolution_api_payload", "value":"Notification: Task for {{ $json.network }} - {{ $json.task }} completed." }]}, "options": {} },
      "id": "V55_EVOLUTION_API_NOTIFY", "name": "[TOOL] Send Result via Evolution API (Mock)", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [ 2300, 1340 ]
    },
    {
      "parameters": { "values": { "string": [{"name": "final_output", "value":"Operation completed. Output: {{ $json.final_agent_output ? $json.final_agent_output.substring(0,100) + '...' : ($json.log_entry && $json.log_entry.output_preview ? $json.log_entry.output_preview : 'No specific output for user.') }}"}] }, "options": {"keepOnlySet": true} },
      "id": "V5_OUTPUT_USER", "name": "[OUTPUT] Send Result to User", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [ 2500, 1340 ]
    },
    {
      "parameters": {
        "functionCode": "let primaryFailures = 0;\nlet backupUses = 0;\n\n// Assumes item has api_primary_failed_count and api_backup_used_count from V62_MERGE_API_RESPONSES_GENERIC\n// or they are 0 if cache hit / direct tool use.\nprimaryFailures = items[0].json.api_primary_failed_count || 0;\nbackupUses = items[0].json.api_backup_used_count || 0;\n\n// Aggregate if running V62_TEST_AUTOMATE, which might pass previous aggregates\nconst previousPrimaryFailures = $json.api_primary_total_failures || 0;\nconst previousBackupUses = $json.api_backup_total_used || 0;\n\nitems[0].json.api_primary_total_failures = previousPrimaryFailures + primaryFailures;\nitems[0].json.api_backup_total_used = previousBackupUses + backupUses;\n\nreturn items;"
      },
      "id": "V62_AGGREGATE_API_METRICS",
      "name": "V6.2 Aggregate API Metrics",
      "type": "n8n-nodes-base.function",
      "typeVersion": 1,
      "position": [2700, 1340]
    },
    {
      "parameters": {
        "values": {
          "string": [
            { "name": "api_primary_total_failures", "value": "={{ $('V62_AGGREGATE_API_METRICS').first().json.api_primary_total_failures }}" }, 
            { "name": "api_backup_total_used", "value": "={{ $('V62_AGGREGATE_API_METRICS').first().json.api_backup_total_used }}" },
            { "name": "redis_retrieval_status", "value": "={{ $('V61_INJECT_RETRIEVED_CONTEXT').first().json.redis_retrieval_status }}" },
            { "name": "local_cache_retrieval_status", "value": "={{ $('V61_INJECT_RETRIEVED_CONTEXT').first().json.local_cache_retrieval_status }}"},
            { "name": "redis_save_status", "value": "={{ $('V62_MERGE_SAVE_STATUSES').first().json.redis_save_status }}" },
            { "name": "local_cache_save_status", "value": "={{ $('V62_MERGE_SAVE_STATUSES').first().json.local_cache_save_status }}"}
          ]
        },
        "options": {"keepOnlySet": true, "include": "all" }
      },
      "id": "V5_FEEDBACK_CHATWOOT", "name": "V6.2 [MONITORING] Metrics to Webhook", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [ 2900, 1340 ] 
    },

    // --- ERROR HANDLING (SHARED) ---
    { "parameters": {}, "id": "V5_ERROR_TRIGGER", "name": "Error Trigger", "type": "n8n-nodes-base.errorTrigger", "typeVersion": 1, "position": [ -1520, 2800 ] },
    { "parameters": { "values": { "string": [ {"name": "errorMessage", "value": "={{ $execution.error ? $execution.error.message : ($input.item.json.errorMessage || 'Unknown error occurred.') }}"}, {"name": "errorNode", "value": "={{ $execution.error ? $execution.error.node.name : ($input.item.json.errorNode || 'N/A') }}"} ] }, "options": {} }, "id": "V5_ERROR_FORMAT", "name": "[ERROR] Format Error Message", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [ -1280, 2800 ] },
    { "parameters": { "message":"🚨 Workflow Error in Agente Mestre Mídia Social! Node: {{ $json.errorNode }}. Message: {{ $json.errorMessage }}", "options":{} }, "id": "V5_ERROR_NOTIFY", "name": "[ALERT] Notify Admin", "type": "n8n-nodes-base.noOp", "typeVersion": 1, "position": [ -1080, 2800 ] },

    // --- SCHEDULED TRIGGER AND TEST AUTOMATION ---
    { "parameters": { "mode": "everyX", "unit": "hours", "value": 24 }, "id": "V51_SCHEDULE_TRIGGER", "name": "Schedule Trigger (24h)", "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1.1, "position": [ -2600, 380 ] },
    { "parameters": { "values": { "string": [ { "name": "user_id", "value": "scheduled_task_runner_001" }, { "name": "network", "value": "LinkedIn" }, { "name": "task", "value": "generate_performance_report" }, { "name": "task_details", "type": "json", "value": "={{ { \"time_period_days\": 7, \"cache_key\": \"li_report_7d_v62_sched\" } }}" } ] }, "options": {} }, "id": "V53_PREPARE_SCHEDULE", "name": "Prepare Scheduled Task", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [ -2380, 380 ] },
    {
      "parameters": {
        "functionCode": "const testCases = [\n  // LinkedIn\n  { user_id: 'test_li_cp', network: 'LinkedIn', task: 'create_post', task_details: { topic: 'Test LinkedIn Post v6.2', cache_key: 'test_li_cp_k_v62' }, expected_output_field: 'final_agent_output' },\n  { user_id: 'test_li_gr', network: 'LinkedIn', task: 'generate_performance_report', task_details: { time_period_days: 1, cache_key: 'test_li_gr_k_v62' }, expected_output_field: 'final_agent_output' },\n  { user_id: 'test_li_rc', network: 'LinkedIn', task: 'respond_to_comment', task_details: { comment_id: 'li_c1_v62', comment_text: 'Test comment for LI v6.2', cache_key: 'test_li_rc_k_v62' }, expected_output_field: 'final_agent_output' },\n  // Instagram\n  { user_id: 'test_ig_cp', network: 'Instagram', task: 'create_post', task_details: { topic: 'Test IG Post v6.2', cache_key: 'test_ig_cp_k_v62' }, expected_output_field: 'final_agent_output' },\n  { user_id: 'test_ig_gr', network: 'Instagram', task: 'generate_performance_report', task_details: { time_period_days: 1, cache_key: 'test_ig_gr_k_v62' }, expected_output_field: 'final_agent_output' },\n  { user_id: 'test_ig_rc', network: 'Instagram', task: 'respond_to_comment', task_details: { comment_id: 'ig_c1_v62', comment_text: 'Test comment for IG v6.2', cache_key: 'test_ig_rc_k_v62' }, expected_output_field: 'final_agent_output' },\n  // X\n  { user_id: 'test_x_cp', network: 'X', task: 'create_post', task_details: { topic: 'Test X Post v6.2', cache_key: 'test_x_cp_k_v62' }, expected_output_field: 'final_agent_output' },\n  { user_id: 'test_x_gr', network: 'X', task: 'generate_performance_report', task_details: { time_period_days: 1, cache_key: 'test_x_gr_k_v62' }, expected_output_field: 'final_agent_output' },\n  { user_id: 'test_x_rc', network: 'X', task: 'respond_to_comment', task_details: { comment_id: 'x_c1_v62', comment_text: 'Test comment for X v6.2', cache_key: 'test_x_rc_k_v62' }, expected_output_field: 'final_agent_output' },\n  // TikTok\n  { user_id: 'test_tt_cp', network: 'TikTok', task: 'create_post', task_details: { topic: 'Test TikTok Post v6.2', cache_key: 'test_tt_cp_k_v62' }, expected_output_field: 'final_agent_output' },\n  { user_id: 'test_tt_gr', network: 'TikTok', task: 'generate_performance_report', task_details: { time_period_days: 1, cache_key: 'test_tt_gr_k_v62' }, expected_output_field: 'final_agent_output' },\n  { user_id: 'test_tt_rc', network: 'TikTok', task: 'respond_to_comment', task_details: { comment_id: 'tt_c1_v62', comment_text: 'Test comment for TikTok v6.2', cache_key: 'test_tt_rc_k_v62' }, expected_output_field: 'final_agent_output' },\n  // Facebook\n  { user_id: 'test_fb_cp', network: 'Facebook', task: 'create_post', task_details: { topic: 'Test FB Post v6.2', audience: 'general', cache_key: 'test_fb_cp_k_v62' }, expected_output_field: 'final_agent_output' },\n  { user_id: 'test_fb_gr', network: 'Facebook', task: 'generate_performance_report', task_details: { time_period_days: 1, cache_key: 'test_fb_gr_k_v62' }, expected_output_field: 'final_agent_output' },\n  { user_id: 'test_fb_rc', network: 'Facebook', task: 'respond_to_comment', task_details: { comment_id: 'fb_c1_v62', comment_text: 'Test comment for FB v6.2', cache_key: 'test_fb_rc_k_v62' }, expected_output_field: 'final_agent_output' },\n];\nreturn testCases.map(tc => ({ json: tc }));"
      },
      "id": "V62_TEST_AUTOMATE",
      "name": "🧪 V6.2 AUTOMATED TEST SUITE Runner",
      "type": "n8n-nodes-base.function",
      "typeVersion": 1,
      "position": [-2600, 200]
    },
    {
      "parameters": {
        "conditions": {
            "boolean": [
                { "value1": "={{ ($json.final_agent_output && typeof $json.final_agent_output === 'string' && $json.final_agent_output.length > 0) || ($json.log_entry && $json.log_entry.success === true) }}", "operation": "equal", "value2": true }
            ]
        }
      },
      "id": "V62_VALIDATE_TEST_OUTPUT",
      "name": "🧪 Validate Test Output",
      "type": "n8n-nodes-base.if",
      "typeVersion": 1,
      "position": [3100, 1340]
    },
    {
        "parameters": {"message": "✅ Test Passed: {{ $('V61_INJECT_RETRIEVED_CONTEXT').first().json.task_details_with_context.network || items[0].json.network }} - {{ $('V61_INJECT_RETRIEVED_CONTEXT').first().json.task_details_with_context.task || items[0].json.task }}"},
        "id": "V62_TEST_PASSED_MSG",
        "name":"Test Passed",
        "type":"n8n-nodes-base.noOp",
        "typeVersion":1,
        "position": [3300, 1340]
    },
    {
        "parameters": {"message": "❌ Test FAILED: {{ $('V61_INJECT_RETRIEVED_CONTEXT').first().json.task_details_with_context.network || items[0].json.network }} - {{ $('V61_INJECT_RETRIEVED_CONTEXT').first().json.task_details_with_context.task || items[0].json.task }} --- ERROR: {{ $json.errorMessage || 'Output missing or invalid.' }}"},
        "id": "V62_TEST_FAILED_MSG",
        "name":"Test FAILED - Go to Error",
        "type":"n8n-nodes-base.noOp", 
        "typeVersion":1,
        "position": [3300, 1460]
    }
  ],
  "connections": {
    "START_NODE": { "main": [ [ { "node": "V61_PREPARE_REDIS_KEYS", "type": "main", "index": 0 } ] ] },
    "V62_TEST_AUTOMATE": { "main": [ [ { "node": "V61_PREPARE_REDIS_KEYS", "type": "main", "index": 0 } ] ] },
    "V51_SCHEDULE_TRIGGER": {"main": [ [ { "node": "V53_PREPARE_SCHEDULE", "type": "main", "index": 0 } ] ]},
    "V53_PREPARE_SCHEDULE": {"main": [ [ { "node": "V61_PREPARE_REDIS_KEYS", "type": "main", "index": 0 } ] ]},

    "V61_PREPARE_REDIS_KEYS": { "main": [ [ { "node": "V61_REDIS_RETRIEVE_CONTEXT_ENCRYPTED", "type": "main", "index": 0 } ] ] },
    "V61_REDIS_RETRIEVE_CONTEXT_ENCRYPTED": { "main": [ [ { "node": "V62_DECRYPT_REDIS_CONTEXT", "type": "main", "index": 0 } ] ] },
    "V62_DECRYPT_REDIS_CONTEXT": { "main": [ [ { "node": "V62_CHECK_REDIS_RETRIEVE_FAIL", "type": "main", "index": 0 } ] ] },
    "V62_CHECK_REDIS_RETRIEVE_FAIL": { "main": [ [ { "node": "V62_SQLITE_RETRIEVE_FALLBACK_ENCRYPTED", "type": "main", "index": 0 } ], [ { "node": "V61_INJECT_RETRIEVED_CONTEXT", "type": "main", "index": 0 } ] ] },
    "V62_SQLITE_RETRIEVE_FALLBACK_ENCRYPTED": { "main": [ [ { "node": "V62_DECRYPT_LOCAL_CACHE_CONTEXT", "type": "main", "index": 0 } ] ] },
    "V62_DECRYPT_LOCAL_CACHE_CONTEXT": { "main": [ [ { "node": "V61_INJECT_RETRIEVED_CONTEXT", "type": "main", "index": 0 } ] ] },
    "V61_INJECT_RETRIEVED_CONTEXT": {"main": [ [ { "node": "V55_VALIDATE_INFO", "type": "main", "index": 0 } ] ] },
    "V55_VALIDATE_INFO": { "main": [ [ { "node": "IF_INVALID_GLOBAL_INPUT", "type": "main", "index": 0 } ] ] },
    "IF_INVALID_GLOBAL_INPUT": {"main": [ [{"node": "V5_ERROR_TRIGGER", "type": "main", "index": 0}], [{"node":"V55_ANALYZE_MEDIA", "type":"main", "index":0}]]},
    "V55_ANALYZE_MEDIA": { "main": [ [ { "node": "V5_ROUTE_NETWORK", "type": "main", "index": 0 } ] ] },
    
    "V5_ROUTE_NETWORK": {
      "main": [
        [ { "node": "V54_ROUTE_LINKEDIN_TASK", "type": "main", "index": 0 } ],
        [ { "node": "V54_ROUTE_INSTAGRAM_TASK", "type": "main", "index": 0 } ],
        [ { "node": "V54_ROUTE_X_TASK", "type": "main", "index": 0 } ],
        [ { "node": "V54_ROUTE_TIKTOK_TASK", "type": "main", "index": 0 } ],
        [ { "node": "V54_ROUTE_FACEBOOK_TASK", "type": "main", "index": 0 } ]
      ]
    },

    // LinkedIn Task Routing and Flow
    "V54_ROUTE_LINKEDIN_TASK": { "main": [ 
        [{"node": "V62_VALIDATE_LINKEDIN_CREATE_POST", "type": "main", "index": 0}], 
        [{"node": "V62_VALIDATE_LINKEDIN_GENERATE_REPORT", "type": "main", "index": 0}], 
        [{"node": "V62_VALIDATE_LINKEDIN_RESPOND_COMMENT", "type": "main", "index": 0}] 
    ]},
    "V62_VALIDATE_LINKEDIN_CREATE_POST": {"main": [[{"node": "V62_AUTH_LINKEDIN", "type":"main", "index": 0}],[{"node":"V5_ERROR_TRIGGER", "type":"main", "index":0}]]},
    "V62_VALIDATE_LINKEDIN_GENERATE_REPORT": {"main": [[{"node": "V62_AUTH_LINKEDIN", "type":"main", "index": 0}],[{"node":"V5_ERROR_TRIGGER", "type":"main", "index":0}]]},
    "V62_VALIDATE_LINKEDIN_RESPOND_COMMENT": {"main": [[{"node": "V62_AUTH_LINKEDIN", "type":"main", "index": 0}],[{"node":"V5_ERROR_TRIGGER", "type":"main", "index":0}]]},
    "V62_AUTH_LINKEDIN": {"main": [[{"node": "V62_CHECK_TOKEN_LINKEDIN", "type":"main", "index":0}]]},
    "V62_CHECK_TOKEN_LINKEDIN": { "main": [
        [{"node": "V62_RENEW_TOKEN_LINKEDIN", "type":"main", "index":0}], 
        [{"node": "V62_SENTIMENT_LINKEDIN_CREATE_POST", "type":"main", "output_name": "false", "index":0}] // Assuming create_post is default or specific
    ]},
    "V62_RENEW_TOKEN_LINKEDIN": {"main": [[{"node": "V62_SENTIMENT_LINKEDIN_CREATE_POST", "type":"main", "index":0}]]},
    // LinkedIn Create Post Flow
    "V62_SENTIMENT_LINKEDIN_CREATE_POST": {"main": [[{"node": "V62_CACHE_CHECK_LINKEDIN_CREATE_POST", "type":"main", "index":0}]]},
    "V62_CACHE_CHECK_LINKEDIN_CREATE_POST": {"main": [
        [{"node": "V62_LOG_LINKEDIN_CREATE_POST", "type":"main", "index":0}], // Cache Hit
        [{"node": "V62_AGENT_LINKEDIN_CREATE_POST", "type":"main", "index":0}]  // Cache Miss
    ]},
    "V62_AGENT_LINKEDIN_CREATE_POST": {"main": [[{"node":"V62_CHECK_PRIMARY_API_FAIL_GENERIC", "type":"main", "index":0}]]},
    // V62_MERGE_API_RESPONSES_GENERIC for LinkedIn Create Post flows to V62_CACHE_SAVE_LINKEDIN_CREATE_POST (see generic connections below)
    "V62_CACHE_SAVE_LINKEDIN_CREATE_POST": {"main": [[{"node": "V62_TOOL_LINKEDIN_CREATE_POST", "type":"main", "index":0}]]},
    "V62_TOOL_LINKEDIN_CREATE_POST": {"main": [[{"node": "V62_LOG_LINKEDIN_CREATE_POST", "type":"main", "index":0}]]},
    "V62_LOG_LINKEDIN_CREATE_POST": {"main": [[{"node": "V62_ENCRYPT_CONTEXT_FOR_STORAGE", "type":"main", "index":0}]]},
    // LinkedIn Generate Report Flow (Auth already leads here or to Sentiment then here for Create Post)
    // Connect V62_CHECK_TOKEN_LINKEDIN (false) or V62_RENEW_TOKEN_LINKEDIN to appropriate sentiment/cache check
    // Assuming from V62_CHECK_TOKEN_LINKEDIN(false) or V62_RENEW_TOKEN_LINKEDIN to Sentiment for specific tasks
    // V62_CHECK_TOKEN_LINKEDIN output "false" for Generate Report path connects to V62_SENTIMENT_LINKEDIN_GENERATE_REPORT
    "V62_SENTIMENT_LINKEDIN_GENERATE_REPORT": {"main": [[{"node": "V62_CACHE_CHECK_LINKEDIN_GENERATE_REPORT", "type":"main", "index":0}]]},
    "V62_CACHE_CHECK_LINKEDIN_GENERATE_REPORT": {"main": [
        [{"node": "V62_LOG_LINKEDIN_GENERATE_REPORT", "type":"main", "index":0}], // Cache Hit
        [{"node": "V62_GET_ANALYTICS_LINKEDIN_REPORT", "type":"main", "index":0}]  // Cache Miss
    ]},
    "V62_GET_ANALYTICS_LINKEDIN_REPORT": {"main": [[{"node": "V62_AGENT_LINKEDIN_GENERATE_REPORT", "type":"main", "index":0}]]},
    "V62_AGENT_LINKEDIN_GENERATE_REPORT": {"main": [[{"node":"V62_CHECK_PRIMARY_API_FAIL_GENERIC", "type":"main", "index":0}]]},
    "V62_CACHE_SAVE_LINKEDIN_GENERATE_REPORT": {"main": [[{"node": "V62_TOOL_LINKEDIN_GENERATE_REPORT", "type":"main", "index":0}]]}, // After API Merge
    "V62_TOOL_LINKEDIN_GENERATE_REPORT": {"main": [[{"node": "V62_LOG_LINKEDIN_GENERATE_REPORT", "type":"main", "index":0}]]},
    "V62_LOG_LINKEDIN_GENERATE_REPORT": {"main": [[{"node": "V62_ENCRYPT_CONTEXT_FOR_STORAGE", "type":"main", "index":0}]]},
    // LinkedIn Respond Comment Flow
    "V62_SENTIMENT_LINKEDIN_RESPOND_COMMENT": {"main": [[{"node": "V62_CACHE_CHECK_LINKEDIN_RESPOND_COMMENT", "type":"main", "index":0}]]},
    "V62_CACHE_CHECK_LINKEDIN_RESPOND_COMMENT": {"main": [
        [{"node": "V62_LOG_LINKEDIN_RESPOND_COMMENT", "type":"main", "index":0}], // Cache Hit
        [{"node": "V62_AGENT_LINKEDIN_RESPOND_COMMENT", "type":"main", "index":0}]  // Cache Miss
    ]},
    "V62_AGENT_LINKEDIN_RESPOND_COMMENT": {"main": [[{"node":"V62_CHECK_PRIMARY_API_FAIL_GENERIC", "type":"main", "index":0}]]},
    "V62_CACHE_SAVE_LINKEDIN_RESPOND_COMMENT": {"main": [[{"node": "V62_TOOL_LINKEDIN_RESPOND_COMMENT", "type":"main", "index":0}]]},
    "V62_TOOL_LINKEDIN_RESPOND_COMMENT": {"main": [[{"node": "V62_LOG_LINKEDIN_RESPOND_COMMENT", "type":"main", "index":0}]]},
    "V62_LOG_LINKEDIN_RESPOND_COMMENT": {"main": [[{"node": "V62_ENCRYPT_CONTEXT_FOR_STORAGE", "type":"main", "index":0}]]},

    // Instagram Task Routing and Flow
    "V54_ROUTE_INSTAGRAM_TASK": { "main": [ 
        [{"node": "V62_VALIDATE_INSTAGRAM_CREATE_POST", "type": "main", "index": 0}], 
        [{"node": "V62_VALIDATE_INSTAGRAM_GENERATE_REPORT", "type": "main", "index": 0}], 
        [{"node": "V62_VALIDATE_INSTAGRAM_RESPOND_COMMENT", "type": "main", "index": 0}] 
    ]},
    "V62_VALIDATE_INSTAGRAM_CREATE_POST": {"main": [[{"node": "V62_AUTH_INSTAGRAM", "type":"main", "index": 0}],[{"node":"V5_ERROR_TRIGGER", "type":"main", "index":0}]]},
    "V62_VALIDATE_INSTAGRAM_GENERATE_REPORT": {"main": [[{"node": "V62_AUTH_INSTAGRAM", "type":"main", "index": 0}],[{"node":"V5_ERROR_TRIGGER", "type":"main", "index":0}]]},
    "V62_VALIDATE_INSTAGRAM_RESPOND_COMMENT": {"main": [[{"node": "V62_AUTH_INSTAGRAM", "type":"main", "index": 0}],[{"node":"V5_ERROR_TRIGGER", "type":"main", "index":0}]]},
    "V62_AUTH_INSTAGRAM": {"main": [[{"node": "V62_CHECK_TOKEN_INSTAGRAM", "type":"main", "index":0}]]},
    "V62_CHECK_TOKEN_INSTAGRAM": { "main": [
        [{"node": "V62_RENEW_TOKEN_INSTAGRAM", "type":"main", "index":0}], 
        [{"node": "V62_SENTIMENT_INSTAGRAM_CREATE_POST", "type":"main", "output_name":"false", "index":0}]
    ]},
    "V62_RENEW_TOKEN_INSTAGRAM": {"main": [[{"node": "V62_SENTIMENT_INSTAGRAM_CREATE_POST", "type":"main", "index":0}]]},
    "V62_SENTIMENT_INSTAGRAM_CREATE_POST": {"main": [[{"node": "V62_CACHE_CHECK_INSTAGRAM_CREATE_POST", "type":"main", "index":0}]]},
    "V62_CACHE_CHECK_INSTAGRAM_CREATE_POST": {"main": [[{"node": "V62_LOG_INSTAGRAM_CREATE_POST", "type":"main", "index":0}], [{"node": "V62_AGENT_INSTAGRAM_CREATE_POST", "type":"main", "index":0}]]},
    "V62_AGENT_INSTAGRAM_CREATE_POST": {"main": [[{"node":"V62_CHECK_PRIMARY_API_FAIL_GENERIC", "type":"main", "index":0}]]},
    "V62_CACHE_SAVE_INSTAGRAM_CREATE_POST": {"main": [[{"node": "V62_TOOL_INSTAGRAM_CREATE_POST", "type":"main", "index":0}]]},
    "V62_TOOL_INSTAGRAM_CREATE_POST": {"main": [[{"node": "V62_LOG_INSTAGRAM_CREATE_POST", "type":"main", "index":0}]]},
    "V62_LOG_INSTAGRAM_CREATE_POST": {"main": [[{"node": "V62_ENCRYPT_CONTEXT_FOR_STORAGE", "type":"main", "index":0}]]},
    "V62_SENTIMENT_INSTAGRAM_GENERATE_REPORT": {"main": [[{"node": "V62_CACHE_CHECK_INSTAGRAM_GENERATE_REPORT", "type":"main", "index":0}]]},
    "V62_CACHE_CHECK_INSTAGRAM_GENERATE_REPORT": {"main": [[{"node": "V62_LOG_INSTAGRAM_GENERATE_REPORT", "type":"main", "index":0}], [{"node": "V62_GET_ANALYTICS_INSTAGRAM_REPORT", "type":"main", "index":0}]]},
    "V62_GET_ANALYTICS_INSTAGRAM_REPORT": {"main": [[{"node": "V62_AGENT_INSTAGRAM_GENERATE_REPORT", "type":"main", "index":0}]]},
    "V62_AGENT_INSTAGRAM_GENERATE_REPORT": {"main": [[{"node":"V62_CHECK_PRIMARY_API_FAIL_GENERIC", "type":"main", "index":0}]]},
    "V62_CACHE_SAVE_INSTAGRAM_GENERATE_REPORT": {"main": [[{"node": "V62_TOOL_INSTAGRAM_GENERATE_REPORT", "type":"main", "index":0}]]},
    "V62_TOOL_INSTAGRAM_GENERATE_REPORT": {"main": [[{"node": "V62_LOG_INSTAGRAM_GENERATE_REPORT", "type":"main", "index":0}]]},
    "V62_LOG_INSTAGRAM_GENERATE_REPORT": {"main": [[{"node": "V62_ENCRYPT_CONTEXT_FOR_STORAGE", "type":"main", "index":0}]]},
    "V62_SENTIMENT_INSTAGRAM_RESPOND_COMMENT": {"main": [[{"node": "V62_CACHE_CHECK_INSTAGRAM_RESPOND_COMMENT", "type":"main", "index":0}]]},
    "V62_CACHE_CHECK_INSTAGRAM_RESPOND_COMMENT": {"main": [[{"node": "V62_LOG_INSTAGRAM_RESPOND_COMMENT", "type":"main", "index":0}], [{"node": "V62_AGENT_INSTAGRAM_RESPOND_COMMENT", "type":"main", "index":0}]]},
    "V62_AGENT_INSTAGRAM_RESPOND_COMMENT": {"main": [[{"node":"V62_CHECK_PRIMARY_API_FAIL_GENERIC", "type":"main", "index":0}]]},
    "V62_CACHE_SAVE_INSTAGRAM_RESPOND_COMMENT": {"main": [[{"node": "V62_TOOL_INSTAGRAM_RESPOND_COMMENT", "type":"main", "index":0}]]},
    "V62_TOOL_INSTAGRAM_RESPOND_COMMENT": {"main": [[{"node": "V62_LOG_INSTAGRAM_RESPOND_COMMENT", "type":"main", "index":0}]]},
    "V62_LOG_INSTAGRAM_RESPOND_COMMENT": {"main": [[{"node": "V62_ENCRYPT_CONTEXT_FOR_STORAGE", "type":"main", "index":0}]]},

    // X Task Routing and Flow (similar pattern to LinkedIn/Instagram)
    "V54_ROUTE_X_TASK": { "main": [ 
        [{"node": "V62_VALIDATE_X_CREATE_POST", "type": "main", "index": 0}], 
        [{"node": "V62_VALIDATE_X_GENERATE_REPORT", "type": "main", "index": 0}], 
        [{"node": "V62_VALIDATE_X_RESPOND_COMMENT", "type": "main", "index": 0}] 
    ]},
    "V62_VALIDATE_X_CREATE_POST": {"main": [[{"node": "V62_AUTH_X", "type":"main", "index": 0}],[{"node":"V5_ERROR_TRIGGER", "type":"main", "index":0}]]},
    "V62_VALIDATE_X_GENERATE_REPORT": {"main": [[{"node": "V62_AUTH_X", "type":"main", "index": 0}],[{"node":"V5_ERROR_TRIGGER", "type":"main", "index":0}]]},
    "V62_VALIDATE_X_RESPOND_COMMENT": {"main": [[{"node": "V62_AUTH_X", "type":"main", "index": 0}],[{"node":"V5_ERROR_TRIGGER", "type":"main", "index":0}]]},
    "V62_AUTH_X": {"main": [[{"node": "V62_CHECK_TOKEN_X", "type":"main", "index":0}]]},
    "V62_CHECK_TOKEN_X": { "main": [
        [{"node": "V62_RENEW_TOKEN_X", "type":"main", "index":0}], 
        [{"node": "V62_SENTIMENT_X_CREATE_POST", "type":"main", "output_name":"false", "index":0}]
    ]},
    "V62_RENEW_TOKEN_X": {"main": [[{"node": "V62_SENTIMENT_X_CREATE_POST", "type":"main", "index":0}]]},
    "V62_SENTIMENT_X_CREATE_POST": {"main": [[{"node": "V62_CACHE_CHECK_X_CREATE_POST", "type":"main", "index":0}]]},
    "V62_CACHE_CHECK_X_CREATE_POST": {"main": [[{"node": "V62_LOG_X_CREATE_POST", "type":"main", "index":0}], [{"node": "V62_AGENT_X_CREATE_POST", "type":"main", "index":0}]]},
    "V62_AGENT_X_CREATE_POST": {"main": [[{"node":"V62_CHECK_PRIMARY_API_FAIL_GENERIC", "type":"main", "index":0}]]},
    "V62_CACHE_SAVE_X_CREATE_POST": {"main": [[{"node": "V62_TOOL_X_CREATE_POST", "type":"main", "index":0}]]},
    "V62_TOOL_X_CREATE_POST": {"main": [[{"node": "V62_LOG_X_CREATE_POST", "type":"main", "index":0}]]},
    "V62_LOG_X_CREATE_POST": {"main": [[{"node": "V62_ENCRYPT_CONTEXT_FOR_STORAGE", "type":"main", "index":0}]]},
    "V62_SENTIMENT_X_GENERATE_REPORT": {"main": [[{"node": "V62_CACHE_CHECK_X_GENERATE_REPORT", "type":"main", "index":0}]]},
    "V62_CACHE_CHECK_X_GENERATE_REPORT": {"main": [[{"node": "V62_LOG_X_GENERATE_REPORT", "type":"main", "index":0}], [{"node": "V62_GET_ANALYTICS_X_REPORT", "type":"main", "index":0}]]},
    "V62_GET_ANALYTICS_X_REPORT": {"main": [[{"node": "V62_AGENT_X_GENERATE_REPORT", "type":"main", "index":0}]]},
    "V62_AGENT_X_GENERATE_REPORT": {"main": [[{"node":"V62_CHECK_PRIMARY_API_FAIL_GENERIC", "type":"main", "index":0}]]},
    "V62_CACHE_SAVE_X_GENERATE_REPORT": {"main": [[{"node": "V62_TOOL_X_GENERATE_REPORT", "type":"main", "index":0}]]},
    "V62_TOOL_X_GENERATE_REPORT": {"main": [[{"node": "V62_LOG_X_GENERATE_REPORT", "type":"main", "index":0}]]},
    "V62_LOG_X_GENERATE_REPORT": {"main": [[{"node": "V62_ENCRYPT_CONTEXT_FOR_STORAGE", "type":"main", "index":0}]]},
    "V62_SENTIMENT_X_RESPOND_COMMENT": {"main": [[{"node": "V62_CACHE_CHECK_X_RESPOND_COMMENT", "type":"main", "index":0}]]},
    "V62_CACHE_CHECK_X_RESPOND_COMMENT": {"main": [[{"node": "V62_LOG_X_RESPOND_COMMENT", "type":"main", "index":0}], [{"node": "V62_AGENT_X_RESPOND_COMMENT", "type":"main", "index":0}]]},
    "V62_AGENT_X_RESPOND_COMMENT": {"main": [[{"node":"V62_CHECK_PRIMARY_API_FAIL_GENERIC", "type":"main", "index":0}]]},
    "V62_CACHE_SAVE_X_RESPOND_COMMENT": {"main": [[{"node": "V62_TOOL_X_RESPOND_COMMENT", "type":"main", "index":0}]]},
    "V62_TOOL_X_RESPOND_COMMENT": {"main": [[{"node": "V62_LOG_X_RESPOND_COMMENT", "type":"main", "index":0}]]},
    "V62_LOG_X_RESPOND_COMMENT": {"main": [[{"node": "V62_ENCRYPT_CONTEXT_FOR_STORAGE", "type":"main", "index":0}]]},

    // TikTok Task Routing and Flow
    "V54_ROUTE_TIKTOK_TASK": { "main": [ 
        [{"node": "V62_VALIDATE_TIKTOK_CREATE_POST", "type": "main", "index": 0}], 
        [{"node": "V62_VALIDATE_TIKTOK_GENERATE_REPORT", "type": "main", "index": 0}], 
        [{"node": "V62_VALIDATE_TIKTOK_RESPOND_COMMENT", "type": "main", "index": 0}] 
    ]},
    "V62_VALIDATE_TIKTOK_CREATE_POST": {"main": [[{"node": "V62_AUTH_TIKTOK", "type":"main", "index": 0}],[{"node":"V5_ERROR_TRIGGER", "type":"main", "index":0}]]},
    "V62_VALIDATE_TIKTOK_GENERATE_REPORT": {"main": [[{"node": "V62_AUTH_TIKTOK", "type":"main", "index": 0}],[{"node":"V5_ERROR_TRIGGER", "type":"main", "index":0}]]},
    "V62_VALIDATE_TIKTOK_RESPOND_COMMENT": {"main": [[{"node": "V62_AUTH_TIKTOK", "type":"main", "index": 0}],[{"node":"V5_ERROR_TRIGGER", "type":"main", "index":0}]]},
    "V62_AUTH_TIKTOK": {"main": [[{"node": "V62_CHECK_TOKEN_TIKTOK", "type":"main", "index":0}]]},
    "V62_CHECK_TOKEN_TIKTOK": { "main": [
        [{"node": "V62_RENEW_TOKEN_TIKTOK", "type":"main", "index":0}], 
        [{"node": "V62_SENTIMENT_TIKTOK_CREATE_POST", "type":"main", "output_name":"false", "index":0}]
    ]},
    "V62_RENEW_TOKEN_TIKTOK": {"main": [[{"node": "V62_SENTIMENT_TIKTOK_CREATE_POST", "type":"main", "index":0}]]},
    "V62_SENTIMENT_TIKTOK_CREATE_POST": {"main": [[{"node": "V62_CACHE_CHECK_TIKTOK_CREATE_POST", "type":"main", "index":0}]]},
    "V62_CACHE_CHECK_TIKTOK_CREATE_POST": {"main": [[{"node": "V62_LOG_TIKTOK_CREATE_POST", "type":"main", "index":0}], [{"node": "V62_AGENT_TIKTOK_CREATE_POST", "type":"main", "index":0}]]},
    "V62_AGENT_TIKTOK_CREATE_POST": {"main": [[{"node":"V62_CHECK_PRIMARY_API_FAIL_GENERIC", "type":"main", "index":0}]]},
    "V62_CACHE_SAVE_TIKTOK_CREATE_POST": {"main": [[{"node": "V62_TOOL_TIKTOK_CREATE_POST", "type":"main", "index":0}]]},
    "V62_TOOL_TIKTOK_CREATE_POST": {"main": [[{"node": "V62_LOG_TIKTOK_CREATE_POST", "type":"main", "index":0}]]},
    "V62_LOG_TIKTOK_CREATE_POST": {"main": [[{"node": "V62_ENCRYPT_CONTEXT_FOR_STORAGE", "type":"main", "index":0}]]},
    "V62_SENTIMENT_TIKTOK_GENERATE_REPORT": {"main": [[{"node": "V62_CACHE_CHECK_TIKTOK_GENERATE_REPORT", "type":"main", "index":0}]]},
    "V62_CACHE_CHECK_TIKTOK_GENERATE_REPORT": {"main": [[{"node": "V62_LOG_TIKTOK_GENERATE_REPORT", "type":"main", "index":0}], [{"node": "V62_GET_ANALYTICS_TIKTOK_REPORT", "type":"main", "index":0}]]},
    "V62_GET_ANALYTICS_TIKTOK_REPORT": {"main": [[{"node": "V62_AGENT_TIKTOK_GENERATE_REPORT", "type":"main", "index":0}]]},
    "V62_AGENT_TIKTOK_GENERATE_REPORT": {"main": [[{"node":"V62_CHECK_PRIMARY_API_FAIL_GENERIC", "type":"main", "index":0}]]},
    "V62_CACHE_SAVE_TIKTOK_GENERATE_REPORT": {"main": [[{"node": "V62_TOOL_TIKTOK_GENERATE_REPORT", "type":"main", "index":0}]]},
    "V62_TOOL_TIKTOK_GENERATE_REPORT": {"main": [[{"node": "V62_LOG_TIKTOK_GENERATE_REPORT", "type":"main", "index":0}]]},
    "V62_LOG_TIKTOK_GENERATE_REPORT": {"main": [[{"node": "V62_ENCRYPT_CONTEXT_FOR_STORAGE", "type":"main", "index":0}]]},
    "V62_SENTIMENT_TIKTOK_RESPOND_COMMENT": {"main": [[{"node": "V62_CACHE_CHECK_TIKTOK_RESPOND_COMMENT", "type":"main", "index":0}]]},
    "V62_CACHE_CHECK_TIKTOK_RESPOND_COMMENT": {"main": [[{"node": "V62_LOG_TIKTOK_RESPOND_COMMENT", "type":"main", "index":0}], [{"node": "V62_AGENT_TIKTOK_RESPOND_COMMENT", "type":"main", "index":0}]]},
    "V62_AGENT_TIKTOK_RESPOND_COMMENT": {"main": [[{"node":"V62_CHECK_PRIMARY_API_FAIL_GENERIC", "type":"main", "index":0}]]},
    "V62_CACHE_SAVE_TIKTOK_RESPOND_COMMENT": {"main": [[{"node": "V62_TOOL_TIKTOK_RESPOND_COMMENT", "type":"main", "index":0}]]},
    "V62_TOOL_TIKTOK_RESPOND_COMMENT": {"main": [[{"node": "V62_LOG_TIKTOK_RESPOND_COMMENT", "type":"main", "index":0}]]},
    "V62_LOG_TIKTOK_RESPOND_COMMENT": {"main": [[{"node": "V62_ENCRYPT_CONTEXT_FOR_STORAGE", "type":"main", "index":0}]]},

    // Facebook Task Routing and Flow
    "V54_ROUTE_FACEBOOK_TASK": { "main": [ 
        [{"node": "V62_VALIDATE_FACEBOOK_CREATE_POST", "type": "main", "index": 0}], 
        [{"node": "V62_VALIDATE_FACEBOOK_GENERATE_REPORT", "type": "main", "index": 0}], 
        [{"node": "V62_VALIDATE_FACEBOOK_RESPOND_COMMENT", "type": "main", "index": 0}] 
    ]},
    "V62_VALIDATE_FACEBOOK_CREATE_POST": {"main": [[{"node": "V62_AUTH_FACEBOOK", "type":"main", "index": 0}],[{"node":"V5_ERROR_TRIGGER", "type":"main", "index":0}]]},
    "V62_VALIDATE_FACEBOOK_GENERATE_REPORT": {"main": [[{"node": "V62_AUTH_FACEBOOK", "type":"main", "index": 0}],[{"node":"V5_ERROR_TRIGGER", "type":"main", "index":0}]]},
    "V62_VALIDATE_FACEBOOK_RESPOND_COMMENT": {"main": [[{"node": "V62_AUTH_FACEBOOK", "type":"main", "index": 0}],[{"node":"V5_ERROR_TRIGGER", "type":"main", "index":0}]]},
    "V62_AUTH_FACEBOOK": {"main": [[{"node": "V62_CHECK_TOKEN_FACEBOOK", "type":"main", "index":0}]]},
    "V62_CHECK_TOKEN_FACEBOOK": { "main": [
        [{"node": "V62_RENEW_TOKEN_FACEBOOK", "type":"main", "index":0}], 
        [{"node": "V62_SENTIMENT_FACEBOOK_CREATE_POST", "type":"main", "output_name":"false", "index":0}]
    ]},
    "V62_RENEW_TOKEN_FACEBOOK": {"main": [[{"node": "V62_SENTIMENT_FACEBOOK_CREATE_POST", "type":"main", "index":0}]]},
    "V62_SENTIMENT_FACEBOOK_CREATE_POST": {"main": [[{"node": "V62_CACHE_CHECK_FACEBOOK_CREATE_POST", "type":"main", "index":0}]]},
    "V62_CACHE_CHECK_FACEBOOK_CREATE_POST": {"main": [[{"node": "V62_LOG_FACEBOOK_CREATE_POST", "type":"main", "index":0}], [{"node": "V62_AGENT_FACEBOOK_CREATE_POST", "type":"main", "index":0}]]},
    "V62_AGENT_FACEBOOK_CREATE_POST": {"main": [[{"node":"V62_CHECK_PRIMARY_API_FAIL_GENERIC", "type":"main", "index":0}]]},
    "V62_CACHE_SAVE_FACEBOOK_CREATE_POST": {"main": [[{"node": "V62_TOOL_FACEBOOK_CREATE_POST", "type":"main", "index":0}]]},
    "V62_TOOL_FACEBOOK_CREATE_POST": {"main": [[{"node": "V62_LOG_FACEBOOK_CREATE_POST", "type":"main", "index":0}]]},
    "V62_LOG_FACEBOOK_CREATE_POST": {"main": [[{"node": "V62_ENCRYPT_CONTEXT_FOR_STORAGE", "type":"main", "index":0}]]},
    "V62_SENTIMENT_FACEBOOK_GENERATE_REPORT": {"main": [[{"node": "V62_CACHE_CHECK_FACEBOOK_GENERATE_REPORT", "type":"main", "index":0}]]},
    "V62_CACHE_CHECK_FACEBOOK_GENERATE_REPORT": {"main": [[{"node": "V62_LOG_FACEBOOK_GENERATE_REPORT", "type":"main", "index":0}], [{"node": "V62_GET_ANALYTICS_FACEBOOK_REPORT", "type":"main", "index":0}]]},
    "V62_GET_ANALYTICS_FACEBOOK_REPORT": {"main": [[{"node": "V62_AGENT_FACEBOOK_GENERATE_REPORT", "type":"main", "index":0}]]},
    "V62_AGENT_FACEBOOK_GENERATE_REPORT": {"main": [[{"node":"V62_CHECK_PRIMARY_API_FAIL_GENERIC", "type":"main", "index":0}]]},
    "V62_CACHE_SAVE_FACEBOOK_GENERATE_REPORT": {"main": [[{"node": "V62_TOOL_FACEBOOK_GENERATE_REPORT", "type":"main", "index":0}]]},
    "V62_TOOL_FACEBOOK_GENERATE_REPORT": {"main": [[{"node": "V62_LOG_FACEBOOK_GENERATE_REPORT", "type":"main", "index":0}]]},
    "V62_LOG_FACEBOOK_GENERATE_REPORT": {"main": [[{"node": "V62_ENCRYPT_CONTEXT_FOR_STORAGE", "type":"main", "index":0}]]},
    "V62_SENTIMENT_FACEBOOK_RESPOND_COMMENT": {"main": [[{"node": "V62_CACHE_CHECK_FACEBOOK_RESPOND_COMMENT", "type":"main", "index":0}]]},
    "V62_CACHE_CHECK_FACEBOOK_RESPOND_COMMENT": {"main": [[{"node": "V62_LOG_FACEBOOK_RESPOND_COMMENT", "type":"main", "index":0}], [{"node": "V62_AGENT_FACEBOOK_RESPOND_COMMENT", "type":"main", "index":0}]]},
    "V62_AGENT_FACEBOOK_RESPOND_COMMENT": {"main": [[{"node":"V62_CHECK_PRIMARY_API_FAIL_GENERIC", "type":"main", "index":0}]]},
    "V62_CACHE_SAVE_FACEBOOK_RESPOND_COMMENT": {"main": [[{"node": "V62_TOOL_FACEBOOK_RESPOND_COMMENT", "type":"main", "index":0}]]},
    "V62_TOOL_FACEBOOK_RESPOND_COMMENT": {"main": [[{"node": "V62_LOG_FACEBOOK_RESPOND_COMMENT", "type":"main", "index":0}]]},
    "V62_LOG_FACEBOOK_RESPOND_COMMENT": {"main": [[{"node": "V62_ENCRYPT_CONTEXT_FOR_STORAGE", "type":"main", "index":0}]]},

    // Connections from V62_CHECK_TOKEN_<NETWORK> (false path) to specific task sentiment/cache check:
    // This is a bit tricky, as the 'false' output of CHECK_TOKEN would go to DIFFERENT sentiment nodes based on the task.
    // To simplify and follow the pattern where SENTIMENT comes after AUTH/TOKEN_CHECK and before CACHE_CHECK:
    // 1. LinkedIn
    "V62_CHECK_TOKEN_LINKEDIN": { "main": [ [{"node": "V62_RENEW_TOKEN_LINKEDIN", "type": "main", "index": 0}], [{"node": "V62_SENTIMENT_LINKEDIN_CREATE_POST", "type": "main", "index": 0}, {"node": "V62_SENTIMENT_LINKEDIN_GENERATE_REPORT", "type": "main", "index": 0}, {"node": "V62_SENTIMENT_LINKEDIN_RESPOND_COMMENT", "type": "main", "index": 0}] ]},
      // Note: This multiple connection from 'false' only works if V54_ROUTE_LINKEDIN_TASK filters the items BEFORE they reach V62_AUTH_LINKEDIN.
      // A more robust way might be an intermediate "IF task is X" after token check before specific sentiment, or sentiment nodes check task type.
      // For now, assuming current data flow has items filtered by task when they reach specific sentiment.
      // This pattern would be similar for other networks. For brevity, I'll rely on individual sentiment nodes being connected correctly
      // from their corresponding "Renew Token" or "Check Token (false)" paths based on the implicit task filtering.
      // Or rather, the path after AUTH and TOKEN RENEW must lead to a specific Sentiment node FOR THAT TASK.
      // So V62_CHECK_TOKEN_LINKEDIN false leads to the Sentiment of current LI task, V62_RENEW_TOKEN_LINKEDIN leads to Sentiment of current LI task.

    // --- GENERIC API FAILOVER CONNECTIONS ---
    "V62_CHECK_PRIMARY_API_FAIL_GENERIC": { "main": [ [ { "node": "V62_CALL_BACKUP_API_GENERIC", "type": "main", "index": 0 } ], [ { "node": "V62_MERGE_API_RESPONSES_GENERIC", "type": "main", "index": 0 } ] ] },
    "V62_CALL_BACKUP_API_GENERIC": { "main": [ [ { "node": "V62_CHECK_BACKUP_API_FAIL_GENERIC", "type": "main", "index": 0 } ] ] },
    "V62_CHECK_BACKUP_API_FAIL_GENERIC": { "main": [ 
        [ { "node": "V5_ERROR_TRIGGER", "type": "main", "index": 0 } ], 
        [ { "node": "V62_MERGE_API_RESPONSES_GENERIC", "type": "main", "index": 0 } ] 
    ]},
    // All V62_MERGE_API_RESPONSES_GENERIC outputs now need to go to their respective Cache Save nodes.
    // Example for LinkedIn Create Post:
    "V62_MERGE_API_RESPONSES_GENERIC": { "main": [ 
        [ {"node": "V62_CACHE_SAVE_LINKEDIN_CREATE_POST", "type":"main", "index": 0} ], //This needs to be conditional / multiplexed
        [ {"node": "V62_CACHE_SAVE_LINKEDIN_GENERATE_REPORT", "type":"main", "index": 0} ],
        [ {"node": "V62_CACHE_SAVE_LINKEDIN_RESPOND_COMMENT", "type":"main", "index": 0} ],
        [ {"node": "V62_CACHE_SAVE_INSTAGRAM_CREATE_POST", "type":"main", "index": 0} ],
        [ {"node": "V62_CACHE_SAVE_INSTAGRAM_GENERATE_REPORT", "type":"main", "index": 0} ],
        [ {"node": "V62_CACHE_SAVE_INSTAGRAM_RESPOND_COMMENT", "type":"main", "index": 0} ],
        [ {"node": "V62_CACHE_SAVE_X_CREATE_POST", "type":"main", "index": 0} ],
        [ {"node": "V62_CACHE_SAVE_X_GENERATE_REPORT", "type":"main", "index": 0} ],
        [ {"node": "V62_CACHE_SAVE_X_RESPOND_COMMENT", "type":"main", "index": 0} ],
        [ {"node": "V62_CACHE_SAVE_TIKTOK_CREATE_POST", "type":"main", "index": 0} ],
        [ {"node": "V62_CACHE_SAVE_TIKTOK_GENERATE_REPORT", "type":"main", "index": 0} ],
        [ {"node": "V62_CACHE_SAVE_TIKTOK_RESPOND_COMMENT", "type":"main", "index": 0} ],
        [ {"node": "V62_CACHE_SAVE_FACEBOOK_CREATE_POST", "type":"main", "index": 0} ],
        [ {"node": "V62_CACHE_SAVE_FACEBOOK_GENERATE_REPORT", "type":"main", "index": 0} ],
        [ {"node": "V62_CACHE_SAVE_FACEBOOK_RESPOND_COMMENT", "type":"main", "index": 0} ]
        // This is not how n8n connections work directly from one node to many conditionally.
        // The V62_MERGE_API_RESPONSES_GENERIC would output ONE item, which then flows to the appropriate CACHE_SAVE.
        // This means there must be a SWITCH or individual MERGE points AFTER V62_MERGE_API_RESPONSES_GENERIC
        // This makes the diagram much larger. Let's assume for now, it's logically routed or a single MERGE node
        // brings all agent paths to MERGE_API_RESPONSES_GENERIC and then another Merge node for all paths to specific cache save.
        // Simpler for diagram: all outputs from MERGE_API_RESPONSES_GENERIC lead to a single node or back to the flow.
        // **Correction:** V62_MERGE_API_RESPONSES_GENERIC will be hit by one task path at a time. The next node in *that specific task's flow*
        // will be its V62_CACHE_SAVE_<TASK> node. So the main array will be empty and individual tasks will route here and then route out.
        // This node will be connected from many sources and have many (implicit) destinations.

        // Re-simplifying the general flow after primary agent:
        // V62_AGENT_<NET>_<TASK> -> V62_CHECK_PRIMARY_API_FAIL_GENERIC.
        // V62_MERGE_API_RESPONSES_GENERIC -> V62_CACHE_SAVE_<NET>_<TASK> (for its respective task)

        // For now, let's just make one connection from V62_MERGE_API_RESPONSES_GENERIC to a general merge point
        // and handle specific task routing on the way to Cache Save.
        // The connection logic for these is tricky because it's a "shared subroutine".
        // Each specific agent (e.g. V62_AGENT_LINKEDIN_CREATE_POST) flows to V62_CHECK_PRIMARY_API_FAIL_GENERIC.
        // The TRUE output of V62_CHECK_BACKUP_API_FAIL_GENERIC flows to respective V62_CACHE_SAVE_<NETWORK>_<TASK>.
        // The FALSE output of V62_CHECK_PRIMARY_API_FAIL_GENERIC flows to respective V62_CACHE_SAVE_<NETWORK>_<TASK>.

        //This means the "main" for V62_MERGE_API_RESPONSES_GENERIC actually will point to the specific cache save based on where it came from.
        // This part requires very careful manual connection in n8n.
        // For the JSON, I will simply not provide a default "main" output for V62_MERGE_API_RESPONSES_GENERIC.
        // It will be connected from all fail/success backup paths and connected TO all cache_save paths.
      ]
    },


    // --- CONTEXT SAVING AND FINAL OUTPUT CONNECTIONS ---
    "V62_ENCRYPT_CONTEXT_FOR_STORAGE": {"main": [ [ { "node": "V61_REDIS_SAVE_CONTEXT", "type": "main", "index": 0 } ] ]},
    "V61_REDIS_SAVE_CONTEXT": { "main": [ [ { "node": "V62_CHECK_REDIS_SAVE_FAIL", "type": "main", "index": 0 } ] ] },
    "V62_CHECK_REDIS_SAVE_FAIL": { "main": [ [ { "node": "V62_SQLITE_SAVE_FALLBACK", "type": "main", "index": 0 } ], [ { "node": "V62_MERGE_SAVE_STATUSES", "type": "main", "index": 0 } ] ] },
    "V62_SQLITE_SAVE_FALLBACK": { "main": [ [ { "node": "V62_MERGE_SAVE_STATUSES", "type": "main", "index": 0 } ] ] },
    "V62_MERGE_SAVE_STATUSES": { "main": [ [ { "node": "V5_MERGE_FINAL", "type": "main", "index": 0 } ] ] },
    "V5_MERGE_FINAL": { "main": [ [ { "node": "V55_EVOLUTION_API_NOTIFY", "type": "main", "index": 0 } ] ] },
    "V55_EVOLUTION_API_NOTIFY": { "main": [ [ { "node": "V5_OUTPUT_USER", "type": "main", "index": 0 } ] ] },
    "V5_OUTPUT_USER": { "main": [ [ { "node": "V62_AGGREGATE_API_METRICS", "type": "main", "index": 0 } ] ] },
    "V62_AGGREGATE_API_METRICS": { "main": [ [ { "node": "V5_FEEDBACK_CHATWOOT", "type": "main", "index": 0 }, { "node": "V62_VALIDATE_TEST_OUTPUT", "type":"main", "index":0 } ] ] },
    "V5_FEEDBACK_CHATWOOT": {"main": [[]] }, 
    "V62_VALIDATE_TEST_OUTPUT": {"main": [[{"node":"V62_TEST_PASSED_MSG", "type":"main", "index":0}],[{"node":"V62_TEST_FAILED_MSG", "type":"main", "index":0}]]},
    "V62_TEST_PASSED_MSG": {"main": [[]]},
    "V62_TEST_FAILED_MSG": {"main": [[{"node":"V5_ERROR_TRIGGER", "type":"main", "index":0}]]},
    
    // ERROR TRIGGER
    "V5_ERROR_TRIGGER": { "main": [ [ { "node": "V5_ERROR_FORMAT", "type": "main", "index": 0 } ] ] },
    "V5_ERROR_FORMAT": { "main": [ [ { "node": "V5_ERROR_NOTIFY", "type": "main", "index": 0 } ] ] },
    "V5_ERROR_NOTIFY": {"main": [[]]}
  },
  "active": false,
  "settings": { "executionOrder": "v1", "errorWorkflow": "V5_ERROR_TRIGGER" },
  "versionId": "ULTRA_RESILIENT_V6_2_EXPANDED_FINAL",
  "meta": {},
  "id": "m1a2b3c4-d5e6-f7a8-b9c0-d1e7f5a3b4c2_v6_2_expanded",
  "tags": ["ultra-resiliente", "v6.2", "masterpiece-final", "full-expansion"]
}