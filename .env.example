# Arquivo de exemplo - copie para .env e preencha os valores
# O script Start-Environment.ps1 gera automaticamente este arquivo com senhas seguras

# Banco de Dados PostgreSQL
POSTGRES_PASSWORD=sua_senha_postgresql_aqui
POSTGRES_DB=postgres
POSTGRES_USER=postgres

# Autenticação da Evolution API
AUTHENTICATION_API_KEY=sua_chave_api_aqui

# MinIO (Armazenamento S3)
MINIO_ROOT_USER=admin
MINIO_ROOT_PASSWORD=sua_senha_minio_aqui

# Chatwoot
SECRET_KEY_BASE=sua_chave_secreta_chatwoot_aqui

# Configurações Redis
REDIS_URL=redis://redis:6379
CACHE_REDIS_ENABLED=true
CACHE_REDIS_URI=redis://redis:6379/6
CACHE_REDIS_PREFIX_KEY=evolution
CACHE_REDIS_SAVE_INSTANCES=false

# Configurações Evolution API
EVOLUTION_API_URL=http://localhost:8080
EVOLUTION_MANAGER_URL=http://localhost:8080/manager

# Configurações Chatwoot
CHATWOOT_URL=http://localhost:3000
RAILS_ENV=production

# Configurações N8N
N8N_URL=http://localhost:5678
N8N_BASIC_AUTH_ACTIVE=false

# Configurações MinIO
MINIO_URL=http://localhost:9000
MINIO_CONSOLE_URL=http://localhost:9001

# Configurações Ngrok (Opcional - para webhooks públicos)
# Obtenha seu token em: https://dashboard.ngrok.com/get-started/your-authtoken
# NGROK_AUTHTOKEN=seu_token_ngrok_aqui
# Descomente a linha acima e adicione seu token para usar o modo público
