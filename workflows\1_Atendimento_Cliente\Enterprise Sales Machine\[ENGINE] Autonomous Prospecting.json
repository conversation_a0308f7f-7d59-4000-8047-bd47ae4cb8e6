{"name": "[ENGINE] Autonomous Prospecting v1.1 - Cost-Aware", "nodes": [{"parameters": {"rule": "cron", "cronTime": "0 9 * * 1"}, "id": "e8d66df2-466d-4bb4-b258-3d1f0ddaf631", "name": "TRIGGER: Weekly (Mon 9am)", "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1.1, "position": [-200, 300]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "WITH ranked_contacts AS (SELECT c.id, c.nome, (SELECT COUNT(*) FROM agent.campaign_interactions ci WHERE ci.contact_id = c.id AND ci.achieved_goal = TRUE) as successful_campaigns, (SELECT AVG((sm.analise_sentimento->>'score')::float) FROM agent.mensagens sm WHERE sm.id_contato = c.id AND sm.direcao = 'entrada') as avg_sentiment_score, c.jornada_status FROM agent.contatos c WHERE c.jornada_status = 'convertido') SELECT id, nome FROM ranked_contacts ORDER BY successful_campaigns DESC, avg_sentiment_score DESC LIMIT 5;"}, "id": "c1f7ca45-1c39-44ef-a6b3-6cf1b4cd3b54", "name": "DB: Find Best Seed Contacts", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [40, 300], "credentials": {"postgres": {"id": "YOUR_POSTGRES_CREDENTIAL_ID"}}}, {"parameters": {"batchSize": 1}, "id": "b0cf31ea-be12-40f4-a034-7fc44a4113ae", "name": "Loop Over Each Seed", "type": "n8n-nodes-base.splitInBatches", "typeVersion": 2, "position": [260, 300]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT *, tags->>'social_club' as social_club, endereco_bairro, empresa_atual FROM agent.contatos WHERE id = $1 LIMIT 1;", "options": {"parameters": {"values": ["={{$json.id}}"]}}}, "id": "df2dae10-64cd-4e8c-859a-5f6068d34195", "name": "DB: Get Seed Details", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [460, 300], "credentials": {"postgres": {"id": "YOUR_POSTGRES_CREDENTIAL_ID"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT content FROM agent.memory_vectors WHERE contact_id = $1 ORDER BY timestamp DESC LIMIT 3;", "options": {"parameters": {"values": ["={{$json.id}}"]}}}, "id": "a1b2c3d4-e5f6-4a7b-8c9d-0e1f2a3b4c5d", "name": "DB: <PERSON> Seed's Long-Term Memory", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [680, 200], "credentials": {"postgres": {"id": "YOUR_POSTGRES_CREDENTIAL_ID"}}}, {"parameters": {"functionCode": "const strategies = ['endereco_bairro', 'empresa_atual', 'semantic_similarity'];\nconst hasMemory = $('a1b2c3d4-e5f6-4a7b-8c9d-0e1f2a3b4c5d').items.length > 0;\nconst availableStrategies = hasMemory ? strategies : ['endereco_bairro', 'empresa_atual'];\nconst randomIndex = Math.floor(Math.random() * availableStrategies.length);\n$json.strategy = availableStrategies[randomIndex];\nreturn $items;", "options": {}}, "id": "ab75d8d3-5569-45da-901d-531e2850fc25", "name": "CODE: Select Prospecting Strategy", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [880, 300]}, {"parameters": {"routing": {"rules": {"values": [{"value1": "={{$json.strategy}}", "value2": "endereco_bairro"}, {"value1": "={{$json.strategy}}", "value2": "empresa_atual"}]}, "fallback": "semantic_similarity"}}, "id": "switch_prospecting_strategy", "name": "SWITCH: Prospecting Strategy", "type": "n8n-nodes-base.switch", "typeVersion": 2, "position": [1080, 300]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT id FROM agent.contatos WHERE endereco_bairro = $1 AND id != $2 AND jornada_status != 'convertido';", "options": {"parameters": {"values": ["={{$json.endereco_bairro}}", "={{$json.id}}"]}}}, "id": "d040a3d4-b789-4e01-b6a6-976cc13d1be5", "name": "DB: Find by Region", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [1320, 100], "credentials": {"postgres": {"id": "YOUR_POSTGRES_CREDENTIAL_ID"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT id FROM agent.contatos WHERE empresa_atual = $1 AND id != $2 AND jornada_status != 'convertido';", "options": {"parameters": {"values": ["={{$json.empresa_atual}}", "={{$json.id}}"]}}}, "id": "ba72ed12-a8c6-4cc2-9d32-d8c9a33bb75f", "name": "DB: Find by Company", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [1320, 300], "credentials": {"postgres": {"id": "YOUR_POSTGRES_CREDENTIAL_ID"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "WITH seed_avg_vector AS (SELECT AVG(embedding) as avg_embedding FROM (SELECT embedding FROM agent.memory_vectors WHERE contact_id = $1 ORDER BY timestamp DESC LIMIT 10) as recent_vectors) SELECT potential_match.contact_id as id, AVG(potential_match.embedding) <-> (SELECT avg_embedding FROM seed_avg_vector) as distance FROM agent.memory_vectors as potential_match WHERE potential_match.contact_id != $1 AND (SELECT jornada_status FROM agent.contatos WHERE id = potential_match.contact_id) != 'convertido' GROUP BY potential_match.contact_id ORDER BY distance ASC LIMIT 10;", "options": {"parameters": {"values": ["={{$json.id}}"]}}}, "id": "f516a7dd-abf1-41d3-bb24-dc7e4fd0d8d7", "name": "DB: Find by <PERSON><PERSON><PERSON> Similarity", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [1320, 500], "credentials": {"postgres": {"id": "YOUR_POSTGRES_CREDENTIAL_ID"}}}, {"parameters": {}, "id": "893d5dfd-b4ef-4de8-9e5c-bd08d3e2de81", "name": "MERGE: Prospects", "type": "n8n-nodes-base.merge", "typeVersion": 2, "position": [1540, 300]}, {"parameters": {"values": {"string": [{"name": "target_tag", "value": "={{`lookalike_auto_${$now.toMillis()}`}}"}, {"name": "campaign_name", "value": "={{`[Auto] Lookalike de ${$('df2dae10-64cd-4e8c-859a-5f6068d34195').item.json.nome}`}}"}]}}, "id": "e98e4d3a-867c-4ab4-9ddb-a64d14af1d6f", "name": "AGENT: Create Campaign Details", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [1760, 200]}, {"parameters": {"batchSize": 1}, "id": "d748f572-132d-4dd6-9e01-d7481bbefc6c", "name": "Loop Over Prospects", "type": "n8n-nodes-base.splitInBatches", "typeVersion": 2, "position": [1760, 400]}, {"parameters": {"workflowId": "={{ $env.DISPATCHER_WORKFLOW_ID }}", "data": "={{ { body: { \n    prompt: `Crie uma mensagem de marketing baseada no perfil semente e na estratégia.\\n\\n**PERFIL SEMENTE:**\\n- Nome: ${$('df2dae10-64cd-4e8c-859a-5f6068d34195').item.json.nome}\\n- Empresa: ${$('df2dae10-64cd-4e8c-859a-5f6068d34195').item.json.empresa_atual}\\n- Região: ${$('df2dae10-64cd-4e8c-859a-5f6068d34195').item.json.endereco_bairro}\\n- Insights sobre ele: ${$('a1b2c3d4-e5f6-4a7b-8c9d-0e1f2a3b4c5d').items.map(item => item.json.content).join(', ')}\\n\\n**ESTRATÉGIA USADA:**\\n- ${$('ab75d8d3-5569-45da-901d-531e2850fc25').item.json.strategy}\\n\\nCrie a mensagem.`,\n    task_type: 'complex_analysis',\n    calling_workflow_id: $workflow.id,\n    calling_node_id: 'call_dispatcher_for_prospecting'\n} } }}", "options": {}}, "id": "call_dispatcher_for_prospecting", "name": "Call Dispatcher for Prospecting", "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 2, "position": [1980, 200]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "INSERT INTO agent.campaigns (name, description, campaign_type, base_prompt, target_tags, goal_metric, status) VALUES ($1, $2, 'proactive_timed', $3, $4::jsonb, 'positive_reply', 'draft');", "options": {"parameters": {"values": ["={{$json.campaign_name}}", "={{`Campanha lookalike baseada em #${$json.id}`}}", "={{$json.choices[0].message.content}}", "={{JSON.stringify([$json.target_tag])}}"]}}}, "id": "c33b2a2a-9f9f-43b3-ac5e-0acdf2d3e432", "name": "DB: Create Draft Campaign", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [2200, 200], "credentials": {"postgres": {"id": "YOUR_POSTGRES_CREDENTIAL_ID"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "UPDATE agent.contatos SET tags = tags || jsonb_build_array($1), jornada_status = 'prospect_lookalike' WHERE id = $2;", "options": {"parameters": {"values": ["={{$('e98e4d3a-867c-4ab4-9ddb-a64d14af1d6f').item.json.target_tag}}", "={{$json.id}}"]}}}, "id": "ed76d05f-fc8c-4fcc-97bd-fd3a8c1f930e", "name": "DB: Tag Prospect & Update Journey", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [1980, 400], "credentials": {"postgres": {"id": "YOUR_POSTGRES_CREDENTIAL_ID"}}}, {"parameters": {"method": "POST", "url": "={{$credentials.slackWebhook.url}}", "body": "={{ ({'blocks': [{'type': 'header', 'text': {'type': 'plain_text', 'text': '✅ Nova Campanha para Aprovação'}}]}) }}"}, "id": "a901844e-a1ac-4648-97e3-ff882e3b2c65", "name": "SLACK: Notify Manager for Approval", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [2420, 200]}], "connections": {"e8d66df2-466d-4bb4-b258-3d1f0ddaf631": {"main": [[{"node": "c1f7ca45-1c39-44ef-a6b3-6cf1b4cd3b54"}]]}, "c1f7ca45-1c39-44ef-a6b3-6cf1b4cd3b54": {"main": [[{"node": "b0cf31ea-be12-40f4-a034-7fc44a4113ae"}]]}, "b0cf31ea-be12-40f4-a034-7fc44a4113ae": {"main": [[{"node": "df2dae10-64cd-4e8c-859a-5f6068d34195"}]]}, "df2dae10-64cd-4e8c-859a-5f6068d34195": {"main": [[{"node": "a1b2c3d4-e5f6-4a7b-8c9d-0e1f2a3b4c5d"}]]}, "a1b2c3d4-e5f6-4a7b-8c9d-0e1f2a3b4c5d": {"main": [[{"node": "ab75d8d3-5569-45da-901d-531e2850fc25"}]]}, "ab75d8d3-5569-45da-901d-531e2850fc25": {"main": [[{"node": "switch_prospecting_strategy"}]]}, "switch_prospecting_strategy": {"main": [[{"node": "d040a3d4-b789-4e01-b6a6-976cc13d1be5"}], [{"node": "ba72ed12-a8c6-4cc2-9d32-d8c9a33bb75f"}], [{"node": "f516a7dd-abf1-41d3-bb24-dc7e4fd0d8d7"}]]}, "d040a3d4-b789-4e01-b6a6-976cc13d1be5": {"main": [[{"node": "893d5dfd-b4ef-4de8-9e5c-bd08d3e2de81", "type": "main", "index": 0}]]}, "ba72ed12-a8c6-4cc2-9d32-d8c9a33bb75f": {"main": [[{"node": "893d5dfd-b4ef-4de8-9e5c-bd08d3e2de81", "type": "main", "index": 1}]]}, "f516a7dd-abf1-41d3-bb24-dc7e4fd0d8d7": {"main": [[{"node": "893d5dfd-b4ef-4de8-9e5c-bd08d3e2de81", "type": "main", "index": 2}]]}, "893d5dfd-b4ef-4de8-9e5c-bd08d3e2de81": {"main": [[{"node": "e98e4d3a-867c-4ab4-9ddb-a64d14af1d6f"}, {"node": "d748f572-132d-4dd6-9e01-d7481bbefc6c"}]]}, "e98e4d3a-867c-4ab4-9ddb-a64d14af1d6f": {"main": [[{"node": "call_dispatcher_for_prospecting"}]]}, "d748f572-132d-4dd6-9e01-d7481bbefc6c": {"main": [[{"node": "ed76d05f-fc8c-4fcc-97bd-fd3a8c1f930e"}]]}, "call_dispatcher_for_prospecting": {"main": [[{"node": "c33b2a2a-9f9f-43b3-ac5e-0acdf2d3e432"}]]}, "c33b2a2a-9f9f-43b3-ac5e-0acdf2d3e432": {"main": [[{"node": "a901844e-a1ac-4648-97e3-ff882e3b2c65"}]]}, "a901844e-a1ac-4648-97e3-ff882e3b2c65": {"main": [[{"node": "a901844e-a1ac-4648-97e3-ff882e3b2c65"}]]}}}