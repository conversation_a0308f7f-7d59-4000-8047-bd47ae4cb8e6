# create_chatwoot_user.ps1 - Script auxiliar para criar o usuário administrador do Chatwoot

param (
    [string]$AdminEmail,
    [string]$AdminPassword
)

Log-Event "Criando usuário administrador do Chatwoot com script auxiliar..." "Chatwoot-Aux"

$rubyScriptTemplate = @'
begin
  u = User.find_by(email: ''{0}'')
  if u.nil?
    u = User.create!(
      email: ''{0}'',
      password: ''{1}'',
      password_confirmation: ''{1}'',
      name: ''Administrator''
    )
    u.add_role(:administrator)
    puts ''Admin user created successfully''
  else
    u.update!(password: ''{1}'', password_confirmation: ''{1}'')
    u.add_role(:administrator) unless u.has_role?(:administrator)
    puts ''Admin user updated successfully''
  end
rescue => e
  puts ''Error creating or updating admin user: '' + e.message
end
'@

$rubyScript = $rubyScriptTemplate -f $AdminEmail, $AdminPassword

$encodedRubyScript = [System.Convert]::ToBase64String([System.Text.Encoding]::UTF8.GetBytes($rubyScript))

$bashCommand = "echo `"$encodedRubyScript`" | base64 -d | RAILS_ENV=production bundle exec rails runner"

$createUserCmd = "docker exec chatwoot-rails-1 bash -c `"$bashCommand`""

try {
    $output = Invoke-Expression $createUserCmd 2>&1
    if ($output -match "successfully") {
        Log-Event "✓ Usuário administrador do Chatwoot configurado com sucesso pelo script auxiliar." "Chatwoot-Aux" "SUCCESS"
    } else {
        Log-Event "⚠ Possível problema ao criar usuário admin do Chatwoot com script auxiliar. Saída: $output" "Chatwoot-Aux" "WARN"
    }
} catch {
    Log-Event "❌ Erro crítico ao executar o comando de criação de usuário do Chatwoot com script auxiliar: $($_.Exception.Message)" "Chatwoot-Aux" "ERROR"
}
