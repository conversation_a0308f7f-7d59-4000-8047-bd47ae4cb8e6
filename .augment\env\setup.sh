#!/bin/bash

# Setup script for N8N Evolution API Enterprise Stack
# This is a Docker-based enterprise stack with PowerShell automation scripts

set -e

echo "=== Setting up N8N Evolution API Enterprise Stack ==="

# Update system packages
sudo apt-get update

# Install basic dependencies
echo "Installing basic dependencies..."
sudo apt-get install -y wget curl gnupg software-properties-common apt-transport-https ca-certificates

# Install PowerShell
echo "Installing PowerShell..."
wget -q https://packages.microsoft.com/config/ubuntu/22.04/packages-microsoft-prod.deb
sudo dpkg -i packages-microsoft-prod.deb
sudo apt-get update
sudo apt-get install -y powershell

# Install Docker CLI (without daemon since we're in a container)
echo "Installing Docker CLI..."
curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg
echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null
sudo apt-get update
sudo apt-get install -y docker-ce-cli docker-compose-plugin

# Install Node.js and npm
echo "Installing Node.js and npm..."
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install Pester PowerShell testing framework
echo "Installing Pester PowerShell testing framework..."
pwsh -Command "Set-PSRepository -Name PSGallery -InstallationPolicy Trusted; Install-Module -Name Pester -Force -Scope CurrentUser"

# Install additional PowerShell modules that might be needed
pwsh -Command "Install-Module -Name PSScriptAnalyzer -Force -Scope CurrentUser"

# Verify installations
echo "Verifying installations..."
docker --version || echo "Docker CLI installed (daemon not available in container)"
pwsh --version
node --version
npm --version

# Add PowerShell to PATH
echo 'export PATH="/opt/microsoft/powershell/7:$PATH"' >> $HOME/.profile

# Create necessary directories if they don't exist
mkdir -p template/tests
mkdir -p template/logs

# Create a simple working test script to verify PowerShell testing works
echo "Creating a simple test script..."
cat > template/tests/Test-Simple.ps1 << 'EOF'
# Simple PowerShell test script
param(
    [string]$TestScope = "Unit"
)

Write-Host "=== Simple PowerShell Test ===" -ForegroundColor Green
Write-Host "Test Scope: $TestScope" -ForegroundColor Cyan
Write-Host "PowerShell Version: $($PSVersionTable.PSVersion)" -ForegroundColor Yellow
Write-Host "Current Directory: $(Get-Location)" -ForegroundColor Magenta

# Test 1: Basic PowerShell functionality
Write-Host "Test 1: Basic PowerShell functionality" -ForegroundColor White
$result1 = 2 + 2
if ($result1 -eq 4) {
    Write-Host "✓ PASSED: Basic arithmetic works" -ForegroundColor Green
} else {
    Write-Host "✗ FAILED: Basic arithmetic failed" -ForegroundColor Red
    exit 1
}

# Test 2: File system operations
Write-Host "Test 2: File system operations" -ForegroundColor White
$testFile = "test_temp.txt"
try {
    "Hello World" | Out-File -FilePath $testFile -Encoding UTF8
    if (Test-Path $testFile) {
        $content = Get-Content $testFile
        if ($content -eq "Hello World") {
            Write-Host "✓ PASSED: File operations work" -ForegroundColor Green
        } else {
            Write-Host "✗ FAILED: File content mismatch" -ForegroundColor Red
            exit 1
        }
        Remove-Item $testFile -Force
    } else {
        Write-Host "✗ FAILED: File was not created" -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "✗ FAILED: File operations failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Test 3: Check if main project files exist
Write-Host "Test 3: Project structure validation" -ForegroundColor White
$projectFiles = @(
    "template/docker-compose.yaml",
    "template/package.json",
    ".gitignore"
)

$allFilesExist = $true
foreach ($file in $projectFiles) {
    if (Test-Path $file) {
        Write-Host "✓ Found: $file" -ForegroundColor Green
    } else {
        Write-Host "✗ Missing: $file" -ForegroundColor Red
        $allFilesExist = $false
    }
}

if ($allFilesExist) {
    Write-Host "✓ PASSED: All expected project files exist" -ForegroundColor Green
} else {
    Write-Host "✗ FAILED: Some project files are missing" -ForegroundColor Red
    exit 1
}

Write-Host "=== All Tests Passed! ===" -ForegroundColor Green
exit 0
EOF

# Clean up
rm -f packages-microsoft-prod.deb

echo "=== Setup completed successfully ==="
echo "PowerShell, Docker CLI, Node.js, and testing frameworks are installed"