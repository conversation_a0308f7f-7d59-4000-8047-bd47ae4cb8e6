# simple-fix.ps1 - Correção simples e direta
Write-Host "Aplicando correções críticas..." -ForegroundColor Yellow

# Ler arquivo
$content = Get-Content "Start-Environment.ps1" -Raw

# Substituições simples para remover caracteres problemáticos
$content = $content -replace 'Log-Event "Dashboard HTML gerado em modo fallback: \$dashboardPath" "Dashboard" "SUCCESS"', 'Log-Event "Dashboard HTML gerado em modo fallback: $dashboardPath" "Dashboard" "SUCCESS"'

$content = $content -replace 'Log-Event "Script finalizado com sucesso!" "Orchestrator" "SUCCESS"', 'Log-Event "Script finalizado com sucesso!" "Orchestrator" "SUCCESS"'

# Salvar
$content | Out-File "Start-Environment.ps1" -Encoding UTF8 -NoNewline

Write-Host "Correções aplicadas!" -ForegroundColor Green
