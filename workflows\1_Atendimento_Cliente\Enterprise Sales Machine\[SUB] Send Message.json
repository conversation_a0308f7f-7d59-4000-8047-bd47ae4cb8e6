{"name": "[SUB] Send Message", "nodes": [{"parameters": {}, "id": "3b29cda1-2c1b-4f9e-8c3d-1a0c8b3d1f3b", "name": "Start", "type": "n8n-nodes-base.start", "typeVersion": 1, "position": [250, 300]}, {"parameters": {"routing": {"rules": {"values": [{"operation": "equal", "value1": "={{$json.channel}}", "value2": "whatsapp"}, {"operation": "equal", "value1": "={{$json.channel}}", "value2": "sms"}, {"operation": "equal", "value1": "={{$json.channel}}", "value2": "telegram"}]}, "fieldToMatch": ""}, "options": {}}, "id": "f8d8c2e1-e9b4-4e9b-9c7d-0a8b2d2c1b3d", "name": "ROUTE by Channel", "type": "n8n-nodes-base.switch", "typeVersion": 2, "position": [450, 300]}, {"parameters": {"method": "POST", "url": "https://api.telegram.org/bot{{$credentials.telegramApi.token}}/sendMessage", "sendBody": true, "bodyParameters": {"parameters": [{"name": "chat_id", "value": "={{$json.recipientId}}"}, {"name": "text", "value": "={{$json.messageContent}}"}]}, "options": {}}, "id": "send_telegram", "name": "SEND: Telegram", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [650, 600], "credentials": {"httpHeaderAuth": {"id": "YOUR_TELEGRAM_BOT_CREDENTIAL_ID", "name": "Telegram API"}}}, {"parameters": {"conditions": {"boolean": [{"value1": "={{$json.messageType === 'audio'}}"}]}}, "id": "a93b2a3a-a8d8-4f2a-b9c1-3a0b8c2e1d9c", "name": "IF: Audio?", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [650, 200]}, {"parameters": {"method": "POST", "url": "={{ $credentials.EvolutionApi.baseUrl }}/message/sendText/{{$json.instanceName}}", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "apikey", "value": "={{ $credentials.EvolutionApi.apiKey }}"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "number", "value": "={{ $json.recipientId }}"}, {"name": "options", "value": "={{ { \"delay\": 1200, \"presence\": \"composing\" } }}", "type": "json"}, {"name": "textMessage", "value": "={{ { \"text\": $json.messageContent } }}"}]}, "options": {}}, "id": "c1d3b2c2-b3a1-4d3a-b2c3-1d0c8d1e3f8b", "name": "SEND: Evolution API (Text)", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [850, 300], "credentials": {"httpHeaderAuth": {"id": "YOUR_EVOLUTION_API_CREDENTIAL_ID", "name": "EvolutionApi"}}}, {"parameters": {"method": "POST", "url": "https://api.elevenlabs.io/v1/text-to-speech/YOUR_VOICE_ID_HERE", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "xi-api-key", "value": "={{ $credentials.elevenLabs.apiKey }}"}, {"name": "Accept", "value": "audio/mpeg"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "text", "value": "={{$json.messageContent}}"}]}, "options": {"response": {"responseFormat": "file"}}}, "id": "b3d4f5e6-g7h8-4a1e-b2c3-d4e5f6g7h8j0", "name": "TOOL: Text-to-Speech", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [850, 100], "credentials": {"httpHeaderAuth": {"id": "YOUR_ELEVENLABS_CREDENTIAL_ID", "name": "ElevenLabs"}}}, {"parameters": {"method": "POST", "url": "={{ $credentials.EvolutionApi.baseUrl }}/message/sendAudio/{{$json.instanceName}}", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "apikey", "value": "={{ $credentials.EvolutionApi.apiKey }}"}]}, "sendBody": true, "contentType": "multipart-form-data", "bodyParameters": {"parameters": [{"name": "number", "value": "={{ $json.recipientId }}"}, {"name": "audioMessage[mimetype]", "value": "audio/mpeg"}, {"name": "audioMessage[audio]", "value": "={{ $binary.data }}"}]}, "options": {}}, "id": "d9e1f2a3-b4c5-4d6e-b7f8-d9e0f1a2b3c4", "name": "SEND: Evolution API (Audio)", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [1050, 100], "credentials": {"httpHeaderAuth": {"id": "YOUR_EVOLUTION_API_CREDENTIAL_ID", "name": "EvolutionApi"}}}, {"parameters": {"method": "POST", "url": "https://api.twilio.com/2010-04-01/Accounts/YOUR_TWILIO_ACCOUNT_SID/Messages.json", "authentication": "predefinedCredential", "credentialType": "t<PERSON><PERSON><PERSON><PERSON>", "sendBody": true, "bodyParameters": {"parameters": [{"name": "To", "value": "={{$json.recipientId}}"}, {"name": "From", "value": "YOUR_TWILIO_PHONE_NUMBER"}, {"name": "Body", "value": "={{$json.messageContent}}"}]}, "options": {}}, "id": "a4b3c2d1-e5f6-4a7b-8c9d-0e1f2a3b4c5d", "name": "SEND: <PERSON><PERSON><PERSON> (SMS)", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [650, 400], "credentials": {"twilioApi": {"id": "YOUR_TWILIO_CREDENTIAL_ID", "name": "Twilio API"}}}], "connections": {"Start": {"main": [[{"node": "ROUTE by Channel", "type": "main", "index": 0}]]}, "ROUTE by Channel": {"main": [[{"node": "IF: Audio?", "type": "main", "index": 0}], [{"node": "a4b3c2d1-e5f6-4a7b-8c9d-0e1f2a3b4c5d", "type": "main", "index": 0}], [{"node": "send_telegram", "type": "main", "index": 0}]]}, "IF: Audio?": {"main": [[{"node": "TOOL: Text-to-Speech", "type": "main", "index": 0}], [{"node": "SEND: Evolution API (Text)", "type": "main", "index": 0}]]}, "TOOL: Text-to-Speech": {"main": [[{"node": "SEND: Evolution API (Audio)", "type": "main", "index": 0}]]}}, "settings": {}, "staticData": null, "triggerCount": 0, "active": true}