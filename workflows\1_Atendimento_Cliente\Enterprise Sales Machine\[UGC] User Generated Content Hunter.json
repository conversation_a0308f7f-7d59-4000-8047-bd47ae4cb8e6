{
  "meta": {
    "instanceId": "UGC_CONTENT_HUNTER"
  },
  "name": "[UGC] User Generated Content Hunter",
  "nodes": [
    {
      "parameters": {
        "rule": {
          "interval": [
            {
              "field": "hours",
              "hoursInterval": 6
            }
          ]
        }
      },
      "id": "trigger_ugc_scan",
      "name": "TRIGGER: UGC Scan Every 6H",
      "type": "n8n-nodes-base.scheduleTrigger",
      "typeVersion": 1.1,
      "position": [140, 300]
    },
    {
      "parameters": {
        "operation": "executeQuery",
        "query": "-- Identificar clientes com potencial para UGC\nSELECT \n  c.id as contact_id,\n  c.nome,\n  c.empresa_atual,\n  c.telefone,\n  c.email,\n  c.tags,\n  c.jornada_status,\n  c.engagement_score,\n  c.ultima_interacao,\n  CASE \n    WHEN c.tags @> '[\"lead_converted\"]' AND c.engagement_score > 90 THEN 'success_story'\n    WHEN c.tags @> '[\"power_user\"]' AND c.engagement_score > 85 THEN 'case_study'\n    WHEN c.engagement_score > 80 AND c.jornada_status = 'convertido' THEN 'testimonial'\n    WHEN c.tags @> '[\"community_active\"]' THEN 'community_spotlight'\n    WHEN c.engagement_score > 75 THEN 'transformation_story'\n    ELSE 'before_after'\n  END as opportunity_type,\n  CASE \n    WHEN c.engagement_score > 95 THEN 100\n    WHEN c.engagement_score > 90 THEN 95\n    WHEN c.engagement_score > 85 THEN 90\n    WHEN c.engagement_score > 80 THEN 85\n    WHEN c.engagement_score > 75 THEN 80\n    ELSE 70\n  END as priority_score,\n  EXTRACT(DAYS FROM NOW() - c.ultima_interacao) as days_since_interaction\nFROM agent.contatos c\nWHERE c.jornada_status IN ('convertido', 'engajado', 'ativo')\nAND c.ultima_interacao > NOW() - INTERVAL '45 days'\nAND c.engagement_score > 70\nAND NOT EXISTS (\n  SELECT 1 FROM agent.ugc_opportunities uo \n  WHERE uo.contact_id = c.id \n  AND uo.status IN ('identified', 'approached', 'creating')\n  AND uo.created_at > NOW() - INTERVAL '30 days'\n)\nORDER BY priority_score DESC, c.engagement_score DESC\nLIMIT 10;"
      },
      "id": "identify_ugc_candidates",
      "name": "🔍 Identify UGC Candidates",
      "type": "n8n-nodes-base.postgres",
      "typeVersion": 2.4,
      "position": [360, 300],
      "credentials": {
        "postgres": {
          "id": "postgres-main",
          "name": "PostgreSQL Main"
        }
      }
    },
    {
      "parameters": {
        "batchSize": 1
      },
      "id": "loop_candidates",
      "name": "Loop UGC Candidates",
      "type": "n8n-nodes-base.splitInBatches",
      "typeVersion": 2,
      "position": [580, 300]
    },
    {
      "parameters": {
        "workflowId": "={{ $env.DISPATCHER_WORKFLOW_ID }}",
        "data": "={{ {\n  body: {\n    prompt: `Você é um especialista em User-Generated Content (UGC) e success stories. Analise o perfil do cliente abaixo e crie uma estratégia personalizada de abordagem para UGC.\\n\\n**PERFIL DO CLIENTE:**\\nNome: ${$json.nome}\\nEmpresa: ${$json.empresa_atual || 'N/A'}\\nStatus da Jornada: ${$json.jornada_status}\\nEngagement Score: ${$json.engagement_score}/100\\nTags: ${JSON.stringify($json.tags)}\\nTipo de Oportunidade: ${$json.opportunity_type}\\nPrioridade: ${$json.priority_score}/100\\n\\n**DIRETRIZES PARA UGC:**\\n${$json.opportunity_type === 'success_story' ? '- Foque nos resultados quantitativos alcançados\\n- Destaque a transformação do negócio\\n- Inclua métricas específicas (ROI, economia, crescimento)' : ''}\\n${$json.opportunity_type === 'case_study' ? '- Detalhe o processo de implementação\\n- Mostre desafios superados\\n- Apresente soluções inovadoras aplicadas' : ''}\\n${$json.opportunity_type === 'testimonial' ? '- Capture depoimento autêntico e emocional\\n- Foque na experiência do cliente\\n- Destaque pontos de diferenciação' : ''}\\n\\nCrie:\\n1. **Mensagem de Abordagem** (WhatsApp): Tom amigável, reconheça seu sucesso, explique o valor mútuo\\n2. **Proposta de Valor**: O que o cliente ganha participando\\n3. **Formato de Conteúdo**: Tipo ideal (vídeo, texto, case study, etc.)\\n4. **Incentivo**: Recompensa ou benefício oferecido\\n5. **Call-to-Action**: Próximo passo claro\\n\\nResposta em JSON:\\n{\\n  \\\"approach_message\\\": \\\"mensagem personalizada aqui\\\",\\n  \\\"value_proposition\\\": \\\"benefícios para o cliente\\\",\\n  \\\"content_format\\\": \\\"formato sugerido\\\",\\n  \\\"incentive\\\": \\\"recompensa oferecida\\\",\\n  \\\"call_to_action\\\": \\\"próximo passo\\\",\\n  \\\"estimated_reach\\\": 0000,\\n  \\\"success_probability\\\": 0.00\\n}`,\n    task_type: 'complex_analysis',\n    calling_workflow_id: $workflow.id,\n    calling_node_id: 'create_ugc_approach'\n  }\n} }}",
        "options": {}
      },
      "id": "create_ugc_approach",
      "name": "🎯 AI: Create UGC Approach",
      "type": "n8n-nodes-base.executeWorkflow",
      "typeVersion": 2,
      "position": [800, 300]
    },
    {
      "parameters": {
        "operation": "executeQuery",
        "query": "-- Salvar oportunidade UGC identificada\nINSERT INTO agent.ugc_opportunities (\n  contact_id,\n  opportunity_type,\n  content_brief,\n  suggested_approach,\n  status,\n  priority_score,\n  estimated_reach,\n  approach_message\n)\nVALUES (\n  $1,\n  $2,\n  $3,\n  $4::jsonb,\n  'identified',\n  $5,\n  $6,\n  $7\n)\nON CONFLICT (contact_id, opportunity_type) \nDO UPDATE SET \n  content_brief = EXCLUDED.content_brief,\n  suggested_approach = EXCLUDED.suggested_approach,\n  priority_score = EXCLUDED.priority_score,\n  estimated_reach = EXCLUDED.estimated_reach,\n  approach_message = EXCLUDED.approach_message,\n  created_at = NOW()\nRETURNING *;",
        "options": {
          "parameters": {
            "values": [
              "={{ $('loop_candidates').item.json.contact_id }}",
              "={{ $('loop_candidates').item.json.opportunity_type }}",
              "={{ 'UGC Opportunity: ' + $('loop_candidates').item.json.opportunity_type + ' para ' + $('loop_candidates').item.json.nome + ($('loop_candidates').item.json.empresa_atual ? ' da ' + $('loop_candidates').item.json.empresa_atual : '') + '. Score: ' + $('loop_candidates').item.json.priority_score + '/100' }}",
              "={{ JSON.stringify({\n                approach_type: $('loop_candidates').item.json.opportunity_type,\n                contact_info: {\n                  name: $('loop_candidates').item.json.nome,\n                  company: $('loop_candidates').item.json.empresa_atual,\n                  phone: $('loop_candidates').item.json.telefone,\n                  email: $('loop_candidates').item.json.email,\n                  engagement_level: $('loop_candidates').item.json.engagement_score,\n                  days_since_interaction: $('loop_candidates').item.json.days_since_interaction\n                },\n                ai_strategy: $json,\n                best_contact_method: 'whatsapp',\n                follow_up_schedule: {\n                  initial: 'immediate',\n                  reminder_1: '3_days',\n                  reminder_2: '7_days',\n                  final: '14_days'\n                }\n              }) }}",
              "={{ $('loop_candidates').item.json.priority_score }}",
              "={{ $json.estimated_reach || (\n                $('loop_candidates').item.json.priority_score > 90 ? 5000 :\n                $('loop_candidates').item.json.priority_score > 80 ? 3000 :\n                $('loop_candidates').item.json.priority_score > 70 ? 2000 : 1000\n              ) }}",
              "={{ $json.approach_message }}"
            ]
          }
        }
      },
      "id": "save_ugc_opportunity",
      "name": "💾 Save UGC Opportunity",
      "type": "n8n-nodes-base.postgres",
      "typeVersion": 2.4,
      "position": [1020, 300],
      "credentials": {
        "postgres": {
          "id": "postgres-main",
          "name": "PostgreSQL Main"
        }
      }
    },
    {
      "parameters": {
        "conditions": {
          "options": {
            "caseSensitive": true,
            "leftValue": "",
            "typeValidation": "strict"
          },
          "conditions": [
            {
              "id": "high_priority_check",
              "leftValue": "={{ $('loop_candidates').item.json.priority_score }}",
              "rightValue": 85,
              "operator": {
                "type": "number",
                "operation": "gte"
              }
            },\n            {\n              \"id\": \"recent_interaction\",\n              \"leftValue\": \"={{ $('loop_candidates').item.json.days_since_interaction }}\",\n              \"rightValue\": 14,\n              \"operator\": {\n                \"type\": \"number\",\n                \"operation\": \"lte\"\n              }\n            }\n          ],\n          \"combineOperation\": \"and\"\n        }\n      },\n      \"id\": \"if_auto_approach\",\n      \"name\": \"IF: Auto-Approach (Priority 85+ & Recent)\",\n      \"type\": \"n8n-nodes-base.if\",\n      \"typeVersion\": 2,\n      \"position\": [1240, 300]\n    },\n    {\n      \"parameters\": {\n        \"workflowId\": \"={{ $env.SEND_MESSAGE_WORKFLOW_ID }}\",\n        \"data\": \"={{ {\n          body: {\n            recipientId: $('loop_candidates').item.json.telefone,\n            message: $('create_ugc_approach').item.json.approach_message,\n            messageType: 'ugc_approach',\n            contactId: $('loop_candidates').item.json.contact_id,\n            campaignType: 'ugc_hunter',\n            priority: 'high'\n          }\n        } }}\",\n        \"options\": {}\n      },\n      \"id\": \"send_ugc_approach\",\n      \"name\": \"📱 Send UGC Approach (WhatsApp)\",\n      \"type\": \"n8n-nodes-base.executeWorkflow\",\n      \"typeVersion\": 2,\n      \"position\": [1460, 200],\n      \"continueOnFail\": true\n    },\n    {\n      \"parameters\": {\n        \"operation\": \"executeQuery\",\n        \"query\": \"-- Atualizar status para approached\\nUPDATE agent.ugc_opportunities \\nSET \\n  status = 'approached',\\n  approached_at = NOW()\\nWHERE contact_id = $1 \\nAND opportunity_type = $2\\nAND status = 'identified'\\nRETURNING *;\",\n        \"options\": {\n          \"parameters\": {\n            \"values\": [\n              \"={{ $('loop_candidates').item.json.contact_id }}\",\n              \"={{ $('loop_candidates').item.json.opportunity_type }}\"\n            ]\n          }\n        }\n      },\n      \"id\": \"update_approached_status\",\n      \"name\": \"✅ Update to 'Approached'\",\n      \"type\": \"n8n-nodes-base.postgres\",\n      \"typeVersion\": 2.4,\n      \"position\": [1680, 200],\n      \"credentials\": {\n        \"postgres\": {\n          \"id\": \"postgres-main\",\n          \"name\": \"PostgreSQL Main\"\n        }\n      }\n    },\n    {\n      \"parameters\": {\n        \"operation\": \"executeQuery\",\n        \"query\": \"-- Agendar follow-up automático para 3 dias\\nINSERT INTO agent.scheduled_tasks (\\n  task_type,\\n  target_id,\\n  execution_date,\\n  task_data,\\n  status\\n)\\nVALUES (\\n  'ugc_followup',\\n  $1,\\n  NOW() + INTERVAL '3 days',\\n  jsonb_build_object(\\n    'contact_id', $1,\\n    'opportunity_type', $2,\\n    'follow_up_number', 1,\\n    'original_message', $3\\n  ),\\n  'pending'\\n)\\nON CONFLICT DO NOTHING;\",\n        \"options\": {\n          \"parameters\": {\n            \"values\": [\n              \"={{ $('loop_candidates').item.json.contact_id }}\",\n              \"={{ $('loop_candidates').item.json.opportunity_type }}\",\n              \"={{ $('create_ugc_approach').item.json.approach_message }}\"\n            ]\n          }\n        }\n      },\n      \"id\": \"schedule_followup\",\n      \"name\": \"⏰ Schedule Follow-up (3 days)\",\n      \"type\": \"n8n-nodes-base.postgres\",\n      \"typeVersion\": 2.4,\n      \"position\": [1900, 200],\n      \"credentials\": {\n        \"postgres\": {\n          \"id\": \"postgres-main\",\n          \"name\": \"PostgreSQL Main\"\n        }\n      },\n      \"continueOnFail\": true\n    },\n    {\n      \"parameters\": {\n        \"method\": \"POST\",\n        \"url\": \"={{ $vars.SLACK_WEBHOOK_URL }}\",\n        \"sendBody\": true,\n        \"specifyBody\": \"json\",\n        \"jsonBody\": \"{\\n  \\\"text\\\": \\\"📋 UGC Hunter - Oportunidade Manual\\\",\\n  \\\"blocks\\\": [\\n    {\\n      \\\"type\\\": \\\"section\\\",\\n      \\\"text\\\": {\\n        \\\"type\\\": \\\"mrkdwn\\\",\\n        \\\"text\\\": \\\"*📋 UGC Opportunity - Manual Review Required*\\\\n\\\\n*Cliente:* {{ $('loop_candidates').item.json.nome }}\\\\n*Empresa:* {{ $('loop_candidates').item.json.empresa_atual || 'N/A' }}\\\\n*Score:* {{ $('loop_candidates').item.json.priority_score }}/100\\\\n*Tipo:* {{ $('loop_candidates').item.json.opportunity_type }}\\\\n\\\\n*Motivo:* Prioridade < 85 ou interação antiga\\\\n\\\\n*Abordagem Sugerida:*\\\\n{{ $('create_ugc_approach').item.json.approach_message.substring(0, 300) }}...\\\"\\n      }\\n    },\\n    {\\n      \\\"type\\\": \\\"actions\\\",\\n      \\\"elements\\\": [\\n        {\\n          \\\"type\\\": \\\"button\\\",\\n          \\\"text\\\": {\\n            \\\"type\\\": \\\"plain_text\\\",\\n            \\\"text\\\": \\\"Aprovar Abordagem\\\"\\n          },\\n          \\\"style\\\": \\\"primary\\\",\\n          \\\"url\\\": \\\"https://wa.me/{{ $('loop_candidates').item.json.telefone }}?text={{ encodeURIComponent($('create_ugc_approach').item.json.approach_message.substring(0, 200)) }}\\\"\\n        },\\n        {\\n          \\\"type\\\": \\\"button\\\",\\n          \\\"text\\\": {\\n            \\\"type\\\": \\\"plain_text\\\",\\n            \\\"text\\\": \\\"Ver Perfil\\\"\\n          },\\n          \\\"url\\\": \\\"https://n8n.${DOMAIN:-localhost}/dashboard/contact/{{ $('loop_candidates').item.json.contact_id }}\\\"\\n        }\\n      ]\\n    }\\n  ],\\n  \\\"channel\\\": \\\"#ugc-opportunities\\\"\\n}\",\n        \"options\": {}\n      },\n      \"id\": \"notify_manual_review\",\n      \"name\": \"👤 Notify Manual Review\",\n      \"type\": \"n8n-nodes-base.httpRequest\",\n      \"typeVersion\": 4.1,\n      \"position\": [1460, 400],\n      \"continueOnFail\": true\n    },\n    {\n      \"parameters\": {\n        \"mode\": \"combine\",\n        \"combinationMode\": \"mergeByPosition\",\n        \"options\": {}\n      },\n      \"id\": \"merge_loop_results\",\n      \"name\": \"Merge Loop Results\",\n      \"type\": \"n8n-nodes-base.merge\",\n      \"typeVersion\": 2.1,\n      \"position\": [2120, 300]\n    },\n    {\n      \"parameters\": {\n        \"operation\": \"executeQuery\",\n        \"query\": \"-- Relatório de execução do UGC Hunter\\nSELECT \\n  COUNT(*) as total_opportunities,\\n  COUNT(CASE WHEN status = 'approached' THEN 1 END) as auto_approached,\\n  COUNT(CASE WHEN status = 'identified' THEN 1 END) as manual_review,\\n  AVG(priority_score) as avg_priority,\\n  AVG(estimated_reach) as avg_estimated_reach\\nFROM agent.ugc_opportunities \\nWHERE created_at > NOW() - INTERVAL '6 hours';\"\n      },\n      \"id\": \"generate_execution_report\",\n      \"name\": \"📊 Generate Execution Report\",\n      \"type\": \"n8n-nodes-base.postgres\",\n      \"typeVersion\": 2.4,\n      \"position\": [2340, 300],\n      \"credentials\": {\n        \"postgres\": {\n          \"id\": \"postgres-main\",\n          \"name\": \"PostgreSQL Main\"\n        }\n      }\n    },\n    {\n      \"parameters\": {\n        \"method\": \"POST\",\n        \"url\": \"={{ $vars.SLACK_WEBHOOK_URL }}\",\n        \"sendBody\": true,\n        \"specifyBody\": \"json\",\n        \"jsonBody\": \"{\\n  \\\"text\\\": \\\"🎯 UGC Hunter - Relatório de Execução\\\",\\n  \\\"blocks\\\": [\\n    {\\n      \\\"type\\\": \\\"header\\\",\\n      \\\"text\\\": {\\n        \\\"type\\\": \\\"plain_text\\\",\\n        \\\"text\\\": \\\"🎯 UGC HUNTER REPORT\\\"\\n      }\\n    },\\n    {\\n      \\\"type\\\": \\\"section\\\",\\n      \\\"fields\\\": [\\n        {\\n          \\\"type\\\": \\\"mrkdwn\\\",\\n          \\\"text\\\": \\\"*🔍 Candidatos Identificados:*\\\\n{{ $('identify_ugc_candidates').all().length }}\\\"\\n        },\\n        {\\n          \\\"type\\\": \\\"mrkdwn\\\",\\n          \\\"text\\\": \\\"*📱 Abordagens Automáticas:*\\\\n{{ $('generate_execution_report').first().json.auto_approached || 0 }}\\\"\\n        },\\n        {\\n          \\\"type\\\": \\\"mrkdwn\\\",\\n          \\\"text\\\": \\\"*👤 Revisão Manual:*\\\\n{{ $('generate_execution_report').first().json.manual_review || 0 }}\\\"\\n        },\\n        {\\n          \\\"type\\\": \\\"mrkdwn\\\",\\n          \\\"text\\\": \\\"*📊 Score Médio:*\\\\n{{ Math.round($('generate_execution_report').first().json.avg_priority || 0) }}/100\\\"\\n        }\\n      ]\\n    },\\n    {\\n      \\\"type\\\": \\\"section\\\",\\n      \\\"text\\\": {\\n        \\\"type\\\": \\\"mrkdwn\\\",\\n        \\\"text\\\": \\\"*🎯 Top Oportunidades:*\\\\n{{ $('identify_ugc_candidates').all().slice(0, 3).map(candidate => `• ${candidate.json.nome} (${candidate.json.empresa_atual || 'Particular'}) - ${candidate.json.priority_score}/100 - ${candidate.json.opportunity_type}`).join('\\\\n') }}\\\"\\n      }\\n    },\\n    {\\n      \\\"type\\\": \\\"section\\\",\\n      \\\"text\\\": {\\n        \\\"type\\\": \\\"mrkdwn\\\",\\n        \\\"text\\\": \\\"*📈 Estimativa de Alcance Total:*\\\\n{{ Math.round(($('generate_execution_report').first().json.avg_estimated_reach || 0) * ($('identify_ugc_candidates').all().length)).toLocaleString() }} impressões\\\"\\n      }\\n    },\\n    {\\n      \\\"type\\\": \\\"context\\\",\\n      \\\"elements\\\": [\\n        {\\n          \\\"type\\\": \\\"mrkdwn\\\",\\n          \\\"text\\\": \\\"Próxima varredura em 6 horas | Follow-ups automáticos agendados\\\"\\n        }\\n      ]\\n    }\\n  ],\\n  \\\"channel\\\": \\\"#ugc-hunter\\\"\\n}\",\n        \"options\": {}\n      },\n      \"id\": \"notify_execution_summary\",\n      \"name\": \"📱 Notify Execution Summary\",\n      \"type\": \"n8n-nodes-base.httpRequest\",\n      \"typeVersion\": 4.1,\n      \"position\": [2560, 300],\n      \"continueOnFail\": true\n    }\n  ],\n  \"connections\": {\n    \"trigger_ugc_scan\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"identify_ugc_candidates\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"identify_ugc_candidates\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"loop_candidates\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"loop_candidates\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"create_ugc_approach\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"create_ugc_approach\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"save_ugc_opportunity\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"save_ugc_opportunity\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"if_auto_approach\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"if_auto_approach\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"send_ugc_approach\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ],\n        [\n          {\n            \"node\": \"notify_manual_review\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"send_ugc_approach\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"update_approached_status\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"update_approached_status\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"schedule_followup\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"schedule_followup\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"merge_loop_results\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"notify_manual_review\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"merge_loop_results\",\n            \"type\": \"main\",\n            \"index\": 1\n          }\n        ]\n      ]\n    },\n    \"merge_loop_results\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"generate_execution_report\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"generate_execution_report\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"notify_execution_summary\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    }\n  },\n  \"settings\": {\n    \"executionOrder\": \"v1\"\n  },\n  \"staticData\": null,\n  \"tags\": [\n    {\n      \"createdAt\": \"2024-01-15T14:00:00.000Z\",\n      \"updatedAt\": \"2024-01-15T14:00:00.000Z\",\n      \"id\": \"ugc-hunter\",\n      \"name\": \"ugc-hunter\"\n    }\n  ],\n  \"triggerCount\": 1,\n  \"updatedAt\": \"2024-01-15T14:00:00.000Z\",\n  \"versionId\": \"1\"\n} 