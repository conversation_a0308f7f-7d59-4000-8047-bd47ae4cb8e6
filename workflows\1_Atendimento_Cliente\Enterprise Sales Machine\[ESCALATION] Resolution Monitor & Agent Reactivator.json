{"meta": {"instanceId": "ESCALATION_RESOLUTION_MONITOR_V2"}, "name": "[ESCALATION] Resolution Monitor & Agent Reactivator v2.0 - Enterprise", "nodes": [{"parameters": {"path": "chatwoot-resolution-webhook", "responseMode": "onReceived", "options": {}}, "id": "trigger_chatwoot_webhook", "name": "TRIGGER: Chatwoot Resolution Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [140, 300], "notes": "Recebe webhook do Chatwoot quando conversa é resolvida/fechada"}, {"parameters": {"path": "manual-resolution-trigger", "responseMode": "onReceived", "options": {}}, "id": "trigger_manual_resolution", "name": "TRIGGER: Manual Resolution", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [140, 500], "notes": "Trigger manual para resolução de escalations via API"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "is_chatwoot_webhook", "leftValue": "={{ $json.body.event || $json.event }}", "rightValue": "conversation_status_changed", "operator": {"type": "string", "operation": "equals"}}, {"id": "is_resolved_status", "leftValue": "={{ $json.body.status || $json.status }}", "rightValue": "resolved", "operator": {"type": "string", "operation": "equals"}}], "combineOperation": "or"}}, "id": "validate_resolution_trigger", "name": "✅ Validate Resolution Trigger", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [360, 300]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "-- <PERSON><PERSON> escalation ativa baseada no ID da conversa Chatwoot ou escalation_id manual\nSELECT \n  he.*,\n  c.nome as contact_name,\n  c.telefone as contact_phone,\n  c.email as contact_email,\n  c.empresa_atual as contact_company,\n  c.jornada_status as contact_journey_status\nFROM agent.human_escalations he\nLEFT JOIN agent.contatos c ON he.contact_id = c.id\nWHERE (\n  (he.chatwoot_conversation_id = $1 AND $1 IS NOT NULL)\n  OR (he.id = $2 AND $2 IS NOT NULL)\n)\nAND he.status IN ('pending', 'assigned_to_human', 'in_progress')\nORDER BY he.created_at DESC\nLIMIT 1;", "options": {"parameters": {"values": ["={{ $json.body.id || $json.body.conversation?.id || null }}", "={{ $json.body.escalation_id || null }}"]}}}, "id": "find_active_escalation", "name": "🔍 Find Active Escalation", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [580, 200], "credentials": {"postgres": {"id": "postgres-main", "name": "PostgreSQL Main"}}, "continueOnFail": true}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "escalation_found", "leftValue": "={{ $json.id }}", "rightValue": "", "operator": {"type": "string", "operation": "isNotEmpty"}}]}}, "id": "if_escalation_found", "name": "IF: Escalation Found?", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [800, 200]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "-- <PERSON>car histórico de interações durante o escalation\nSELECT \n  ci.message_sent,\n  ci.interaction_type,\n  ci.achieved_goal,\n  ci.created_at,\n  ai.display_name as agent_name,\n  ai.category as agent_category\nFROM agent.campaign_interactions ci\nLEFT JOIN agent.agent_identities ai ON ci.identity_specialist_id = ai.id\nWHERE ci.contact_id = $1\nAND ci.created_at >= $2\nORDER BY ci.created_at DESC\nLIMIT 5;", "options": {"parameters": {"values": ["={{ $json.contact_id }}", "={{ $json.created_at }}"]}}}, "id": "get_escalation_context", "name": "📋 Get Escalation Context", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [1020, 200], "credentials": {"postgres": {"id": "postgres-main", "name": "PostgreSQL Main"}}, "continueOnFail": true}, {"parameters": {"workflowId": "={{ $env.DISPATCHER_WORKFLOW_ID }}", "data": "={{ {\n  body: {\n    prompt: `SISTEMA DE HANDOFF INTELIGENTE - REATIVAÇÃO DE AGENTES\\n\\n**CONTEXTO DO ESCALATION RESOLVIDO:**\\n• ID do Escalation: ${$('find_active_escalation').item.json.id}\\n• Motivo Original: ${$('find_active_escalation').item.json.escalation_reason}\\n• Duração: ${Math.round((new Date() - new Date($('find_active_escalation').item.json.created_at)) / (1000 * 60))} minutos\\n• Fila: ${$('find_active_escalation').item.json.assigned_queue}\\n\\n**PERFIL DO CLIENTE:**\\n• Nome: ${$('find_active_escalation').item.json.contact_name || 'N/A'}\\n• Empresa: ${$('find_active_escalation').item.json.contact_company || 'N/A'}\\n• Status da Jornada: ${$('find_active_escalation').item.json.contact_journey_status || 'unknown'}\\n\\n**CONTEXTO DURANTE ESCALATION:**\\n${$('get_escalation_context').all().map(ctx => `• ${ctx.json.agent_name || 'Sistema'}: ${ctx.json.interaction_type || 'Interação'} - ${(ctx.json.message_sent || 'Sem mensagem').substring(0, 100)}...`).join('\\n')}\\n\\n**RESUMO EXECUTIVO ORIGINAL:**\\n${$('find_active_escalation').item.json.executive_summary || 'Não disponível'}\\n\\n**MISSÃO:**\\nCrie um handoff inteligente para reativar os agentes autônomos:\\n1. Resumo do que foi resolvido\\n2. Contexto atualizado para o agente IA\\n3. Próximos passos recomendados\\n4. Tom/abordagem sugerida\\n5. Alertas ou cuidados especiais\\n\\nResposta em JSON:\\n{\\n  \\\"resolution_summary\\\": \\\"resumo do que foi resolvido\\\",\\n  \\\"updated_context\\\": \\\"contexto atualizado para IA\\\",\\n  \\\"recommended_next_steps\\\": [\\\"passo 1\\\", \\\"passo 2\\\"],\\n  \\\"suggested_tone\\\": \\\"tom/abordagem\\\",\\n  \\\"special_alerts\\\": [\\\"alerta 1\\\", \\\"alerta 2\\\"],\\n  \\\"follow_up_timing\\\": \\\"quando fazer follow-up\\\",\\n  \\\"conversation_priority\\\": \\\"high/medium/low\\\",\\n  \\\"handoff_message\\\": \\\"mensagem de transição para o cliente\\\"\\n}`,\n    task_type: 'handoff_analysis',\n    calling_workflow_id: $workflow.id,\n    calling_node_id: 'ai_handoff_analysis'\n  }\n} }}", "options": {}}, "id": "ai_handoff_analysis", "name": "🤖 AI: Intelligent Handoff Analysis", "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 2, "position": [1240, 200], "continueOnFail": true}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "ai_handoff_success", "leftValue": "={{ $json.choices?.[0]?.message?.content }}", "rightValue": "", "operator": {"type": "string", "operation": "isNotEmpty"}}]}}, "id": "if_ai_handoff_success", "name": "IF: AI Handoff Success?", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [1460, 200]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "-- Marcar escalation como resolvido\nUPDATE agent.human_escalations \nSET \n  status = 'resolved',\n  resolved_at = NOW(),\n  resolution_summary = $1,\n  resolution_context = $2::jsonb\nWHERE id = $3\nRETURNING *;", "options": {"parameters": {"values": ["={{ JSON.parse($('ai_handoff_analysis').item.json.choices[0].message.content).resolution_summary || 'Resolvido via atendimento humano' }}", "={{ JSON.stringify({\n                handoff_analysis: JSON.parse($('ai_handoff_analysis').item.json.choices[0].message.content),\n                resolution_trigger: $('trigger_chatwoot_webhook').item.json.body || $('trigger_manual_resolution').item.json.body,\n                escalation_context: $('get_escalation_context').all(),\n                resolved_at: new Date().toISOString()\n              }) }}", "={{ $('find_active_escalation').item.json.id }}"]}}}, "id": "mark_escalation_resolved", "name": "✅ <PERSON> as Resolved", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [1680, 200], "credentials": {"postgres": {"id": "postgres-main", "name": "PostgreSQL Main"}}, "continueOnFail": true}, {"parameters": {"operation": "execute<PERSON>uery", "query": "-- Reativar agentes autônomos para o contato\nUPDATE agent.contact_automation_status \nSET \n  automation_paused = false,\n  reactivated_at = NOW(),\n  reactivation_context = $1::jsonb,\n  handoff_instructions = $2\nWHERE contact_id = $3\nAND escalation_id = $4\nRETURNING *;", "options": {"parameters": {"values": ["={{ JSON.stringify(JSON.parse($('ai_handoff_analysis').item.json.choices[0].message.content)) }}", "={{ JSON.parse($('ai_handoff_analysis').item.json.choices[0].message.content).updated_context || 'Reativação após resolução humana' }}", "={{ $('find_active_escalation').item.json.contact_id }}", "={{ $('find_active_escalation').item.json.id }}"]}}}, "id": "reactivate_autonomous_agents", "name": "🔄 Reactivate Autonomous Agents", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [1680, 380], "credentials": {"postgres": {"id": "postgres-main", "name": "PostgreSQL Main"}}, "continueOnFail": true}, {"parameters": {"operation": "execute<PERSON>uery", "query": "-- Registrar no histórico de handoffs\nINSERT INTO agent.handoff_history (\n  escalation_id,\n  contact_id,\n  handoff_type,\n  handoff_direction,\n  handoff_context,\n  quality_score,\n  context_preservation_score\n)\nVALUES (\n  $1,\n  $2,\n  'human_to_ai',\n  'reactivation',\n  $3::jsonb,\n  $4,\n  $5\n)\nRETURNING *;", "options": {"parameters": {"values": ["={{ $('find_active_escalation').item.json.id }}", "={{ $('find_active_escalation').item.json.contact_id }}", "={{ JSON.stringify({\n                resolution_summary: JSON.parse($('ai_handoff_analysis').item.json.choices[0].message.content).resolution_summary,\n                handoff_instructions: JSON.parse($('ai_handoff_analysis').item.json.choices[0].message.content).updated_context,\n                recommended_actions: JSON.parse($('ai_handoff_analysis').item.json.choices[0].message.content).recommended_next_steps,\n                special_alerts: JSON.parse($('ai_handoff_analysis').item.json.choices[0].message.content).special_alerts\n              }) }}", "={{ JSON.parse($('ai_handoff_analysis').item.json.choices[0].message.content).conversation_priority === 'high' ? 9 : JSON.parse($('ai_handoff_analysis').item.json.choices[0].message.content).conversation_priority === 'medium' ? 7 : 5 }}", "8"]}}}, "id": "record_handoff_history", "name": "📊 Record Handoff History", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [1900, 200], "credentials": {"postgres": {"id": "postgres-main", "name": "PostgreSQL Main"}}, "continueOnFail": true}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "should_send_followup", "leftValue": "={{ JSON.parse($('ai_handoff_analysis').item.json.choices[0].message.content).handoff_message }}", "rightValue": "", "operator": {"type": "string", "operation": "isNotEmpty"}}]}}, "id": "if_should_send_followup", "name": "IF: Should Send Follow-up?", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [2120, 200]}, {"parameters": {"workflowId": "{{ $vars.SEND_MESSAGE_WORKFLOW_ID }}", "data": "={{ {\n  contact_id: $('find_active_escalation').item.json.contact_id,\n  message: JSON.parse($('ai_handoff_analysis').item.json.choices[0].message.content).handoff_message || 'Obrigado pelo seu contato! Estamos aqui para continuar ajudando você.',\n  message_type: 'follow_up',\n  context: {\n    source: 'escalation_resolution',\n    escalation_id: $('find_active_escalation').item.json.id,\n    priority: JSON.parse($('ai_handoff_analysis').item.json.choices[0].message.content).conversation_priority || 'medium',\n    special_instructions: JSON.parse($('ai_handoff_analysis').item.json.choices[0].message.content).updated_context\n  }\n} }}", "options": {}}, "id": "send_followup_message", "name": "💬 Send Follow-up Message", "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 2, "position": [2340, 200], "continueOnFail": true}, {"parameters": {"method": "POST", "url": "={{ $vars.SLACK_WEBHOOK_URL }}", "sendBody": true, "specifyBody": "json", "jsonBody": "{\n  \"text\": \"✅ Escalation Resolvido - Agentes Reativados\",\n  \"blocks\": [\n    {\n      \"type\": \"header\",\n      \"text\": {\n        \"type\": \"plain_text\",\n        \"text\": \"✅ ESCALATION RESOLVIDO - AGENTES REATIVADOS\"\n      }\n    },\n    {\n      \"type\": \"section\",\n      \"fields\": [\n        {\n          \"type\": \"mrkdwn\",\n          \"text\": \"*👤 Cliente:*\\n{{ $('find_active_escalation').item.json.contact_name || 'N/A' }}\"\n        },\n        {\n          \"type\": \"mrkdwn\",\n          \"text\": \"*🏢 Empresa:*\\n{{ $('find_active_escalation').item.json.contact_company || 'N/A' }}\"\n        },\n        {\n          \"type\": \"mrkdwn\",\n          \"text\": \"*🎫 Ticket:*\\n#{{ $('find_active_escalation').item.json.id }}\"\n        },\n        {\n          \"type\": \"mrkdwn\",\n          \"text\": \"*⏱️ Duração:*\\n{{ Math.round((new Date() - new Date($('find_active_escalation').item.json.created_at)) / (1000 * 60)) }} minutos\"\n        }\n      ]\n    },\n    {\n      \"type\": \"section\",\n      \"text\": {\n        \"type\": \"mrkdwn\",\n        \"text\": \"*📋 Resumo da Resolução:*\\n{{ JSON.parse($('ai_handoff_analysis').item.json.choices[0].message.content).resolution_summary || 'Resolvido via atendimento humano' }}\"\n      }\n    },\n    {\n      \"type\": \"section\",\n      \"text\": {\n        \"type\": \"mrkdwn\",\n        \"text\": \"*🎯 Próximos Passos para IA:*\\n{{ (JSON.parse($('ai_handoff_analysis').item.json.choices[0].message.content).recommended_next_steps || ['Continuar atendimento normal']).map(step => `• ${step}`).join('\\n') }}\"\n      }\n    },\n    {\n      \"type\": \"section\",\n      \"text\": {\n        \"type\": \"mrkdwn\",\n        \"text\": \"*⚠️ Alertas Especiais:*\\n{{ (JSON.parse($('ai_handoff_analysis').item.json.choices[0].message.content).special_alerts || ['Nenhum alerta especial']).map(alert => `• ${alert}`).join('\\n') }}\"\n      }\n    },\n    {\n      \"type\": \"context\",\n      \"elements\": [\n        {\n          \"type\": \"mrkdwn\",\n          \"text\": \"🔄 Agentes autônomos reativados | Handoff inteligente aplicado | Qualidade: {{ JSON.parse($('ai_handoff_analysis').item.json.choices[0].message.content).conversation_priority || 'medium' }}\"\n        }\n      ]\n    }\n  ],\n  \"channel\": \"#human-escalations\"\n}", "options": {}}, "id": "notify_resolution_team", "name": "📱 Notify Resolution Team", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [2340, 380], "continueOnFail": true}, {"parameters": {"content": "{\n  \"escalation_id\": {{ $('find_active_escalation').item.json.id || 'null' }},\n  \"status\": \"resolved_and_reactivated\",\n  \"contact_id\": {{ $('find_active_escalation').item.json.contact_id || 'null' }},\n  \"resolution_summary\": \"{{ JSON.parse($('ai_handoff_analysis').item.json.choices[0].message.content).resolution_summary || 'Resolved successfully' }}\",\n  \"agents_reactivated\": true,\n  \"handoff_quality_score\": {{ JSON.parse($('ai_handoff_analysis').item.json.choices[0].message.content).conversation_priority === 'high' ? 9 : JSON.parse($('ai_handoff_analysis').item.json.choices[0].message.content).conversation_priority === 'medium' ? 7 : 5 }},\n  \"follow_up_sent\": {{ $('send_followup_message').item.json ? 'true' : 'false' }},\n  \"next_steps\": {{ JSON.stringify(JSON.parse($('ai_handoff_analysis').item.json.choices[0].message.content).recommended_next_steps || ['Continue normal operations']) }}\n}", "options": {}}, "id": "respond_resolution_success", "name": "✅ Respond: Resolution Success", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [2560, 200]}, {"parameters": {"content": "{\n  \"error\": \"handoff_analysis_failed\",\n  \"escalation_id\": {{ $('find_active_escalation').item.json.id || 'null' }},\n  \"reason\": \"AI handoff analysis failed - using fallback reactivation\",\n  \"fallback_action\": \"Agents reactivated with basic context\"\n}", "options": {}}, "id": "respond_handoff_fallback", "name": "⚠️ Respond: Handoff Fallback", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1460, 400]}, {"parameters": {"content": "{\n  \"error\": \"escalation_not_found\",\n  \"message\": \"No active escalation found for the provided conversation or escalation ID\",\n  \"received_data\": {{ JSON.stringify($('trigger_chatwoot_webhook').item.json.body || $('trigger_manual_resolution').item.json.body) }}\n}", "options": {}}, "id": "respond_escalation_not_found", "name": "❌ Respond: Escalation Not Found", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [800, 400]}, {"parameters": {"content": "{\n  \"error\": \"invalid_resolution_trigger\",\n  \"message\": \"Invalid webhook data - not a resolution event\",\n  \"received_data\": {{ JSON.stringify($('trigger_chatwoot_webhook').item.json.body || $('trigger_manual_resolution').item.json.body) }}\n}", "options": {}}, "id": "respond_invalid_trigger", "name": "❌ Respond: <PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [360, 500]}], "connections": {"trigger_chatwoot_webhook": {"main": [[{"node": "validate_resolution_trigger", "type": "main", "index": 0}]]}, "trigger_manual_resolution": {"main": [[{"node": "validate_resolution_trigger", "type": "main", "index": 0}]]}, "validate_resolution_trigger": {"main": [[{"node": "find_active_escalation", "type": "main", "index": 0}], [{"node": "respond_invalid_trigger", "type": "main", "index": 0}]]}, "find_active_escalation": {"main": [[{"node": "if_escalation_found", "type": "main", "index": 0}]]}, "if_escalation_found": {"main": [[{"node": "get_escalation_context", "type": "main", "index": 0}], [{"node": "respond_escalation_not_found", "type": "main", "index": 0}]]}, "get_escalation_context": {"main": [[{"node": "ai_handoff_analysis", "type": "main", "index": 0}]]}, "ai_handoff_analysis": {"main": [[{"node": "if_ai_handoff_success", "type": "main", "index": 0}]]}, "if_ai_handoff_success": {"main": [[{"node": "mark_escalation_resolved", "type": "main", "index": 0}, {"node": "reactivate_autonomous_agents", "type": "main", "index": 0}], [{"node": "respond_handoff_fallback", "type": "main", "index": 0}]]}, "mark_escalation_resolved": {"main": [[{"node": "record_handoff_history", "type": "main", "index": 0}]]}, "reactivate_autonomous_agents": {"main": [[{"node": "record_handoff_history", "type": "main", "index": 1}]]}, "record_handoff_history": {"main": [[{"node": "if_should_send_followup", "type": "main", "index": 0}]]}, "if_should_send_followup": {"main": [[{"node": "send_followup_message", "type": "main", "index": 0}], [{"node": "notify_resolution_team", "type": "main", "index": 0}]]}, "send_followup_message": {"main": [[{"node": "notify_resolution_team", "type": "main", "index": 0}]]}, "notify_resolution_team": {"main": [[{"node": "respond_resolution_success", "type": "main", "index": 0}]]}}, "settings": {"executionOrder": "v1", "saveManualExecutions": true, "callerPolicy": "workflowsFromSameOwner", "errorWorkflow": {"id": "escalation_error_handler"}}, "staticData": null, "tags": [{"createdAt": "2024-01-15T20:00:00.000Z", "updatedAt": "2024-01-15T20:00:00.000Z", "id": "human-escalation", "name": "human-escalation"}, {"createdAt": "2024-01-15T20:00:00.000Z", "updatedAt": "2024-01-15T20:00:00.000Z", "id": "enterprise-grade", "name": "enterprise-grade"}, {"createdAt": "2024-01-15T20:00:00.000Z", "updatedAt": "2024-01-15T20:00:00.000Z", "id": "error-resilient", "name": "error-resilient"}, {"createdAt": "2024-01-15T20:00:00.000Z", "updatedAt": "2024-01-15T20:00:00.000Z", "id": "intelligent-handoff", "name": "intelligent-handoff"}], "triggerCount": 2, "updatedAt": "2024-01-15T20:00:00.000Z", "versionId": "2"}