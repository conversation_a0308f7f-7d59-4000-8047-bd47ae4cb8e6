{"name": "[CREATOR] Infoproduct Generation Engine v1.1 - Cost-Aware", "nodes": [{"parameters": {"path": "create-infoproduct", "options": {}}, "id": "trigger_webhook", "name": "TRIGGER: From Approved Opportunity", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [400, 300], "notes": "Acionado por um gestor que aprova uma oportunidade. Espera-se: { opportunityId: 123 }"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT * FROM agent.product_opportunities WHERE id = $1;", "options": {"parameters": {"values": ["={{$json.body.opportunityId}}"]}}}, "id": "db_get_opportunity", "name": "DB: Get Opportunity Details", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [640, 300], "credentials": {"postgres": {"id": "YOUR_POSTGRES_CREDENTIAL_ID"}}}, {"parameters": {"workflowId": "={{ $env.DISPATCHER_WORKFLOW_ID }}", "data": "={{ { body: { \n    prompt: `Você é um arquiteto de conteúdo educacional. Sua tarefa é criar a estrutura (índice) de um infoproduto.\\n\\nTEMA: ${$json.product_name}\\nDESCRIÇÃO: ${$json.description}\\nOBJETIVO: ${$json.demand_reasoning}\\n\\nCrie um índice detalhado para um e-book/curso sobre este tema. Pense em uma progressão lógica, do básico ao avançado. Formate como um array JSON de strings, onde cada string é um capítulo ou módulo.\\n\\nExemplo de Saída: [\\\"Capítulo 1: Introdução aos Fundamentos\\\", \\\"Capítulo 2: As Ferramentas Essenciais\\\", \\\"Capítulo 3: Estratégias Avançadas\\\"]`,\n    task_type: 'complex_analysis',\n    calling_workflow_id: $workflow.id,\n    calling_node_id: 'call_dispatcher_for_structure'\n} } }}", "options": {}}, "id": "call_dispatcher_for_structure", "name": "Call Dispatcher for Structure", "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 2, "position": [880, 300]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "INSERT INTO agent.generated_infoproducts (opportunity_id, title, product_type, content_structure, status) VALUES ($1, $2, $3, $4::jsonb, 'generating_content') RETURNING id;", "options": {"parameters": {"values": ["={{$('db_get_opportunity').item.json.id}}", "={{$('db_get_opportunity').item.json.product_name}}", "={{$('db_get_opportunity').item.json.product_type}}", "={{JSON.stringify($json)}}"]}}}, "id": "db_save_structure", "name": "DB: Save Product Structure", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [1120, 300], "credentials": {"postgres": {"id": "YOUR_POSTGRES_CREDENTIAL_ID"}}}, {"parameters": {"functionCode": "const chapters = $('call_dispatcher_for_structure').item.json.body.choices[0].message.content;\nconst infoproductId = $json.id;\nconst opportunityName = $('db_get_opportunity').item.json.product_name;\n\n// A resposta do dispatcher é uma string JSON, então precisamos parseá-la primeiro.\nconst parsedChapters = JSON.parse(chapters);\n\nconst items = parsedChapters.map((chapterTitle, index) => {\n  return {\n    json: {\n      infoproduct_id: infoproductId,\n      opportunity_name: opportunityName,\n      chapter_title: chapterTitle,\n      chapter_order: index + 1\n    }\n  };\n});\n\nreturn items;", "options": {}}, "id": "code_prepare_sub_input", "name": "CODE: Prepare Sub-Workflow Input", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1340, 300]}, {"parameters": {"batchSize": 1}, "id": "loop_chapters", "name": "Loop Over Chapters", "type": "n8n-nodes-base.splitInBatches", "typeVersion": 2, "position": [1540, 300]}, {"parameters": {"workflowId": "={{$env.SUB_WRITE_CHAPTER_WORKFLOW_ID}}", "options": {"runIn": "background", "parameters": {"values": {"json": [{"name": "data", "value": "={{$json}}"}]}}}}, "id": "execute_sub_workflow", "name": "EXECUTE: Write Chapter", "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 2, "position": [1760, 300], "notes": "Dispara um sub-workflow para escrever cada capítulo em paralelo."}], "connections": {"trigger_webhook": {"main": [[{"node": "db_get_opportunity"}]]}, "db_get_opportunity": {"main": [[{"node": "call_dispatcher_for_structure"}]]}, "call_dispatcher_for_structure": {"main": [[{"node": "db_save_structure"}]]}, "db_save_structure": {"main": [[{"node": "code_prepare_sub_input"}]]}, "code_prepare_sub_input": {"main": [[{"node": "loop_chapters"}]]}, "loop_chapters": {"main": [[{"node": "execute_sub_workflow"}]]}}}