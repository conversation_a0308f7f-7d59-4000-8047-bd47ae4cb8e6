# Guia de Configuração do Ngrok

## Visão Geral

O ngrok permite expor seus webhooks locais para a internet, tornando possível receber webhooks de serviços externos como WhatsApp Business API, Telegram, e outros serviços que precisam enviar dados para seu n8n.

## Pré-requisitos

1. **Conta Ngrok**: Crie uma conta gratuita em [ngrok.com](https://ngrok.com)
2. **Token de Autenticação**: Obtenha seu token em [dashboard.ngrok.com](https://dashboard.ngrok.com/get-started/your-authtoken)

## Configuração

### 1. Obter Token do Ngrok

1. Acesse [dashboard.ngrok.com](https://dashboard.ngrok.com/get-started/your-authtoken)
2. Faça login em sua conta
3. Copie seu token de autenticação

### 2. Configurar o Token

**Opção A: Adicionar ao arquivo .env**
```bash
# Edite o arquivo .env e adicione:
NGROK_AUTHTOKEN=seu_token_aqui
```

**Opção B: Configurar durante a execução**
- Execute `Start-Environment.ps1`
- Selecione "Modo Público (ngrok)"
- Se o token não estiver configurado, o script informará como adicioná-lo

### 3. Executar com Ngrok

```powershell
# Execute o script principal
.\Start-Environment.ps1

# No menu, selecione:
# [2] Modo Público (ngrok)
```

## Como Funciona

### Fluxo de Inicialização

1. **Menu de Seleção**: O script apresenta opções de modo local ou público
2. **Verificação de Token**: Valida se `NGROK_AUTHTOKEN` está configurado
3. **Inicialização dos Serviços**: Inicia todos os serviços incluindo o ngrok
4. **Obtenção da URL Pública**: Consulta a API do ngrok para obter a URL pública
5. **Configuração de Webhooks**: Atualiza automaticamente as variáveis de ambiente
6. **Reinicialização**: Reinicia serviços que dependem da URL pública

### URLs Geradas

Quando o ngrok está ativo, você terá:

- **URL Pública do N8N**: `https://abc123.ngrok.io`
- **Webhook Evolution**: `https://abc123.ngrok.io/webhook/evolution`
- **Inspector Ngrok**: `http://localhost:4040`

## Monitoramento

### Inspector do Ngrok

Acesse `http://localhost:4040` para:
- Ver todas as requisições HTTP em tempo real
- Inspecionar headers e payloads
- Replay de requisições para debug
- Estatísticas de tráfego

### Logs do Sistema

O script gera logs detalhados sobre:
- Status da inicialização do ngrok
- URL pública obtida
- Reinicialização de serviços
- Erros e recuperação automática

## Solução de Problemas

### Erro: "Token não configurado"

**Problema**: `NGROK_AUTHTOKEN` não está definido no `.env`

**Solução**:
```bash
# Adicione ao arquivo .env
echo "NGROK_AUTHTOKEN=seu_token_aqui" >> .env
```

### Erro: "Ngrok não ficou disponível"

**Possíveis Causas**:
- Token inválido ou expirado
- Limite de túneis atingido (conta gratuita)
- Problemas de conectividade

**Soluções**:
1. Verifique se o token está correto
2. Verifique se não há outros túneis ngrok rodando
3. Reinicie o script

### Erro: "Falha ao obter URL pública"

**Problema**: API do ngrok não responde

**Solução**:
1. Aguarde alguns segundos e tente novamente
2. Verifique se o container ngrok está rodando: `docker ps | grep ngrok`
3. Verifique os logs: `docker logs ngrok_tunnel`

### Fallback Automático

O sistema possui recuperação automática:
- Se o ngrok falhar, o sistema continua em modo local
- URLs são automaticamente reconfiguradas
- Serviços continuam funcionando normalmente

## Limitações da Conta Gratuita

### Ngrok Free Tier
- **1 túnel simultâneo**: Apenas um túnel por vez
- **URL aleatória**: URL muda a cada reinicialização
- **Sem domínio customizado**: URLs são do formato `abc123.ngrok.io`
- **Limite de requisições**: 20.000 requisições/mês

### Recomendações

1. **Para desenvolvimento**: Conta gratuita é suficiente
2. **Para produção**: Considere upgrade para conta paga
3. **URLs estáveis**: Contas pagas permitem domínios customizados

## Integração com Serviços Externos

### WhatsApp Business API

```bash
# Configure o webhook no WhatsApp Business
Webhook URL: https://sua-url-ngrok.ngrok.io/webhook/evolution
Verify Token: seu_token_de_verificacao
```

### Telegram Bot

```bash
# Configure o webhook do Telegram
curl -X POST "https://api.telegram.org/bot<TOKEN>/setWebhook" \
     -H "Content-Type: application/json" \
     -d '{"url": "https://sua-url-ngrok.ngrok.io/webhook/telegram"}'
```

### Outros Serviços

Para qualquer serviço que precise enviar webhooks:
1. Use a URL base: `https://sua-url-ngrok.ngrok.io`
2. Configure o endpoint específico no n8n
3. Teste usando o Inspector do ngrok

## Segurança

### Boas Práticas

1. **Não compartilhe URLs**: URLs ngrok são públicas mas não listadas
2. **Validação de webhooks**: Sempre valide tokens e assinaturas
3. **Monitoramento**: Use o Inspector para detectar tráfego suspeito
4. **Rotação de URLs**: Reinicie periodicamente para gerar novas URLs

### Configurações de Segurança

```bash
# No n8n, configure validação de webhooks
# Use headers de autenticação quando possível
# Implemente rate limiting se necessário
```

## Comandos Úteis

### Verificar Status

```powershell
# Verificar se ngrok está rodando
docker ps | grep ngrok

# Ver logs do ngrok
docker logs ngrok_tunnel

# Verificar URL atual
curl http://localhost:4040/api/tunnels
```

### Reiniciar Ngrok

```powershell
# Reiniciar apenas o ngrok
docker restart ngrok_tunnel

# Reiniciar todo o ambiente
.\Start-Environment.ps1
```

### Parar Ngrok

```powershell
# Parar ngrok mantendo outros serviços
docker stop ngrok_tunnel

# Parar todos os serviços
docker compose down
```

## Suporte

Para problemas específicos do ngrok:
1. Consulte a [documentação oficial](https://ngrok.com/docs)
2. Verifique o [status do serviço](https://status.ngrok.com/)
3. Acesse o [suporte da comunidade](https://github.com/inconshreveable/ngrok)

Para problemas com este projeto:
1. Verifique os logs em `install.log`
2. Execute diagnósticos com `post-setup-automation.ps1`
3. Consulte outros guias na pasta `docs/`