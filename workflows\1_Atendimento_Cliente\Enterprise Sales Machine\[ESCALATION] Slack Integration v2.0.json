{"name": "[ESCALATION] Slack Integration v2.0", "nodes": [{"parameters": {"httpMethod": "POST", "path": "escalation-slack-webhook", "options": {"noResponseBody": false}}, "id": "slack-webhook-trigger", "name": "🎯 Slack Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [240, 300], "webhookId": "slack-escalation-webhook"}, {"parameters": {"jsCode": "// Processar dados do webhook do Slack\nconst body = $input.first().json.body || $input.first().json;\n\n// Verificar se é um evento válido do Slack\nif (!body.type && !body.event) {\n  throw new Error('Webhook inválido: tipo de evento ausente');\n}\n\n// Processar diferentes tipos de eventos do Slack\nlet processedData = {\n  timestamp: new Date().toISOString(),\n  source: 'slack'\n};\n\n// Verificar se é um evento de escalação manual\nif (body.type === 'event_callback' && body.event) {\n  const event = body.event;\n  \n  if (event.type === 'message' && event.text) {\n    // Verificar se a mensagem contém comandos de escalação\n    const escalationCommands = [\n      '/escalate',\n      '!escalate',\n      'escalação',\n      'escalation',\n      'urgent help',\n      'need manager'\n    ];\n    \n    const hasEscalationCommand = escalationCommands.some(cmd => \n      event.text.toLowerCase().includes(cmd.toLowerCase())\n    );\n    \n    if (hasEscalationCommand) {\n      processedData = {\n        ...processedData,\n        event_type: 'manual_escalation_request',\n        slack_data: {\n          channel: event.channel,\n          user: event.user,\n          text: event.text,\n          ts: event.ts,\n          thread_ts: event.thread_ts\n        },\n        escalation_required: true,\n        escalation_reason: ['manual_slack_request'],\n        escalation_priority: 'high'\n      };\n    } else {\n      processedData = {\n        ...processedData,\n        event_type: 'slack_message',\n        escalation_required: false\n      };\n    }\n  }\n} else if (body.type === 'interactive_message' || body.payload) {\n  // Processar interações com botões/formulários\n  const payload = body.payload ? JSON.parse(body.payload) : body;\n  \n  processedData = {\n    ...processedData,\n    event_type: 'slack_interaction',\n    interaction_data: payload,\n    escalation_required: false\n  };\n  \n  // Verificar se é uma ação de escalação\n  if (payload.actions && payload.actions[0]) {\n    const action = payload.actions[0];\n    if (action.value && action.value.includes('escalate')) {\n      processedData.escalation_required = true;\n      processedData.escalation_reason = ['slack_button_escalation'];\n      processedData.escalation_priority = 'medium';\n    }\n  }\n} else if (body.escalation_data) {\n  // Webhook direto para notificação de escalação\n  processedData = {\n    ...processedData,\n    event_type: 'escalation_notification',\n    escalation_data: body.escalation_data,\n    notification_type: body.notification_type || 'new_escalation',\n    escalation_required: false // Já é uma notificação, não precisa criar nova escalação\n  };\n}\n\nreturn [{ json: processedData }];"}, "id": "process-slack-data", "name": "⚙️ Process Slack Data", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [460, 300]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "is-escalation", "leftValue": "={{ $json.escalation_required }}", "rightValue": true, "operator": {"type": "boolean", "operation": "equal"}}], "combinator": "and"}, "options": {}}, "id": "check-escalation-request", "name": "❓ Escalation Request?", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [680, 300]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "is-notification", "leftValue": "={{ $json.event_type }}", "rightValue": "escalation_notification", "operator": {"type": "string", "operation": "equals"}}], "combinator": "and"}, "options": {}}, "id": "check-notification-type", "name": "❓ Notification Type?", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [680, 500]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "-- Buscar informações do usuário Slack\nSELECT \n  sa.agent_id,\n  sa.slack_user_id,\n  sa.slack_username,\n  sa.department,\n  sa.role,\n  a.name as agent_name,\n  a.email as agent_email,\n  a.specializations\nFROM agent.slack_agents sa\nJOIN agent.agents a ON sa.agent_id = a.agent_id\nWHERE sa.slack_user_id = $1\n  AND sa.status = 'active'\nLIMIT 1;", "options": {"queryReplacement": "={{ $json.slack_data.user }}"}}, "id": "get-slack-user-info", "name": "👤 Get Slack User Info", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [900, 200], "credentials": {"postgres": {"id": "postgres-escalation-db", "name": "PostgreSQL Escalation DB"}}}, {"parameters": {"jsCode": "// Preparar dados para escalação via Slack\nconst slackData = $('process-slack-data').item.json;\nconst userInfo = $('get-slack-user-info').item?.json;\n\n// Extrair informações do texto da mensagem\nconst messageText = slackData.slack_data?.text || '';\nconst lines = messageText.split('\\n');\n\n// Tentar extrair informações estruturadas da mensagem\nlet title = 'Escalação via Slack';\nlet description = messageText;\nlet priority = slackData.escalation_priority || 'medium';\nlet category = 'general';\n\n// Procurar por padrões na mensagem\nlines.forEach(line => {\n  const lowerLine = line.toLowerCase();\n  \n  if (lowerLine.includes('título:') || lowerLine.includes('title:')) {\n    title = line.split(':')[1]?.trim() || title;\n  }\n  \n  if (lowerLine.includes('prioridade:') || lowerLine.includes('priority:')) {\n    const priorityText = line.split(':')[1]?.trim().toLowerCase();\n    if (['critical', 'crítica', 'alta', 'high'].includes(priorityText)) {\n      priority = 'critical';\n    } else if (['high', 'alta'].includes(priorityText)) {\n      priority = 'high';\n    }\n  }\n  \n  if (lowerLine.includes('categoria:') || lowerLine.includes('category:')) {\n    const categoryText = line.split(':')[1]?.trim().toLowerCase();\n    if (['technical', 'técnico', 'tech'].includes(categoryText)) {\n      category = 'technical';\n    } else if (['billing', 'financeiro', 'cobrança'].includes(categoryText)) {\n      category = 'billing';\n    } else if (['sales', 'vendas', 'comercial'].includes(categoryText)) {\n      category = 'sales';\n    }\n  }\n});\n\nconst escalationData = {\n  title: title,\n  description: `${description}\\n\\n--- Informações do Slack ---\\nCanal: ${slackData.slack_data?.channel}\\nUsuário: ${userInfo?.agent_name || slackData.slack_data?.user}\\nTimestamp: ${slackData.slack_data?.ts}`,\n  priority: priority,\n  category: category,\n  source_system: 'slack',\n  external_conversation_id: `${slackData.slack_data?.channel}-${slackData.slack_data?.ts}`,\n  customer_data: {\n    slack_user_id: slackData.slack_data?.user,\n    slack_username: userInfo?.slack_username,\n    agent_name: userInfo?.agent_name,\n    department: userInfo?.department\n  },\n  context_data: {\n    slack_channel: slackData.slack_data?.channel,\n    slack_thread: slackData.slack_data?.thread_ts,\n    original_message: messageText,\n    requesting_agent: userInfo,\n    escalation_method: 'slack_command'\n  },\n  urgency_level: priority === 'critical' ? 'immediate' : \n                 priority === 'high' ? 'urgent' : 'normal',\n  estimated_resolution_time: priority === 'critical' ? 30 : \n                            priority === 'high' ? 120 : 240,\n  created_by_agent_id: userInfo?.agent_id\n};\n\nreturn [{ json: escalationData }];"}, "id": "prepare-slack-escalation", "name": "📋 Prepare Slack Escalation", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1120, 200]}, {"parameters": {"url": "http://localhost:5678/webhook/escalation-main", "options": {}, "bodyParametersUi": {"parameter": [{"name": "title", "value": "={{ $json.title }}"}, {"name": "description", "value": "={{ $json.description }}"}, {"name": "priority", "value": "={{ $json.priority }}"}, {"name": "category", "value": "={{ $json.category }}"}, {"name": "source_system", "value": "={{ $json.source_system }}"}, {"name": "external_conversation_id", "value": "={{ $json.external_conversation_id }}"}, {"name": "customer_data", "value": "={{ JSON.stringify($json.customer_data) }}"}, {"name": "context_data", "value": "={{ JSON.stringify($json.context_data) }}"}, {"name": "urgency_level", "value": "={{ $json.urgency_level }}"}, {"name": "created_by_agent_id", "value": "={{ $json.created_by_agent_id }}"}]}}, "id": "create-slack-escalation", "name": "🚀 Create Slack Escalation", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1340, 200]}, {"parameters": {"authentication": "oAuth2", "select": "channel", "channelId": {"__rl": true, "value": "={{ $('process-slack-data').item.json.slack_data.channel }}", "mode": "id"}, "text": "✅ **Escalação Criada com Sucesso**\n\n📋 **ID:** {{ $json.escalation_id }}\n🎯 **Prioridade:** {{ $('prepare-slack-escalation').item.json.priority }}\n📂 **Categoria:** {{ $('prepare-slack-escalation').item.json.category }}\n⏰ **Criada em:** {{ new Date().toLocaleString('pt-BR') }}\n\n🔄 **Status:** Aguardando roteamento\n👥 **Próximos passos:** Um agente especializado será designado automaticamente\n\n_Você receberá atualizações sobre o progresso desta escalação._", "otherOptions": {"thread_ts": "={{ $('process-slack-data').item.json.slack_data.thread_ts || $('process-slack-data').item.json.slack_data.ts }}"}}, "id": "send-slack-confirmation", "name": "✅ Send Slack Confirmation", "type": "n8n-nodes-base.slack", "typeVersion": 2.1, "position": [1560, 200], "credentials": {"slackOAuth2Api": {"id": "slack-oauth-escalation", "name": "Slack O<PERSON>uth Escalation"}}}, {"parameters": {"jsCode": "// Processar notificação de escalação para Slack\nconst data = $input.first().json;\nconst escalationData = data.escalation_data;\n\n// Determinar canal baseado na prioridade e categoria\nlet targetChannel = '#escalations';\nif (escalationData.priority === 'critical') {\n  targetChannel = '#critical-escalations';\n} else if (escalationData.category === 'technical') {\n  targetChannel = '#tech-escalations';\n} else if (escalationData.category === 'billing') {\n  targetChannel = '#billing-escalations';\n}\n\n// Preparar dados da notificação\nconst notificationData = {\n  channel: targetChannel,\n  escalation: escalationData,\n  notification_type: data.notification_type,\n  timestamp: new Date().toISOString()\n};\n\nreturn [{ json: notificationData }];"}, "id": "process-escalation-notification", "name": "📢 Process Escalation Notification", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [900, 500]}, {"parameters": {"authentication": "oAuth2", "select": "channel", "channelId": {"__rl": true, "value": "={{ $json.channel }}", "mode": "name"}, "blocksUi": {"blocksValues": [{"type": "section", "sectionBlock": {"message": {"text": "🚨 **Nova Escalação Recebida**", "type": "mrkdwn"}}}, {"type": "section", "sectionBlock": {"message": {"text": "*ID:* {{ $json.escalation.escalation_id }}\n*Título:* {{ $json.escalation.title }}\n*Prioridade:* {{ $json.escalation.priority }}\n*Categoria:* {{ $json.escalation.category }}\n*Sistema:* {{ $json.escalation.source_system }}\n*Cliente:* {{ $json.escalation.customer_data.name || 'N/A' }}", "type": "mrkdwn"}}}, {"type": "section", "sectionBlock": {"message": {"text": "*Descrição:*\n{{ $json.escalation.description }}", "type": "mrkdwn"}}}, {"type": "actions", "actionsBlock": {"elements": [{"type": "button", "text": "🎯 <PERSON><PERSON><PERSON>", "style": "primary", "value": "assign_{{ $json.escalation.escalation_id }}", "actionId": "assign_escalation"}, {"type": "button", "text": "👀 <PERSON>er <PERSON>", "value": "details_{{ $json.escalation.escalation_id }}", "actionId": "view_details"}, {"type": "button", "text": "⚡ Priorizar", "style": "danger", "value": "prioritize_{{ $json.escalation.escalation_id }}", "actionId": "prioritize_escalation"}]}}]}}, "id": "send-escalation-notification", "name": "📢 Send Escalation Notification", "type": "n8n-nodes-base.slack", "typeVersion": 2.1, "position": [1120, 500], "credentials": {"slackOAuth2Api": {"id": "slack-oauth-escalation", "name": "Slack O<PERSON>uth Escalation"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "-- Registrar notific<PERSON> Slack\nINSERT INTO agent.slack_notifications (\n  escalation_id,\n  channel,\n  message_ts,\n  notification_type,\n  slack_data,\n  sent_at\n) VALUES (\n  $1,\n  $2,\n  $3,\n  $4,\n  $5,\n  NOW()\n)\nRETURNING notification_id, escalation_id, channel;", "options": {"queryReplacement": "={{ $('process-escalation-notification').item.json.escalation.escalation_id }},{{ $('process-escalation-notification').item.json.channel }},{{ $json.ts }},{{ $('process-escalation-notification').item.json.notification_type }},{{ JSON.stringify($('process-escalation-notification').item.json) }}"}}, "id": "log-slack-notification", "name": "📝 Log Slack Notification", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [1340, 500], "credentials": {"postgres": {"id": "postgres-escalation-db", "name": "PostgreSQL Escalation DB"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "-- Registrar interação Slack\nINSERT INTO agent.slack_interactions (\n  escalation_id,\n  slack_user_id,\n  agent_id,\n  interaction_type,\n  slack_channel,\n  message_text,\n  interaction_data,\n  processed_at\n) VALUES (\n  $1,\n  $2,\n  $3,\n  $4,\n  $5,\n  $6,\n  $7,\n  NOW()\n)\nRETURNING interaction_id, escalation_id, interaction_type;", "options": {"queryReplacement": "={{ $('create-slack-escalation').item.json.escalation_id }},{{ $('process-slack-data').item.json.slack_data.user }},{{ $('get-slack-user-info').item.json.agent_id }},escalation_request,{{ $('process-slack-data').item.json.slack_data.channel }},{{ $('process-slack-data').item.json.slack_data.text }},{{ JSON.stringify($('process-slack-data').item.json) }}"}}, "id": "log-slack-interaction", "name": "📝 Log Slack Interaction", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [1560, 400], "credentials": {"postgres": {"id": "postgres-escalation-db", "name": "PostgreSQL Escalation DB"}}}, {"parameters": {"jsCode": "// Resposta para eventos Slack não relacionados a escalação\nconst data = $input.first().json;\n\nreturn [{\n  json: {\n    status: 'processed',\n    event_type: data.event_type,\n    escalation_required: false,\n    message: 'Evento Slack processado - não requer escalação',\n    timestamp: new Date().toISOString(),\n    processing_details: {\n      event_analyzed: true,\n      escalation_criteria_checked: true,\n      escalation_triggered: false\n    }\n  }\n}];"}, "id": "format-non-escalation-response", "name": "📄 Format Non-Escalation Response", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [680, 700]}, {"parameters": {"jsCode": "// Resposta final para escalações via Slack\nconst escalationData = $('create-slack-escalation').item.json;\nconst slackData = $('process-slack-data').item.json;\n\nreturn [{\n  json: {\n    status: 'escalation_created',\n    escalation_id: escalationData.escalation_id,\n    slack_channel: slackData.slack_data?.channel,\n    slack_user: slackData.slack_data?.user,\n    priority: escalationData.priority,\n    message: 'Escalação criada via Slack com sucesso',\n    timestamp: new Date().toISOString(),\n    processing_details: {\n      source: 'slack',\n      escalation_method: slackData.event_type,\n      user_notified: true,\n      team_notified: true,\n      interaction_logged: true\n    },\n    next_steps: [\n      'Escalação será roteada automaticamente',\n      'Usuário receberá confirmação no Slack',\n      'Equipe será notificada no canal apropriado'\n    ]\n  }\n}];"}, "id": "format-escalation-response", "name": "📄 Format Escalation Response", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1780, 300]}, {"parameters": {"jsCode": "// Resposta para notificações enviadas\nconst notificationData = $('process-escalation-notification').item.json;\nconst slackResponse = $('send-escalation-notification').item.json;\n\nreturn [{\n  json: {\n    status: 'notification_sent',\n    escalation_id: notificationData.escalation.escalation_id,\n    channel: notificationData.channel,\n    message_ts: slackResponse.ts,\n    notification_type: notificationData.notification_type,\n    message: 'Notificação enviada para o Slack com sucesso',\n    timestamp: new Date().toISOString(),\n    processing_details: {\n      channel_notified: notificationData.channel,\n      interactive_buttons_included: true,\n      notification_logged: true\n    }\n  }\n}];"}, "id": "format-notification-response", "name": "📄 Format Notification Response", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1560, 500]}], "pinData": {}, "connections": {"slack-webhook-trigger": {"main": [[{"node": "process-slack-data", "type": "main", "index": 0}]]}, "process-slack-data": {"main": [[{"node": "check-escalation-request", "type": "main", "index": 0}, {"node": "check-notification-type", "type": "main", "index": 0}]]}, "check-escalation-request": {"main": [[{"node": "get-slack-user-info", "type": "main", "index": 0}], [{"node": "format-non-escalation-response", "type": "main", "index": 0}]]}, "check-notification-type": {"main": [[{"node": "process-escalation-notification", "type": "main", "index": 0}], []]}, "get-slack-user-info": {"main": [[{"node": "prepare-slack-escalation", "type": "main", "index": 0}]]}, "prepare-slack-escalation": {"main": [[{"node": "create-slack-escalation", "type": "main", "index": 0}]]}, "create-slack-escalation": {"main": [[{"node": "send-slack-confirmation", "type": "main", "index": 0}, {"node": "log-slack-interaction", "type": "main", "index": 0}]]}, "send-slack-confirmation": {"main": [[{"node": "format-escalation-response", "type": "main", "index": 0}]]}, "process-escalation-notification": {"main": [[{"node": "send-escalation-notification", "type": "main", "index": 0}]]}, "send-escalation-notification": {"main": [[{"node": "log-slack-notification", "type": "main", "index": 0}]]}, "log-slack-notification": {"main": [[{"node": "format-notification-response", "type": "main", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1", "saveManualExecutions": true, "callerPolicy": "workflowsFromSameOwner", "errorWorkflow": "error-handler-workflow"}, "versionId": "slack-integration-v2.0", "meta": {"templateCredsSetupCompleted": true, "instanceId": "escalation-slack-integration"}, "id": "slack-integration-workflow", "tags": [{"createdAt": "2024-01-15T10:00:00.000Z", "updatedAt": "2024-01-15T10:00:00.000Z", "id": "escalation-integration", "name": "escalation-integration"}]}