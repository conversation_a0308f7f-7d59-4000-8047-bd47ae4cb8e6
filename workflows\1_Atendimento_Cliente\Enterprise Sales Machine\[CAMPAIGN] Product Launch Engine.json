{"name": "[CAMPAIGN] Product Launch Engine v1.1 - Cost-Aware", "nodes": [{"parameters": {"options": {"items": [{"name": "campaignName", "type": "string", "default": "Lançamento do Produto X"}, {"name": "productId", "type": "number", "default": 1}, {"name": "launchDate", "type": "dateTime", "default": "2024-08-01T12:00:00.000Z"}, {"name": "influencerIds", "type": "string", "default": "1,2", "description": "IDs dos influenciadores, separados por vírgula"}]}}, "id": "manual_trigger", "name": "TRIGGER: Manual Launch", "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [400, 300]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT * FROM agent.products WHERE id = $1;", "options": {"parameters": {"values": ["={{$json.productId}}"]}}}, "id": "db_get_product", "name": "DB: Get Product Details", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [640, 200], "credentials": {"postgres": {"id": "YOUR_POSTGRES_CREDENTIAL_ID"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "INSERT INTO agent.launch_campaigns (name, product_id, launch_date) VALUES ($1, $2, $3) RETURNING id;", "options": {"parameters": {"values": ["={{$json.campaignName}}", "={{$json.productId}}", "={{$json.launchDate}}"]}}}, "id": "db_create_campaign", "name": "DB: Create Campaign Record", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [640, 400], "credentials": {"postgres": {"id": "YOUR_POSTGRES_CREDENTIAL_ID"}}}, {"parameters": {}, "id": "merge_start", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.merge", "typeVersion": 2, "position": [840, 300]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT * FROM agent.influencer_personas WHERE id = ANY($1::int[]);", "options": {"parameters": {"values": ["={{$json.influencerIds.split(',')}}"]}}}, "id": "db_get_influencers", "name": "DB: Get Influencers", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [1040, 300], "credentials": {"postgres": {"id": "YOUR_POSTGRES_CREDENTIAL_ID"}}}, {"parameters": {"batchSize": 1}, "id": "loop_influencers", "name": "Loop Over Influencers", "type": "n8n-nodes-base.splitInBatches", "typeVersion": 2, "position": [1240, 300]}, {"parameters": {"workflowId": "={{ $env.DISPATCHER_WORKFLOW_ID }}", "data": "={{ { body: { \n    prompt: `Você é um estrategista de marketing de influência. Crie uma mini-campanha para ${$json.name} para promover '${$('db_get_product').item.json.name}'.\\n\\n**PRODUTO:**\\n- Descrição: ${$('db_get_product').item.json.description}\\n\\n**INFLUENCIADOR:**\\n- Persona: ${$json.archetype}\\n- Plataforma: ${$json.platform_primary}\\n\\nCrie 3 posts (teaser, lançamento, lembrete) em JSON Array:\\n[\\n  { \\\"post_type\\\": \\\"teaser\\\", \\\"content\\\": \\\"...\\\", \\\"schedule_days_offset\\\": -3 },\\n  { \\\"post_type\\\": \\\"launch_day\\\", \\\"content\\\": \\\"...\\\", \\\"schedule_days_offset\\\": 0 },\\n  { \\\"post_type\\\": \\\"post_launch_reminder\\\", \\\"content\\\": \\\"...\\\", \\\"schedule_days_offset\\\": 5 }\\n]\\n`,\n    task_type: 'complex_analysis',\n    calling_workflow_id: $workflow.id,\n    calling_node_id: 'call_dispatcher_for_campaign'\n} } }}", "options": {}}, "id": "call_dispatcher_for_campaign", "name": "Call Dispatcher for Campaign", "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 2, "position": [1440, 300]}, {"parameters": {"batchSize": 1}, "id": "loop_posts", "name": "Loop Over Posts", "type": "n8n-nodes-base.splitInBatches", "typeVersion": 2, "position": [1640, 300]}, {"parameters": {"functionCode": "const launchDate = new Date($('manual_trigger').item.json.launchDate);\nconst offset = $json.schedule_days_offset;\n// Cria uma NOVA data para evitar mutação\nconst scheduleDate = new Date(launchDate.getTime());\nscheduleDate.setDate(scheduleDate.getDate() + offset);\n\n$json.scheduled_at_iso = scheduleDate.toISOString();\nreturn $items;", "options": {}}, "id": "code_calculate_date", "name": "CODE: Calculate Schedule Date", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1840, 300]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "INSERT INTO agent.launch_posts (launch_campaign_id, influencer_id, post_type, generated_content, scheduled_at) VALUES ($1, $2, $3, $4, $5);", "options": {"parameters": {"values": ["={{$('db_create_campaign').item.json.id}}", "={{$('loop_influencers').item.json.id}}", "={{$json.post_type}}", "={{$json.content}}", "={{$json.scheduled_at_iso}}"]}}}, "id": "db_log_post", "name": "DB: Log Scheduled Post", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [2040, 300], "credentials": {"postgres": {"id": "YOUR_POSTGRES_CREDENTIAL_ID"}}}, {"parameters": {}, "id": "merge_end_loop", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.merge", "typeVersion": 2, "position": [2240, 300]}, {"parameters": {"method": "POST", "url": "={{$credentials.slackWebhook.url}}", "body": "={{ ({ 'text': `🚀 Nova Campanha de Lançamento Criada: ${$('manual_trigger').item.json.campaignName}\\nProduto: ${$('db_get_product').item.json.name}\\nStatus: Pronta para execução.` }) }}"}, "id": "slack_notify_manager", "name": "SLACK: Notify Manager", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [2440, 300], "credentials": {"httpHeaderAuth": {"id": "YOUR_SLACK_WEBHOOK_CREDENTIAL"}}}], "connections": {"manual_trigger": {"main": [[{"node": "db_get_product"}, {"node": "db_create_campaign"}]]}, "db_get_product": {"main": [[{"node": "merge_start"}]]}, "db_create_campaign": {"main": [[{"node": "merge_start", "type": "main", "index": 1}]]}, "merge_start": {"main": [[{"node": "db_get_influencers"}]]}, "db_get_influencers": {"main": [[{"node": "loop_influencers"}]]}, "loop_influencers": {"main": [[{"node": "call_dispatcher_for_campaign"}]]}, "call_dispatcher_for_campaign": {"main": [[{"node": "loop_posts"}]]}, "loop_posts": {"main": [[{"node": "code_calculate_date"}]]}, "code_calculate_date": {"main": [[{"node": "db_log_post"}]]}, "db_log_post": {"main": [[{"node": "merge_end_loop"}]]}, "merge_end_loop": {"main": [[{"node": "slack_notify_manager"}]]}}}