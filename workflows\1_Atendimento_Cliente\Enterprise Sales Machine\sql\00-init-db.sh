#!/bin/bash
set -e

# Este script executa todos os arquivos .sql, .sql.gz, ou .sh no diretório.
# PostgreSQL já faz isso por padrão, mas este arquivo serve como um
# ponto de entrada explícito e customizável se necessário.

echo ">>> Executando scripts de inicialização do banco de dados..."

psql -v ON_ERROR_STOP=1 --username "$POSTGRES_USER" --dbname "$POSTGRES_DB" <<-EOSQL
    CREATE SCHEMA IF NOT EXISTS agent;
    -- Cria usuários/roles específicos se necessário
    -- GRANT USAGE ON SCHEMA agent TO some_user;
EOSQL

echo ">>> Scripts de inicialização finalizados."