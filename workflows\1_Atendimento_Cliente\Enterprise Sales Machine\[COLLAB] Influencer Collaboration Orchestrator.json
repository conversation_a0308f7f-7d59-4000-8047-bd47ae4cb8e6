{"meta": {"instanceId": "MULTI_IDENTITY_COLLABORATION_ORCHESTRATOR"}, "name": "[COLLAB] Influencer Collaboration Orchestrator", "nodes": [{"parameters": {"rule": {"interval": [{"field": "hours", "hoursInterval": 6}]}}, "id": "schedule_collaboration_check", "name": "TRIGGER: Check Collaborations", "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1.1, "position": [140, 300]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "-- Buscar identidades disponíveis para colaboração\nSELECT \n  ai.id,\n  ai.identity_name,\n  ai.display_name,\n  ai.category,\n  ai.bio,\n  ai.expertise_areas,\n  ai.content_themes,\n  ai.collaboration_openness,\n  ai.platform_focus,\n  ai.target_audience,\n  ai.content_pillars,\n  -- Calcular score de atividade recente\n  COALESCE(recent_activity.activity_score, 0) as activity_score,\n  -- Verificar última colaboração\n  COALESCE(last_collab.last_collaboration, '1970-01-01'::timestamptz) as last_collaboration_date\nFROM agent.agent_identities ai\nLEFT JOIN (\n  SELECT \n    identity_id,\n    COUNT(*) * 10 + AVG(viral_score) as activity_score\n  FROM agent.identity_content_history \n  WHERE published_at > NOW() - INTERVAL '7 days'\n  AND status = 'published'\n  GROUP BY identity_id\n) recent_activity ON ai.id = recent_activity.identity_id\nLEFT JOIN (\n  SELECT \n    collaborator_id as identity_id,\n    MAX(created_at) as last_collaboration\n  FROM agent.influencer_collaborations ic\n  WHERE ic.status = 'completed'\n  GROUP BY collaborator_id\n) last_collab ON ai.id = last_collab.identity_id\nWHERE ai.status = 'active'\nAND ai.collaboration_openness > 30\nORDER BY activity_score DESC, last_collaboration_date ASC;"}, "id": "get_collaboration_candidates", "name": "🤝 Get Collaboration Candidates", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [360, 300], "credentials": {"postgres": {"id": "postgres-main", "name": "PostgreSQL Main"}}}, {"parameters": {"batchSize": 1}, "id": "loop_primary_candidates", "name": "Loop Primary Candidates", "type": "n8n-nodes-base.splitInBatches", "typeVersion": 2, "position": [580, 300]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "-- Encontrar potenciais colaboradores para esta identidade\nSELECT \n  ai2.id,\n  ai2.identity_name,\n  ai2.display_name,\n  ai2.category,\n  ai2.expertise_areas,\n  ai2.content_themes,\n  ai2.platform_focus,\n  ai2.collaboration_openness,\n  -- Calcular score de sinergia\n  CASE \n    -- Sinergias pré-definidas entre categorias\n    WHEN $2 = 'tech_analyst' AND ai2.category = 'scale_strategist' THEN 0.85\n    WHEN $2 = 'creator_catalyst' AND ai2.category = 'content_creator' THEN 0.90\n    WHEN $2 = 'sales_expert' AND ai2.category = 'scale_strategist' THEN 0.80\n    WHEN $2 = 'tech_analyst' AND ai2.category = 'creator_catalyst' THEN 0.75\n    -- Sinergia baseada em plataformas em comum\n    WHEN ai2.platform_focus && $3::text[] THEN 0.70\n    -- Sinergia baseada em expertise complementar\n    WHEN NOT (ai2.expertise_areas && $4::text[]) AND ai2.category != $2 THEN 0.65\n    ELSE 0.30\n  END as synergy_score,\n  -- Verificar se já colaboraram recentemente\n  CASE \n    WHEN EXISTS(\n      SELECT 1 FROM agent.influencer_collaborations ic \n      WHERE (ic.influencer_id = $1 AND ic.collaborator_id = ai2.id)\n      OR (ic.influencer_id = ai2.id AND ic.collaborator_id = $1)\n      AND ic.created_at > NOW() - INTERVAL '30 days'\n    ) THEN true\n    ELSE false\n  END as recently_collaborated\nFROM agent.agent_identities ai2\nWHERE ai2.id != $1\nAND ai2.status = 'active'\nAND ai2.collaboration_openness > 40\nHAVING synergy_score > 0.60 AND recently_collaborated = false\nORDER BY synergy_score DESC\nLIMIT 3;", "options": {"parameters": {"values": ["={{ $json.id }}", "={{ $json.category }}", "={{ $json.platform_focus ? JSON.stringify($json.platform_focus) : '{}' }}", "={{ $json.expertise_areas ? JSON.stringify($json.expertise_areas) : '{}' }}"]}}}, "id": "find_collaboration_partners", "name": "🔍 Find Collaboration Partners", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [800, 300], "credentials": {"postgres": {"id": "postgres-main", "name": "PostgreSQL Main"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "has_collaboration_opportunities", "leftValue": "={{ $json.length }}", "rightValue": 0, "operator": {"type": "number", "operation": "gt"}}]}}, "id": "if_has_collaboration_opportunities", "name": "IF: Has Collaboration Opportunities?", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [1020, 300]}, {"parameters": {"batchSize": 1}, "id": "loop_collaboration_partners", "name": "Loop Collaboration Partners", "type": "n8n-nodes-base.splitInBatches", "typeVersion": 2, "position": [1240, 200]}, {"parameters": {"workflowId": "={{ $env.DISPATCHER_WORKFLOW_ID }}", "data": "={{ {\n  body: {\n    prompt: `PLANEJAMENTO DE COLABORAÇÃO ENTRE IDENTIDADES\\n\\n**IDENTIDADE PRINCIPAL:**\\n• Nome: ${$('loop_primary_candidates').item.json.display_name}\\n• Categoria: ${$('loop_primary_candidates').item.json.category}\\n• Bio: ${$('loop_primary_candidates').item.json.bio}\\n• Especialidades: ${JSON.stringify($('loop_primary_candidates').item.json.expertise_areas)}\\n• Temas: ${JSON.stringify($('loop_primary_candidates').item.json.content_themes)}\\n• Plataformas: ${JSON.stringify($('loop_primary_candidates').item.json.platform_focus)}\\n\\n**IDENTIDADE COLABORADORA:**\\n• Nome: ${$json.display_name}\\n• Categoria: ${$json.category}\\n• Especialidades: ${JSON.stringify($json.expertise_areas)}\\n• Temas: ${JSON.stringify($json.content_themes)}\\n• Plataformas: ${JSON.stringify($json.platform_focus)}\\n• Score de Sinergia: ${Math.round($json.synergy_score * 100)}%\\n\\n**OBJETIVO:**\\nCrie um plano de colaboração que:\\n1. Mantenha a identidade única de cada agente\\n2. Explore sinergias naturais entre suas especialidades\\n3. Ofereça valor complementar às audiências de ambos\\n4. Preserve o contexto e voz específica de cada um\\n\\n**TIPOS DE COLABORAÇÃO POSSÍVEIS:**\\n- Cross-Interview: Entrevista mútua sobre especialidades\\n- Joint Analysis: Análise conjunta de tópico complexo\\n- Debate Construtivo: Diferentes perspectivas sobre mesmo tema\\n- Case Study Duo: Análise colaborativa de caso real\\n- Mentorship Content: Um orienta o outro em área específica\\n\\n**INSTRUÇÕES CRÍTICAS:**\\n1. Cada identidade deve manter sua voz característica\\n2. Explorar pontos de convergência e divergência\\n3. Criar valor único que nenhum faria sozinho\\n4. Sugerir formato adequado às plataformas de ambos\\n5. Considerar timing e frequência ideal\\n\\nResposta em JSON:\\n{\\n  \\\"collaboration_type\\\": \\\"tipo de colaboração\\\",\\n  \\\"title\\\": \\\"título da colaboração\\\",\\n  \\\"concept\\\": \\\"conceito detalhado\\\",\\n  \\\"format\\\": \\\"formato de conteúdo\\\",\\n  \\\"primary_platform\\\": \\\"plataforma principal\\\",\\n  \\\"cross_promotion\\\": \\\"estratégia de promoção cruzada\\\",\\n  \\\"identity_roles\\\": {\\n    \\\"${$('loop_primary_candidates').item.json.identity_name}\\\": \\\"papel específico\\\",\\n    \\\"${$json.identity_name}\\\": \\\"papel específico\\\"\\n  },\\n  \\\"content_outline\\\": [\\\"tópico 1\\\", \\\"tópico 2\\\", \\\"tópico 3\\\"],\\n  \\\"expected_outcomes\\\": [\\\"resultado esperado\\\"],\\n  \\\"synergy_score\\\": ${$json.synergy_score},\\n  \\\"estimated_engagement\\\": 0.00,\\n  \\\"implementation_timeline\\\": \\\"cronograma sugerido\\\"\\n}`,\n    task_type: 'collaboration_planning',\n    calling_workflow_id: $workflow.id,\n    calling_node_id: 'plan_collaboration',\n    collaboration_context: {\n      primary_identity: {\n        id: $('loop_primary_candidates').item.json.id,\n        name: $('loop_primary_candidates').item.json.identity_name,\n        category: $('loop_primary_candidates').item.json.category\n      },\n      partner_identity: {\n        id: $json.id,\n        name: $json.identity_name,\n        category: $json.category\n      },\n      synergy_score: $json.synergy_score\n    }\n  }\n} }}", "options": {}}, "id": "plan_collaboration", "name": "🤖 AI: Plan Collaboration", "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 2, "position": [1460, 200]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "synergy_high_enough", "leftValue": "={{ $json.synergy_score }}", "rightValue": 0.7, "operator": {"type": "number", "operation": "gte"}}, {"id": "engagement_potential_good", "leftValue": "={{ $json.estimated_engagement }}", "rightValue": 0.6, "operator": {"type": "number", "operation": "gte"}}], "combineOperation": "and"}}, "id": "if_collaboration_viable", "name": "IF: Collaboration Viable?", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [1680, 200]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "-- <PERSON><PERSON><PERSON> colaboração no banco\nINSERT INTO agent.influencer_collaborations (\n  influencer_id,\n  collaborator_id,\n  collaboration_type,\n  content_plan,\n  status,\n  scheduled_date,\n  expected_engagement,\n  synergy_score\n)\nVALUES (\n  $1,\n  $2,\n  $3,\n  $4::jsonb,\n  'planned',\n  $5::timestamptz,\n  $6,\n  $7\n)\nRETURNING *;", "options": {"parameters": {"values": ["={{ $('loop_primary_candidates').item.json.id }}", "={{ $('loop_collaboration_partners').item.json.id }}", "={{ $('plan_collaboration').item.json.collaboration_type }}", "={{ JSON.stringify($('plan_collaboration').item.json) }}", "={{ $now.plus({ days: Math.floor(Math.random() * 7) + 1 }) }}", "={{ $('plan_collaboration').item.json.estimated_engagement }}", "={{ $('plan_collaboration').item.json.synergy_score }}"]}}}, "id": "create_collaboration_record", "name": "💾 Create Collaboration Record", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [1900, 200], "credentials": {"postgres": {"id": "postgres-main", "name": "PostgreSQL Main"}}}, {"parameters": {"workflowId": "={{ $env.IDENTITY_MANAGER_WORKFLOW_ID }}", "data": "={{ {\n  body: {\n    action: 'generate_collaboration_content',\n    collaboration_id: $('create_collaboration_record').item.json.id,\n    primary_identity_name: $('loop_primary_candidates').item.json.identity_name,\n    partner_identity_name: $('loop_collaboration_partners').item.json.identity_name,\n    collaboration_plan: $('plan_collaboration').item.json,\n    auto_schedule: true\n  }\n} }}", "options": {}}, "id": "trigger_collaboration_content", "name": "🎬 Trigger Collaboration Content", "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 2, "position": [2120, 200], "continueOnFail": true}, {"parameters": {"method": "POST", "url": "={{ $vars.SLACK_WEBHOOK_URL }}", "sendBody": true, "specifyBody": "json", "jsonBody": "{\n  \"text\": \"🤝 Nova Colaboração Criada\",\n  \"blocks\": [\n    {\n      \"type\": \"header\",\n      \"text\": {\n        \"type\": \"plain_text\",\n        \"text\": \"🤝 NOVA COLABORAÇÃO PLANEJADA\"\n      }\n    },\n    {\n      \"type\": \"section\",\n      \"fields\": [\n        {\n          \"type\": \"mrkdwn\",\n          \"text\": \"*👤 Identidade Principal:*\\n{{ $('loop_primary_candidates').item.json.display_name }}\\n({{ $('loop_primary_candidates').item.json.category }})\"\n        },\n        {\n          \"type\": \"mrkdwn\",\n          \"text\": \"*🤝 Parceiro:*\\n{{ $('loop_collaboration_partners').item.json.display_name }}\\n({{ $('loop_collaboration_partners').item.json.category }})\"\n        },\n        {\n          \"type\": \"mrkdwn\",\n          \"text\": \"*🎯 Tipo:*\\n{{ $('plan_collaboration').item.json.collaboration_type }}\"\n        },\n        {\n          \"type\": \"mrkdwn\",\n          \"text\": \"*⚡ Score de Sinergia:*\\n{{ Math.round($('plan_collaboration').item.json.synergy_score * 100) }}%\"\n        }\n      ]\n    },\n    {\n      \"type\": \"section\",\n      \"text\": {\n        \"type\": \"mrkdwn\",\n        \"text\": \"*📋 Conceito:*\\n{{ $('plan_collaboration').item.json.concept.substring(0, 200) }}...\"\n      }\n    },\n    {\n      \"type\": \"section\",\n      \"text\": {\n        \"type\": \"mrkdwn\",\n        \"text\": \"*🎬 Formato:* {{ $('plan_collaboration').item.json.format }}\\n*📱 Plataforma:* {{ $('plan_collaboration').item.json.primary_platform }}\\n*📅 Timeline:* {{ $('plan_collaboration').item.json.implementation_timeline }}\"\n      }\n    },\n    {\n      \"type\": \"section\",\n      \"text\": {\n        \"type\": \"mrkdwn\",\n        \"text\": \"*🎭 Papéis das Identidades:*\\n{{ Object.entries($('plan_collaboration').item.json.identity_roles).map(([identity, role]) => `• ${identity}: ${role}`).join('\\n') }}\"\n      }\n    },\n    {\n      \"type\": \"context\",\n      \"elements\": [\n        {\n          \"type\": \"mrkdwn\",\n          \"text\": \"Colaboração ID: {{ $('create_collaboration_record').item.json.id }} | Status: Planejada | Conteúdo será gerado automaticamente\"\n        }\n      ]\n    }\n  ],\n  \"channel\": \"#collaborations\"\n}", "options": {}}, "id": "notify_collaboration_created", "name": "📱 Notify Collaboration Created", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [2340, 200], "continueOnFail": true}, {"parameters": {"method": "POST", "url": "={{ $vars.SLACK_WEBHOOK_URL }}", "sendBody": true, "specifyBody": "json", "jsonBody": "{\n  \"text\": \"❌ Colaboração rejeitada por baixo potencial\",\n  \"blocks\": [\n    {\n      \"type\": \"section\",\n      \"text\": {\n        \"type\": \"mrkdwn\",\n        \"text\": \"*❌ Collaboration Rejected*\\n\\n*Identidades:*\\n• {{ $('loop_primary_candidates').item.json.display_name }} ({{ $('loop_primary_candidates').item.json.category }})\\n• {{ $('loop_collaboration_partners').item.json.display_name }} ({{ $('loop_collaboration_partners').item.json.category }})\\n\\n*Motivos:*\\n• Score de Sinergia: {{ Math.round($('plan_collaboration').item.json.synergy_score * 100) }}% (min: 70%)\\n• Potencial de Engajamento: {{ Math.round($('plan_collaboration').item.json.estimated_engagement * 100) }}% (min: 60%)\\n\\n_Colaboração não foi criada devido aos critérios mínimos._\"\n      }\n    }\n  ],\n  \"channel\": \"#collaborations\"\n}", "options": {}}, "id": "notify_collaboration_rejected", "name": "⚠️ Notify Collaboration Rejected", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [1900, 400], "continueOnFail": true}, {"parameters": {"mode": "combine", "combinationMode": "mergeByPosition", "options": {}}, "id": "merge_collaboration_results", "name": "Merge Collaboration Results", "type": "n8n-nodes-base.merge", "typeVersion": 2.1, "position": [2560, 300]}, {"parameters": {"mode": "combine", "combinationMode": "mergeByPosition", "options": {}}, "id": "merge_partner_results", "name": "Merge Partner Results", "type": "n8n-nodes-base.merge", "typeVersion": 2.1, "position": [2780, 300]}, {"parameters": {"mode": "combine", "combinationMode": "mergeByPosition", "options": {}}, "id": "merge_candidate_results", "name": "Merge Candidate Results", "type": "n8n-nodes-base.merge", "typeVersion": 2.1, "position": [3000, 300]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "-- Verificar colaborações pendentes de execução\nSELECT \n  ic.id,\n  ic.collaboration_type,\n  ic.scheduled_date,\n  ic.expected_engagement,\n  ic.synergy_score,\n  ai1.identity_name as primary_identity,\n  ai1.display_name as primary_display,\n  ai2.identity_name as partner_identity,\n  ai2.display_name as partner_display,\n  ic.content_plan\nFROM agent.influencer_collaborations ic\nJOIN agent.agent_identities ai1 ON ic.influencer_id = ai1.id\nJOIN agent.agent_identities ai2 ON ic.collaborator_id = ai2.id\nWHERE ic.status = 'planned'\nAND ic.scheduled_date <= NOW() + INTERVAL '24 hours'\nORDER BY ic.scheduled_date ASC;"}, "id": "check_pending_collaborations", "name": "📅 Check Pending Collaborations", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [3220, 300], "credentials": {"postgres": {"id": "postgres-main", "name": "PostgreSQL Main"}}}, {"parameters": {"batchSize": 1}, "id": "loop_pending_collaborations", "name": "Loop Pending Collaborations", "type": "n8n-nodes-base.splitInBatches", "typeVersion": 2, "position": [3440, 300]}, {"parameters": {"workflowId": "={{ $env.IDENTITY_MANAGER_WORKFLOW_ID }}", "data": "={{ {\n  body: {\n    action: 'execute_collaboration',\n    collaboration_id: $json.id,\n    primary_identity_name: $json.primary_identity,\n    partner_identity_name: $json.partner_identity,\n    collaboration_plan: $json.content_plan,\n    auto_post: true\n  }\n} }}", "options": {}}, "id": "execute_pending_collaboration", "name": "🚀 Execute Pending Collaboration", "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 2, "position": [3660, 300], "continueOnFail": true}, {"parameters": {"operation": "execute<PERSON>uery", "query": "-- Atualizar status da colaboração para executada\nUPDATE agent.influencer_collaborations \nSET status = 'executed', executed_at = NOW()\nWHERE id = $1\nRETURNING *;", "options": {"parameters": {"values": ["={{ $('loop_pending_collaborations').item.json.id }}"]}}}, "id": "update_collaboration_status", "name": "✅ Update Collaboration Status", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [3880, 300], "credentials": {"postgres": {"id": "postgres-main", "name": "PostgreSQL Main"}}}, {"parameters": {"mode": "combine", "combinationMode": "mergeByPosition", "options": {}}, "id": "merge_execution_results", "name": "Merge Execution Results", "type": "n8n-nodes-base.merge", "typeVersion": 2.1, "position": [4100, 300]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "-- Relatório de execução de colaborações\nSELECT \n  COUNT(CASE WHEN status = 'planned' AND created_at > NOW() - INTERVAL '6 hours' THEN 1 END) as new_collaborations,\n  COUNT(CASE WHEN status = 'executed' AND executed_at > NOW() - INTERVAL '6 hours' THEN 1 END) as executed_collaborations,\n  COUNT(CASE WHEN status = 'planned' AND scheduled_date <= NOW() + INTERVAL '24 hours' THEN 1 END) as pending_execution,\n  AVG(CASE WHEN created_at > NOW() - INTERVAL '7 days' THEN synergy_score END) as avg_synergy_score,\n  AVG(CASE WHEN created_at > NOW() - INTERVAL '7 days' THEN expected_engagement END) as avg_expected_engagement\nFROM agent.influencer_collaborations;"}, "id": "generate_collaboration_report", "name": "📊 Generate Collaboration Report", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [4320, 300], "credentials": {"postgres": {"id": "postgres-main", "name": "PostgreSQL Main"}}}, {"parameters": {"method": "POST", "url": "={{ $vars.SLACK_WEBHOOK_URL }}", "sendBody": true, "specifyBody": "json", "jsonBody": "{\n  \"text\": \"🤝 Collaboration Orchestrator - Relatório de Execução\",\n  \"blocks\": [\n    {\n      \"type\": \"header\",\n      \"text\": {\n        \"type\": \"plain_text\",\n        \"text\": \"🤝 COLLABORATION ORCHESTRATOR REPORT\"\n      }\n    },\n    {\n      \"type\": \"section\",\n      \"fields\": [\n        {\n          \"type\": \"mrkdwn\",\n          \"text\": \"*🆕 Novas Colaborações:*\\n{{ $('generate_collaboration_report').item.json.new_collaborations || 0 }}\"\n        },\n        {\n          \"type\": \"mrkdwn\",\n          \"text\": \"*✅ Colaborações Executadas:*\\n{{ $('generate_collaboration_report').item.json.executed_collaborations || 0 }}\"\n        },\n        {\n          \"type\": \"mrkdwn\",\n          \"text\": \"*⏳ Pendentes (24h):*\\n{{ $('generate_collaboration_report').item.json.pending_execution || 0 }}\"\n        },\n        {\n          \"type\": \"mrkdwn\",\n          \"text\": \"*⚡ Score Médio de Sinergia:*\\n{{ Math.round(($('generate_collaboration_report').item.json.avg_synergy_score || 0) * 100) }}%\"\n        }\n      ]\n    },\n    {\n      \"type\": \"section\",\n      \"text\": {\n        \"type\": \"mrkdwn\",\n        \"text\": \"*🎯 Candidatos Processados:*\\n{{ $('get_collaboration_candidates').all().length }} identidades analisadas\\n\\n*🔍 Parcerias Avaliadas:*\\n{{ $('find_collaboration_partners').all().length }} oportunidades identificadas\\n\\n*💫 Engajamento Esperado:*\\n{{ Math.round(($('generate_collaboration_report').item.json.avg_expected_engagement || 0) * 100) }}% médio\"\n      }\n    },\n    {\n      \"type\": \"section\",\n      \"text\": {\n        \"type\": \"mrkdwn\",\n        \"text\": \"*🔒 Context Separation:*\\n✅ Cada identidade mantém sua voz única\\n✅ Sinergias naturais preservadas\\n✅ Colaborações respeitam expertise individual\"\n      }\n    },\n    {\n      \"type\": \"context\",\n      \"elements\": [\n        {\n          \"type\": \"mrkdwn\",\n          \"text\": \"Orchestrator executando a cada 6 horas | Próxima verificação: {{ $now.plus({ hours: 6 }).toFormat('dd/MM HH:mm') }}\"\n        }\n      ]\n    }\n  ],\n  \"channel\": \"#collaborations\"\n}", "options": {}}, "id": "notify_orchestrator_summary", "name": "📱 Notify Orchestrator Summary", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [4540, 300], "continueOnFail": true}], "connections": {"schedule_collaboration_check": {"main": [[{"node": "get_collaboration_candidates", "type": "main", "index": 0}]]}, "get_collaboration_candidates": {"main": [[{"node": "loop_primary_candidates", "type": "main", "index": 0}]]}, "loop_primary_candidates": {"main": [[{"node": "find_collaboration_partners", "type": "main", "index": 0}]]}, "find_collaboration_partners": {"main": [[{"node": "if_has_collaboration_opportunities", "type": "main", "index": 0}]]}, "if_has_collaboration_opportunities": {"main": [[{"node": "loop_collaboration_partners", "type": "main", "index": 0}], [{"node": "merge_candidate_results", "type": "main", "index": 0}]]}, "loop_collaboration_partners": {"main": [[{"node": "plan_collaboration", "type": "main", "index": 0}]]}, "plan_collaboration": {"main": [[{"node": "if_collaboration_viable", "type": "main", "index": 0}]]}, "if_collaboration_viable": {"main": [[{"node": "create_collaboration_record", "type": "main", "index": 0}], [{"node": "notify_collaboration_rejected", "type": "main", "index": 0}]]}, "create_collaboration_record": {"main": [[{"node": "trigger_collaboration_content", "type": "main", "index": 0}]]}, "trigger_collaboration_content": {"main": [[{"node": "notify_collaboration_created", "type": "main", "index": 0}]]}, "notify_collaboration_created": {"main": [[{"node": "merge_collaboration_results", "type": "main", "index": 0}]]}, "notify_collaboration_rejected": {"main": [[{"node": "merge_collaboration_results", "type": "main", "index": 1}]]}, "merge_collaboration_results": {"main": [[{"node": "merge_partner_results", "type": "main", "index": 0}]]}, "merge_partner_results": {"main": [[{"node": "merge_candidate_results", "type": "main", "index": 1}]]}, "merge_candidate_results": {"main": [[{"node": "check_pending_collaborations", "type": "main", "index": 0}]]}, "check_pending_collaborations": {"main": [[{"node": "loop_pending_collaborations", "type": "main", "index": 0}]]}, "loop_pending_collaborations": {"main": [[{"node": "execute_pending_collaboration", "type": "main", "index": 0}]]}, "execute_pending_collaboration": {"main": [[{"node": "update_collaboration_status", "type": "main", "index": 0}]]}, "update_collaboration_status": {"main": [[{"node": "merge_execution_results", "type": "main", "index": 0}]]}, "merge_execution_results": {"main": [[{"node": "generate_collaboration_report", "type": "main", "index": 0}]]}, "generate_collaboration_report": {"main": [[{"node": "notify_orchestrator_summary", "type": "main", "index": 0}]]}}, "settings": {"executionOrder": "v1"}, "staticData": null, "tags": [{"createdAt": "2024-01-15T19:00:00.000Z", "updatedAt": "2024-01-15T19:00:00.000Z", "id": "multi-identity-collaboration", "name": "multi-identity-collaboration"}], "triggerCount": 1, "updatedAt": "2024-01-15T19:00:00.000Z", "versionId": "1"}