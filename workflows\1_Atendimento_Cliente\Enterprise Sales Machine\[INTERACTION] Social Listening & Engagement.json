{"name": "[INTERACTION] Social Listening & Engagement v1.1 - Cost-Aware", "nodes": [{"parameters": {"rule": "cron", "cronTime": "*/15 * * * *"}, "id": "trigger_interval", "name": "TRIGGER: Every 15 Mins", "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1.1, "position": [200, 300]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT * FROM agent.influencer_personas WHERE status = 'active';"}, "id": "get_influencers", "name": "DB: Get Active Influencers", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [420, 300], "credentials": {"postgres": {"id": "YOUR_POSTGRES_CREDENTIAL_ID"}}}, {"parameters": {"batchSize": 1}, "id": "loop_influencers", "name": "Loop Over Influencers", "type": "n8n-nodes-base.splitInBatches", "typeVersion": 2, "position": [620, 300]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT * FROM agent.agent_accounts WHERE persona_id = $1 AND status = 'active';", "options": {"parameters": {"values": ["={{$json.id}}"]}}}, "id": "db_get_accounts", "name": "DB: <PERSON> Accounts", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [820, 300], "credentials": {"postgres": {"id": "YOUR_POSTGRES_CREDENTIAL_ID"}}}, {"parameters": {"batchSize": 1}, "id": "loop_accounts", "name": "Loop Over Accounts", "type": "n8n-nodes-base.splitInBatches", "typeVersion": 2, "position": [1020, 300]}, {"parameters": {"url": "https://api.twitter.com/2/tweets/search/recent?query=@{{$json.platform_account_id}} OR \"automação de marketing\" OR \"n8n\" -is:retweet", "options": {}}, "id": "http_search_mentions", "name": "HTTP: Search Mentions (X/Twitter)", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [1240, 300], "continueOnFail": true, "notes": "Assume que a plataforma é X/Twitter. Para outras, um SWITCH seria necessário.", "credentials": {"httpHeaderAuth": {"id": "={{$json.n8n_credential_id}}"}}}, {"parameters": {"batchSize": 1}, "id": "loop_mentions", "name": "Loop Over Mentions", "type": "n8n-nodes-base.splitInBatches", "typeVersion": 2, "position": [1440, 300]}, {"parameters": {"workflowId": "={{ $env.DISPATCHER_WORKFLOW_ID }}", "data": "={{ { body: { \n    prompt: `${$('loop_influencers').item.json.master_prompt}\\n\\nVocê viu a seguinte postagem no ${$('loop_accounts').item.json.platform}: '${$json.text}'.\\n\\nAnalise-a. É uma pergunta? Uma reclamação? Uma oportunidade de venda? Gere uma resposta autêntica, útil e alinhada à sua personalidade. Se for uma oportunidade clara de negócio, termine sua resposta com um convite para conversar no privado via WhatsApp. Use o link: https://wa.me/YOUR_NUMBER?text=Oi%20${$('loop_influencers').item.json.name}\\n\\nResponda em JSON com a seguinte estrutura: {\\\"reply_text\\\": \\\"Sua resposta aqui...\\\", \\\"is_lead\\\": true_ou_false, \\\"lead_platform_id\\\": \\\"${$json.author_id}\\\"}`,\n    task_type: 'complex_analysis',\n    calling_workflow_id: $workflow.id,\n    calling_node_id: 'call_dispatcher_for_interaction'\n} } }}", "options": {}}, "id": "call_dispatcher_for_interaction", "name": "Call Dispatcher for Interaction", "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 2, "position": [1640, 300], "continueOnFail": true}, {"parameters": {"method": "POST", "url": "https://api.twitter.com/2/tweets", "body": "={{ ({ text: $json.reply_text, reply: { in_reply_to_tweet_id: $json.id } }) }}", "options": {}}, "id": "http_post_reply", "name": "HTTP: Post Reply", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [1860, 300], "continueOnFail": true, "credentials": {"httpOAuth2": {"id": "={{$('loop_accounts').item.json.n8n_credential_id}}"}}}, {"parameters": {"conditions": {"boolean": [{"value1": "={{$json.is_lead}}"}]}}, "id": "if_is_lead", "name": "IF: Is Lead?", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [2060, 300]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "INSERT INTO agent.contatos (twitter_id, nome, tags)\nVALUES ($1, $2, jsonb_build_array('lead_from_' || $3))\nON CONFLICT (twitter_id) \nDO UPDATE SET\n  nome = EXCLUDED.nome,\n  tags = contatos.tags || EXCLUDED.tags,\n  updated_at = NOW()\nWHERE NOT (contatos.tags @> EXCLUDED.tags)\nRETURNING *;", "options": {"parameters": {"values": ["={{$json.lead_platform_id}}", "={{$json.lead_platform_id}}", "={{$('loop_accounts').item.json.platform}}"]}}}, "id": "db_log_lead", "name": "DB: Create/Update Lead", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [2260, 300], "continueOnFail": true, "credentials": {"postgres": {"id": "YOUR_POSTGRES_CREDENTIAL_ID"}}}], "connections": {"trigger_interval": {"main": [[{"node": "get_influencers", "type": "main", "index": 0}]]}, "get_influencers": {"main": [[{"node": "loop_influencers", "type": "main", "index": 0}]]}, "loop_influencers": {"main": [[{"node": "db_get_accounts", "type": "main", "index": 0}]]}, "db_get_accounts": {"main": [[{"node": "loop_accounts", "type": "main", "index": 0}]]}, "loop_accounts": {"main": [[{"node": "http_search_mentions", "type": "main", "index": 0}]]}, "http_search_mentions": {"main": [[{"node": "loop_mentions", "type": "main", "index": 0}]]}, "loop_mentions": {"main": [[{"node": "call_dispatcher_for_interaction", "type": "main", "index": 0}]]}, "call_dispatcher_for_interaction": {"main": [[{"node": "http_post_reply", "type": "main", "index": 0}]]}, "http_post_reply": {"main": [[{"node": "if_is_lead", "type": "main", "index": 0}]]}, "if_is_lead": {"main": [[{"node": "db_log_lead", "type": "main", "index": 0}]]}}}