{"name": "[ESCALATION] Main Intelligent Router v2.0", "nodes": [{"parameters": {"httpMethod": "POST", "path": "escalation-router", "options": {"rawBody": true}}, "id": "webhook-escalation-router", "name": "🎯 Escalation Router Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300], "webhookId": "escalation-router-main"}, {"parameters": {"jsCode": "// 📥 Process and validate incoming escalation data\nconst inputData = $input.all()[0].json;\n\n// Extract escalation data\nconst escalationData = {\n  source: inputData.source || 'unknown',\n  source_id: inputData.source_id || null,\n  customer_id: inputData.customer_id || null,\n  conversation_id: inputData.conversation_id || null,\n  priority: inputData.priority || 'medium',\n  category: inputData.category || 'general',\n  subject: inputData.subject || 'Escalation Request',\n  description: inputData.description || '',\n  customer_data: inputData.customer_data || {},\n  context_data: inputData.context_data || {},\n  ai_analysis: inputData.ai_analysis || null,\n  routing_rules: inputData.routing_rules || {},\n  metadata: inputData.metadata || {},\n  created_at: new Date().toISOString(),\n  escalation_id: `ESC_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`\n};\n\n// Validate required fields\nconst requiredFields = ['source', 'priority', 'subject'];\nconst missingFields = requiredFields.filter(field => !escalationData[field]);\n\nif (missingFields.length > 0) {\n  throw new Error(`Missing required fields: ${missingFields.join(', ')}`);\n}\n\n// Set priority level for routing\nconst priorityLevels = {\n  'critical': 1,\n  'high': 2,\n  'medium': 3,\n  'low': 4\n};\n\nescalationData.priority_level = priorityLevels[escalationData.priority] || 3;\n\n// Add processing timestamp\nescalationData.processing_started_at = new Date().toISOString();\n\nconsole.log('🎯 Processing escalation:', escalationData.escalation_id);\nconsole.log('📊 Priority:', escalationData.priority, '(Level:', escalationData.priority_level, ')');\nconsole.log('🏷️ Category:', escalationData.category);\nconsole.log('📍 Source:', escalationData.source);\n\nreturn { escalationData };"}, "id": "process-escalation-data", "name": "📥 Process Escalation Data", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [460, 300]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "existing-escalation-check", "leftValue": "={{ $json.escalationData.source_id }}", "rightValue": "", "operator": {"type": "string", "operation": "notEmpty"}}], "combinator": "and"}, "options": {}}, "id": "check-existing-escalation", "name": "🔍 Check Existing Escalation", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [680, 300]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT id, status, priority, assigned_agent_id, created_at, updated_at\nFROM agent.intelligent_escalations\nWHERE source = $1 AND source_id = $2\nAND status NOT IN ('resolved', 'closed')\nORDER BY created_at DESC\nLIMIT 1", "additionalFields": {"mode": "independently"}, "options": {}}, "id": "query-existing-escalation", "name": "🔎 Query Existing Escalation", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [900, 200], "credentials": {"postgres": {"id": "postgres-escalation-db", "name": "PostgreSQL - Escalation DB"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "has-existing-escalation", "leftValue": "={{ $json.length }}", "rightValue": 0, "operator": {"type": "number", "operation": "gt"}}], "combinator": "and"}, "options": {}}, "id": "has-existing-escalation", "name": "❓ Has Existing Escalation", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [1120, 200]}, {"parameters": {"jsCode": "// 🔄 Update existing escalation\nconst existingEscalation = $input.first().json;\nconst escalationData = $('process-escalation-data').first().json.escalationData;\n\n// Merge new data with existing escalation\nconst updatedEscalation = {\n  id: existingEscalation.id,\n  escalation_id: escalationData.escalation_id,\n  priority: escalationData.priority,\n  priority_level: escalationData.priority_level,\n  description: escalationData.description,\n  context_data: JSON.stringify({\n    ...JSON.parse(existingEscalation.context_data || '{}'),\n    ...escalationData.context_data,\n    update_reason: 'escalation_update',\n    previous_priority: existingEscalation.priority,\n    updated_at: new Date().toISOString()\n  }),\n  metadata: JSON.stringify({\n    ...JSON.parse(existingEscalation.metadata || '{}'),\n    ...escalationData.metadata,\n    update_count: (JSON.parse(existingEscalation.metadata || '{}').update_count || 0) + 1\n  }),\n  updated_at: new Date().toISOString(),\n  status: 'updated'\n};\n\nconsole.log('🔄 Updating existing escalation:', existingEscalation.id);\nconsole.log('📈 Priority change:', existingEscalation.priority, '->', updatedEscalation.priority);\n\nreturn { updatedEscalation, isUpdate: true };"}, "id": "update-existing-escalation", "name": "🔄 Update Existing Escalation", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1340, 120]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "UPDATE agent.intelligent_escalations\nSET \n  priority = $2,\n  priority_level = $3,\n  description = $4,\n  context_data = $5::jsonb,\n  metadata = $6::jsonb,\n  updated_at = $7::timestamp,\n  status = $8\nWHERE id = $1\nRETURNING *", "additionalFields": {"mode": "independently"}, "options": {}}, "id": "execute-escalation-update", "name": "💾 Execute Escalation Update", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [1560, 120], "credentials": {"postgres": {"id": "postgres-escalation-db", "name": "PostgreSQL - Escalation DB"}}}, {"parameters": {"jsCode": "// 🆕 Prepare new escalation creation\nconst escalationData = $input.first().json.escalationData;\n\n// Generate unique escalation ID if not provided\nif (!escalationData.escalation_id) {\n  escalationData.escalation_id = `ESC_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n}\n\n// Prepare database insert data\nconst newEscalation = {\n  escalation_id: escalationData.escalation_id,\n  source: escalationData.source,\n  source_id: escalationData.source_id,\n  customer_id: escalationData.customer_id,\n  conversation_id: escalationData.conversation_id,\n  priority: escalationData.priority,\n  priority_level: escalationData.priority_level,\n  category: escalationData.category,\n  subject: escalationData.subject,\n  description: escalationData.description,\n  customer_data: JSON.stringify(escalationData.customer_data),\n  context_data: JSON.stringify(escalationData.context_data),\n  ai_analysis: escalationData.ai_analysis ? JSON.stringify(escalationData.ai_analysis) : null,\n  routing_rules: JSON.stringify(escalationData.routing_rules),\n  metadata: JSON.stringify({\n    ...escalationData.metadata,\n    created_via: 'intelligent_router',\n    processing_started_at: escalationData.processing_started_at\n  }),\n  status: 'pending',\n  created_at: escalationData.created_at,\n  updated_at: escalationData.created_at\n};\n\nconsole.log('🆕 Creating new escalation:', newEscalation.escalation_id);\nconsole.log('📊 Priority:', newEscalation.priority, '(Level:', newEscalation.priority_level, ')');\nconsole.log('🏷️ Category:', newEscalation.category);\n\nreturn { newEscalation, isUpdate: false };"}, "id": "prepare-new-escalation", "name": "🆕 Prepare New Escalation", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1340, 280]}, {"parameters": {"operation": "insert", "schema": "agent", "table": "intelligent_escalations", "columns": "escalation_id, source, source_id, customer_id, conversation_id, priority, priority_level, category, subject, description, customer_data, context_data, ai_analysis, routing_rules, metadata, status, created_at, updated_at", "additionalFields": {"mode": "independently"}, "options": {}}, "id": "create-new-escalation", "name": "💾 Create New Escalation", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [1560, 280], "credentials": {"postgres": {"id": "postgres-escalation-db", "name": "PostgreSQL - Escalation DB"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "needs-ai-analysis", "leftValue": "={{ $json.ai_analysis }}", "rightValue": "", "operator": {"type": "string", "operation": "isEmpty"}}, {"id": "has-sufficient-text", "leftValue": "={{ $json.description.length }}", "rightValue": 50, "operator": {"type": "number", "operation": "gte"}}], "combinator": "and"}, "options": {}}, "id": "needs-ai-analysis", "name": "🤖 Needs AI Analysis?", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [1780, 200]}, {"parameters": {"method": "POST", "url": "http://localhost:5678/webhook/ai-analysis", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}, {"name": "X-Escalation-Source", "value": "intelligent-router"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "escalation_id", "value": "={{ $json.escalation_id }}"}, {"name": "text_content", "value": "={{ $json.subject + ' ' + $json.description }}"}, {"name": "customer_data", "value": "={{ $json.customer_data }}"}, {"name": "context_data", "value": "={{ $json.context_data }}"}, {"name": "priority", "value": "={{ $json.priority }}"}, {"name": "category", "value": "={{ $json.category }}"}]}, "options": {"timeout": 30000}}, "id": "trigger-ai-analysis", "name": "🧠 Trigger AI Analysis", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [2000, 120], "credentials": {"httpHeaderAuth": {"id": "n8n-webhook-auth", "name": "N8N Webhook Auth"}}}, {"parameters": {"jsCode": "// 🎯 Intelligent routing logic\nconst escalation = $input.first().json;\nconst aiAnalysis = $('trigger-ai-analysis').first()?.json || null;\n\n// Extract routing information\nconst routingData = {\n  escalation_id: escalation.escalation_id,\n  priority: escalation.priority,\n  priority_level: escalation.priority_level,\n  category: escalation.category,\n  ai_analysis: aiAnalysis,\n  routing_timestamp: new Date().toISOString()\n};\n\n// Define routing rules based on priority and category\nconst routingRules = {\n  critical: {\n    departments: ['emergency_response', 'senior_support'],\n    max_wait_time: 300, // 5 minutes\n    escalation_path: ['team_lead', 'manager', 'director'],\n    notification_channels: ['slack_critical', 'email_urgent', 'sms']\n  },\n  high: {\n    departments: ['priority_support', 'technical_support'],\n    max_wait_time: 900, // 15 minutes\n    escalation_path: ['senior_agent', 'team_lead'],\n    notification_channels: ['slack_high', 'email_urgent']\n  },\n  medium: {\n    departments: ['general_support', 'technical_support'],\n    max_wait_time: 3600, // 1 hour\n    escalation_path: ['agent', 'senior_agent'],\n    notification_channels: ['slack_general', 'email']\n  },\n  low: {\n    departments: ['general_support'],\n    max_wait_time: 14400, // 4 hours\n    escalation_path: ['agent'],\n    notification_channels: ['email']\n  }\n};\n\n// Get routing rule for current priority\nconst currentRule = routingRules[escalation.priority] || routingRules.medium;\n\n// AI-enhanced routing if available\nif (aiAnalysis && aiAnalysis.routing_recommendation) {\n  const aiRouting = aiAnalysis.routing_recommendation;\n  \n  // Override department if AI suggests better match\n  if (aiRouting.recommended_department) {\n    currentRule.departments = [aiRouting.recommended_department, ...currentRule.departments];\n  }\n  \n  // Adjust priority if AI detects urgency\n  if (aiRouting.urgency_score > 0.8 && escalation.priority !== 'critical') {\n    routingData.ai_priority_boost = true;\n    routingData.original_priority = escalation.priority;\n    routingData.boosted_priority = 'high';\n  }\n  \n  // Add required skills\n  if (aiRouting.required_skills && aiRouting.required_skills.length > 0) {\n    currentRule.required_skills = aiRouting.required_skills;\n  }\n}\n\n// Category-specific routing\nconst categoryRouting = {\n  'technical': ['technical_support', 'engineering'],\n  'billing': ['billing_support', 'finance'],\n  'account': ['account_management', 'customer_success'],\n  'product': ['product_support', 'technical_support'],\n  'security': ['security_team', 'technical_support'],\n  'compliance': ['compliance_team', 'legal']\n};\n\nif (categoryRouting[escalation.category]) {\n  currentRule.departments = [...categoryRouting[escalation.category], ...currentRule.departments];\n}\n\n// Final routing decision\nroutingData.routing_decision = {\n  target_departments: [...new Set(currentRule.departments)], // Remove duplicates\n  max_wait_time: currentRule.max_wait_time,\n  escalation_path: currentRule.escalation_path,\n  notification_channels: currentRule.notification_channels,\n  required_skills: currentRule.required_skills || [],\n  routing_score: aiAnalysis?.confidence_score || 0.7,\n  routing_reason: aiAnalysis ? 'ai_enhanced' : 'rule_based'\n};\n\nconsole.log('🎯 Routing decision for:', routingData.escalation_id);\nconsole.log('🏢 Target departments:', routingData.routing_decision.target_departments);\nconsole.log('⏱️ Max wait time:', routingData.routing_decision.max_wait_time, 'seconds');\nconsole.log('🔄 Escalation path:', routingData.routing_decision.escalation_path);\n\nif (routingData.ai_priority_boost) {\n  console.log('🚀 AI Priority Boost:', routingData.original_priority, '->', routingData.boosted_priority);\n}\n\nreturn { routingData };"}, "id": "intelligent-routing-logic", "name": "🎯 Intelligent Routing Logic", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [2220, 200]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "-- Find available agents in target departments\nSELECT \n  a.id as agent_id,\n  a.name as agent_name,\n  a.department,\n  a.skills,\n  a.current_workload,\n  a.max_concurrent_escalations,\n  a.status,\n  a.last_activity,\n  (a.max_concurrent_escalations - a.current_workload) as available_capacity\nFROM agent.agents a\nWHERE \n  a.department = ANY($1::text[])\n  AND a.status = 'available'\n  AND a.current_workload < a.max_concurrent_escalations\n  AND a.last_activity > NOW() - INTERVAL '30 minutes'\nORDER BY \n  a.current_workload ASC,\n  a.last_activity DESC\nLIMIT 10", "additionalFields": {"mode": "independently"}, "options": {}}, "id": "find-available-agents", "name": "👥 Find Available Agents", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [2440, 200], "credentials": {"postgres": {"id": "postgres-escalation-db", "name": "PostgreSQL - Escalation DB"}}}, {"parameters": {"jsCode": "// 👤 Agent assignment logic\nconst availableAgents = $input.all();\nconst routingData = $('intelligent-routing-logic').first().json.routingData;\nconst escalation = $('create-new-escalation').first()?.json || $('execute-escalation-update').first()?.json;\n\nlet assignmentResult = {\n  escalation_id: routingData.escalation_id,\n  assignment_status: 'pending',\n  assigned_agent: null,\n  queue_position: null,\n  estimated_wait_time: null,\n  assignment_reason: '',\n  assignment_timestamp: new Date().toISOString()\n};\n\nif (availableAgents.length === 0) {\n  // No agents available - queue the escalation\n  assignmentResult.assignment_status = 'queued';\n  assignmentResult.assignment_reason = 'no_agents_available';\n  \n  // Calculate queue position and estimated wait time\n  // This would typically query the current queue size\n  assignmentResult.queue_position = Math.floor(Math.random() * 10) + 1; // Placeholder\n  assignmentResult.estimated_wait_time = assignmentResult.queue_position * 15 * 60; // 15 min per position\n  \n  console.log('⏳ No agents available - queuing escalation');\n  console.log('📍 Queue position:', assignmentResult.queue_position);\n  console.log('⏱️ Estimated wait:', assignmentResult.estimated_wait_time, 'seconds');\n} else {\n  // Find best agent match\n  const requiredSkills = routingData.routing_decision.required_skills || [];\n  \n  // Score agents based on skills match and availability\n  const scoredAgents = availableAgents.map(agent => {\n    const agentData = agent.json;\n    let score = 0;\n    \n    // Base score from available capacity\n    score += agentData.available_capacity * 10;\n    \n    // Bonus for skill matches\n    if (requiredSkills.length > 0 && agentData.skills) {\n      const agentSkills = Array.isArray(agentData.skills) ? agentData.skills : JSON.parse(agentData.skills || '[]');\n      const skillMatches = requiredSkills.filter(skill => agentSkills.includes(skill)).length;\n      score += skillMatches * 20;\n    }\n    \n    // Penalty for high workload\n    score -= agentData.current_workload * 5;\n    \n    // Bonus for recent activity\n    const lastActivity = new Date(agentData.last_activity);\n    const minutesSinceActivity = (new Date() - lastActivity) / (1000 * 60);\n    if (minutesSinceActivity < 5) score += 15;\n    else if (minutesSinceActivity < 15) score += 10;\n    \n    return {\n      ...agentData,\n      assignment_score: score\n    };\n  });\n  \n  // Select best agent\n  const bestAgent = scoredAgents.sort((a, b) => b.assignment_score - a.assignment_score)[0];\n  \n  assignmentResult.assignment_status = 'assigned';\n  assignmentResult.assigned_agent = {\n    agent_id: bestAgent.agent_id,\n    agent_name: bestAgent.agent_name,\n    department: bestAgent.department,\n    assignment_score: bestAgent.assignment_score\n  };\n  assignmentResult.assignment_reason = 'best_match_available';\n  assignmentResult.estimated_wait_time = 0;\n  \n  console.log('✅ Agent assigned:', bestAgent.agent_name);\n  console.log('🏢 Department:', bestAgent.department);\n  console.log('📊 Assignment score:', bestAgent.assignment_score);\n  console.log('💼 Agent workload:', bestAgent.current_workload, '/', bestAgent.max_concurrent_escalations);\n}\n\nreturn { assignmentResult };"}, "id": "agent-assignment-logic", "name": "👤 Agent Assignment Logic", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [2660, 200]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "UPDATE agent.intelligent_escalations\nSET \n  assigned_agent_id = $2,\n  assignment_data = $3::jsonb,\n  status = $4,\n  updated_at = NOW()\nWHERE escalation_id = $1\nRETURNING *", "additionalFields": {"mode": "independently"}, "options": {}}, "id": "update-escalation-assignment", "name": "💾 Update Escalation Assignment", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [2880, 200], "credentials": {"postgres": {"id": "postgres-escalation-db", "name": "PostgreSQL - Escalation DB"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "is-critical-priority", "leftValue": "={{ $('agent-assignment-logic').first().json.assignmentResult.escalation_id }}", "rightValue": "", "operator": {"type": "string", "operation": "contains", "rightValue": "critical"}}], "combinator": "or"}, "options": {}}, "id": "needs-immediate-notification", "name": "🚨 Needs Immediate Notification?", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [3100, 120]}, {"parameters": {"method": "POST", "url": "http://localhost:5678/webhook/slack-notification", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}, {"name": "X-Notification-Source", "value": "intelligent-router"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "escalation_data", "value": "={{ $json }}"}, {"name": "assignment_data", "value": "={{ $('agent-assignment-logic').first().json.assignmentResult }}"}, {"name": "notification_type", "value": "critical_escalation"}, {"name": "channels", "value": "[\"#critical-escalations\", \"#tech-escalations\"]"}]}, "options": {"timeout": 10000}}, "id": "send-critical-notification", "name": "📢 Send Critical Notification", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [3320, 80], "credentials": {"httpHeaderAuth": {"id": "n8n-webhook-auth", "name": "N8N Webhook Auth"}}}, {"parameters": {"operation": "insert", "schema": "agent", "table": "escalation_events", "columns": "escalation_id, event_type, event_data, created_at", "additionalFields": {"mode": "independently"}, "options": {}}, "id": "log-routing-event", "name": "📝 Log Routing Event", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [3100, 280], "credentials": {"postgres": {"id": "postgres-escalation-db", "name": "PostgreSQL - Escalation DB"}}}, {"parameters": {"jsCode": "// 📊 Generate routing response\nconst escalation = $('update-escalation-assignment').first().json;\nconst assignmentResult = $('agent-assignment-logic').first().json.assignmentResult;\nconst routingData = $('intelligent-routing-logic').first().json.routingData;\n\n// Create comprehensive response\nconst response = {\n  success: true,\n  escalation_id: escalation.escalation_id,\n  status: escalation.status,\n  assignment: {\n    status: assignmentResult.assignment_status,\n    assigned_agent: assignmentResult.assigned_agent,\n    queue_position: assignmentResult.queue_position,\n    estimated_wait_time: assignmentResult.estimated_wait_time,\n    assignment_reason: assignmentResult.assignment_reason\n  },\n  routing: {\n    target_departments: routingData.routing_decision.target_departments,\n    routing_score: routingData.routing_decision.routing_score,\n    routing_reason: routingData.routing_decision.routing_reason,\n    ai_enhanced: routingData.routing_decision.routing_reason === 'ai_enhanced'\n  },\n  timestamps: {\n    created_at: escalation.created_at,\n    updated_at: escalation.updated_at,\n    assignment_timestamp: assignmentResult.assignment_timestamp,\n    routing_timestamp: routingData.routing_timestamp\n  },\n  metadata: {\n    priority: escalation.priority,\n    priority_level: escalation.priority_level,\n    category: escalation.category,\n    source: escalation.source,\n    ai_priority_boost: routingData.ai_priority_boost || false\n  }\n};\n\n// Add performance metrics\nconst processingStartTime = new Date(routingData.routing_timestamp);\nconst processingEndTime = new Date();\nresponse.performance = {\n  total_processing_time_ms: processingEndTime - processingStartTime,\n  routing_completed_at: processingEndTime.toISOString()\n};\n\nconsole.log('✅ Routing completed for:', response.escalation_id);\nconsole.log('📊 Assignment status:', response.assignment.status);\nconsole.log('⏱️ Processing time:', response.performance.total_processing_time_ms, 'ms');\n\nif (response.assignment.assigned_agent) {\n  console.log('👤 Assigned to:', response.assignment.assigned_agent.agent_name);\n} else if (response.assignment.queue_position) {\n  console.log('⏳ Queued at position:', response.assignment.queue_position);\n}\n\nreturn response;"}, "id": "generate-routing-response", "name": "📊 Generate Routing Response", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [3320, 280]}], "pinData": {}, "connections": {"webhook-escalation-router": {"main": [[{"node": "process-escalation-data", "type": "main", "index": 0}]]}, "process-escalation-data": {"main": [[{"node": "check-existing-escalation", "type": "main", "index": 0}]]}, "check-existing-escalation": {"main": [[{"node": "query-existing-escalation", "type": "main", "index": 0}], [{"node": "prepare-new-escalation", "type": "main", "index": 0}]]}, "query-existing-escalation": {"main": [[{"node": "has-existing-escalation", "type": "main", "index": 0}]]}, "has-existing-escalation": {"main": [[{"node": "update-existing-escalation", "type": "main", "index": 0}], [{"node": "prepare-new-escalation", "type": "main", "index": 0}]]}, "update-existing-escalation": {"main": [[{"node": "execute-escalation-update", "type": "main", "index": 0}]]}, "execute-escalation-update": {"main": [[{"node": "needs-ai-analysis", "type": "main", "index": 0}]]}, "prepare-new-escalation": {"main": [[{"node": "create-new-escalation", "type": "main", "index": 0}]]}, "create-new-escalation": {"main": [[{"node": "needs-ai-analysis", "type": "main", "index": 0}]]}, "needs-ai-analysis": {"main": [[{"node": "trigger-ai-analysis", "type": "main", "index": 0}], [{"node": "intelligent-routing-logic", "type": "main", "index": 0}]]}, "trigger-ai-analysis": {"main": [[{"node": "intelligent-routing-logic", "type": "main", "index": 0}]]}, "intelligent-routing-logic": {"main": [[{"node": "find-available-agents", "type": "main", "index": 0}]]}, "find-available-agents": {"main": [[{"node": "agent-assignment-logic", "type": "main", "index": 0}]]}, "agent-assignment-logic": {"main": [[{"node": "update-escalation-assignment", "type": "main", "index": 0}]]}, "update-escalation-assignment": {"main": [[{"node": "needs-immediate-notification", "type": "main", "index": 0}, {"node": "log-routing-event", "type": "main", "index": 0}]]}, "needs-immediate-notification": {"main": [[{"node": "send-critical-notification", "type": "main", "index": 0}], []]}, "send-critical-notification": {"main": [[{"node": "generate-routing-response", "type": "main", "index": 0}]]}, "log-routing-event": {"main": [[{"node": "generate-routing-response", "type": "main", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1", "saveManualExecutions": true, "callerPolicy": "workflowsFromSameOwner", "errorWorkflow": "escalation-error-handler"}, "versionId": "v2.0", "meta": {"templateCredsSetupCompleted": true, "instanceId": "escalation-main-router-v2"}, "id": "escalation-main-router-v2", "tags": [{"createdAt": "2024-01-15T10:00:00.000Z", "updatedAt": "2024-01-15T10:00:00.000Z", "id": "escalation-v2", "name": "escalation-v2"}, {"createdAt": "2024-01-15T10:00:00.000Z", "updatedAt": "2024-01-15T10:00:00.000Z", "id": "intelligent-routing", "name": "intelligent-routing"}, {"createdAt": "2024-01-15T10:00:00.000Z", "updatedAt": "2024-01-15T10:00:00.000Z", "id": "ai-enhanced", "name": "ai-enhanced"}]}