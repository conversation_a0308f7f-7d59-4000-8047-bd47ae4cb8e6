{"nodes": [{"parameters": {"httpMethod": "POST", "path": "c2d4021e-2d8c-45f0-a12d-9becb13033db", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-1500, -1160], "id": "e8197607-8159-4766-93a3-26af70455e7f", "name": "Webhook", "webhookId": "c2d4021e-2d8c-45f0-a12d-9becb13033db"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "17498f92-2498-48dd-9531-a8326f09e1a3", "leftValue": "={{ $json.body.data.key.fromMe }}", "rightValue": "", "operator": {"type": "boolean", "operation": "true", "singleValue": true}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [-1160, -1560], "id": "02b3d48c-77ad-4732-b80f-c5f5b7f60e23", "name": "If"}, {"parameters": {"resource": "messages-api", "instanceName": "={{ $('Webhook').item.json.body.instance }}", "remoteJid": "={{ $('Webhook').item.json.body.data.key.remoteJid }}", "messageText": "={{ $json.messages }}", "options_message": {"delay": 1200}}, "type": "n8n-nodes-evolution-api.evolutionApi", "typeVersion": 1, "position": [2720, -1620], "id": "2ea30bea-1e0d-417d-bc47-a0f183a91783", "name": "Evolution API", "credentials": {"evolutionApi": {"id": "xCmkDcKfDU8am3wW", "name": "Evolution account"}}}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{ $('Webhook').item.json.body.data.messageType }}", "rightValue": "conversation", "operator": {"type": "string", "operation": "equals"}, "id": "e7cc40a5-523e-4e8f-9906-94294f9a1979"}], "combinator": "and"}, "renameOutput": true, "outputKey": "text"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "3e68dc0b-bd39-4481-9c19-d3a87695dd1a", "leftValue": "={{ $('Webhook').item.json.body.data.messageType }}", "rightValue": "extendedTextMessage", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "extentedText"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "8440464b-558f-41c6-a3e9-4cf9a4efee21", "leftValue": "={{ $('Webhook').item.json.body.data.messageType }}", "rightValue": "imageMessage", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "image"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "12c87e30-81ab-4963-99d1-0e6029658974", "leftValue": "={{ $('Webhook').item.json.body.data.messageType }}", "rightValue": "videoMessage", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "video"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "944037f2-272a-458d-9d3e-1c8d56af11c8", "leftValue": "={{ $('Webhook').item.json.body.data.messageType }}", "rightValue": "audioMessage", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "audio"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "02920603-2de4-44be-b016-04562ed9cabb", "leftValue": "={{ $('Webhook').item.json.body.data.messageType }}", "rightValue": "stickerMessage", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "sticker"}]}, "options": {}}, "type": "n8n-nodes-base.switch", "typeVersion": 3.2, "position": [-500, -700], "id": "c8883af0-e9d9-437e-bab8-3b022c5bdd35", "name": "Switch"}, {"parameters": {"content": "## Start \n**Mensagem recebida**", "height": 260, "width": 340, "color": 4}, "type": "n8n-nodes-base.stickyNote", "position": [-1600, -1240], "typeVersion": 1, "id": "8719da4a-1037-49d2-8220-d54ccccdc9a8", "name": "<PERSON><PERSON>"}, {"parameters": {"content": "## Fluxo de teste - enviando msg pra eu mesmo\n", "height": 380, "width": 600, "color": 4}, "type": "n8n-nodes-base.stickyNote", "position": [-1180, -1620], "typeVersion": 1, "id": "02ade09a-f6e6-4a43-9b6a-62c54739b384", "name": "Sticky Note1"}, {"parameters": {"content": "## Testa tipo de mensagem\n**Principais tipos mapeados**\n**Se for imagem, deve enviar para processamento de imagem**\n**Se for audio, deve enviar para processamento de audio**\n**Existem mais tipos**\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n**Ver documentação (https://baileys.wiki/docs/api/namespaces/proto/classes/Message/)**", "height": 500, "width": 1080, "color": 4}, "type": "n8n-nodes-base.stickyNote", "position": [-1220, -740], "typeVersion": 1, "id": "85d249ec-7a4c-4437-b8bc-ca767f90cdae", "name": "Sticky Note2"}, {"parameters": {"content": "## Agent\n**Processamento básico de texto**\n**<PERSON><PERSON> pode ser um AI Agent, com Tools, MCP, etc**", "height": 660, "width": 860, "color": 5}, "type": "n8n-nodes-base.stickyNote", "position": [1340, -1220], "typeVersion": 1, "id": "54201fd2-70bf-437b-a36c-03272371ca6b", "name": "Sticky Note3"}, {"parameters": {"content": "## Envia mensagens\n**Quebra mensagens grandes e envia com um wait**", "height": 460, "width": 1160, "color": 3}, "type": "n8n-nodes-base.stickyNote", "position": [2000, -1740], "typeVersion": 1, "id": "92a8bab4-aa3c-45d4-881a-c42f6aba36b8", "name": "Sticky Note4"}, {"parameters": {"promptType": "define", "text": "={{ $json.mensagens }}", "options": {"systemMessage": "You are a helpful assistant\n\n- USE QUEBRA DE LINHA PARA RESPOSTAS GRANDES \"\\n\\n\"", "maxIterations": 5}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.9, "position": [1640, -1040], "id": "eaf9ad9f-0503-4d9c-b4da-bba36d58cdae", "name": "AI Agent"}, {"parameters": {"assignments": {"assignments": [{"id": "aeeff064-0d78-4b04-a484-fbd2279eaf78", "name": "name", "value": "={{ $json.body.data.pushName }}", "type": "string"}, {"id": "d6acb2e2-88dc-4797-9630-ef6f5bd20eca", "name": "whatsapp_number", "value": "={{ $json.body.data.key.remoteJid }}", "type": "string"}, {"id": "88e22c89-999e-4a18-b01a-321bb757c974", "name": "message_type", "value": "={{ $json.body.data.messageType }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-420, -1180], "id": "b35d3179-eacd-41bf-a658-ce116ea07ec5", "name": "mapeia_campos"}, {"parameters": {"amount": 6}, "type": "n8n-nodes-base.wait", "typeVersion": 1.1, "position": [500, -1460], "id": "a9bb51b1-fe61-4dc3-9387-c093cba73fea", "name": "Wait", "webhookId": "28bbdc58-5736-464d-885d-f44db280b6fc"}, {"parameters": {"resource": "audio", "operation": "transcribe", "options": {}}, "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.8, "position": [-340, -160], "id": "bfbacfcd-1b9b-4263-b7b6-3615f1f94b46", "name": "OpenAI", "credentials": {"openAiApi": {"id": "bHUYIvnk6TW05hV9", "name": "OpenAi account"}}}, {"parameters": {"operation": "toBinary", "sourceProperty": "data.base64", "options": {"fileName": "=file.ogg", "mimeType": "={{ $json.data.mimetype }}"}}, "type": "n8n-nodes-base.convertToFile", "typeVersion": 1.1, "position": [-540, -160], "id": "c8958036-7259-4bed-816e-d215c77b73e7", "name": "Convert to File"}, {"parameters": {"content": "## Transcreve audio", "height": 260, "width": 1140}, "type": "n8n-nodes-base.stickyNote", "position": [-1000, -180], "typeVersion": 1, "id": "f9a098e1-6254-4320-9789-f84512818274", "name": "Sticky Note5"}, {"parameters": {"content": "## Buffer", "height": 480, "width": 960}, "type": "n8n-nodes-base.stickyNote", "position": [240, -1520], "typeVersion": 1, "id": "bfd7d49b-d8f1-4883-9978-3851d7d9bc9e", "name": "Sticky Note6"}, {"parameters": {}, "type": "n8n-nodes-base.noOp", "typeVersion": 1, "position": [740, -1180], "id": "9cb0b0c0-167c-4df0-8694-c5fc504af537", "name": "No Operation, do nothing"}, {"parameters": {"content": "## Tratar recebimento de imagens e videos \n", "height": 400, "width": 280}, "type": "n8n-nodes-base.stickyNote", "position": [400, -420], "typeVersion": 1, "id": "d774b694-e197-4580-a33f-a256b2e5b367", "name": "Sticky Note7"}, {"parameters": {}, "type": "n8n-nodes-base.noOp", "typeVersion": 1, "position": [500, -280], "id": "0402e30c-58be-4b0c-9d02-388a75a84ec4", "name": "No Operation, do nothing1"}, {"parameters": {"assignments": {"assignments": [{"id": "95a5668b-59c1-4466-bb54-94022b6f4a15", "name": "message", "value": "={{ $('Webhook').item.json.body.data.message.conversation }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-60, -720], "id": "7b14795a-b57f-47db-a027-29bc21e27438", "name": "mapeia_mensagem_texto"}, {"parameters": {"assignments": {"assignments": [{"id": "50ef5fba-0d30-4933-9a8b-b9a84293fdea", "name": "message", "value": "={{ $json.text }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-120, -160], "id": "fbb0ec99-9bdb-4186-a4a9-c06fbae007c2", "name": "mapeia_mensagem_audio"}, {"parameters": {"operation": "push", "list": "={{ $('mapeia_campos').item.json.whatsapp_number }}", "messageData": "={{ $json.message }}", "tail": true}, "type": "n8n-nodes-base.redis", "typeVersion": 1, "position": [300, -1460], "id": "f6cb0026-66ad-4fed-9e4e-fe28999098ba", "name": "redis_salva_mensagem", "credentials": {"redis": {"id": "m4VeErr6zeyVj7u4", "name": "Redis account"}}}, {"parameters": {"operation": "get", "propertyName": "mensagens", "key": "={{ $('Switch').item.json.whatsapp_number }}", "options": {}}, "type": "n8n-nodes-base.redis", "typeVersion": 1, "position": [660, -1460], "id": "c6137d0f-8b5c-49f5-8bba-dc2dabdd0f8d", "name": "redis_get_mensagens", "credentials": {"redis": {"id": "m4VeErr6zeyVj7u4", "name": "Redis account"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "7462a97c-9dbb-407a-8213-f79730be24f2", "leftValue": "={{ $json.mensagens.last() }}", "rightValue": "={{ $('redis_salva_mensagem').item.json.message }}", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [800, -1460], "id": "d21d8454-561e-4a24-a15f-f1643f5c0755", "name": "last_mensagem"}, {"parameters": {"resource": "chat-api", "operation": "get-media-base64", "instanceName": "={{ $('Webhook').item.json.body.instance }}", "messageId": "={{ $('Webhook').item.json.body.data.key.id }}"}, "type": "n8n-nodes-evolution-api.evolutionApi", "typeVersion": 1, "position": [-740, -160], "id": "d3136f0f-6551-4278-acac-65e0c2192846", "name": "get_audio", "credentials": {"evolutionApi": {"id": "xCmkDcKfDU8am3wW", "name": "Evolution account"}}}, {"parameters": {}, "type": "n8n-nodes-base.noOp", "typeVersion": 1, "position": [-840, -1380], "id": "45dbeefc-fcea-4176-9ec4-8d90ed13c9bc", "name": "No Operation, do nothing2"}, {"parameters": {"operation": "delete", "key": "={{ $json.whatsapp }}"}, "type": "n8n-nodes-base.redis", "typeVersion": 1, "position": [1020, -1240], "id": "3b3277da-5b20-41f5-ade7-a23c55b67bec", "name": "redis_deleta_msgs", "credentials": {"redis": {"id": "m4VeErr6zeyVj7u4", "name": "Redis account"}}}, {"parameters": {"operation": "delete", "key": "=<EMAIL>"}, "type": "n8n-nodes-base.redis", "typeVersion": 1, "position": [1240, -1520], "id": "69a38250-5bd6-42e7-badf-b297171e1c77", "name": "redis_deleta_msgs1", "credentials": {"redis": {"id": "m4VeErr6zeyVj7u4", "name": "Redis account"}}}, {"parameters": {"model": {"__rl": true, "value": "gpt-4.1-nano", "mode": "list", "cachedResultName": "gpt-4.1-nano"}, "options": {"maxRetries": 2}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [1440, -900], "id": "df2b1cd2-d597-4d8e-a53f-8a4e3be8bc70", "name": "OpenAI Chat Model", "credentials": {"openAiApi": {"id": "vWJy8Y4lV5OEnOSr", "name": "OpenAi account 2"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "6911e281-670d-4bf0-bc11-16ca4c747968", "leftValue": "={{ $('Webhook').item.json.body.sender }}", "rightValue": "={{ $('Webhook').item.json.body.data.key.remoteJid }}", "operator": {"type": "string", "operation": "equals"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [-900, -1560], "id": "58e1bafa-255e-478b-91bd-5824b917bfbf", "name": "If1"}, {"parameters": {"assignments": {"assignments": [{"id": "f3e990c0-60f4-491b-a9ab-95f69abe4b8e", "name": "mensagens", "value": "={{ $json.mensagens }}", "type": "string"}, {"id": "5ef66390-c1ac-4926-a898-c9072fe25b57", "name": "whatsapp", "value": "={{ $('Switch').item.json.whatsapp_number }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [980, -1480], "id": "d803242f-bfdf-4f48-b3b6-fbce2d328653", "name": "<PERSON>"}, {"parameters": {"sessionIdType": "customKey", "sessionKey": "={{ $('Edit Fields').item.json.whatsapp }}"}, "type": "@n8n/n8n-nodes-langchain.memoryPostgresChat", "typeVersion": 1.3, "position": [1600, -780], "id": "********-f587-4adc-9904-620b1f7b0020", "name": "Postgres Chat Memory", "credentials": {"postgres": {"id": "ifBVR4pF4hoz9bIM", "name": "Postgres account"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "17498f92-2498-48dd-9531-a8326f09e1a3", "leftValue": "={{ $json.body.data.key.fromMe }}", "rightValue": "", "operator": {"type": "boolean", "operation": "true", "singleValue": true}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [-1160, -1120], "id": "4c91e89b-eed7-44e1-ac4e-4be48f3155ef", "name": "If2"}, {"parameters": {"content": "## Fluxo de prod - clientes enviando msg\n", "height": 380, "width": 560, "color": 4}, "type": "n8n-nodes-base.stickyNote", "position": [-1180, -1220], "typeVersion": 1, "id": "1c40905e-1e93-4f29-b586-a7aa8d82f629", "name": "Sticky Note8"}, {"parameters": {}, "type": "n8n-nodes-base.noOp", "typeVersion": 1, "position": [-960, -1160], "id": "8a48dfce-4122-433b-a3c4-bd03a0a03ccf", "name": "No Operation, do nothing3"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "6911e281-670d-4bf0-bc11-16ca4c747968", "leftValue": "={{ $('Webhook').item.json.body.sender }}", "rightValue": "={{ $('Webhook').item.json.body.data.key.remoteJid }}", "operator": {"type": "string", "operation": "equals"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [-800, -1040], "id": "87d3ea2d-d634-430d-877d-d2e61019aad2", "name": "If3"}, {"parameters": {"content": "**Para produção deve ligar o Webhook no fluxo de PROD e remover do fluxo de TESTE**", "height": 340, "width": 300}, "type": "n8n-nodes-base.stickyNote", "position": [-500, -1280], "typeVersion": 1, "id": "6993a7b5-55f5-44fb-bbfc-31f063b92fc0", "name": "Sticky Note9"}, {"parameters": {"assignments": {"assignments": [{"id": "cd54b73e-ca85-4015-b575-00cf861a1d26", "name": "messages", "value": "={{ $json.output }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [2060, -1460], "id": "17026432-1c25-4bcf-ba49-3bae7d4c0ced", "name": "editMessages"}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [2520, -1680], "id": "966245cc-ac42-47c0-b92c-b0c613247ea7", "name": "Loop Over Items"}, {"parameters": {"amount": 2}, "type": "n8n-nodes-base.wait", "typeVersion": 1.1, "position": [2920, -1480], "id": "d90ffccd-3e93-4a69-9d02-e86678ec67e9", "name": "Wait1", "webhookId": "cdcaae50-da4f-4972-9b42-8fef8f08a7af"}, {"parameters": {"assignments": {"assignments": [{"id": "1e55a645-9c25-4ecb-8220-1b04ef235235", "name": "messages", "value": "={{ $json.messages.split('\\n\\n') }}", "type": "array"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [2200, -1620], "id": "5b7de302-2d31-473c-bd4b-08598e217b11", "name": "StringToArray"}, {"parameters": {"fieldToSplitOut": "messages", "options": {}}, "type": "n8n-nodes-base.splitOut", "typeVersion": 1, "position": [2340, -1460], "id": "90393ff4-1a97-4611-ad31-5a79a74cbc76", "name": "SplitMsgs"}, {"parameters": {"content": "## Usar o Community Node da Evolution API ou o node HTTP Request"}, "type": "n8n-nodes-base.stickyNote", "position": [-820, 40], "typeVersion": 1, "id": "ce17acc8-601b-4cf9-ac44-efa4317334f2", "name": "Sticky Note10"}, {"parameters": {"content": "## Usar o Community Node da Evolution API ou o node HTTP Request"}, "type": "n8n-nodes-base.stickyNote", "position": [2840, -1760], "typeVersion": 1, "id": "164c98dd-7a3a-487a-886f-37d60f206329", "name": "Sticky Note11"}], "connections": {"Webhook": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "If": {"main": [[{"node": "If1", "type": "main", "index": 0}], [{"node": "No Operation, do nothing2", "type": "main", "index": 0}]]}, "Evolution API": {"main": [[{"node": "Wait1", "type": "main", "index": 0}]]}, "Switch": {"main": [[{"node": "mapeia_mensagem_texto", "type": "main", "index": 0}], [{"node": "No Operation, do nothing1", "type": "main", "index": 0}], [{"node": "No Operation, do nothing1", "type": "main", "index": 0}], [{"node": "No Operation, do nothing1", "type": "main", "index": 0}], [{"node": "get_audio", "type": "main", "index": 0}], [{"node": "No Operation, do nothing1", "type": "main", "index": 0}]]}, "AI Agent": {"main": [[{"node": "editMessages", "type": "main", "index": 0}]]}, "mapeia_campos": {"main": [[{"node": "Switch", "type": "main", "index": 0}]]}, "Wait": {"main": [[{"node": "redis_get_mensagens", "type": "main", "index": 0}]]}, "OpenAI": {"main": [[{"node": "mapeia_mensagem_audio", "type": "main", "index": 0}]]}, "Convert to File": {"main": [[{"node": "OpenAI", "type": "main", "index": 0}]]}, "mapeia_mensagem_texto": {"main": [[{"node": "redis_salva_mensagem", "type": "main", "index": 0}]]}, "mapeia_mensagem_audio": {"main": [[{"node": "redis_salva_mensagem", "type": "main", "index": 0}]]}, "redis_salva_mensagem": {"main": [[{"node": "Wait", "type": "main", "index": 0}]]}, "redis_get_mensagens": {"main": [[{"node": "last_mensagem", "type": "main", "index": 0}]]}, "last_mensagem": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}], [{"node": "No Operation, do nothing", "type": "main", "index": 0}]]}, "get_audio": {"main": [[{"node": "Convert to File", "type": "main", "index": 0}]]}, "redis_deleta_msgs": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "If1": {"main": [[{"node": "mapeia_campos", "type": "main", "index": 0}], [{"node": "No Operation, do nothing2", "type": "main", "index": 0}]]}, "Edit Fields": {"main": [[{"node": "redis_deleta_msgs", "type": "main", "index": 0}]]}, "Postgres Chat Memory": {"ai_memory": [[{"node": "AI Agent", "type": "ai_memory", "index": 0}]]}, "If2": {"main": [[{"node": "No Operation, do nothing3", "type": "main", "index": 0}], [{"node": "If3", "type": "main", "index": 0}]]}, "No Operation, do nothing3": {"main": [[]]}, "If3": {"main": [[{"node": "No Operation, do nothing3", "type": "main", "index": 0}], [{"node": "mapeia_campos", "type": "main", "index": 0}]]}, "editMessages": {"main": [[{"node": "StringToArray", "type": "main", "index": 0}]]}, "Loop Over Items": {"main": [[], [{"node": "Evolution API", "type": "main", "index": 0}]]}, "Wait1": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "StringToArray": {"main": [[{"node": "SplitMsgs", "type": "main", "index": 0}]]}, "SplitMsgs": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}}, "pinData": {}, "meta": {"templateCredsSetupCompleted": true, "instanceId": "2ed1cfddb977f640387f1d77cf713a54d5c3220d8acbc23a70f9a6d172f71a75"}}