{"nodes": [{"parameters": {"workflowInputs": {"values": [{"name": "telefone"}, {"name": "nome"}, {"name": "ultima_mensagem"}, {"name": "id_conta"}, {"name": "id_conversa"}, {"name": "telegram_chat_id"}, {"name": "url_chatwoot"}]}}, "type": "n8n-nodes-base.executeWorkflowTrigger", "typeVersion": 1.1, "position": [-260, -100], "id": "8564e49a-cea8-4942-8400-a4da8e97044b", "name": "When Executed by Another Workflow"}, {"parameters": {"chatId": "={{ $('When Executed by Another Workflow').item.json.telegram_chat_id }}", "text": "=Assistente desabilitado para o usuário {{ $('When Executed by Another Workflow').item.json.nome || '(usuário não cadastrado)' }} ({{ $('When Executed by Another Workflow').item.json.telefone }}).\n\nÚltima mensagem:\n\n---\n\n{{ $('When Executed by Another Workflow').item.json.ultima_mensagem }}", "additionalFields": {}}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [520, -100], "id": "1415901d-5339-41e0-816f-159d4d218f58", "name": "Enviar alerta", "webhookId": "153d7fb2-c401-4894-95a5-c123c8bbdd0d", "credentials": {}}, {"parameters": {"content": "[![fazer.ai](https://framerusercontent.com/images/HqY9djLTzyutSKnuLLqBr92KbM.png?scale-down-to=256)](https://fazer.ai?utm_source=n8n&utm_campaign=sec-ep2&utm_medium=cw-4)\n\n## Esse é um template faça você mesmo do canal\n## <PERSON> Moreira\n\n### Inscreva-se no nosso canal no YouTube\n[![YouTube Lucas Moreira](https://img.shields.io/youtube/channel/subscribers/UCtmp6SxzLscu0GRTbgM8FTw?style=flat-square&logo=youtube&label=Inscreva-se&color=f00)](https://youtube.com/@eulucassmoreira?si=0lH7hwX9pukjhmPQ)\n\n### Siga nosso GitHub\n[![GitHub fazer.ai](https://img.shields.io/badge/github-%23121011.svg?style=for-the-badge&logo=github&logoColor=white&label)](https://github.com/fazer-ai)\n", "height": 440, "width": 550, "color": 7}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-340, -620], "id": "35a883d4-84ad-42ae-a787-16fad71143ea", "name": "Sticky Note10"}, {"parameters": {"content": "## Quer entender como funciona?\n\n\n### Assista o vídeo, deixe um like, e se inscreva no canal para ter acesso a mais workflows como esse!\n\n[![IMAGE ALT TEXT HERE](https://i1.ytimg.com/vi_webp/cvTWGNJGAu4/maxresdefault.webp)](https://www.youtube.com/watch?v=cvTWGNJGAu4)", "height": 440, "width": 500, "color": 7}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [220, -620], "id": "df19ba4f-9fb6-4994-9d2c-b6d108468d59", "name": "Sticky Note13"}, {"parameters": {"method": "POST", "url": "={{ $('When Executed by Another Workflow').item.json.url_chatwoot }}/api/v1/accounts/{{ $('When Executed by Another Workflow').item.json.id_conta }}/conversations/{{ $('When Executed by Another Workflow').item.json.id_conversa }}/labels", "authentication": "predefinedCredentialType", "nodeCredentialType": "chatwootApi", "sendBody": true, "bodyParameters": {"parameters": [{"name": "labels", "value": "={{ $json.payload.append('agente-off').unique() }}"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [260, -100], "id": "06792ccd-a847-4be2-bc0f-d1a944e5c7e9", "name": "Colocar etiqueta agente-off", "credentials": {}}, {"parameters": {"method": "POST", "url": "=<colar sua url do chatwoot>/api/v1/accounts/{{ $json.account_id }}/labels", "authentication": "predefinedCredentialType", "nodeCredentialType": "chatwootApi", "sendBody": true, "specifyBody": "json", "jsonBody": "{\n  \"color\": \"#6233D0\",\n  \"description\": \"Agente IA não responderá esse contato.\",\n  \"title\": \"agente-off\",\n  \"show_on_sidebar\": true\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [260, 260], "id": "34a554ef-b825-46a5-8f59-747ca778d236", "name": "Criar etiqueta agente-off", "credentials": {}}, {"parameters": {"content": "## Criar etiqueta\n\nVocê pode criar a etiqueta \"agente-off\" por aqui, ou pela interface do Chatwoot.\n\n\n\n\n\n\n\n\n\n\n\n\n\n", "height": 340, "width": 1040, "color": 6}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-320, 140], "id": "14f0d014-c2b1-4b36-a183-4fc646368a24", "name": "<PERSON><PERSON>"}, {"parameters": {"url": "={{ $('When Executed by Another Workflow').item.json.url_chatwoot }}/api/v1/accounts/{{ $('When Executed by Another Workflow').item.json.id_conta }}/conversations/{{ $('When Executed by Another Workflow').item.json.id_conversa }}/labels", "authentication": "predefinedCredentialType", "nodeCredentialType": "chatwootApi", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [20, -100], "id": "7613ada7-6483-4420-ac0b-e817c007b22a", "name": "Listar etiquetas da conversa", "credentials": {}}, {"parameters": {"resource": "Profile", "operation": "Fetch Profile", "requestOptions": {}}, "type": "@devlikeapro/n8n-nodes-chatwoot.chatWoot", "typeVersion": 1, "position": [20, 260], "id": "c78c19bc-38cc-4658-981a-6e10b7b31049", "name": "Buscar informações da conta", "credentials": {}}, {"parameters": {}, "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [-260, 260], "id": "23e31c1d-d9eb-4bb4-a1bd-1437b00898e7", "name": "When clicking ‘Test workflow’"}, {"parameters": {"content": "![Chatwoot](https://app.chatwoot.com/brand-assets/logo_dark.svg)", "height": 100, "width": 280, "color": 7}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-120, -320], "id": "6b90e09a-81f9-4e18-9fa9-0854f3c3dcfb", "name": "Sticky Note23"}, {"parameters": {"content": "## 4. <PERSON><PERSON><PERSON> humano", "height": 80, "width": 540, "color": 5}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-340, -720], "id": "522e7754-e1ff-45d7-8bee-c46134506ed9", "name": "Sticky Note30"}, {"parameters": {"method": "POST", "url": "=<colar sua url do chatwoot>/api/v1/accounts/{{ $json.account_id }}/labels", "authentication": "predefinedCredentialType", "nodeCredentialType": "chatwootApi", "sendBody": true, "specifyBody": "json", "jsonBody": "{\n  \"color\": \"#A44C74\",\n  \"title\": \"testando-agente\",\n  \"show_on_sidebar\": true\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [520, 260], "id": "d2b0b164-b104-4046-b4cc-a5cb60907120", "name": "Criar etiqueta testando-agente", "credentials": {}}], "connections": {"When Executed by Another Workflow": {"main": [[{"node": "Listar etiquetas da conversa", "type": "main", "index": 0}]]}, "Colocar etiqueta agente-off": {"main": [[{"node": "Enviar alerta", "type": "main", "index": 0}]]}, "Criar etiqueta agente-off": {"main": [[{"node": "Criar etiqueta testando-agente", "type": "main", "index": 0}]]}, "Listar etiquetas da conversa": {"main": [[{"node": "Colocar etiqueta agente-off", "type": "main", "index": 0}]]}, "Buscar informações da conta": {"main": [[{"node": "Criar etiqueta agente-off", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Buscar informações da conta", "type": "main", "index": 0}]]}}, "pinData": {}, "meta": {}}