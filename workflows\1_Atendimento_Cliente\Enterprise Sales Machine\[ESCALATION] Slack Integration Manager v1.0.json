{"name": "[ESCALATION] Slack Integration Manager v1.0", "nodes": [{"parameters": {"path": "escalation-slack-notify", "httpMethod": "POST", "responseMode": "responseNode", "options": {"rawBody": true, "allowedOrigins": "*"}}, "id": "webhook-trigger", "name": "🚀 Trigger: Slack Notification", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300], "webhookId": "escalation-slack-notify"}, {"parameters": {"jsCode": "// =====================================================\n// SLACK INTEGRATION - REQUEST VALIDATOR\n// =====================================================\n\nconst Joi = require('joi');\nconst crypto = require('crypto');\n\n// Schema de validação para notificações Slack\nconst slackNotificationSchema = Joi.object({\n  escalation_id: Joi.number().integer().positive().required(),\n  notification_type: Joi.string().valid('escalation_created', 'agent_assigned', 'status_changed', 'resolution_completed', 'supervisor_alert', 'metrics_report').required(),\n  priority: Joi.string().valid('low', 'medium', 'high', 'urgent', 'critical').default('medium'),\n  channel: Joi.string().optional(),\n  recipient_type: Joi.string().valid('channel', 'user', 'supervisor').default('channel'),\n  recipient_id: Joi.string().optional(),\n  escalation_data: Joi.object({\n    contact_id: Joi.number().integer().positive().required(),\n    urgency_level: Joi.string().valid('low', 'medium', 'high', 'critical').required(),\n    specialization_required: Joi.string().optional(),\n    escalation_reason: Joi.string().min(10).max(1000).required(),\n    ai_analysis_score: Joi.number().min(0).max(100).optional(),\n    assigned_human: Joi.string().optional(),\n    assigned_queue: Joi.string().optional(),\n    status: Joi.string().optional(),\n    created_at: Joi.string().isoDate().optional(),\n    updated_at: Joi.string().isoDate().optional()\n  }).required(),\n  metrics_data: Joi.object().optional(),\n  custom_message: Joi.string().max(500).optional(),\n  metadata: Joi.object().optional()\n});\n\nfunction generateCorrelationId() {\n  return crypto.randomUUID();\n}\n\nconst startTime = Date.now();\nconst correlationId = generateCorrelationId();\n\ntry {\n  const requestBody = $input.first().json.body;\n  const headers = $input.first().json.headers;\n  \n  console.log(`[${correlationId}] Slack notification request received`, {\n    timestamp: new Date().toISOString(),\n    notificationType: requestBody.notification_type,\n    escalationId: requestBody.escalation_id,\n    priority: requestBody.priority\n  });\n  \n  const { error, value: validatedData } = slackNotificationSchema.validate(requestBody, {\n    abortEarly: false,\n    stripUnknown: true,\n    convert: true\n  });\n  \n  if (error) {\n    const validationErrors = error.details.map(detail => ({\n      field: detail.path.join('.'),\n      message: detail.message,\n      value: detail.context?.value\n    }));\n    \n    return [{\n      json: {\n        success: false,\n        error: 'VALIDATION_ERROR',\n        message: 'Slack notification validation failed',\n        details: validationErrors,\n        correlation_id: correlationId,\n        timestamp: new Date().toISOString()\n      }\n    }];\n  }\n  \n  const processingTime = Date.now() - startTime;\n  \n  const outputData = {\n    ...validatedData,\n    correlation_id: correlationId,\n    validation_time_ms: processingTime,\n    received_at: new Date().toISOString(),\n    user_agent: headers['user-agent'],\n    ip_address: headers['x-forwarded-for'] || headers['x-real-ip'] || 'unknown',\n    validation_status: 'success',\n    next_step: 'slack_notification'\n  };\n  \n  console.log(`[${correlationId}] Slack validation successful`, {\n    processingTime,\n    notificationType: validatedData.notification_type,\n    escalationId: validatedData.escalation_id\n  });\n  \n  return [{ json: outputData }];\n  \n} catch (error) {\n  console.error(`[${correlationId}] Slack validation error:`, error);\n  \n  return [{\n    json: {\n      success: false,\n      error: 'INTERNAL_ERROR',\n      message: 'Internal Slack validation error',\n      correlation_id: correlationId,\n      timestamp: new Date().toISOString(),\n      details: {\n        error: error.message,\n        stack: error.stack\n      }\n    }\n  }];\n}"}, "id": "request-validator", "name": "✅ Validator: <PERSON><PERSON>ck Request", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [460, 300]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "validation-success", "leftValue": "={{ $json.validation_status }}", "rightValue": "success", "operator": {"type": "string", "operation": "equals"}}], "combinator": "and"}, "options": {}}, "id": "validation-check", "name": "🔍 Check: Validation Status", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [680, 300]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "escalation-created", "leftValue": "={{ $json.notification_type }}", "rightValue": "escalation_created", "operator": {"type": "string", "operation": "equals"}}, {"id": "agent-assigned", "leftValue": "={{ $json.notification_type }}", "rightValue": "agent_assigned", "operator": {"type": "string", "operation": "equals"}}, {"id": "status-changed", "leftValue": "={{ $json.notification_type }}", "rightValue": "status_changed", "operator": {"type": "string", "operation": "equals"}}, {"id": "supervisor-alert", "leftValue": "={{ $json.notification_type }}", "rightValue": "supervisor_alert", "operator": {"type": "string", "operation": "equals"}}], "combinator": "or"}, "options": {}}, "id": "notification-router", "name": "🔀 Router: Notification Type", "type": "n8n-nodes-base.switch", "typeVersion": 3, "position": [900, 300]}, {"parameters": {"jsCode": "// =====================================================\n// SLACK - ESCALATION CREATED NOTIFICATION\n// =====================================================\n\nconst notificationData = $input.first().json;\nconst escalationData = notificationData.escalation_data;\nconst correlationId = notificationData.correlation_id;\n\ntry {\n  // Mapear urgência para emoji e cor\n  function getUrgencyDisplay(urgency) {\n    const mapping = {\n      'low': { emoji: '🟢', color: '#36a64f', text: 'BAIXA' },\n      'medium': { emoji: '🟡', color: '#ffb347', text: 'MÉDIA' },\n      'high': { emoji: '🟠', color: '#ff6b35', text: 'ALTA' },\n      'critical': { emoji: '🔴', color: '#ff0000', text: 'CRÍTICA' }\n    };\n    return mapping[urgency] || mapping['medium'];\n  }\n  \n  // Determinar canal baseado na urgência e especialização\n  function getTargetChannel(urgency, specialization) {\n    if (urgency === 'critical') {\n      return process.env.SLACK_CRITICAL_CHANNEL || process.env.SLACK_DEFAULT_CHANNEL;\n    }\n    \n    const channelMapping = {\n      'technical': process.env.SLACK_TECHNICAL_CHANNEL,\n      'billing': process.env.SLACK_BILLING_CHANNEL,\n      'sales': process.env.SLACK_SALES_CHANNEL,\n      'support': process.env.SLACK_SUPPORT_CHANNEL,\n      'compliance': process.env.SLACK_COMPLIANCE_CHANNEL,\n      'legal': process.env.SLACK_LEGAL_CHANNEL,\n      'product': process.env.SLACK_PRODUCT_CHANNEL\n    };\n    \n    return channelMapping[specialization] || process.env.SLACK_DEFAULT_CHANNEL;\n  }\n  \n  const urgencyDisplay = getUrgencyDisplay(escalationData.urgency_level);\n  const targetChannel = notificationData.channel || getTargetChannel(\n    escalationData.urgency_level,\n    escalationData.specialization_required\n  );\n  \n  // Construir mensagem Slack\n  const slackMessage = {\n    channel: targetChannel,\n    username: 'Escalation Agent',\n    icon_emoji: ':warning:',\n    attachments: [\n      {\n        color: urgencyDisplay.color,\n        title: `${urgencyDisplay.emoji} Nova Escalação Humana Criada`,\n        title_link: `${process.env.DASHBOARD_URL}/escalations/${escalationData.escalation_id}`,\n        fields: [\n          {\n            title: 'ID da Escalação',\n            value: `#${notificationData.escalation_id}`,\n            short: true\n          },\n          {\n            title: 'Urgência',\n            value: `${urgencyDisplay.emoji} ${urgencyDisplay.text}`,\n            short: true\n          },\n          {\n            title: 'Especialização',\n            value: escalationData.specialization_required || 'Geral',\n            short: true\n          },\n          {\n            title: 'Score IA',\n            value: `${escalationData.ai_analysis_score || 'N/A'}/100`,\n            short: true\n          },\n          {\n            title: 'Motivo da Escalação',\n            value: escalationData.escalation_reason.substring(0, 200) + (escalationData.escalation_reason.length > 200 ? '...' : ''),\n            short: false\n          },\n          {\n            title: 'Fila Atribuída',\n            value: escalationData.assigned_queue || 'Não atribuída',\n            short: true\n          },\n          {\n            title: 'Agente Atribuído',\n            value: escalationData.assigned_human || 'Aguardando atribuição',\n            short: true\n          }\n        ],\n        footer: 'Sistema de Escalação Inteligente',\n        footer_icon: 'https://platform.slack-edge.com/img/default_application_icon.png',\n        ts: Math.floor(Date.now() / 1000),\n        actions: [\n          {\n            type: 'button',\n            text: 'Ver Detalhes',\n            url: `${process.env.DASHBOARD_URL}/escalations/${notificationData.escalation_id}`,\n            style: 'primary'\n          },\n          {\n            type: 'button',\n            text: 'Atribuir Agente',\n            name: 'assign_agent',\n            value: notificationData.escalation_id.toString(),\n            style: 'default'\n          }\n        ]\n      }\n    ]\n  };\n  \n  // Adicionar menção para escalações críticas\n  if (escalationData.urgency_level === 'critical') {\n    slackMessage.text = `<!channel> 🚨 **ESCALAÇÃO CRÍTICA** - Atenção imediata necessária!`;\n  }\n  \n  console.log(`[${correlationId}] Slack escalation notification prepared`, {\n    channel: targetChannel,\n    urgency: escalationData.urgency_level,\n    escalationId: notificationData.escalation_id\n  });\n  \n  return [{\n    json: {\n      ...notificationData,\n      slack_message: slackMessage,\n      target_channel: targetChannel,\n      notification_prepared: true\n    }\n  }];\n  \n} catch (error) {\n  console.error(`[${correlationId}] Error preparing Slack notification:`, error);\n  \n  return [{\n    json: {\n      ...notificationData,\n      error: 'SLACK_PREPARATION_ERROR',\n      message: 'Failed to prepare Slack notification',\n      details: {\n        error: error.message,\n        stack: error.stack\n      }\n    }\n  }];\n}"}, "id": "escalation-created-prep", "name": "📝 Prepare: Escalation Created", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1120, 200]}, {"parameters": {"jsCode": "// =====================================================\n// SLACK - SUPERVISOR ALERT NOTIFICATION\n// =====================================================\n\nconst notificationData = $input.first().json;\nconst escalationData = notificationData.escalation_data;\nconst correlationId = notificationData.correlation_id;\n\ntry {\n  // Preparar alerta para supervisor\n  const supervisorChannel = process.env.SLACK_SUPERVISOR_CHANNEL || process.env.SLACK_DEFAULT_CHANNEL;\n  \n  const slackMessage = {\n    channel: supervisorChannel,\n    username: 'Escalation Supervisor Alert',\n    icon_emoji: ':rotating_light:',\n    text: '🚨 **ALERTA DE SUPERVISÃO** - Intervenção necessária!',\n    attachments: [\n      {\n        color: '#ff0000',\n        title: '⚠️ Escalação Requer Supervisão',\n        fields: [\n          {\n            title: 'ID da Escalação',\n            value: `#${notificationData.escalation_id}`,\n            short: true\n          },\n          {\n            title: 'Tipo de Alerta',\n            value: notificationData.custom_message || 'Supervisão necessária',\n            short: true\n          },\n          {\n            title: 'Urgência',\n            value: `🔴 ${escalationData.urgency_level?.toUpperCase() || 'ALTA'}`,\n            short: true\n          },\n          {\n            title: 'Tempo Decorrido',\n            value: escalationData.created_at ? \n              `${Math.floor((Date.now() - new Date(escalationData.created_at).getTime()) / (1000 * 60))} minutos` :\n              'N/A',\n            short: true\n          },\n          {\n            title: 'Status Atual',\n            value: escalationData.status || 'Pendente',\n            short: true\n          },\n          {\n            title: 'Agente Atribuído',\n            value: escalationData.assigned_human || 'Não atribuído',\n            short: true\n          }\n        ],\n        footer: 'Sistema de Escalação - Alerta de Supervisão',\n        ts: Math.floor(Date.now() / 1000),\n        actions: [\n          {\n            type: 'button',\n            text: 'Intervir Agora',\n            url: `${process.env.DASHBOARD_URL}/escalations/${notificationData.escalation_id}?supervisor=true`,\n            style: 'danger'\n          },\n          {\n            type: 'button',\n            text: 'Reatribuir',\n            name: 'reassign_escalation',\n            value: notificationData.escalation_id.toString(),\n            style: 'primary'\n          }\n        ]\n      }\n    ]\n  };\n  \n  console.log(`[${correlationId}] Slack supervisor alert prepared`, {\n    channel: supervisorChannel,\n    escalationId: notificationData.escalation_id\n  });\n  \n  return [{\n    json: {\n      ...notificationData,\n      slack_message: slackMessage,\n      target_channel: supervisorChannel,\n      notification_prepared: true\n    }\n  }];\n  \n} catch (error) {\n  console.error(`[${correlationId}] Error preparing supervisor alert:`, error);\n  \n  return [{\n    json: {\n      ...notificationData,\n      error: 'SUPERVISOR_ALERT_ERROR',\n      message: 'Failed to prepare supervisor alert',\n      details: {\n        error: error.message,\n        stack: error.stack\n      }\n    }\n  }];\n}"}, "id": "supervisor-alert-prep", "name": "🚨 Prepare: Supervisor Alert", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1120, 500]}, {"parameters": {"url": "={{ process.env.SLACK_WEBHOOK_URL }}", "sendBody": true, "specifyBody": "json", "jsonBody": "={{ $json.slack_message }}", "options": {"timeout": 30000, "retry": {"enabled": true, "maxTries": 3}}}, "id": "slack-send-api", "name": "📤 API: Send Slack Message", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1340, 300]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "INSERT INTO agent.notification_logs (escalation_id, notification_type, channel, status, sent_at, correlation_id, metadata) VALUES ($1, $2, $3, 'sent', NOW(), $4, $5)", "options": {"queryParameters": "={{ [\n            $json.escalation_id,\n            $json.notification_type,\n            $json.target_channel,\n            $json.correlation_id,\n            JSON.stringify({\n              slack_response: $('slack-send-api').first().json,\n              priority: $json.priority,\n              recipient_type: $json.recipient_type\n            })\n          ] }}"}}, "id": "log-notification", "name": "📝 Log: Notification Sent", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [1560, 300], "credentials": {"postgres": {"id": "postgres-main", "name": "PostgreSQL - Main Database"}}}, {"parameters": {"respondWith": "json", "responseBody": "={{ {\n  success: true,\n  message: 'Slack notification sent successfully',\n  data: {\n    escalation_id: $json.escalation_id,\n    notification_type: $json.notification_type,\n    channel: $json.target_channel,\n    correlation_id: $json.correlation_id,\n    sent_at: new Date().toISOString()\n  }\n} }}"}, "id": "success-response", "name": "✅ Response: Success", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1780, 300]}, {"parameters": {"respondWith": "json", "responseBody": "={{ $json }}", "options": {"responseCode": 400}}, "id": "error-response", "name": "❌ Response: Error", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [680, 500]}], "connections": {"webhook-trigger": {"main": [[{"node": "request-validator", "type": "main", "index": 0}]]}, "request-validator": {"main": [[{"node": "validation-check", "type": "main", "index": 0}]]}, "validation-check": {"main": [[{"node": "notification-router", "type": "main", "index": 0}], [{"node": "error-response", "type": "main", "index": 0}]]}, "notification-router": {"main": [[{"node": "escalation-created-prep", "type": "main", "index": 0}], [], [], [{"node": "supervisor-alert-prep", "type": "main", "index": 0}]]}, "escalation-created-prep": {"main": [[{"node": "slack-send-api", "type": "main", "index": 0}]]}, "supervisor-alert-prep": {"main": [[{"node": "slack-send-api", "type": "main", "index": 0}]]}, "slack-send-api": {"main": [[{"node": "log-notification", "type": "main", "index": 0}]]}, "log-notification": {"main": [[{"node": "success-response", "type": "main", "index": 0}]]}}, "pinData": {}, "settings": {"executionOrder": "v1"}, "staticData": null, "tags": [{"createdAt": "2024-01-15T10:00:00.000Z", "updatedAt": "2024-01-15T10:00:00.000Z", "id": "escalation", "name": "escalation"}, {"createdAt": "2024-01-15T10:00:00.000Z", "updatedAt": "2024-01-15T10:00:00.000Z", "id": "slack", "name": "slack"}, {"createdAt": "2024-01-15T10:00:00.000Z", "updatedAt": "2024-01-15T10:00:00.000Z", "id": "notification", "name": "notification"}], "triggerCount": 0, "updatedAt": "2024-01-15T10:00:00.000Z", "versionId": "1"}