{"name": "[TOOL] Google Calendar Engine", "nodes": [{"parameters": {}, "id": "start_node", "name": "Start", "type": "n8n-nodes-base.start", "typeVersion": 1, "position": [400, 300]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT credentials FROM agent.user_credentials WHERE contact_id = $1 AND service = 'google' AND ('https://www.googleapis.com/auth/calendar.events' = ANY(scopes) OR 'https://www.googleapis.com/auth/calendar' = ANY(scopes) OR 'https://mail.google.com/' = ANY(scopes));", "options": {"parameters": {"values": ["={{$json.params.contactId}}"]}}}, "id": "db_get_credentials", "name": "DB: Get Google Credentials", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [620, 300], "notes": "Busca as credenciais OAuth do usuário que autorizou o acesso ao Google Calendar.", "credentials": {"postgres": {"id": "YOUR_POSTGRES_CREDENTIAL_ID"}}}, {"parameters": {"conditions": {"string": [{"value1": "={{$json.credentials}}", "operation": "isEmpty"}]}}, "id": "if_no_credentials", "name": "IF: No Credentials or Scope", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [840, 300]}, {"parameters": {"functionCode": "return { json: { error: 'Permissão para criar eventos no Google Calendar não concedida. Por favor, autorize o acesso.' } };", "options": {}}, "id": "return_auth_error", "name": "Return Auth Error", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1060, 400]}, {"parameters": {"authentication": "oAuth2", "calendar": "primary", "resource": "event", "operation": "create", "title": "={{$json.params.summary}}", "startDateTime": "={{$json.params.startDateTime}}", "endDateTime": "={{$json.params.endDateTime}}", "options": {"sendUpdates": "all"}}, "id": "google_calendar_create_event", "name": "Google Calendar: Create Event", "type": "n8n-nodes-base.googleCalendar", "typeVersion": 3, "position": [1060, 200], "notes": "Cria o evento na agenda do usuário usando as credenciais dinâmicas.", "credentials": {"googleCalendarApi": {"id": "={{$json.credentials.id}}", "data": "={{$json.credentials.data}}"}}}], "connections": {"start_node": {"main": [[{"node": "db_get_credentials"}]]}, "db_get_credentials": {"main": [[{"node": "if_no_credentials"}]]}, "if_no_credentials": {"main": [[{"node": "google_calendar_create_event"}], [{"node": "return_auth_error"}]]}}}