{"name": "[SYNC] Product & Stock Management", "nodes": [{"parameters": {"rule": "cron", "cronTime": "*/15 * * * *"}, "id": "schedule_trigger", "name": "TRIGGER: Every 15 Mins", "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1.1, "position": [400, 100]}, {"parameters": {}, "id": "manual_trigger", "name": "TRIGGER: Manual Sync", "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [400, 300]}, {"parameters": {}, "id": "merge_triggers", "name": "MERGE: Triggers", "type": "n8n-nodes-base.merge", "typeVersion": 2, "position": [600, 200]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT p.id as product_id, p.sku, p.stock_quantity, ml.marketplace_name, ml.marketplace_product_id\nFROM agent.products p\nJOIN agent.marketplace_listings ml ON p.id = ml.product_id;", "options": {}}, "id": "get_products", "name": "DB: Get All Product Listings", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [800, 200], "credentials": {"postgres": {"id": "YOUR_POSTGRES_CREDENTIAL_ID"}}}, {"parameters": {"batchSize": 1, "options": {}}, "id": "loop_listings", "name": "Loop Over Each Listing", "type": "n8n-nodes-base.splitInBatches", "typeVersion": 2, "position": [1000, 200]}, {"parameters": {"routing": {"rules": {"values": [{"value1": "={{$json.marketplace_name}}", "value2": "Mercado <PERSON>"}, {"value1": "={{$json.marketplace_name}}", "value2": "Amazon"}, {"value1": "={{$json.marketplace_name}}", "value2": "<PERSON>ee"}]}}}, "id": "switch_marketplace", "name": "SWITCH: Marketplace", "type": "n8n-nodes-base.switch", "typeVersion": 2, "position": [1200, 200]}, {"parameters": {"method": "PUT", "url": "https://api.mercadolibre.com/items/{{$json.marketplace_product_id}}", "sendBody": true, "body": "={{ ({ available_quantity: $json.stock_quantity }) }}", "options": {}, "continueOnFail": true}, "id": "http_meli", "name": "HTTP: Sync Mercado Livre Stock", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [1400, 0], "continueOnFail": true, "notes": "PLACEHOLDER: Requer autenticação OAuth 2.0 do Mercado Livre.", "credentials": {"httpHeaderAuth": {"id": "YOUR_MELI_OAUTH_CREDENTIAL_ID"}}}, {"parameters": {"method": "PATCH", "url": "https://sellingpartnerapi.amazon.com/listings/2021-08-01/items/{{$json.sellerId}}/{{$json.sku}}", "options": {}, "continueOnFail": true}, "id": "http_amazon", "name": "HTTP: Sync Amazon Stock", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [1400, 200], "continueOnFail": true, "notes": "PLACEHOLDER: <PERSON>quer autenticação AWS Signature V4 complexa."}, {"parameters": {"method": "POST", "url": "={{$credentials.slackWebhook.url}}", "sendBody": true, "body": "={{ ({ \"text\": `🚨 ALERTA: Falha ao sincronizar estoque!\\n\\nNão foi possível sincronizar o SKU: ${$json.sku} para o marketplace: ${$json.marketplace_name}. INTERVENÇÃO MANUAL NECESSÁRIA.` }) }}", "options": {}}, "id": "slack_notify_error", "name": "SLACK: Notify Sync Error", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [1400, 400], "credentials": {"httpHeaderAuth": {"id": "YOUR_SLACK_WEBHOOK_CREDENTIAL"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "UPDATE agent.marketplace_listings SET last_sync = NOW() WHERE product_id = $1 AND marketplace_name = $2;", "options": {"parameters": {"values": ["={{$json.product_id}}", "={{$json.marketplace_name}}"]}}}, "id": "db_update_sync", "name": "DB: Update Sync Timestamp", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [1600, 200], "credentials": {"postgres": {"id": "YOUR_POSTGRES_CREDENTIAL_ID"}}}], "connections": {"schedule_trigger": {"main": [[{"node": "merge_triggers", "type": "main", "index": 0}]]}, "manual_trigger": {"main": [[{"node": "merge_triggers", "type": "main", "index": 1}]]}, "merge_triggers": {"main": [[{"node": "get_products", "type": "main", "index": 0}]]}, "get_products": {"main": [[{"node": "loop_listings", "type": "main", "index": 0}]]}, "loop_listings": {"main": [[{"node": "switch_marketplace", "type": "main", "index": 0}]]}, "switch_marketplace": {"main": [[{"node": "http_meli", "type": "main", "index": 0}], [{"node": "http_amazon", "type": "main", "index": 0}]]}, "http_meli": {"main": [[{"node": "db_update_sync", "type": "main", "index": 0}]], "error": [[{"node": "slack_notify_error", "type": "error", "index": 0}]]}, "http_amazon": {"main": [[{"node": "db_update_sync", "type": "main", "index": 0}]], "error": [[{"node": "slack_notify_error", "type": "error", "index": 0}]]}}}