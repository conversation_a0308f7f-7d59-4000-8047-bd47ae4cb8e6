{"name": "Agente de IA com Servidores MCP", "nodes": [{"parameters": {"httpMethod": "POST", "path": "b3ea2895-6fee-4583-bf47-4ff1dc384d68", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-840, 0], "id": "ef31551c-c22a-4864-ab9b-181d8de6a05a", "name": "Webhook", "webhookId": "b3ea2895-6fee-4583-bf47-4ff1dc384d68"}, {"parameters": {"model": {"__rl": true, "value": "gpt-4.1-mini", "mode": "list", "cachedResultName": "gpt-4.1-mini"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [680, 240], "id": "2a47f1aa-0b51-4eaf-92e3-cedabe62cb98", "name": "OpenAI Chat Model", "credentials": {"openAiApi": {"id": "CuWaB2tKGSdlqxVC", "name": "OpenAi account"}}}, {"parameters": {"sessionIdType": "customKey", "sessionKey": "={{ $('Webhook').item.json.body.data.key.remoteJid }}", "contextWindowLength": 20}, "type": "@n8n/n8n-nodes-langchain.memoryPostgresChat", "typeVersion": 1.3, "position": [840, 240], "id": "2f7090f8-a8c2-467b-b895-37ab3a8db5a4", "name": "Postgres Chat Memory", "credentials": {"postgres": {"id": "gGThQqNeQcfuTLVT", "name": "Postgres | Siha"}}}, {"parameters": {"descriptionType": "manual", "toolDescription": "Use para saber quais ferramentas relacionadas ao Airbnb você tem à disposição."}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [1220, 260], "id": "********-399a-42a7-b0a6-60877faf4965", "name": "Airbnb List Tools", "credentials": {"mcpClientApi": {"id": "TfiQLAGRnwJyjYCI", "name": "MCP: Airbnb"}}}, {"parameters": {"descriptionType": "manual", "toolDescription": "Use quando você for executar alguma ferramenta do airbnb.", "operation": "executeTool", "toolName": "={{ $fromAI(\"tool_name\", \"Nome da ferramenta a ser executada\", \"string\") }}", "toolParameters": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Tool_Parameters', `Parâmet<PERSON> da ferramenta`, 'json') }}"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [1360, 260], "id": "3fa2ff14-45bc-4c74-8cbe-2eb035497e2d", "name": "Airbnb Execute Tools", "credentials": {"mcpClientApi": {"id": "TfiQLAGRnwJyjYCI", "name": "MCP: Airbnb"}}}, {"parameters": {"descriptionType": "manual", "toolDescription": "Use para listar as ferramentas disponíveis para poder pesquisar na internet."}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [1220, 540], "id": "e1578d85-3fa3-4612-bb06-4ad1e0fb0756", "name": "<PERSON><PERSON> <PERSON>", "credentials": {"mcpClientApi": {"id": "LVdJ9jZn82yNZ2RF", "name": "MCP: <PERSON><PERSON>"}}}, {"parameters": {"descriptionType": "manual", "toolDescription": "Use para executar as ferramentas disponíveis para poder pesquisar na internet.", "operation": "executeTool", "toolName": "={{ $fromAI(\"tool_name\", \"Nome da ferramenta que será executada\", \"string\") }}", "toolParameters": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Tool_Parameters', `Parâmet<PERSON> da ferramenta`, 'json') }}"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [1360, 540], "id": "70570e11-dc49-4348-91f0-5b0ced118ba0", "name": "<PERSON><PERSON> Ex<PERSON>ute <PERSON>ls", "credentials": {"mcpClientApi": {"id": "LVdJ9jZn82yNZ2RF", "name": "MCP: <PERSON><PERSON>"}}}, {"parameters": {"content": "## Airbnb Tools\nRepositório do servidor [aqui](https://github.com/openbnb-org/mcp-server-airbnb)", "height": 260, "width": 460, "color": 4}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [1020, 180], "id": "9c69b797-3663-4452-bc7f-697d79e15f0d", "name": "<PERSON><PERSON>"}, {"parameters": {"content": "## <PERSON><PERSON> Tools\nRepositório do servidor [aqui](https://github.com/tavily-ai/tavily-mcp)", "height": 260, "width": 460, "color": 5}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [1020, 460], "id": "505cfee0-a07e-4051-bd7a-52ede2d70778", "name": "Sticky Note1"}, {"parameters": {"content": "## C<PERSON><PERSON>bro", "height": 220, "width": 380, "color": 3}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [580, 180], "id": "4528f038-e331-492f-903e-700002deef92", "name": "Sticky Note2"}, {"parameters": {"content": "## Tratamento de Dados", "height": 640, "width": 800, "color": 6}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-200, -300], "id": "5f814a6d-cbbe-4343-8c0a-2a617dc08b4e", "name": "Sticky Note3"}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{ $json.body.data.messageType }}", "rightValue": "conversation", "operator": {"type": "string", "operation": "equals"}, "id": "e015fcd2-e945-4d57-af67-a0059156aa74"}], "combinator": "and"}, "renameOutput": true, "outputKey": "Texto"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "a05b5da9-e6f2-4c20-b0de-d4e10e6d0d62", "leftValue": "={{ $json.body.data.messageType }}", "rightValue": "audioMessage", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "<PERSON><PERSON><PERSON>"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "df3f6803-7f5f-4417-8a75-02df480fb7d8", "leftValue": "={{ $json.body.data.messageType }}", "rightValue": "imageMessage", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "Imagem"}]}, "options": {}}, "type": "n8n-nodes-base.switch", "typeVersion": 3.2, "position": [-400, -20], "id": "a104e5be-55e5-4946-92fa-951496a545c8", "name": "Qual tipo de dado?"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "fc8d2c42-17c3-438f-b87b-c307680299f7", "leftValue": "={{ $json.body.data.key.fromMe }}", "rightValue": "", "operator": {"type": "boolean", "operation": "false", "singleValue": true}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [-640, 0], "id": "49627c06-c2d5-4f93-aeb8-ba06205f1487", "name": "Mensagem não é minha"}, {"parameters": {"assignments": {"assignments": [{"id": "86d13735-39c8-4f6f-8d28-82b42b84dd99", "name": "content", "value": "={{ $json.body.data.message.conversation }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [100, -220], "id": "ed6e784c-0163-4d52-a348-cf96ce2cc251", "name": "Definindo mensagem de texto"}, {"parameters": {"operation": "toBinary", "sourceProperty": "body.data.message.base64", "options": {}}, "type": "n8n-nodes-base.convertToFile", "typeVersion": 1.1, "position": [-120, 140], "id": "a321f5f2-3122-4805-a3cf-85ef766546d1", "name": "Base64 pra binário"}, {"parameters": {"operation": "toBinary", "sourceProperty": "body.data.message.base64", "options": {"fileName": "data", "mimeType": "audio/ogg"}}, "type": "n8n-nodes-base.convertToFile", "typeVersion": 1.1, "position": [-120, -40], "id": "17c054a1-e265-4fd4-8768-3dc671fcc61d", "name": "base64 pra binário"}, {"parameters": {"resource": "audio", "operation": "transcribe", "options": {}}, "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.8, "position": [100, -40], "id": "88230c45-2c5e-4d45-83ea-f29643a025e6", "name": "Transcrição do áudio", "credentials": {"openAiApi": {"id": "CuWaB2tKGSdlqxVC", "name": "OpenAi account"}}}, {"parameters": {"resource": "image", "operation": "analyze", "modelId": {"__rl": true, "value": "gpt-4o-mini", "mode": "list", "cachedResultName": "GPT-4O-MINI"}, "text": "Descreva todos os elementos presentes na imagem, inclusive tudo o que está escrito. Inclusive, nome de marcas, apps etc.", "inputType": "base64", "options": {}}, "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.8, "position": [300, 140], "id": "8794cacb-c5dc-4f43-8980-84b81f34c7e7", "name": "Analisador de Imagens", "credentials": {"openAiApi": {"id": "CuWaB2tKGSdlqxVC", "name": "OpenAi account"}}}, {"parameters": {"assignments": {"assignments": [{"id": "8b910260-e778-4b34-9697-6c85b79b7d39", "name": "content", "value": "={{ $json.text }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [300, -40], "id": "e1043629-3992-4f40-936c-************", "name": "Definindo mensagem de áudio"}, {"parameters": {"promptType": "define", "text": "={{$json.content}}", "options": {"systemMessage": "=Você é um assistente pessoal do Anwar Hermuche.\n\n# Ferramentas\nAntes de pesquisar na internet, use a ferramenta \"list_internet_tools\". Depois que já souber quais ferramentas você tem acesso para pesquisar na internet, use \"execute_internet_tools\".\n\nO mesmo para pesquisar sobre Airbnbs. Use o \"list_airbnb_tools\" primeiro e depois use o \"execute_airbnb_tools\".\n\n\n# REGRAS\n- Data e hora atual: {{ $now.format(\"dd/MM/yyyy HH:mm:ss\") }}, horário de Brasília.\n- Não use markdown. Use *negrito* e _italico_. Além disso, bullet points são permitidos também."}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.9, "position": [720, -40], "id": "bc0f4af2-f883-44c9-83a1-3af15921b87b", "name": "Agente de IA"}, {"parameters": {"resource": "messages-api", "instanceName": "<PERSON>e <PERSON>", "remoteJid": "={{ $('Webhook').item.json.body.data.key.remoteJid.split(\"@\")[0] }}", "messageText": "={{ $json.output }}", "options_message": {}}, "type": "n8n-nodes-evolution-api.evolutionApi", "typeVersion": 1, "position": [1180, -40], "id": "6eb1b0d5-9eea-414d-89d9-14d6f8998654", "name": "API WhatsApp", "credentials": {"evolutionApi": {"id": "zPJ29D1i4ThQcFtH", "name": "Evolution account"}}}, {"parameters": {"content": "## Recebimento dos dados", "height": 340, "width": 620, "color": 7}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-880, -140], "id": "875189c3-c0e2-492f-8988-398fc2536841", "name": "Sticky Note4"}], "pinData": {}, "connections": {"Webhook": {"main": [[{"node": "Mensagem não é minha", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "Agente de IA", "type": "ai_languageModel", "index": 0}]]}, "Postgres Chat Memory": {"ai_memory": [[{"node": "Agente de IA", "type": "ai_memory", "index": 0}]]}, "Airbnb List Tools": {"ai_tool": [[{"node": "Agente de IA", "type": "ai_tool", "index": 0}]]}, "Airbnb Execute Tools": {"ai_tool": [[{"node": "Agente de IA", "type": "ai_tool", "index": 0}]]}, "Tavily List Tools": {"ai_tool": [[{"node": "Agente de IA", "type": "ai_tool", "index": 0}]]}, "Tavily Execute Tools": {"ai_tool": [[{"node": "Agente de IA", "type": "ai_tool", "index": 0}]]}, "Qual tipo de dado?": {"main": [[{"node": "Definindo mensagem de texto", "type": "main", "index": 0}], [{"node": "base64 pra binário", "type": "main", "index": 0}], [{"node": "Base64 pra binário", "type": "main", "index": 0}]]}, "Mensagem não é minha": {"main": [[{"node": "Qual tipo de dado?", "type": "main", "index": 0}]]}, "Definindo mensagem de texto": {"main": [[{"node": "Agente de IA", "type": "main", "index": 0}]]}, "Base64 pra binário": {"main": [[{"node": "Analisador de Imagens", "type": "main", "index": 0}]]}, "base64 pra binário": {"main": [[{"node": "Transcrição do áudio", "type": "main", "index": 0}]]}, "Transcrição do áudio": {"main": [[{"node": "Definindo mensagem de áudio", "type": "main", "index": 0}]]}, "Analisador de Imagens": {"main": [[{"node": "Agente de IA", "type": "main", "index": 0}]]}, "Definindo mensagem de áudio": {"main": [[{"node": "Agente de IA", "type": "main", "index": 0}]]}, "Agente de IA": {"main": [[{"node": "API WhatsApp", "type": "main", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1"}, "versionId": "466c3197-3a75-47d3-b457-5fdcf9eeb44d", "meta": {"templateCredsSetupCompleted": true, "instanceId": "8f08bc4de954eba538bc7c426d06b49bcfd62adfd8636b43e1fc355b04452366"}, "id": "HIOfHfCPaQWCg1rm", "tags": []}