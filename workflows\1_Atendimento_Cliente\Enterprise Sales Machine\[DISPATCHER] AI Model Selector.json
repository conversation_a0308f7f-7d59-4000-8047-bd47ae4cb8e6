{
  "name": "[DISPATCHER] AI Model Selector",
  "nodes": [
    {
      "parameters": {
        "workflowId": "={{ $env.EXECUTE_WORKFLOW_ID }}"
      },
      "id": "start-node",
      "name": "Start",
      "type": "n8n-nodes-base.start",
      "typeVersion": 1,
      "position": [
        240,
        640
      ]
    },
    {
      "parameters": {
        "functionCode": "const taskType = $json.body.task_type || 'default';\nconst prompt = $json.body.prompt;\n\nif (!prompt) {\n  throw new Error('O parâmetro \\"prompt\\" é obrigatório.');\n}\n\n// Mapeamento de task_type para modelos e provedores\n// Esta lógica pode ser expandida com mais modelos e regras\nconst routingMap = {\n  'simple_task': { provider: 'OpenRouter', model: 'anthropic/claude-3-haiku', cost_input: 0.********, cost_output: 0.******** },\n  'complex_analysis': { provider: 'OpenAI', model: 'gpt-4-turbo', cost_input: 0.00001, cost_output: 0.00003 },\n  'default': { provider: 'OpenAI', model: 'gpt-3.5-turbo', cost_input: 0.0000005, cost_output: 0.0000015 }\n};\n\nconst route = routingMap[taskType] || routingMap['default'];\n\n// Prepara os dados para o próximo nó\n$json.ai_route = route;\n$json.prompt = prompt;\n\nreturn $items;"
      },
      "id": "prepare-routing",
      "name": "Prepare AI Routing",
      "type": "n8n-nodes-base.function",
      "typeVersion": 2,
      "position": [
        440,
        640
      ],
      "notes": "Define qual modelo de IA usar com base no tipo de tarefa."
    },
    {
      "parameters": {
        "url": "={{ $json.ai_route.provider === 'OpenRouter' ? 'https://openrouter.ai/api/v1/chat/completions' : 'https://api.openai.com/v1/chat/completions' }}",
        "authentication": "headerAuth",
        "sendHeaders": true,
        "headerParameters": {
          "parameters": [
            {
              "name": "Authorization",
              "value": "={{ $json.ai_route.provider === 'OpenRouter' ? 'Bearer ' + $credentials.openRouterModule.apiKey : 'Bearer ' + $credentials.openAiApi.apiKey }}"
            }
          ]
        },
        "sendBody": true,
        "bodyParameters": {
          "parameters": [
            {
              "name": "model",
              "value": "={{$json.ai_route.model}}"
            },
            {
              "name": "messages",
              "value": [
                {
                  "role": "user",
                  "content": "={{$json.prompt}}"
                }
              ]
            }
          ]
        },
        "options": {}
      },
      "id": "call-ai-model",
      "name": "Call AI Model",
      "type": "n8n-nodes-base.httpRequest",
      "typeVersion": 4.1,
      "position": [
        640,
        640
      ],
      "credentials": {
        "httpHeaderAuth": [
          {
            "id": "openRouterModule",
            "name": "OpenRouter API"
          },
          {
            "id": "openAiApi",
            "name": "OpenAI API"
          }
        ]
      }
    },
    {
      "parameters": {
        "workflowId": "={{ $env.AI_LOGGER_WORKFLOW_ID }}",
        "data": "={{ { body: { \n    calling_workflow_id: $json.body.calling_workflow_id, \n    calling_node_id: $json.body.calling_node_id, \n    task_type: $json.body.task_type, \n    provider: $json.ai_route.provider, \n    model_used: $json.ai_route.model, \n    prompt_tokens: $json.usage.prompt_tokens, \n    completion_tokens: $json.usage.completion_tokens, \n    total_tokens: $json.usage.total_tokens, \n    estimated_cost: ($json.usage.prompt_tokens / 1000 * $json.ai_route.cost_input) + ($json.usage.completion_tokens / 1000 * $json.ai_route.cost_output),\n    response_time_ms: $json.response_time_ms \n} } }}",
        "waitFor": "nothing"
      },
      "id": "log-ai-usage",
      "name": "Log AI Usage",
      "type": "n8n-nodes-base.executeWorkflow",
      "typeVersion": 2,
      "position": [
        840,
        640
      ],
      "notes": "Chama um sub-workflow para registrar a chamada de IA no DB."
    },
    {
      "parameters": {
        "options": {}
      },
      "id": "return-response",
      "name": "Return AI Response",
      "type": "n8n-nodes-base.workflow-proxy-passthrough",
      "typeVersion": 1,
      "position": [
        1040,
        640
      ]
    }
  ],
  "connections": {
    "start-node": {
      "main": [
        [
          {
            "node": "prepare-routing",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "prepare-routing": {
      "main": [
        [
          {
            "node": "call-ai-model",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "call-ai-model": {
      "main": [
        [
          {
            "node": "log-ai-usage",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "log-ai-usage": {
      "main": [
        [
          {
            "node": "return-response",
            "type": "main",
            "index": 0
          }
        ]
      ]
    }
  },
  "pinData": {}
} 