{"meta": {"instanceId": "HUMAN_IN_THE_LOOP_ESCALATION_MANAGER_V2"}, "name": "[ESCALATION] Human-In-The-Loop Manager v2.0 - Enterprise", "nodes": [{"parameters": {"path": "escalate-to-human", "responseMode": "onReceived", "options": {}}, "id": "trigger_escalation_request", "name": "TRIGGER: Escalation Request", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [140, 300], "notes": "Recebe: { contact_id, escalation_reason, urgency_level, context_data, agent_identity, conversation_history }"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "has_contact_id", "leftValue": "={{ $json.body.contact_id }}", "rightValue": "", "operator": {"type": "string", "operation": "isNotEmpty"}}, {"id": "has_escalation_reason", "leftValue": "={{ $json.body.escalation_reason }}", "rightValue": "", "operator": {"type": "string", "operation": "isNotEmpty"}}], "combineOperation": "and"}}, "id": "validate_escalation_request", "name": "✅ Validate Escalation Request", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [360, 300]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "-- Verificar se existe escalation ativa para este contato\nSELECT \n  id, \n  status, \n  created_at,\n  escalation_reason\nFROM agent.human_escalations \nWHERE contact_id = $1 \nAND status IN ('pending', 'assigned_to_human', 'in_progress')\nORDER BY created_at DESC \nLIMIT 1;", "options": {"parameters": {"values": ["={{ $json.body.contact_id }}"]}}}, "id": "check_existing_escalation", "name": "🔍 Check Existing Escalation", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [580, 200], "credentials": {"postgres": {"id": "postgres-main", "name": "PostgreSQL Main"}}, "continueOnFail": true}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "no_active_escalation", "leftValue": "={{ $json.id }}", "rightValue": "", "operator": {"type": "string", "operation": "isEmpty"}}]}}, "id": "if_no_active_escalation", "name": "IF: No Active Escalation?", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [800, 200]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "-- Buscar dados completos do contato e histórico\nSELECT \n  c.id,\n  c.nome,\n  c.telefone,\n  c.email,\n  c.empresa_atual,\n  c.jornada_status,\n  c.engagement_score,\n  c.tags,\n  c.enriched_data,\n  c.created_at,\n  -- Calcular score de prioridade\n  CASE \n    WHEN c.jornada_status = 'convertido' THEN 10\n    WHEN c.jornada_status = 'engajado' THEN 8\n    WHEN c.jornada_status = 'interessado' THEN 6\n    WHEN c.jornada_status = 'prospect' THEN 4\n    ELSE 2\n  END + \n  CASE \n    WHEN c.engagement_score > 80 THEN 5\n    WHEN c.engagement_score > 60 THEN 3\n    WHEN c.engagement_score > 40 THEN 1\n    ELSE 0\n  END as priority_score\nFROM agent.contatos c\nWHERE c.id = $1;", "options": {"parameters": {"values": ["={{ $json.body.contact_id }}"]}}}, "id": "get_contact_details", "name": "📋 Get Contact Details & Priority", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [1020, 200], "credentials": {"postgres": {"id": "postgres-main", "name": "PostgreSQL Main"}}, "continueOnFail": true}, {"parameters": {"operation": "execute<PERSON>uery", "query": "-- Buscar histórico de interações recentes\nSELECT \n  ci.message_sent,\n  ci.interaction_type,\n  ci.achieved_goal,\n  ci.created_at,\n  ai.display_name as agent_name,\n  ai.category as agent_category\nFROM agent.campaign_interactions ci\nLEFT JOIN agent.agent_identities ai ON ci.identity_specialist_id = ai.id\nWHERE ci.contact_id = $1\nORDER BY ci.created_at DESC\nLIMIT 10;", "options": {"parameters": {"values": ["={{ $json.body.contact_id }}"]}}}, "id": "get_interaction_history", "name": "📈 Get Interaction History", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [1020, 380], "credentials": {"postgres": {"id": "postgres-main", "name": "PostgreSQL Main"}}, "continueOnFail": true}, {"parameters": {"operation": "execute<PERSON>uery", "query": "-- Verificar disponibilidade de agentes humanos por fila\nSELECT \n  queue_name,\n  available_agents,\n  current_queue_size,\n  avg_response_time_minutes,\n  specialization,\n  priority_threshold\nFROM agent.human_queues\nWHERE status = 'active'\nORDER BY \n  CASE queue_name\n    WHEN 'vip_support' THEN 1\n    WHEN 'technical_support' THEN 2\n    WHEN 'sales_specialist' THEN 3\n    WHEN 'general_support' THEN 4\n    ELSE 5\n  END;", "options": {}}, "id": "check_human_queue_availability", "name": "👥 Check Human Queue Availability", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [1240, 200], "credentials": {"postgres": {"id": "postgres-main", "name": "PostgreSQL Main"}}, "continueOnFail": true}, {"parameters": {"workflowId": "={{ $env.DISPATCHER_WORKFLOW_ID }}", "data": "={{ {\n  body: {\n    prompt: `SISTEMA DE TRIAGEM INTELIGENTE\\n\\n**CONTEXTO DO ESCALATION:**\\n• Motivo: ${$('trigger_escalation_request').item.json.body.escalation_reason}\\n• Urgência: ${$('trigger_escalation_request').item.json.body.urgency_level || 'medium'}\\n• Agente que escalou: ${$('trigger_escalation_request').item.json.body.agent_identity}\\n\\n**PERFIL DO CLIENTE:**\\n• Nome: ${$('get_contact_details').item.json.nome || 'N/A'}\\n• Status: ${$('get_contact_details').item.json.jornada_status || 'unknown'}\\n• Empresa: ${$('get_contact_details').item.json.empresa_atual || 'N/A'}\\n• Score de Engajamento: ${$('get_contact_details').item.json.engagement_score || 0}\\n• Score de Prioridade: ${$('get_contact_details').item.json.priority_score || 0}\\n\\n**HISTÓRICO RECENTE:**\\n${$('get_interaction_history').all().map(h => `• ${h.json.agent_name || 'Unknown'}: ${h.json.message_sent?.substring(0, 100) || 'No message'}...`).join('\\n')}\\n\\n**FILAS DISPONÍVEIS:**\\n${$('check_human_queue_availability').all().map(q => `• ${q.json.queue_name}: ${q.json.available_agents} agentes (${q.json.current_queue_size} na fila)`).join('\\n')}\\n\\n**MISSÃO:**\\nAnalise o contexto e determine:\\n1. Qual fila humana é mais adequada\\n2. Nível de prioridade (1-5)\\n3. Resumo executivo para o agente humano\\n4. Ações recomendadas\\n\\nResposta em JSON:\\n{\\n  \\\"recommended_queue\\\": \\\"nome_da_fila\\\",\\n  \\\"priority_level\\\": 1-5,\\n  \\\"executive_summary\\\": \\\"resumo para o agente humano\\\",\\n  \\\"recommended_actions\\\": [\\\"ação 1\\\", \\\"ação 2\\\"],\\n  \\\"estimated_resolution_time\\\": \\\"tempo estimado\\\",\\n  \\\"requires_specialist\\\": true/false,\\n  \\\"context_tags\\\": [\\\"tag1\\\", \\\"tag2\\\"]\\n}`,\n    task_type: 'complex_analysis',\n    calling_workflow_id: $workflow.id,\n    calling_node_id: 'ai_triage_analysis'\n  }\n} }}", "options": {}}, "id": "ai_triage_analysis", "name": "🤖 AI: Intelligent Triage Analysis", "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 2, "position": [1460, 200], "continueOnFail": true}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "ai_analysis_success", "leftValue": "={{ $json.choices?.[0]?.message?.content }}", "rightValue": "", "operator": {"type": "string", "operation": "isNotEmpty"}}]}}, "id": "if_ai_analysis_success", "name": "IF: AI Analysis Success?", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [1680, 200]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "-- C<PERSON>r ticket de escalation no sistema\nINSERT INTO agent.human_escalations (\n  contact_id,\n  escalation_reason,\n  urgency_level,\n  assigned_queue,\n  priority_level,\n  executive_summary,\n  context_data,\n  agent_identity_source,\n  status,\n  estimated_resolution_time\n)\nVALUES (\n  $1,\n  $2,\n  $3,\n  $4,\n  $5,\n  $6,\n  $7::jsonb,\n  $8,\n  'pending',\n  $9\n)\nRETURNING *;", "options": {"parameters": {"values": ["={{ $('trigger_escalation_request').item.json.body.contact_id }}", "={{ $('trigger_escalation_request').item.json.body.escalation_reason }}", "={{ $('trigger_escalation_request').item.json.body.urgency_level || 'medium' }}", "={{ JSON.parse($('ai_triage_analysis').item.json.choices[0].message.content).recommended_queue || 'general_support' }}", "={{ JSON.parse($('ai_triage_analysis').item.json.choices[0].message.content).priority_level || 3 }}", "={{ JSON.parse($('ai_triage_analysis').item.json.choices[0].message.content).executive_summary || 'Escalation automático necessário' }}", "={{ JSON.stringify({\n                contact_details: $('get_contact_details').item.json,\n                interaction_history: $('get_interaction_history').all(),\n                escalation_context: $('trigger_escalation_request').item.json.body,\n                ai_analysis: JSON.parse($('ai_triage_analysis').item.json.choices[0].message.content),\n                queue_availability: $('check_human_queue_availability').all()\n              }) }}", "={{ $('trigger_escalation_request').item.json.body.agent_identity }}", "={{ JSON.parse($('ai_triage_analysis').item.json.choices[0].message.content).estimated_resolution_time || '2 horas' }}"]}}}, "id": "create_escalation_ticket", "name": "🎫 Create Escalation Ticket", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [1900, 200], "credentials": {"postgres": {"id": "postgres-main", "name": "PostgreSQL Main"}}, "continueOnFail": true}, {"parameters": {"method": "POST", "url": "={{ $vars.CHATWOOT_API_URL }}/api/v1/accounts/{{ $vars.CHATWOOT_ACCOUNT_ID }}/conversations", "authentication": "headerAuth", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "api_access_token", "value": "={{ $vars.CHATWOOT_API_TOKEN }}"}, {"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "{\n  \"source_id\": \"{{ $('get_contact_details').item.json.telefone || $('get_contact_details').item.json.email || 'unknown' }}\",\n  \"inbox_id\": {{ $vars.CHATWOOT_ESCALATION_INBOX_ID || $vars.CHATWOOT_DEFAULT_INBOX_ID }},\n  \"contact\": {\n    \"name\": \"{{ $('get_contact_details').item.json.nome || 'Cliente' }}\",\n    \"email\": \"{{ $('get_contact_details').item.json.email || '' }}\",\n    \"phone_number\": \"{{ $('get_contact_details').item.json.telefone || '' }}\",\n    \"custom_attributes\": {\n      \"company\": \"{{ $('get_contact_details').item.json.empresa_atual || '' }}\",\n      \"journey_status\": \"{{ $('get_contact_details').item.json.jornada_status || 'unknown' }}\",\n      \"engagement_score\": {{ $('get_contact_details').item.json.engagement_score || 0 }},\n      \"priority_score\": {{ $('get_contact_details').item.json.priority_score || 0 }},\n      \"escalation_id\": {{ $('create_escalation_ticket').item.json.id }},\n      \"escalation_reason\": \"{{ $('trigger_escalation_request').item.json.body.escalation_reason }}\",\n      \"agent_source\": \"{{ $('trigger_escalation_request').item.json.body.agent_identity }}\"\n    }\n  },\n  \"message\": {\n    \"content\": \"🚨 **ESCALATION AUTOMÁTICO**\\n\\n**Cliente:** {{ $('get_contact_details').item.json.nome || 'N/A' }}\\n**Empresa:** {{ $('get_contact_details').item.json.empresa_atual || 'N/A' }}\\n**Motivo:** {{ $('trigger_escalation_request').item.json.body.escalation_reason }}\\n**Urgência:** {{ $('trigger_escalation_request').item.json.body.urgency_level || 'medium' }}\\n\\n**RESUMO EXECUTIVO:**\\n{{ JSON.parse($('ai_triage_analysis').item.json.choices[0].message.content).executive_summary || 'Análise automática necessária' }}\\n\\n**AÇÕES RECOMENDADAS:**\\n{{ (JSON.parse($('ai_triage_analysis').item.json.choices[0].message.content).recommended_actions || ['Verificar contexto', 'Responder ao cliente']).map(action => `• ${action}`).join('\\n') }}\\n\\n**CONTEXTO COMPLETO:** Ticket #{{ $('create_escalation_ticket').item.json.id }}\",\n    \"message_type\": \"incoming\"\n  },\n  \"status\": \"open\",\n  \"priority\": \"{{ (JSON.parse($('ai_triage_analysis').item.json.choices[0].message.content).priority_level || 3) === 5 ? 'urgent' : (JSON.parse($('ai_triage_analysis').item.json.choices[0].message.content).priority_level || 3) >= 3 ? 'high' : 'medium' }}\"\n}", "options": {}}, "id": "create_chatwoot_conversation", "name": "💬 Create Chatwoot Conversation", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [2120, 200], "continueOnFail": true}, {"parameters": {"method": "POST", "url": "={{ $vars.CHATWOOT_API_URL }}/api/v1/accounts/{{ $vars.CHATWOOT_ACCOUNT_ID }}/conversations/{{ $json.id }}/assignments", "authentication": "headerAuth", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "api_access_token", "value": "={{ $vars.CHATWOOT_API_TOKEN }}"}, {"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "{\n  \"assignee_id\": null,\n  \"team_id\": {{ $vars[`CHATWOOT_TEAM_${(JSON.parse($('ai_triage_analysis').item.json.choices[0].message.content).recommended_queue || 'general_support').toUpperCase()}_ID`] || $vars.CHATWOOT_DEFAULT_TEAM_ID || 1 }}\n}", "options": {}}, "id": "assign_to_team_queue", "name": "👥 Assign to Team Queue", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [2340, 200], "continueOnFail": true}, {"parameters": {"operation": "execute<PERSON>uery", "query": "-- Atualizar ticket com ID da conversa do Chatwoot\nUPDATE agent.human_escalations \nSET \n  chatwoot_conversation_id = $1,\n  status = 'assigned_to_human',\n  assigned_at = NOW()\nWHERE id = $2\nRETURNING *;", "options": {"parameters": {"values": ["={{ $('create_chatwoot_conversation').item.json.id || null }}", "={{ $('create_escalation_ticket').item.json.id }}"]}}}, "id": "update_escalation_with_chatwoot", "name": "🔄 Update Escalation with Chatwoot ID", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [2560, 200], "credentials": {"postgres": {"id": "postgres-main", "name": "PostgreSQL Main"}}, "continueOnFail": true}, {"parameters": {"operation": "execute<PERSON>uery", "query": "-- Pausar agentes autônomos para este contato\nINSERT INTO agent.contact_automation_status (\n  contact_id,\n  automation_paused,\n  paused_reason,\n  paused_by,\n  escalation_id\n)\nVALUES (\n  $1,\n  true,\n  'human_escalation',\n  'escalation_system',\n  $2\n)\nON CONFLICT (contact_id) \nDO UPDATE SET \n  automation_paused = true,\n  paused_reason = 'human_escalation',\n  paused_at = NOW(),\n  escalation_id = $2;", "options": {"parameters": {"values": ["={{ $('trigger_escalation_request').item.json.body.contact_id }}", "={{ $('create_escalation_ticket').item.json.id }}"]}}}, "id": "pause_autonomous_agents", "name": "⏸️ Pause Autonomous Agents", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [2560, 380], "credentials": {"postgres": {"id": "postgres-main", "name": "PostgreSQL Main"}}, "continueOnFail": true}, {"parameters": {"method": "POST", "url": "={{ $vars.SLACK_WEBHOOK_URL }}", "sendBody": true, "specifyBody": "json", "jsonBody": "{\n  \"text\": \"🚨 Escalation para Atendimento Humano\",\n  \"blocks\": [\n    {\n      \"type\": \"header\",\n      \"text\": {\n        \"type\": \"plain_text\",\n        \"text\": \"🚨 ESCALATION PARA ATENDIMENTO HUMANO\"\n      }\n    },\n    {\n      \"type\": \"section\",\n      \"fields\": [\n        {\n          \"type\": \"mrkdwn\",\n          \"text\": \"*👤 Cliente:*\\n{{ $('get_contact_details').item.json.nome || 'N/A' }}\"\n        },\n        {\n          \"type\": \"mrkdwn\",\n          \"text\": \"*🏢 Empresa:*\\n{{ $('get_contact_details').item.json.empresa_atual || 'N/A' }}\"\n        },\n        {\n          \"type\": \"mrkdwn\",\n          \"text\": \"*🚨 Motivo:*\\n{{ $('trigger_escalation_request').item.json.body.escalation_reason }}\"\n        },\n        {\n          \"type\": \"mrkdwn\",\n          \"text\": \"*⚡ Urgência:*\\n{{ $('trigger_escalation_request').item.json.body.urgency_level || 'medium' }}\"\n        },\n        {\n          \"type\": \"mrkdwn\",\n          \"text\": \"*🎯 Fila Recomendada:*\\n{{ JSON.parse($('ai_triage_analysis').item.json.choices[0].message.content).recommended_queue || 'general_support' }}\"\n        },\n        {\n          \"type\": \"mrkdwn\",\n          \"text\": \"*📊 Score de Prioridade:*\\n{{ $('get_contact_details').item.json.priority_score || 0 }}/15\"\n        }\n      ]\n    },\n    {\n      \"type\": \"section\",\n      \"text\": {\n        \"type\": \"mrkdwn\",\n        \"text\": \"*🤖 Agente que Escalou:*\\n{{ $('trigger_escalation_request').item.json.body.agent_identity }}\\n\\n*📋 Resumo Executivo:*\\n{{ JSON.parse($('ai_triage_analysis').item.json.choices[0].message.content).executive_summary || 'Análise automática necessária' }}\"\n      }\n    },\n    {\n      \"type\": \"section\",\n      \"text\": {\n        \"type\": \"mrkdwn\",\n        \"text\": \"*🎯 Ações Recomendadas:*\\n{{ (JSON.parse($('ai_triage_analysis').item.json.choices[0].message.content).recommended_actions || ['Verificar contexto', 'Responder ao cliente']).map(action => `• ${action}`).join('\\n') }}\"\n      }\n    },\n    {\n      \"type\": \"section\",\n      \"text\": {\n        \"type\": \"mrkdwn\",\n        \"text\": \"*💬 Chatwoot:* <{{ $vars.CHATWOOT_BASE_URL || 'https://chatwoot.local' }}/app/accounts/{{ $vars.CHATWOOT_ACCOUNT_ID || 1 }}/conversations/{{ $('create_chatwoot_conversation').item.json.id || 'N/A' }}|Abrir Conversa>\\n*🎫 Ticket:* #{{ $('create_escalation_ticket').item.json.id }}\\n*⏱️ Tempo Estimado:* {{ JSON.parse($('ai_triage_analysis').item.json.choices[0].message.content).estimated_resolution_time || '2 horas' }}\"\n      }\n    },\n    {\n      \"type\": \"context\",\n      \"elements\": [\n        {\n          \"type\": \"mrkdwn\",\n          \"text\": \"⏸️ Agentes autônomos pausados para este contato | Sistema aguardando resolução humana\"\n        }\n      ]\n    }\n  ],\n  \"channel\": \"#human-escalations\"\n}", "options": {}}, "id": "notify_escalation_team", "name": "📱 Notify Escalation Team", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [2780, 200], "continueOnFail": true}, {"parameters": {"content": "{\n  \"escalation_id\": {{ $('create_escalation_ticket').item.json.id || 'null' }},\n  \"chatwoot_conversation_id\": {{ $('create_chatwoot_conversation').item.json.id || 'null' }},\n  \"status\": \"escalated_successfully\",\n  \"assigned_queue\": \"{{ JSON.parse($('ai_triage_analysis').item.json.choices[0].message.content).recommended_queue || 'general_support' }}\",\n  \"priority_level\": {{ JSON.parse($('ai_triage_analysis').item.json.choices[0].message.content).priority_level || 3 }},\n  \"estimated_resolution_time\": \"{{ JSON.parse($('ai_triage_analysis').item.json.choices[0].message.content).estimated_resolution_time || '2 horas' }}\",\n  \"autonomous_agents_paused\": true,\n  \"next_steps\": \"Human agent will handle via Chatwoot. System will monitor for resolution.\"\n}", "options": {}}, "id": "respond_escalation_success", "name": "✅ Respond: Escalation Success", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [3000, 200]}, {"parameters": {"content": "{\n  \"error\": \"escalation_failed\",\n  \"reason\": \"AI analysis failed or missing required data\",\n  \"contact_id\": {{ $('trigger_escalation_request').item.json.body.contact_id || 'null' }},\n  \"escalation_reason\": \"{{ $('trigger_escalation_request').item.json.body.escalation_reason || 'unknown' }}\",\n  \"fallback_action\": \"Manual review required\"\n}", "options": {}}, "id": "respond_escalation_fallback", "name": "⚠️ Respond: Escalation Fallback", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1680, 400]}, {"parameters": {"content": "{\n  \"error\": \"escalation_already_exists\",\n  \"existing_escalation_id\": {{ $('check_existing_escalation').item.json.id || 'null' }},\n  \"existing_status\": \"{{ $('check_existing_escalation').item.json.status || 'unknown' }}\",\n  \"message\": \"Active escalation already exists for this contact\"\n}", "options": {}}, "id": "respond_escalation_exists", "name": "🔄 Respond: Escalation Already Exists", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [800, 400]}, {"parameters": {"content": "{\n  \"error\": \"invalid_escalation_request\",\n  \"message\": \"Missing required fields: contact_id and/or escalation_reason\",\n  \"received_data\": {{ JSON.stringify($('trigger_escalation_request').item.json.body) }}\n}", "options": {}}, "id": "respond_validation_error", "name": "❌ Respond: Validation Error", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [360, 500]}], "connections": {"trigger_escalation_request": {"main": [[{"node": "validate_escalation_request", "type": "main", "index": 0}]]}, "validate_escalation_request": {"main": [[{"node": "check_existing_escalation", "type": "main", "index": 0}], [{"node": "respond_validation_error", "type": "main", "index": 0}]]}, "check_existing_escalation": {"main": [[{"node": "if_no_active_escalation", "type": "main", "index": 0}]]}, "if_no_active_escalation": {"main": [[{"node": "get_contact_details", "type": "main", "index": 0}, {"node": "get_interaction_history", "type": "main", "index": 0}], [{"node": "respond_escalation_exists", "type": "main", "index": 0}]]}, "get_contact_details": {"main": [[{"node": "check_human_queue_availability", "type": "main", "index": 0}]]}, "get_interaction_history": {"main": [[{"node": "check_human_queue_availability", "type": "main", "index": 1}]]}, "check_human_queue_availability": {"main": [[{"node": "ai_triage_analysis", "type": "main", "index": 0}]]}, "ai_triage_analysis": {"main": [[{"node": "if_ai_analysis_success", "type": "main", "index": 0}]]}, "if_ai_analysis_success": {"main": [[{"node": "create_escalation_ticket", "type": "main", "index": 0}], [{"node": "respond_escalation_fallback", "type": "main", "index": 0}]]}, "create_escalation_ticket": {"main": [[{"node": "create_chatwoot_conversation", "type": "main", "index": 0}]]}, "create_chatwoot_conversation": {"main": [[{"node": "assign_to_team_queue", "type": "main", "index": 0}]]}, "assign_to_team_queue": {"main": [[{"node": "update_escalation_with_chatwoot", "type": "main", "index": 0}]]}, "update_escalation_with_chatwoot": {"main": [[{"node": "pause_autonomous_agents", "type": "main", "index": 0}]]}, "pause_autonomous_agents": {"main": [[{"node": "notify_escalation_team", "type": "main", "index": 0}]]}, "notify_escalation_team": {"main": [[{"node": "respond_escalation_success", "type": "main", "index": 0}]]}}, "settings": {"executionOrder": "v1", "saveManualExecutions": true, "callerPolicy": "workflowsFromSameOwner", "errorWorkflow": {"id": "escalation_error_handler"}}, "staticData": null, "tags": [{"createdAt": "2024-01-15T20:00:00.000Z", "updatedAt": "2024-01-15T20:00:00.000Z", "id": "human-escalation", "name": "human-escalation"}, {"createdAt": "2024-01-15T20:00:00.000Z", "updatedAt": "2024-01-15T20:00:00.000Z", "id": "enterprise-grade", "name": "enterprise-grade"}, {"createdAt": "2024-01-15T20:00:00.000Z", "updatedAt": "2024-01-15T20:00:00.000Z", "id": "error-resilient", "name": "error-resilient"}], "triggerCount": 1, "updatedAt": "2024-01-15T20:00:00.000Z", "versionId": "2"}