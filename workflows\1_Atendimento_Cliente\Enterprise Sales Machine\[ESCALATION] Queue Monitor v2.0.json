{"name": "[ESCALATION] Queue Monitor v2.0", "nodes": [{"parameters": {"rule": {"interval": [{"field": "minutes", "minutesInterval": 5}]}}, "id": "queue-monitor-cron", "name": "⏰ Queue Monitor Schedule", "type": "n8n-nodes-base.cron", "typeVersion": 1, "position": [240, 300]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "-- Verificar filas ativas e suas métricas\nSELECT \n  q.queue_id,\n  q.queue_name,\n  q.queue_type,\n  q.max_capacity,\n  q.current_load,\n  q.status as queue_status,\n  q.priority_weight,\n  COUNT(e.escalation_id) as pending_escalations,\n  AVG(EXTRACT(EPOCH FROM (NOW() - e.created_at))/60) as avg_wait_time_minutes,\n  COUNT(CASE WHEN e.priority = 'critical' THEN 1 END) as critical_count,\n  COUNT(CASE WHEN e.priority = 'high' THEN 1 END) as high_count,\n  CASE \n    WHEN q.current_load >= q.max_capacity * 0.9 THEN 'overloaded'\n    WHEN q.current_load >= q.max_capacity * 0.7 THEN 'high_load'\n    WHEN q.current_load >= q.max_capacity * 0.5 THEN 'medium_load'\n    ELSE 'low_load'\n  END as load_status,\n  CASE \n    WHEN AVG(EXTRACT(EPOCH FROM (NOW() - e.created_at))/60) > 30 THEN 'sla_risk'\n    WHEN AVG(EXTRACT(EPOCH FROM (NOW() - e.created_at))/60) > 15 THEN 'sla_warning'\n    ELSE 'sla_ok'\n  END as sla_status\nFROM agent.escalation_queues q\nLEFT JOIN agent.escalations e ON q.queue_id = e.assigned_queue_id \n  AND e.status IN ('pending', 'in_progress')\nWHERE q.status = 'active'\nGROUP BY q.queue_id, q.queue_name, q.queue_type, q.max_capacity, q.current_load, q.status, q.priority_weight\nORDER BY \n  CASE \n    WHEN q.current_load >= q.max_capacity * 0.9 THEN 1\n    WHEN AVG(EXTRACT(EPOCH FROM (NOW() - e.created_at))/60) > 30 THEN 2\n    ELSE 3\n  END,\n  q.priority_weight DESC;", "options": {}}, "id": "get-queue-metrics", "name": "📊 Get Queue Metrics", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [460, 300], "credentials": {"postgres": {"id": "postgres-escalation-db", "name": "PostgreSQL Escalation DB"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "has-queues", "leftValue": "={{ $json.length }}", "rightValue": 0, "operator": {"type": "number", "operation": "gt"}}], "combinator": "and"}, "options": {}}, "id": "check-queues-exist", "name": "❓ Queues Exist?", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [680, 300]}, {"parameters": {"jsCode": "// Processar métricas das filas e identificar problemas\nconst queues = $input.all();\nconst alerts = [];\nconst metrics = {\n  total_queues: queues.length,\n  overloaded_queues: 0,\n  high_load_queues: 0,\n  sla_risk_queues: 0,\n  total_pending: 0,\n  total_critical: 0,\n  avg_wait_time: 0\n};\n\nlet totalWaitTime = 0;\nlet queuesWithWait = 0;\n\nfor (const queue of queues) {\n  const queueData = queue.json;\n  \n  // Atualizar métricas gerais\n  metrics.total_pending += queueData.pending_escalations || 0;\n  metrics.total_critical += queueData.critical_count || 0;\n  \n  if (queueData.avg_wait_time_minutes) {\n    totalWaitTime += queueData.avg_wait_time_minutes;\n    queuesWithWait++;\n  }\n  \n  // Verificar status de carga\n  if (queueData.load_status === 'overloaded') {\n    metrics.overloaded_queues++;\n    alerts.push({\n      type: 'queue_overloaded',\n      severity: 'critical',\n      queue_id: queueData.queue_id,\n      queue_name: queueData.queue_name,\n      message: `Fila ${queueData.queue_name} está sobrecarregada (${queueData.current_load}/${queueData.max_capacity})`,\n      current_load: queueData.current_load,\n      max_capacity: queueData.max_capacity,\n      pending_escalations: queueData.pending_escalations\n    });\n  } else if (queueData.load_status === 'high_load') {\n    metrics.high_load_queues++;\n    alerts.push({\n      type: 'queue_high_load',\n      severity: 'warning',\n      queue_id: queueData.queue_id,\n      queue_name: queueData.queue_name,\n      message: `Fila ${queueData.queue_name} com alta carga (${queueData.current_load}/${queueData.max_capacity})`,\n      current_load: queueData.current_load,\n      max_capacity: queueData.max_capacity,\n      pending_escalations: queueData.pending_escalations\n    });\n  }\n  \n  // Verificar SLA\n  if (queueData.sla_status === 'sla_risk') {\n    metrics.sla_risk_queues++;\n    alerts.push({\n      type: 'sla_risk',\n      severity: 'critical',\n      queue_id: queueData.queue_id,\n      queue_name: queueData.queue_name,\n      message: `Fila ${queueData.queue_name} em risco de SLA (tempo médio: ${Math.round(queueData.avg_wait_time_minutes)}min)`,\n      avg_wait_time: queueData.avg_wait_time_minutes,\n      pending_escalations: queueData.pending_escalations,\n      critical_count: queueData.critical_count\n    });\n  } else if (queueData.sla_status === 'sla_warning') {\n    alerts.push({\n      type: 'sla_warning',\n      severity: 'warning',\n      queue_id: queueData.queue_id,\n      queue_name: queueData.queue_name,\n      message: `Fila ${queueData.queue_name} com tempo de espera elevado (${Math.round(queueData.avg_wait_time_minutes)}min)`,\n      avg_wait_time: queueData.avg_wait_time_minutes,\n      pending_escalations: queueData.pending_escalations\n    });\n  }\n  \n  // Verificar escalações críticas acumuladas\n  if (queueData.critical_count >= 5) {\n    alerts.push({\n      type: 'critical_backlog',\n      severity: 'critical',\n      queue_id: queueData.queue_id,\n      queue_name: queueData.queue_name,\n      message: `Fila ${queueData.queue_name} com ${queueData.critical_count} escalações críticas pendentes`,\n      critical_count: queueData.critical_count,\n      pending_escalations: queueData.pending_escalations\n    });\n  }\n}\n\n// Calcular tempo médio de espera\nif (queuesWithWait > 0) {\n  metrics.avg_wait_time = totalWaitTime / queuesWithWait;\n}\n\n// Determinar status geral do sistema\nlet system_status = 'healthy';\nif (metrics.overloaded_queues > 0 || metrics.sla_risk_queues > 0) {\n  system_status = 'critical';\n} else if (metrics.high_load_queues > 0 || alerts.some(a => a.severity === 'warning')) {\n  system_status = 'warning';\n}\n\nreturn [{\n  json: {\n    timestamp: new Date().toISOString(),\n    system_status,\n    metrics,\n    alerts,\n    queues: queues.map(q => q.json),\n    summary: {\n      total_alerts: alerts.length,\n      critical_alerts: alerts.filter(a => a.severity === 'critical').length,\n      warning_alerts: alerts.filter(a => a.severity === 'warning').length,\n      needs_immediate_attention: system_status === 'critical'\n    }\n  }\n}];"}, "id": "process-queue-metrics", "name": "⚙️ Process Queue Metrics", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [900, 200]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "has-alerts", "leftValue": "={{ $json.alerts.length }}", "rightValue": 0, "operator": {"type": "number", "operation": "gt"}}], "combinator": "and"}, "options": {}}, "id": "check-alerts-exist", "name": "🚨 Alerts Exist?", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [1120, 200]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "-- Registrar alertas no sistema\nINSERT INTO agent.system_alerts (\n  alert_type,\n  severity,\n  message,\n  metadata,\n  source,\n  status,\n  created_at\n)\nSELECT \n  alert_data->>'type' as alert_type,\n  alert_data->>'severity' as severity,\n  alert_data->>'message' as message,\n  alert_data as metadata,\n  'queue_monitor' as source,\n  'active' as status,\n  NOW() as created_at\nFROM (\n  SELECT jsonb_array_elements($1::jsonb) as alert_data\n) alerts\nWHERE NOT EXISTS (\n  SELECT 1 FROM agent.system_alerts sa\n  WHERE sa.alert_type = alert_data->>'type'\n    AND sa.metadata->>'queue_id' = alert_data->>'queue_id'\n    AND sa.status = 'active'\n    AND sa.created_at > NOW() - INTERVAL '1 hour'\n)\nRETURNING alert_id, alert_type, severity, message;", "options": {"queryReplacement": "={{ JSON.stringify($json.alerts) }}"}}, "id": "create-system-alerts", "name": "📝 Create System Alerts", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [1340, 100], "credentials": {"postgres": {"id": "postgres-escalation-db", "name": "PostgreSQL Escalation DB"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "is-critical", "leftValue": "={{ $('process-queue-metrics').item.json.summary.critical_alerts }}", "rightValue": 0, "operator": {"type": "number", "operation": "gt"}}], "combinator": "and"}, "options": {}}, "id": "check-critical-alerts", "name": "🔴 Critical Alerts?", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [1340, 300]}, {"parameters": {"url": "={{ $vars.SLACK_WEBHOOK_URL }}", "options": {}, "bodyParametersUi": {"parameter": [{"name": "text", "value": "🚨 *ALERTA CRÍTICO - Sistema de Filas*"}, {"name": "attachments", "value": "={{ JSON.stringify([{\n  color: 'danger',\n  fields: [\n    {\n      title: 'Status do Sistema',\n      value: $('process-queue-metrics').item.json.system_status.toUpperCase(),\n      short: true\n    },\n    {\n      title: 'Alertas Críticos',\n      value: $('process-queue-metrics').item.json.summary.critical_alerts.toString(),\n      short: true\n    },\n    {\n      title: '<PERSON><PERSON> Sobrecarregadas',\n      value: $('process-queue-metrics').item.json.metrics.overloaded_queues.toString(),\n      short: true\n    },\n    {\n      title: 'Escalações Pendentes',\n      value: $('process-queue-metrics').item.json.metrics.total_pending.toString(),\n      short: true\n    }\n  ],\n  text: $('process-queue-metrics').item.json.alerts\n    .filter(a => a.severity === 'critical')\n    .map(a => `• ${a.message}`)\n    .join('\\n')\n}]) }}"}]}}, "id": "send-critical-slack-alert", "name": "📢 Send Critical Slack <PERSON>", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1560, 200]}, {"parameters": {"url": "http://localhost:5678/webhook/escalation-intelligent-routing", "options": {}, "bodyParametersUi": {"parameter": [{"name": "trigger_type", "value": "queue_rebalancing"}, {"name": "queue_metrics", "value": "={{ JSON.stringify($('process-queue-metrics').item.json.queues) }}"}, {"name": "system_status", "value": "={{ $('process-queue-metrics').item.json.system_status }}"}, {"name": "overloaded_queues", "value": "={{ JSON.stringify($('process-queue-metrics').item.json.alerts.filter(a => a.type === 'queue_overloaded')) }}"}]}}, "id": "trigger-queue-rebalancing", "name": "⚖️ Trigger Queue Rebalancing", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1560, 400]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "-- Registrar métricas de monitoramento\nINSERT INTO agent.queue_monitoring_logs (\n  timestamp,\n  system_status,\n  total_queues,\n  overloaded_queues,\n  high_load_queues,\n  sla_risk_queues,\n  total_pending_escalations,\n  total_critical_escalations,\n  avg_wait_time_minutes,\n  total_alerts,\n  critical_alerts,\n  warning_alerts,\n  metrics_data\n) VALUES (\n  NOW(),\n  $1,\n  $2,\n  $3,\n  $4,\n  $5,\n  $6,\n  $7,\n  $8,\n  $9,\n  $10,\n  $11,\n  $12\n)\nRETURNING monitoring_log_id, timestamp;", "options": {"queryReplacement": "={{ $json.system_status }},{{ $json.metrics.total_queues }},{{ $json.metrics.overloaded_queues }},{{ $json.metrics.high_load_queues }},{{ $json.metrics.sla_risk_queues }},{{ $json.metrics.total_pending }},{{ $json.metrics.total_critical }},{{ Math.round($json.metrics.avg_wait_time) }},{{ $json.summary.total_alerts }},{{ $json.summary.critical_alerts }},{{ $json.summary.warning_alerts }},{{ JSON.stringify($json) }}"}}, "id": "log-monitoring-metrics", "name": "📊 Log Monitoring Metrics", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [1120, 500], "credentials": {"postgres": {"id": "postgres-escalation-db", "name": "PostgreSQL Escalation DB"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "-- Atualizar estatísticas das filas baseado no monitoramento\nUPDATE agent.escalation_queues \nSET \n  last_monitored_at = NOW(),\n  current_load = queue_stats.pending_count,\n  avg_wait_time_minutes = queue_stats.avg_wait,\n  updated_at = NOW()\nFROM (\n  SELECT \n    q.queue_id,\n    COUNT(e.escalation_id) as pending_count,\n    COALESCE(AVG(EXTRACT(EPOCH FROM (NOW() - e.created_at))/60), 0) as avg_wait\n  FROM agent.escalation_queues q\n  LEFT JOIN agent.escalations e ON q.queue_id = e.assigned_queue_id \n    AND e.status IN ('pending', 'in_progress')\n  WHERE q.status = 'active'\n  GROUP BY q.queue_id\n) queue_stats\nWHERE agent.escalation_queues.queue_id = queue_stats.queue_id\nRETURNING queue_id, queue_name, current_load, avg_wait_time_minutes;", "options": {}}, "id": "update-queue-stats", "name": "🔄 Update Queue Stats", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [1340, 500], "credentials": {"postgres": {"id": "postgres-escalation-db", "name": "PostgreSQL Escalation DB"}}}, {"parameters": {"jsCode": "// Gerar relatório de monitoramento\nconst data = $input.first().json;\nconst timestamp = new Date().toISOString();\n\nconst report = {\n  timestamp,\n  monitoring_cycle: 'completed',\n  system_health: {\n    status: data.system_status,\n    total_queues: data.metrics.total_queues,\n    healthy_queues: data.metrics.total_queues - data.metrics.overloaded_queues - data.metrics.high_load_queues,\n    problematic_queues: data.metrics.overloaded_queues + data.metrics.high_load_queues,\n    sla_compliance: data.metrics.sla_risk_queues === 0 ? 'good' : 'at_risk'\n  },\n  workload: {\n    total_pending: data.metrics.total_pending,\n    critical_pending: data.metrics.total_critical,\n    avg_wait_time: Math.round(data.metrics.avg_wait_time),\n    load_distribution: data.queues.map(q => ({\n      queue_name: q.queue_name,\n      load_percentage: Math.round((q.current_load / q.max_capacity) * 100),\n      pending_count: q.pending_escalations,\n      status: q.load_status\n    }))\n  },\n  alerts: {\n    total: data.summary.total_alerts,\n    critical: data.summary.critical_alerts,\n    warnings: data.summary.warning_alerts,\n    immediate_attention_required: data.summary.needs_immediate_attention\n  },\n  recommendations: []\n};\n\n// Gerar recomendações baseadas nos dados\nif (data.metrics.overloaded_queues > 0) {\n  report.recommendations.push({\n    type: 'capacity_scaling',\n    priority: 'high',\n    message: `${data.metrics.overloaded_queues} fila(s) sobrecarregada(s) - considere aumentar capacidade ou redistribuir carga`\n  });\n}\n\nif (data.metrics.sla_risk_queues > 0) {\n  report.recommendations.push({\n    type: 'sla_optimization',\n    priority: 'high',\n    message: `${data.metrics.sla_risk_queues} fila(s) em risco de SLA - priorize escalações críticas`\n  });\n}\n\nif (data.metrics.avg_wait_time > 20) {\n  report.recommendations.push({\n    type: 'performance_optimization',\n    priority: 'medium',\n    message: `Tempo médio de espera elevado (${Math.round(data.metrics.avg_wait_time)}min) - otimize roteamento`\n  });\n}\n\nif (data.metrics.total_critical > 10) {\n  report.recommendations.push({\n    type: 'critical_backlog',\n    priority: 'critical',\n    message: `${data.metrics.total_critical} escalações críticas pendentes - ação imediata necessária`\n  });\n}\n\nreturn [{ json: report }];"}, "id": "generate-monitoring-report", "name": "📋 Generate Monitoring Report", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1560, 500]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "-- Limpar alertas antigos resolvidos automaticamente\nUPDATE agent.system_alerts \nSET \n  status = 'auto_resolved',\n  resolved_at = NOW(),\n  updated_at = NOW()\nWHERE status = 'active'\n  AND alert_type IN ('queue_high_load', 'sla_warning')\n  AND created_at < NOW() - INTERVAL '30 minutes'\n  AND NOT EXISTS (\n    SELECT 1 FROM (\n      SELECT jsonb_array_elements($1::jsonb) as current_alert\n    ) ca\n    WHERE ca.current_alert->>'type' = agent.system_alerts.alert_type\n      AND ca.current_alert->>'queue_id' = agent.system_alerts.metadata->>'queue_id'\n  )\nRETURNING alert_id, alert_type, message;", "options": {"queryReplacement": "={{ JSON.stringify($('process-queue-metrics').item.json.alerts) }}"}}, "id": "cleanup-resolved-alerts", "name": "🧹 Cleanup Resolved Alerts", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [900, 500], "credentials": {"postgres": {"id": "postgres-escalation-db", "name": "PostgreSQL Escalation DB"}}}, {"parameters": {"jsCode": "// Log de monitoramento sem filas ativas\nreturn [{\n  json: {\n    timestamp: new Date().toISOString(),\n    status: 'no_active_queues',\n    message: 'Nenhuma fila ativa encontrada para monitoramento',\n    action: 'monitoring_skipped'\n  }\n}];"}, "id": "log-no-queues", "name": "📝 Log No Queues", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [680, 500]}], "pinData": {}, "connections": {"queue-monitor-cron": {"main": [[{"node": "get-queue-metrics", "type": "main", "index": 0}]]}, "get-queue-metrics": {"main": [[{"node": "check-queues-exist", "type": "main", "index": 0}]]}, "check-queues-exist": {"main": [[{"node": "process-queue-metrics", "type": "main", "index": 0}], [{"node": "log-no-queues", "type": "main", "index": 0}]]}, "process-queue-metrics": {"main": [[{"node": "check-alerts-exist", "type": "main", "index": 0}, {"node": "cleanup-resolved-alerts", "type": "main", "index": 0}]]}, "check-alerts-exist": {"main": [[{"node": "create-system-alerts", "type": "main", "index": 0}, {"node": "check-critical-alerts", "type": "main", "index": 0}], [{"node": "log-monitoring-metrics", "type": "main", "index": 0}]]}, "check-critical-alerts": {"main": [[{"node": "send-critical-slack-alert", "type": "main", "index": 0}, {"node": "trigger-queue-rebalancing", "type": "main", "index": 0}], []]}, "cleanup-resolved-alerts": {"main": [[{"node": "log-monitoring-metrics", "type": "main", "index": 0}]]}, "log-monitoring-metrics": {"main": [[{"node": "update-queue-stats", "type": "main", "index": 0}]]}, "update-queue-stats": {"main": [[{"node": "generate-monitoring-report", "type": "main", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1", "saveManualExecutions": true, "callerPolicy": "workflowsFromSameOwner", "errorWorkflow": "error-handler-workflow"}, "versionId": "queue-monitor-v2.0", "meta": {"templateCredsSetupCompleted": true, "instanceId": "escalation-queue-monitor"}, "id": "queue-monitor-workflow", "tags": [{"createdAt": "2024-01-15T10:00:00.000Z", "updatedAt": "2024-01-15T10:00:00.000Z", "id": "escalation-monitoring", "name": "escalation-monitoring"}]}