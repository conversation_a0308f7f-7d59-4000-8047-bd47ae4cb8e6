{"name": "[LEARNING] AI Feedback Learning Engine", "nodes": [{"parameters": {"httpMethod": "POST", "path": "ai-learning", "options": {}}, "id": "f47ac10b-58cc-4372-a567-0e02b2c3d479", "name": "🧠 Webhook - Learning Request", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300], "webhookId": "ai-learning"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "c1", "leftValue": "={{ $json.action }}", "rightValue": "process_feedback", "operator": {"type": "string", "operation": "equals"}}, {"id": "c2", "leftValue": "={{ $json.action }}", "rightValue": "update_prompts", "operator": {"type": "string", "operation": "equals"}}, {"id": "c3", "leftValue": "={{ $json.action }}", "rightValue": "generate_insights", "operator": {"type": "string", "operation": "equals"}}, {"id": "c4", "leftValue": "={{ $json.action }}", "rightValue": "optimize_model_selection", "operator": {"type": "string", "operation": "equals"}}], "combinator": "or"}, "options": {}}, "id": "8f14e45f-ceea-467a-9b87-7df96c6f135e", "name": "🔀 Route Action", "type": "n8n-nodes-base.switch", "typeVersion": 3, "position": [460, 300]}, {"parameters": {"operation": "select", "schema": {"value": "agent"}, "table": {"value": "human_feedback"}, "where": {"values": [{"column": "created_at", "condition": "greaterThan", "value": "={{ new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString() }}"}]}, "sort": {"values": [{"column": "created_at", "direction": "DESC"}]}, "limit": 50, "options": {}}, "id": "6ba7b810-9dad-11d1-80b4-00c04fd430c8", "name": "📝 Get Recent Feedback", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [680, 120], "credentials": {"postgres": {"id": "postgres-main", "name": "PostgreSQL - Main DB"}}}, {"parameters": {"jsCode": "// 🧠 Processar feedback humano para aprendizado da IA\nconst feedbackItems = $input.all();\n\nif (feedbackItems.length === 0) {\n  return [{\n    learning_insights: [],\n    patterns_identified: 0,\n    recommendations: ['Aguardando mais feedback para análise'],\n    confidence_score: 0\n  }];\n}\n\n// Analisar padrões de feedback\nconst feedbackAnalysis = {\n  approval_rate: 0,\n  rejection_reasons: {},\n  quality_trends: [],\n  common_issues: [],\n  improvement_areas: []\n};\n\nlet totalApprovals = 0;\nlet totalRejections = 0;\nlet qualityScores = [];\nlet brandAlignmentScores = [];\nlet engagementScores = [];\n\nfeedbackItems.forEach(item => {\n  const feedback = item.json;\n  \n  // Contar aprovações/rejeições\n  if (feedback.feedback_type === 'approval') {\n    totalApprovals++;\n  } else if (feedback.feedback_type === 'rejection') {\n    totalRejections++;\n    \n    // Analisar razões de rejeição\n    if (feedback.feedback_text) {\n      const reason = feedback.feedback_text.toLowerCase();\n      \n      // Categorizar razões comuns\n      if (reason.includes('marca') || reason.includes('brand')) {\n        feedbackAnalysis.rejection_reasons['brand_alignment'] = \n          (feedbackAnalysis.rejection_reasons['brand_alignment'] || 0) + 1;\n      }\n      if (reason.includes('qualidade') || reason.includes('quality')) {\n        feedbackAnalysis.rejection_reasons['quality_issues'] = \n          (feedbackAnalysis.rejection_reasons['quality_issues'] || 0) + 1;\n      }\n      if (reason.includes('tom') || reason.includes('tone')) {\n        feedbackAnalysis.rejection_reasons['tone_issues'] = \n          (feedbackAnalysis.rejection_reasons['tone_issues'] || 0) + 1;\n      }\n      if (reason.includes('conteúdo') || reason.includes('content')) {\n        feedbackAnalysis.rejection_reasons['content_issues'] = \n          (feedbackAnalysis.rejection_reasons['content_issues'] || 0) + 1;\n      }\n    }\n  }\n  \n  // Coletar scores\n  if (feedback.quality_score) qualityScores.push(feedback.quality_score);\n  if (feedback.brand_alignment_score) brandAlignmentScores.push(feedback.brand_alignment_score);\n  if (feedback.engagement_potential_score) engagementScores.push(feedback.engagement_potential_score);\n});\n\n// Calcular taxa de aprovação\nconst totalFeedback = totalApprovals + totalRejections;\nfeedbackAnalysis.approval_rate = totalFeedback > 0 ? (totalApprovals / totalFeedback) * 100 : 0;\n\n// Calcular médias dos scores\nconst avgQuality = qualityScores.length > 0 ? \n  qualityScores.reduce((a, b) => a + b, 0) / qualityScores.length : 0;\nconst avgBrandAlignment = brandAlignmentScores.length > 0 ? \n  brandAlignmentScores.reduce((a, b) => a + b, 0) / brandAlignmentScores.length : 0;\nconst avgEngagement = engagementScores.length > 0 ? \n  engagementScores.reduce((a, b) => a + b, 0) / engagementScores.length : 0;\n\n// Identificar áreas de melhoria\nconst improvementAreas = [];\nif (avgQuality < 7) improvementAreas.push('quality_improvement');\nif (avgBrandAlignment < 7) improvementAreas.push('brand_alignment');\nif (avgEngagement < 7) improvementAreas.push('engagement_optimization');\nif (feedbackAnalysis.approval_rate < 70) improvementAreas.push('overall_approval_rate');\n\n// Gerar insights de aprendizado\nconst learningInsights = [];\n\nif (feedbackAnalysis.approval_rate > 80) {\n  learningInsights.push({\n    type: 'positive_trend',\n    insight: `Alta taxa de aprovação (${Math.round(feedbackAnalysis.approval_rate)}%)`,\n    action: 'maintain_current_approach',\n    confidence: 'high'\n  });\n} else if (feedbackAnalysis.approval_rate < 60) {\n  learningInsights.push({\n    type: 'improvement_needed',\n    insight: `Taxa de aprovação baixa (${Math.round(feedbackAnalysis.approval_rate)}%)`,\n    action: 'review_content_strategy',\n    confidence: 'high'\n  });\n}\n\n// Analisar principais problemas\nconst topRejectionReason = Object.entries(feedbackAnalysis.rejection_reasons)\n  .sort(([,a], [,b]) => b - a)[0];\n\nif (topRejectionReason) {\n  learningInsights.push({\n    type: 'common_issue',\n    insight: `Principal motivo de rejeição: ${topRejectionReason[0]}`,\n    action: `focus_on_${topRejectionReason[0]}`,\n    confidence: topRejectionReason[1] >= 3 ? 'high' : 'medium'\n  });\n}\n\n// Insights sobre scores\nif (avgQuality > 0) {\n  learningInsights.push({\n    type: 'quality_analysis',\n    insight: `Score médio de qualidade: ${Math.round(avgQuality * 10) / 10}/10`,\n    action: avgQuality < 7 ? 'improve_content_quality' : 'maintain_quality_standards',\n    confidence: qualityScores.length >= 5 ? 'high' : 'medium'\n  });\n}\n\n// Gerar recomendações específicas\nconst recommendations = [];\n\nif (improvementAreas.includes('quality_improvement')) {\n  recommendations.push('Revisar prompts para melhorar qualidade do conteúdo');\n  recommendations.push('Implementar validações adicionais antes da submissão');\n}\n\nif (improvementAreas.includes('brand_alignment')) {\n  recommendations.push('Fortalecer diretrizes de marca nos prompts');\n  recommendations.push('Adicionar exemplos de conteúdo alinhado à marca');\n}\n\nif (improvementAreas.includes('engagement_optimization')) {\n  recommendations.push('Analisar padrões de conteúdo com alto engajamento');\n  recommendations.push('Ajustar prompts para focar em elementos engajadores');\n}\n\nif (topRejectionReason && topRejectionReason[1] >= 3) {\n  recommendations.push(`Priorizar correção de: ${topRejectionReason[0]}`);\n}\n\n// Calcular score de confiança baseado no volume de dados\nconst confidenceScore = Math.min(1, feedbackItems.length / 20); // Máximo com 20+ feedbacks\n\nreturn [{\n  learning_insights: learningInsights,\n  feedback_analysis: {\n    ...feedbackAnalysis,\n    avg_quality_score: Math.round(avgQuality * 10) / 10,\n    avg_brand_alignment: Math.round(avgBrandAlignment * 10) / 10,\n    avg_engagement_potential: Math.round(avgEngagement * 10) / 10\n  },\n  improvement_areas: improvementAreas,\n  recommendations: recommendations,\n  patterns_identified: learningInsights.length,\n  confidence_score: Math.round(confidenceScore * 100) / 100,\n  sample_size: feedbackItems.length,\n  analysis_period: '24 horas',\n  processed_at: new Date().toISOString()\n}];"}, "id": "8f14e45f-ceea-467a-9b87-7df96c6f135f", "name": "🧠 Process Learning Insights", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [900, 120]}, {"parameters": {"operation": "select", "schema": {"value": "agent"}, "table": {"value": "influencer_personas"}, "where": {"values": [{"column": "status", "condition": "equal", "value": "active"}]}, "options": {}}, "id": "6ba7b811-9dad-11d1-80b4-00c04fd430c8", "name": "👤 Get Active Personas", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [680, 240], "credentials": {"postgres": {"id": "postgres-main", "name": "PostgreSQL - Main DB"}}}, {"parameters": {"jsCode": "// 🔧 Atualizar prompts baseado no aprendizado\nconst personas = $('👤 Get Active Personas').all();\nconst learningData = $('🧠 Process Learning Insights').first().json;\nconst updateRequest = $input.first().json;\n\nif (!updateRequest.persona_id && personas.length === 0) {\n  throw new Error('Nenhuma persona encontrada para atualização');\n}\n\n// Determinar quais personas atualizar\nconst targetPersonas = updateRequest.persona_id ? \n  personas.filter(p => p.json.id === updateRequest.persona_id) : personas;\n\nconst updatedPrompts = [];\n\ntargetPersonas.forEach(persona => {\n  const currentPrompt = persona.json.master_prompt;\n  let updatedPrompt = currentPrompt;\n  const improvements = [];\n  \n  // Aplicar melhorias baseadas no feedback\n  if (learningData.improvement_areas.includes('quality_improvement')) {\n    if (!currentPrompt.includes('qualidade excepcional')) {\n      updatedPrompt += '\\n\\nFOCO EM QUALIDADE:\\n- Sempre priorize qualidade excepcional do conteúdo\\n- Revise cada palavra antes de finalizar\\n- Mantenha padrões profissionais elevados';\n      improvements.push('quality_focus');\n    }\n  }\n  \n  if (learningData.improvement_areas.includes('brand_alignment')) {\n    if (!currentPrompt.includes('alinhamento de marca')) {\n      updatedPrompt += '\\n\\nALINHAMENTO DE MARCA:\\n- Sempre mantenha consistência com a identidade da marca\\n- Use tom e linguagem apropriados ao público-alvo\\n- Evite conteúdo que possa comprometer a imagem da marca';\n      improvements.push('brand_alignment');\n    }\n  }\n  \n  if (learningData.improvement_areas.includes('engagement_optimization')) {\n    if (!currentPrompt.includes('engajamento')) {\n      updatedPrompt += '\\n\\nOTIMIZAÇÃO DE ENGAJAMENTO:\\n- Crie conteúdo que gere interação genuína\\n- Use elementos que incentivem comentários e compartilhamentos\\n- Mantenha relevância com tendências atuais';\n      improvements.push('engagement_optimization');\n    }\n  }\n  \n  // Adicionar insights específicos do feedback\n  if (learningData.learning_insights.length > 0) {\n    const highConfidenceInsights = learningData.learning_insights\n      .filter(insight => insight.confidence === 'high')\n      .slice(0, 2);\n    \n    if (highConfidenceInsights.length > 0) {\n      updatedPrompt += '\\n\\nINSIGHTS DO FEEDBACK HUMANO:';\n      highConfidenceInsights.forEach(insight => {\n        updatedPrompt += `\\n- ${insight.insight}`;\n      });\n      improvements.push('feedback_insights');\n    }\n  }\n  \n  // Adicionar timestamp da última atualização\n  updatedPrompt += `\\n\\n[Última atualização baseada em feedback: ${new Date().toISOString()}]`;\n  \n  updatedPrompts.push({\n    persona_id: persona.json.id,\n    persona_name: persona.json.name,\n    original_prompt_length: currentPrompt.length,\n    updated_prompt: updatedPrompt,\n    updated_prompt_length: updatedPrompt.length,\n    improvements_applied: improvements,\n    learning_confidence: learningData.confidence_score\n  });\n});\n\nreturn updatedPrompts;"}, "id": "8f14e45f-ceea-467a-9b87-7df96c6f1360", "name": "🔧 Generate Updated Prompts", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [900, 240]}, {"parameters": {"operation": "update", "schema": {"value": "agent"}, "table": {"value": "influencer_personas"}, "where": {"values": [{"column": "id", "condition": "equal", "value": "={{ $json.persona_id }}"}]}, "columns": {"mappingMode": "defineBelow", "values": [{"column": "master_prompt", "value": "={{ $json.updated_prompt }}"}, {"column": "updated_by", "value": "ai_learning_engine"}]}, "options": {}}, "id": "6ba7b812-9dad-11d1-80b4-00c04fd430c8", "name": "💾 Update Persona Prompts", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [1120, 240], "credentials": {"postgres": {"id": "postgres-main", "name": "PostgreSQL - Main DB"}}}, {"parameters": {"operation": "select", "schema": {"value": "agent"}, "table": {"value": "success_patterns"}, "where": {"values": [{"column": "created_at", "condition": "greaterThan", "value": "={{ new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString() }}"}, {"column": "confidence_score", "condition": "greaterThan", "value": "0.7"}]}, "sort": {"values": [{"column": "confidence_score", "direction": "DESC"}]}, "limit": 10, "options": {}}, "id": "6ba7b813-9dad-11d1-80b4-00c04fd430c8", "name": "🎯 Get Success Patterns", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [680, 360], "credentials": {"postgres": {"id": "postgres-main", "name": "PostgreSQL - Main DB"}}}, {"parameters": {"jsCode": "// 💡 Gerar insights estratégicos para melhoria contínua\nconst successPatterns = $input.all();\nconst learningData = $('🧠 Process Learning Insights').first()?.json;\n\nif (successPatterns.length === 0) {\n  return [{\n    strategic_insights: [],\n    actionable_recommendations: ['Aguardando padrões de sucesso para análise'],\n    confidence_level: 'low'\n  }];\n}\n\n// Analisar padrões de sucesso\nconst patternAnalysis = {\n  platform_preferences: {},\n  timing_patterns: {},\n  content_types: {},\n  keyword_trends: []\n};\n\nconst strategicInsights = [];\nconst actionableRecommendations = [];\n\nsuccessPatterns.forEach(pattern => {\n  const data = JSON.parse(pattern.json.pattern_data);\n  \n  if (data.success_patterns) {\n    data.success_patterns.forEach(successPattern => {\n      switch (successPattern.type) {\n        case 'platform':\n          const platform = successPattern.pattern.replace('Plataforma ', '');\n          patternAnalysis.platform_preferences[platform] = {\n            avg_engagement: successPattern.avg_engagement,\n            confidence: successPattern.confidence,\n            sample_size: successPattern.sample_size\n          };\n          break;\n          \n        case 'timing':\n          const timing = successPattern.pattern.replace('Publicação no período da ', '');\n          patternAnalysis.timing_patterns[timing] = {\n            avg_engagement: successPattern.avg_engagement,\n            confidence: successPattern.confidence\n          };\n          break;\n          \n        case 'content_length':\n          const length = successPattern.pattern.replace('Textos ', '').replace('s', '');\n          patternAnalysis.content_types[length] = {\n            avg_engagement: successPattern.avg_engagement,\n            confidence: successPattern.confidence\n          };\n          break;\n          \n        case 'keyword':\n          const keyword = successPattern.pattern.match(/\"(.*?)\"/)?.[1];\n          if (keyword) {\n            patternAnalysis.keyword_trends.push({\n              keyword: keyword,\n              avg_engagement: successPattern.avg_engagement,\n              confidence: successPattern.confidence\n            });\n          }\n          break;\n      }\n    });\n  }\n});\n\n// Gerar insights estratégicos\nif (Object.keys(patternAnalysis.platform_preferences).length > 0) {\n  const bestPlatform = Object.entries(patternAnalysis.platform_preferences)\n    .sort(([,a], [,b]) => b.avg_engagement - a.avg_engagement)[0];\n  \n  if (bestPlatform && bestPlatform[1].confidence === 'high') {\n    strategicInsights.push({\n      type: 'platform_optimization',\n      insight: `${bestPlatform[0]} demonstra consistentemente melhor performance`,\n      impact: 'high',\n      data: bestPlatform[1]\n    });\n    \n    actionableRecommendations.push({\n      action: `Aumentar frequência de posts em ${bestPlatform[0]}`,\n      priority: 'high',\n      expected_impact: 'Aumento de 15-25% no engajamento médio'\n    });\n  }\n}\n\nif (Object.keys(patternAnalysis.timing_patterns).length > 0) {\n  const bestTiming = Object.entries(patternAnalysis.timing_patterns)\n    .sort(([,a], [,b]) => b.avg_engagement - a.avg_engagement)[0];\n  \n  if (bestTiming) {\n    strategicInsights.push({\n      type: 'timing_optimization',\n      insight: `Período da ${bestTiming[0]} gera maior engajamento`,\n      impact: 'medium',\n      data: bestTiming[1]\n    });\n    \n    actionableRecommendations.push({\n      action: `Concentrar publicações no período da ${bestTiming[0]}`,\n      priority: 'medium',\n      expected_impact: 'Melhoria de 10-20% na taxa de engajamento'\n    });\n  }\n}\n\n// Analisar keywords de alto desempenho\nconst topKeywords = patternAnalysis.keyword_trends\n  .filter(k => k.confidence === 'high')\n  .sort((a, b) => b.avg_engagement - a.avg_engagement)\n  .slice(0, 5);\n\nif (topKeywords.length > 0) {\n  strategicInsights.push({\n    type: 'content_optimization',\n    insight: `${topKeywords.length} palavras-chave de alto impacto identificadas`,\n    impact: 'high',\n    data: { keywords: topKeywords.map(k => k.keyword) }\n  });\n  \n  actionableRecommendations.push({\n    action: `Incorporar palavras-chave de alto desempenho: ${topKeywords.slice(0, 3).map(k => k.keyword).join(', ')}`,\n    priority: 'high',\n    expected_impact: 'Potencial aumento de 20-30% no engajamento'\n  });\n}\n\n// Combinar com dados de feedback se disponível\nif (learningData) {\n  if (learningData.feedback_analysis.approval_rate > 85) {\n    strategicInsights.push({\n      type: 'quality_validation',\n      insight: `Alta taxa de aprovação humana (${Math.round(learningData.feedback_analysis.approval_rate)}%) valida estratégia atual`,\n      impact: 'high',\n      data: { approval_rate: learningData.feedback_analysis.approval_rate }\n    });\n  }\n  \n  // Identificar oportunidades de melhoria\n  if (learningData.improvement_areas.length > 0) {\n    strategicInsights.push({\n      type: 'improvement_opportunity',\n      insight: `${learningData.improvement_areas.length} áreas de melhoria identificadas pelo feedback humano`,\n      impact: 'medium',\n      data: { areas: learningData.improvement_areas }\n    });\n    \n    learningData.improvement_areas.forEach(area => {\n      actionableRecommendations.push({\n        action: `Implementar melhorias em: ${area.replace('_', ' ')}`,\n        priority: 'medium',\n        expected_impact: 'Redução de 15-25% na taxa de rejeição'\n      });\n    });\n  }\n}\n\n// Calcular nível de confiança geral\nconst highConfidenceInsights = strategicInsights.filter(i => i.impact === 'high').length;\nconst totalInsights = strategicInsights.length;\nconst confidenceLevel = totalInsights === 0 ? 'low' : \n  highConfidenceInsights / totalInsights > 0.6 ? 'high' : \n  highConfidenceInsights / totalInsights > 0.3 ? 'medium' : 'low';\n\nreturn [{\n  strategic_insights: strategicInsights,\n  pattern_analysis: patternAnalysis,\n  actionable_recommendations: actionableRecommendations.slice(0, 10), // Top 10\n  confidence_level: confidenceLevel,\n  insights_count: strategicInsights.length,\n  high_impact_insights: highConfidenceInsights,\n  data_sources: {\n    success_patterns: successPatterns.length,\n    feedback_data: learningData ? 'available' : 'not_available'\n  },\n  generated_at: new Date().toISOString()\n}];"}, "id": "8f14e45f-ceea-467a-9b87-7df96c6f1361", "name": "💡 Generate Strategic Insights", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [900, 360]}, {"parameters": {"operation": "select", "schema": {"value": "agent"}, "table": {"value": "llm_responses"}, "where": {"values": [{"column": "created_at", "condition": "greaterThan", "value": "={{ new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString() }}"}]}, "sort": {"values": [{"column": "created_at", "direction": "DESC"}]}, "limit": 100, "options": {}}, "id": "6ba7b814-9dad-11d1-80b4-00c04fd430c8", "name": "🤖 Get AI Model Usage", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [680, 480], "credentials": {"postgres": {"id": "postgres-main", "name": "PostgreSQL - Main DB"}}}, {"parameters": {"jsCode": "// 🎯 Otimizar seleção de modelos baseado em performance\nconst modelUsage = $input.all();\nconst learningData = $('🧠 Process Learning Insights').first()?.json;\nconst strategicInsights = $('💡 Generate Strategic Insights').first()?.json;\n\nif (modelUsage.length === 0) {\n  return [{\n    optimization_recommendations: [],\n    model_performance: {},\n    cost_analysis: {},\n    confidence: 'low'\n  }];\n}\n\n// Analisar performance por modelo\nconst modelPerformance = {};\nconst costAnalysis = {};\n\nmodelUsage.forEach(usage => {\n  const data = usage.json;\n  const model = data.model_used;\n  \n  if (!modelPerformance[model]) {\n    modelPerformance[model] = {\n      total_requests: 0,\n      total_tokens: 0,\n      total_cost: 0,\n      avg_response_time: 0,\n      success_rate: 0,\n      response_times: [],\n      success_count: 0\n    };\n  }\n  \n  const perf = modelPerformance[model];\n  perf.total_requests++;\n  perf.total_tokens += (data.input_tokens || 0) + (data.output_tokens || 0);\n  perf.total_cost += data.cost_usd || 0;\n  \n  if (data.response_time_ms) {\n    perf.response_times.push(data.response_time_ms);\n  }\n  \n  if (data.status === 'success') {\n    perf.success_count++;\n  }\n});\n\n// Calcular métricas finais\nObject.keys(modelPerformance).forEach(model => {\n  const perf = modelPerformance[model];\n  perf.avg_response_time = perf.response_times.length > 0 ? \n    perf.response_times.reduce((a, b) => a + b, 0) / perf.response_times.length : 0;\n  perf.success_rate = (perf.success_count / perf.total_requests) * 100;\n  perf.cost_per_request = perf.total_cost / perf.total_requests;\n  perf.tokens_per_request = perf.total_tokens / perf.total_requests;\n});\n\n// Identificar modelo mais eficiente\nconst modelRankings = Object.entries(modelPerformance)\n  .map(([model, perf]) => ({\n    model,\n    ...perf,\n    efficiency_score: (perf.success_rate / 100) * (1000 / Math.max(perf.avg_response_time, 1)) * (1 / Math.max(perf.cost_per_request, 0.001))\n  }))\n  .sort((a, b) => b.efficiency_score - a.efficiency_score);\n\n// Gerar recomendações de otimização\nconst optimizationRecommendations = [];\n\nif (modelRankings.length > 1) {\n  const bestModel = modelRankings[0];\n  const worstModel = modelRankings[modelRankings.length - 1];\n  \n  if (bestModel.efficiency_score > worstModel.efficiency_score * 1.5) {\n    optimizationRecommendations.push({\n      type: 'model_preference',\n      recommendation: `Priorizar uso do modelo ${bestModel.model}`,\n      reason: `${Math.round((bestModel.efficiency_score / worstModel.efficiency_score - 1) * 100)}% mais eficiente`,\n      impact: 'high',\n      metrics: {\n        cost_savings: `${Math.round((worstModel.cost_per_request - bestModel.cost_per_request) * 100)}% redução de custo`,\n        speed_improvement: `${Math.round((worstModel.avg_response_time - bestModel.avg_response_time))}ms mais rápido`\n      }\n    });\n  }\n}\n\n// Analisar padrões de uso por task_type\nconst taskTypeAnalysis = {};\nmodelUsage.forEach(usage => {\n  const taskType = usage.json.task_type || 'unknown';\n  const model = usage.json.model_used;\n  \n  if (!taskTypeAnalysis[taskType]) {\n    taskTypeAnalysis[taskType] = {};\n  }\n  \n  if (!taskTypeAnalysis[taskType][model]) {\n    taskTypeAnalysis[taskType][model] = {\n      count: 0,\n      avg_cost: 0,\n      avg_time: 0,\n      success_rate: 0\n    };\n  }\n  \n  taskTypeAnalysis[taskType][model].count++;\n});\n\n// Recomendações baseadas em task type\nObject.entries(taskTypeAnalysis).forEach(([taskType, models]) => {\n  const modelEntries = Object.entries(models);\n  if (modelEntries.length > 1) {\n    const bestForTask = modelEntries\n      .filter(([model, _]) => modelPerformance[model])\n      .sort(([,a], [,b]) => {\n        const perfA = modelPerformance[Object.keys(modelPerformance).find(m => m === a)];\n        const perfB = modelPerformance[Object.keys(modelPerformance).find(m => m === b)];\n        return (perfB?.efficiency_score || 0) - (perfA?.efficiency_score || 0);\n      })[0];\n    \n    if (bestForTask) {\n      optimizationRecommendations.push({\n        type: 'task_specific_optimization',\n        recommendation: `Para ${taskType}, usar preferencialmente ${bestForTask[0]}`,\n        reason: 'Melhor performance para este tipo de tarefa',\n        impact: 'medium'\n      });\n    }\n  }\n});\n\n// Integrar com dados de feedback se disponível\nif (learningData && learningData.feedback_analysis.approval_rate < 70) {\n  optimizationRecommendations.push({\n    type: 'quality_improvement',\n    recommendation: 'Considerar modelos mais avançados para melhorar qualidade',\n    reason: `Taxa de aprovação atual: ${Math.round(learningData.feedback_analysis.approval_rate)}%`,\n    impact: 'high',\n    suggested_models: modelRankings.slice(0, 2).map(m => m.model)\n  });\n}\n\n// Análise de custo\nconst totalCost = Object.values(modelPerformance).reduce((sum, perf) => sum + perf.total_cost, 0);\nconst avgCostPerRequest = totalCost / modelUsage.length;\n\ncostAnalysis.total_cost_7days = Math.round(totalCost * 100) / 100;\ncostAnalysis.avg_cost_per_request = Math.round(avgCostPerRequest * 10000) / 10000;\ncostAnalysis.projected_monthly_cost = Math.round(totalCost * 4.3 * 100) / 100;\n\n// Recomendações de economia\nif (totalCost > 10) { // Se custo semanal > $10\n  const cheapestModel = modelRankings.sort((a, b) => a.cost_per_request - b.cost_per_request)[0];\n  if (cheapestModel) {\n    const potentialSavings = (avgCostPerRequest - cheapestModel.cost_per_request) * modelUsage.length;\n    if (potentialSavings > 1) {\n      optimizationRecommendations.push({\n        type: 'cost_optimization',\n        recommendation: `Migrar para ${cheapestModel.model} pode economizar $${Math.round(potentialSavings * 100) / 100}/semana`,\n        reason: 'Redução significativa de custos mantendo qualidade',\n        impact: 'medium'\n      });\n    }\n  }\n}\n\nreturn [{\n  optimization_recommendations: optimizationRecommendations,\n  model_performance: modelPerformance,\n  model_rankings: modelRankings,\n  cost_analysis: costAnalysis,\n  task_type_analysis: taskTypeAnalysis,\n  confidence: modelUsage.length >= 20 ? 'high' : modelUsage.length >= 10 ? 'medium' : 'low',\n  analysis_period: '7 dias',\n  total_requests_analyzed: modelUsage.length,\n  generated_at: new Date().toISOString()\n}];"}, "id": "8f14e45f-ceea-467a-9b87-7df96c6f1362", "name": "🎯 Optimize Model Selection", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [900, 480]}, {"parameters": {"respondWith": "json", "responseBody": "={{ $json }}", "options": {}}, "id": "f47ac10b-58cc-4372-a567-0e02b2c3d480", "name": "📤 Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1340, 300]}, {"parameters": {"jsCode": "// 📤 Formatar resposta final\nconst action = $('🧠 Webhook - Learning Request').first().json.action;\nconst input = $input.first().json;\n\nlet response = {\n  success: true,\n  action: action,\n  timestamp: new Date().toISOString()\n};\n\nswitch (action) {\n  case 'process_feedback':\n    response.data = {\n      learning_insights: input,\n      patterns_identified: input.patterns_identified,\n      confidence_score: input.confidence_score,\n      recommendations_count: input.recommendations ? input.recommendations.length : 0\n    };\n    break;\n    \n  case 'update_prompts':\n    response.message = `${input.length} prompts atualizados com sucesso`;\n    response.data = {\n      updated_personas: input.map(p => ({\n        persona_id: p.persona_id,\n        persona_name: p.persona_name,\n        improvements_applied: p.improvements_applied\n      }))\n    };\n    break;\n    \n  case 'generate_insights':\n    response.data = {\n      strategic_insights: input,\n      insights_count: input.insights_count,\n      confidence_level: input.confidence_level,\n      high_impact_insights: input.high_impact_insights\n    };\n    break;\n    \n  case 'optimize_model_selection':\n    response.data = {\n      optimization: input,\n      recommendations_count: input.optimization_recommendations ? input.optimization_recommendations.length : 0,\n      confidence: input.confidence,\n      cost_analysis: input.cost_analysis\n    };\n    break;\n}\n\nreturn response;"}, "id": "8f14e45f-ceea-467a-9b87-7df96c6f1363", "name": "📋 Format Final Response", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1120, 300]}, {"parameters": {"operation": "insert", "schema": {"value": "agent"}, "table": {"value": "system_logs"}, "columns": {"mappingMode": "defineBelow", "values": [{"column": "event_type", "value": "ai_learning_process"}, {"column": "event_data", "value": "={{ JSON.stringify({action: $('🧠 Webhook - Learning Request').first().json.action, result: $json}) }}"}, {"column": "severity", "value": "info"}]}, "options": {}}, "id": "6ba7b815-9dad-11d1-80b4-00c04fd430c8", "name": "📝 Log Learning Process", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [1340, 240], "credentials": {"postgres": {"id": "postgres-main", "name": "PostgreSQL - Main DB"}}}, {"parameters": {"jsCode": "// 🚨 Tratamento de erro\nconst error = $input.first().json;\n\nreturn {\n  success: false,\n  error: {\n    message: error.message || 'Erro interno do servidor',\n    code: error.code || 'LEARNING_ERROR',\n    details: error.details || null\n  },\n  timestamp: new Date().toISOString()\n};"}, "id": "8f14e45f-ceea-467a-9b87-7df96c6f1364", "name": "🚨 <PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1120, 540]}, {"parameters": {"operation": "insert", "schema": {"value": "agent"}, "table": {"value": "system_logs"}, "columns": {"mappingMode": "defineBelow", "values": [{"column": "event_type", "value": "learning_error"}, {"column": "event_data", "value": "={{ JSON.stringify({error: $json.error, action: $('🧠 Webhook - Learning Request').first().json.action}) }}"}, {"column": "severity", "value": "error"}]}, "options": {}}, "id": "6ba7b816-9dad-11d1-80b4-00c04fd430c8", "name": "📝 Log Error", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [1340, 540], "credentials": {"postgres": {"id": "postgres-main", "name": "PostgreSQL - Main DB"}}}], "connections": {"🧠 Webhook - Learning Request": {"main": [[{"node": "🔀 Route Action", "type": "main", "index": 0}]]}, "🔀 Route Action": {"main": [[{"node": "📝 Get Recent Feedback", "type": "main", "index": 0}], [{"node": "👤 Get Active Personas", "type": "main", "index": 0}], [{"node": "🎯 Get Success Patterns", "type": "main", "index": 0}], [{"node": "🤖 Get AI Model Usage", "type": "main", "index": 0}]]}, "📝 Get Recent Feedback": {"main": [[{"node": "🧠 Process Learning Insights", "type": "main", "index": 0}]]}, "🧠 Process Learning Insights": {"main": [[{"node": "📋 Format Final Response", "type": "main", "index": 0}]]}, "👤 Get Active Personas": {"main": [[{"node": "🔧 Generate Updated Prompts", "type": "main", "index": 0}]]}, "🔧 Generate Updated Prompts": {"main": [[{"node": "💾 Update Persona Prompts", "type": "main", "index": 0}]]}, "💾 Update Persona Prompts": {"main": [[{"node": "📋 Format Final Response", "type": "main", "index": 0}]]}, "🎯 Get Success Patterns": {"main": [[{"node": "💡 Generate Strategic Insights", "type": "main", "index": 0}]]}, "💡 Generate Strategic Insights": {"main": [[{"node": "📋 Format Final Response", "type": "main", "index": 0}]]}, "🤖 Get AI Model Usage": {"main": [[{"node": "🎯 Optimize Model Selection", "type": "main", "index": 0}]]}, "🎯 Optimize Model Selection": {"main": [[{"node": "📋 Format Final Response", "type": "main", "index": 0}]]}, "📋 Format Final Response": {"main": [[{"node": "📝 Log Learning Process", "type": "main", "index": 0}]]}, "📝 Log Learning Process": {"main": [[{"node": "📤 Response", "type": "main", "index": 0}]]}, "🚨 Error Handler": {"main": [[{"node": "📝 Log Error", "type": "main", "index": 0}]]}, "📝 Log Error": {"main": [[{"node": "📤 Response", "type": "main", "index": 0}]]}}, "pinData": {}, "settings": {"executionOrder": "v1"}, "staticData": null, "tags": [{"createdAt": "2024-12-19T10:00:00.000Z", "updatedAt": "2024-12-19T10:00:00.000Z", "id": "learning", "name": "learning"}, {"createdAt": "2024-12-19T10:00:00.000Z", "updatedAt": "2024-12-19T10:00:00.000Z", "id": "ai-optimization", "name": "ai-optimization"}], "triggerCount": 1, "updatedAt": "2024-12-19T10:00:00.000Z", "versionId": "1.0.0"}