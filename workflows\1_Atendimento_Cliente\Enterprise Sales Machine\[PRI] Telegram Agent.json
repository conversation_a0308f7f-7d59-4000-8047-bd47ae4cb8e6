{"name": "[PRI] Telegram Agent v12.1 - Stateful & Secure", "nodes": [{"parameters": {"path": "telegram", "responseMode": "onReceived"}, "id": "trigger_telegram", "name": "TRIGGER: Telegram", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [-2000, 840]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "INSERT INTO agent.contatos (telegram_id, nome, language) VALUES ($1, $2, 'pt-BR') ON CONFLICT (telegram_id) DO UPDATE SET nome = EXCLUDED.nome, updated_at = NOW() RETURNING *;", "options": {"parameters": {"values": ["={{$json.body.message.from.id}}", "={{$json.body.message.from.first_name}}"]}}}, "id": "db_get_or_create_contact", "name": "DB: Get/Create Contact", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [-1800, 840], "credentials": {"postgres": {"id": "YOUR_POSTGRES_CREDENTIAL_ID"}}}, {"parameters": {"keepOnlySet": true, "values": {"string": [{"name": "textContent", "value": "={{$json.body.message.text}}"}], "number": [{"name": "contactDbId", "value": "={{$json.id}}"}], "json": [{"name": "contactLanguage", "value": "={{$json.language}}"}]}}, "id": "set_initial_data", "name": "SET: Initial Data", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [-1640, 840]}, {"parameters": {"functionCode": "const crypto = require('crypto');\nconst creds = $credentials.DefaultAESKey;\nif (!creds || !creds.key || creds.key.length !== 32) { throw new Error('Chave AES não encontrada.'); }\nitems[0].json.encryption_key = creds.key;\nreturn items;"}, "id": "sec_prepare_crypto", "name": "SEC: Prepare Crypto Key", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [-1440, 840], "credentials": {"genericCredential": {"id": "DefaultAESKey"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT encrypted_content FROM agent.memory_vectors WHERE contact_id = $1 ORDER BY timestamp DESC LIMIT 10;", "options": {"parameters": {"values": ["={{$json.contactDbId}}"]}}}, "id": "db_get_encrypted_memories", "name": "DB: Get Encrypted Memories", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [-1240, 840], "credentials": {"postgres": {"id": "YOUR_POSTGRES_CREDENTIAL_ID"}}}, {"parameters": {"functionCode": "const crypto = require('crypto');\nconst key = Buffer.from($('sec_prepare_crypto').first().json.encryption_key);\nconst decryptedMemories = [];\nfor (const item of $items) {\n  const encryptedContent = item.json.encrypted_content;\n  if (!encryptedContent || !encryptedContent.includes(':')) continue;\n  const parts = encryptedContent.split(':');\n  const iv = Buffer.from(parts.shift(), 'hex');\n  const encryptedText = Buffer.from(parts.join(':'), 'hex');\n  const decipher = crypto.createDecipheriv('aes-256-cbc', key, iv);\n  let decrypted = decipher.update(encryptedText);\n  decrypted = Buffer.concat([decrypted, decipher.final()]);\n  decryptedMemories.push(decrypted.toString());\n}\n// Pega os dados do nó anterior e adiciona o contexto decifrado\nconst previousData = $('db_get_or_create_contact').first().json;\nreturn [{ json: { ...previousData, decrypted_context: decryptedMemories.reverse().join('\\n') } }];"}, "id": "sec_decrypt_memories", "name": "SEC: Decrypt Memories", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [-1020, 840]}, {"parameters": {"model": "gpt-4-turbo", "prompt": "CONTEXTO: {{$json.decrypted_context}}\n\nMENSAGEM: {{$json.textContent}}\n\nResponda em JSON: {\"reply\": \"...\"}", "jsonOutput": true}, "id": "ai_general_conversation", "name": "AI: General Conversation", "type": "n8n-nodes-base.openAiChat", "typeVersion": 1, "position": [-800, 840], "credentials": {"openAiApi": {"id": "YOUR_OPENAI_CREDENTIAL_ID"}}}, {"parameters": {"functionCode": "const crypto = require('crypto');\nconst userInput = $('set_initial_data').first().json.textContent;\nconst aiReply = $json.reply;\nconst memoryToSave = `User: ${userInput}\\nAI: ${aiReply}`;\nconst encryptionKey = $('sec_prepare_crypto').first().json.encryption_key;\nconst iv = crypto.randomBytes(16);\nconst cipher = crypto.createCipheriv('aes-256-cbc', Buffer.from(encryptionKey), iv);\nlet encrypted = cipher.update(memoryToSave, 'utf8', 'hex');\nencrypted += cipher.final('hex');\nreturn [{ json: { ...$items[0].json, encrypted_content: iv.toString('hex') + ':' + encrypted } }];"}, "id": "sec_encrypt_memory", "name": "SEC: Encrypt Memory", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [-580, 840]}, {"parameters": {"workflowId": "={{$env.SEND_MESSAGE_WORKFLOW_ID}}", "options": {"parameters": {"values": {"string": [{"name": "channel", "value": "telegram"}, {"name": "recipientId", "value": "={{$('trigger_telegram').item.json.body.message.chat.id}}"}, {"name": "messageContent", "value": "={{$json.reply}}"}]}}}}, "id": "send_message", "name": "SUB: Send Message", "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 2, "position": [-360, 740]}, {"parameters": {"model": "text-embedding-3-small", "input": "={{$('set_initial_data').item.json.textContent}}"}, "id": "ai_generate_embedding", "name": "AI: Generate Embedding", "type": "n8n-nodes-base.openAi", "typeVersion": 4, "position": [-360, 940], "credentials": {"openAiApi": {"id": "YOUR_OPENAI_CREDENTIAL_ID"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "INSERT INTO agent.memory_vectors (contact_id, content, encrypted_content, embedding) VALUES ($1, 'Message encrypted.', $2, $3::vector);", "options": {"parameters": {"values": ["{{$node[\"set_initial_data\"].json.contactDbId}}", "{{$json.encrypted_content}}", "{{'[' + $node[\"ai_generate_embedding\"].json.embedding.join(',') + ']'}}"]}}}, "id": "db_insert_memory", "name": "DB: Insert Encrypted Memory", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [-140, 940], "credentials": {"postgres": {"id": "YOUR_POSTGRES_CREDENTIAL_ID"}}}], "connections": {"trigger_telegram": {"main": [[{"node": "db_get_or_create_contact"}]]}, "db_get_or_create_contact": {"main": [[{"node": "set_initial_data"}]]}, "set_initial_data": {"main": [[{"node": "sec_prepare_crypto"}]]}, "sec_prepare_crypto": {"main": [[{"node": "db_get_encrypted_memories"}]]}, "db_get_encrypted_memories": {"main": [[{"node": "sec_decrypt_memories"}]]}, "sec_decrypt_memories": {"main": [[{"node": "ai_general_conversation"}]]}, "ai_general_conversation": {"main": [[{"node": "sec_encrypt_memory"}]]}, "sec_encrypt_memory": {"main": [[{"node": "send_message"}, {"node": "ai_generate_embedding"}]]}, "ai_generate_embedding": {"main": [[{"node": "db_insert_memory"}]]}}}