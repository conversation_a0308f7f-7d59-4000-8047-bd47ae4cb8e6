# Start-Environment.ps1 - Orquestrador Zero-Touch Robusto com Logging Aprimorado e Entrega Completa de Credenciais
# Versão: 3.0 - Módulo centralizado e arquitetura robusta

# =====================================================
# IMPORTAR MÓDULO DE UTILITÁRIOS DE AUTOMAÇÃO
# =====================================================

$utilityModuleName = "AutomationUtils"
# Assume que o módulo está na mesma pasta do script PS, ou em um subdiretório
# Se PowerShellModules estiver no mesmo nível que os scripts PS:
$modulePath = Join-Path $PSScriptRoot ".\PowerShellModules\$utilityModuleName.psm1"
# Se PowerShellModules estiver um diretório acima dos scripts PS:
# $modulePath = Join-Path $PSScriptRoot "..\PowerShellModules\$utilityModuleName.psm1"

if (Test-Path $modulePath) {
    try {
        Import-Module $modulePath -Force -ErrorAction Stop
        if (-not (Get-Command Log-Event -ErrorAction SilentlyContinue)) { # Verificação de fallback robusta
            Write-Error "ERRO FATAL: Módulo '$utilityModuleName' importado, mas a função Log-Event não foi encontrada. O módulo pode estar corrompido. Verifique '$modulePath'."
            exit 1
        }
        Log-Event "Módulo '$utilityModuleName' carregado com sucesso." "ModuleLoader" "SUCCESS"
    } catch {
        Write-Error "ERRO FATAL: Falha crítica ao importar o módulo '$utilityModuleName' de '$modulePath'. As funções utilitárias são essenciais. Verifique a instalação do módulo."
        exit 1
    }
} else {
    Write-Error "ERRO FATAL: Módulo de utilidades do PowerShell não encontrado em '$modulePath'. O projeto não pode continuar sem funções de suporte essenciais."
    exit 1
}

# Importar módulo de geração de dashboard
$dashboardModulePath = Join-Path $PSScriptRoot ".\PowerShellModules\Generate-ServiceDashboard.psm1"
if (Test-Path $dashboardModulePath) {
    try {
        Import-Module $dashboardModulePath -Force -ErrorAction Stop
        Log-Event "Módulo 'Generate-ServiceDashboard' carregado com sucesso." "ModuleLoader" "SUCCESS"
    } catch {
        Write-Warning "Aviso: Falha ao importar módulo Generate-ServiceDashboard. Dashboard HTML será gerado de forma alternativa."
        Log-Event "Falha ao carregar módulo Generate-ServiceDashboard: $($_.Exception.Message)" "ModuleLoader" "WARN"
    }
} else {
    Write-Warning "Módulo Generate-ServiceDashboard não encontrado. Dashboard HTML será gerado de forma alternativa."
    Log-Event "Módulo Generate-ServiceDashboard não encontrado em '$dashboardModulePath'" "ModuleLoader" "WARN"
}

# =====================================================
# INÍCIO DA EXECUÇÃO PRINCIPAL
# =====================================================

Write-Host-Color "`n╔════════════════════════════════════════════════════════════╗" "Cyan"
Write-Host-Color "║      N8N EVOLUTION ENVIRONMENT - INSTALADOR ROBUSTO      ║" "Cyan"
Write-Host-Color "║           Versão 2.0 - Diagnósticos Avançados            ║" "Cyan"
Write-Host-Color "╚════════════════════════════════════════════════════════════╝" "Cyan"

# =====================================================
# MENU DE INICIALIZAÇÃO
# =====================================================

Write-Host-Color "`nSelecione o modo de inicialização:" "Yellow"
Write-Host-Color "───────────────────────────────────────" "Yellow"
Write-Host "[1] 🏠 Modo Local (padrão)" -ForegroundColor White
Write-Host "    • Acesso apenas via localhost/IP local" -ForegroundColor Gray
Write-Host "    • Ideal para desenvolvimento local" -ForegroundColor Gray
Write-Host "`n[2] 🌐 Modo Público (ngrok)" -ForegroundColor White
Write-Host "    • Webhook público via túnel ngrok" -ForegroundColor Gray
Write-Host "    • Acesso externo para webhooks" -ForegroundColor Gray
Write-Host "    • Requer token ngrok configurado" -ForegroundColor Gray

$useNgrok = $false
$userChoice = $null

# Verificar se há entrada do usuário ou usar countdown
Write-Host "`nEscolha [1-2] ou aguarde para modo padrão: " -NoNewline -ForegroundColor Yellow

# Implementar countdown com verificação de tecla
$countdownCompleted = Show-CountdownTimer -Seconds 10 -Message "Iniciando modo local em"

if (-not $countdownCompleted) {
    # Usuário pressionou uma tecla, ler a escolha
    $userChoice = Read-Host "`nDigite sua escolha [1-2]"
    
    switch ($userChoice) {
        "1" {
            Write-Host-Color "`n✅ Modo Local selecionado" "Green"
            $useNgrok = $false
        }
        "2" {
            Write-Host-Color "`n🌐 Modo Público (ngrok) selecionado" "Green"
            $useNgrok = $true
            
            # Verificar se NGROK_AUTHTOKEN está configurado
            if (-not $env:NGROK_AUTHTOKEN -or $env:NGROK_AUTHTOKEN.Trim() -eq "") {
                Write-Host-Color "`n⚠️  ATENÇÃO: Token do ngrok não configurado!" "Red"
                Write-Host "Para usar o modo público, você precisa:" -ForegroundColor Yellow
                Write-Host "1. Criar uma conta em https://ngrok.com" -ForegroundColor White
                Write-Host "2. Obter seu token de autenticação" -ForegroundColor White
                Write-Host "3. Adicionar NGROK_AUTHTOKEN=seu_token ao arquivo .env" -ForegroundColor White
                Write-Host "`nContinuando em modo local..." -ForegroundColor Gray
                $useNgrok = $false
            }
        }
        default {
            Write-Host-Color "`n⚠️  Opção inválida. Usando modo local padrão." "Yellow"
            $useNgrok = $false
        }
    }
} else {
    # Countdown completou, usar modo padrão
    Write-Host-Color "`n✅ Modo Local (padrão) selecionado automaticamente" "Green"
    $useNgrok = $false
}

Log-Event "Modo de inicialização selecionado: $(if ($useNgrok) { 'Público (ngrok)' } else { 'Local' })" "Orchestrator" "INFO"

# =====================================================
# VALIDAÇÃO DE PRÉ-REQUISITOS
# =====================================================

# 1. Validação de pré-requisitos
Log-Event "Iniciando validação de pré-requisitos..." "Orchestrator" "INFO"
if (-not (Get-Command docker -ErrorAction SilentlyContinue)) { 
    Abort-Install "Docker não está instalado ou não está no PATH." "Prerequisites"
}
if (-not (Get-Command docker-compose -ErrorAction SilentlyContinue) -and -not (docker compose version 2>$null)) {
    Abort-Install "Docker Compose não está instalado ou não está no PATH." "Prerequisites"
}
if ($PSVersionTable.PSVersion.Major -lt 5) { 
    Abort-Install "PowerShell 5.1 ou superior é necessário." "Prerequisites"
}
Log-Event "✓ Validação de pré-requisitos concluída com sucesso." "Prerequisites" "SUCCESS"

# 2. Gestão de Configuração (.env) - COMPLETA
Log-Event "Iniciando gerenciamento de configuração .env..." "Config" "INFO"
$envFile = ".env"
$credentialsGenerated = $false

if (-not (Test-Path $envFile)) {
    Log-Event "Arquivo .env não encontrado. Gerando novas credenciais completas..." "Config" "WARN"
    $credentialsGenerated = $true
    
    # Obter IP do host de forma robusta
    if ($env:HOST_IP -and $env:HOST_IP.Trim()) {
        $hostIp = $env:HOST_IP.Trim()
        Log-Event "HOST_IP pré-definido detectado: $hostIp" "Config" "INFO"
    } else {
        $hostIp = Get-PrimaryPrivateIPv4
        Log-Event "HOST_IP detectado automaticamente: $hostIp" "Config" "INFO"
    }
    
    # Gerar todas as credenciais necessárias
    $postgresPassword = Generate-RandomPassword 16
    $minioPassword = Generate-RandomPassword 20
    $evolutionApiKey = Generate-RandomPassword 32
    $secretKeyBase = Generate-RandomPassword 64
    $n8nEncryptionKey = Generate-SecureKey 32
    $chatwootAdminEmail = "<EMAIL>"
    $chatwootAdminPassword = Generate-RandomPassword 16
    
    # Criar arquivo .env completo
    @"
# Credenciais do PostgreSQL
POSTGRES_PASSWORD=$postgresPassword

# Credenciais do MinIO
MINIO_ROOT_USER=admin
MINIO_ROOT_PASSWORD=$minioPassword

# Evolution API
AUTHENTICATION_API_KEY=$evolutionApiKey

# Chatwoot
SECRET_KEY_BASE=$secretKeyBase
CHATWOOT_ADMIN_EMAIL=$chatwootAdminEmail
CHATWOOT_ADMIN_PASSWORD=$chatwootAdminPassword

# n8n
N8N_ENCRYPTION_KEY=$n8nEncryptionKey

# Sistema
HOST_IP=$hostIp

# Configuração da Evolution API para QR Code
CONFIG_SESSION_PHONE_VERSION=2.3000.1020885143
"@ | Out-File -FilePath $envFile -Encoding utf8
    Log-Event "✓ Arquivo .env criado com todas as credenciais!" "Config" "SUCCESS"
} else {
    Log-Event "Arquivo .env existente encontrado. Verificando integridade..." "Config" "INFO"
    
    # Verificar e adicionar credenciais faltantes
    $envContent = Get-Content $envFile -Raw
    $updates = @()
    
    if ($envContent -notmatch "(?m)^SECRET_KEY_BASE\s*=\s*\S+") {
        $newSecret = Generate-RandomPassword 64
        Add-Content -Path $envFile -Value "SECRET_KEY_BASE=$newSecret"
        $updates += "SECRET_KEY_BASE"
    }
    
    if ($envContent -notmatch "(?m)^N8N_ENCRYPTION_KEY\s*=\s*\S+") {
        $n8nKey = Generate-SecureKey 32
        Add-Content -Path $envFile -Value "N8N_ENCRYPTION_KEY=$n8nKey"
        $updates += "N8N_ENCRYPTION_KEY"
    }
    
    if ($envContent -notmatch "(?m)^CHATWOOT_ADMIN_EMAIL\s*=\s*\S+") {
        Add-Content -Path $envFile -Value "CHATWOOT_ADMIN_EMAIL=<EMAIL>"
        $updates += "CHATWOOT_ADMIN_EMAIL"
    }
    
    if ($envContent -notmatch "(?m)^CHATWOOT_ADMIN_PASSWORD\s*=\s*\S+") {
        $chatwootPwd = Generate-RandomPassword 16
        Add-Content -Path $envFile -Value "CHATWOOT_ADMIN_PASSWORD=$chatwootPwd"
        $updates += "CHATWOOT_ADMIN_PASSWORD"
    }

    if ($envContent -notmatch "(?m)^CONFIG_SESSION_PHONE_VERSION\s*=\s*\S+") {
        Add-Content -Path $envFile -Value "CONFIG_SESSION_PHONE_VERSION=2.3000.1020885143"
        $updates += "CONFIG_SESSION_PHONE_VERSION"
    }
    
    if ($updates.Count -gt 0) {
        Log-Event "Variáveis adicionadas ao .env: $($updates -join ', ')" "Config" "WARN"
    }
}

Log-Event "Carregando variáveis de ambiente do arquivo .env..." "Config" "INFO"
# Carrega todas as variáveis do .env para a sessão atual
$envCredentials = @{}
Get-Content $envFile | ForEach-Object {
    if ($_ -match '^([^=]+)=(.*)$') {
        $key = $matches[1].Trim()
        $value = $matches[2].Trim()
        [System.Environment]::SetEnvironmentVariable($key, $value)
        $envCredentials[$key] = $value
    }
}
Log-Event "✓ Variáveis de ambiente carregadas com sucesso." "Config" "SUCCESS"

# Exibir chave da Evolution API para o usuário colar no Manager
if ($envCredentials.ContainsKey('AUTHENTICATION_API_KEY')) {
    $apiKeyDisplay = $envCredentials['AUTHENTICATION_API_KEY']
    Log-Event "⚠️ Copie esta chave para o Manager da Evolution API: $apiKeyDisplay" "Evolution" "WARN"
    Write-Host "[Evolution API] Chave de autenticação: $apiKeyDisplay" -ForegroundColor Yellow
}

# 3. Criação da Rede Docker
Log-Event "Verificando rede Docker 'app_network'..." "Docker" "INFO"
$inspectOutput = docker network inspect app_network --format "{{.Driver}}" 2>$null

if ($LASTEXITCODE -ne 0) {
    # Rede não existe – criar com driver bridge para compatibilidade
    Log-Event "Rede 'app_network' não encontrada. Criando..." "Docker" "WARN"
    Run-Command "docker network create --driver bridge app_network" "Falha ao criar a rede Docker 'app_network'." "Docker"
    Log-Event "✓ Rede 'app_network' criada com sucesso (driver bridge)." "Docker" "SUCCESS"
} else {
    $driver = $inspectOutput.Trim()
    Log-Event "Rede 'app_network' encontrada com driver '$driver'." "Docker" "INFO"
    if ($driver -ne "bridge") {
        Log-Event "⚠️ Driver da rede difere de 'bridge'. Certifique-se de que isso é intencional para evitar problemas de conectividade." "Docker" "WARN"
    }
}

# 4. Configurar variáveis de ambiente para webhooks
if ($useNgrok) {
    Log-Event "Configurando variáveis de ambiente para modo ngrok..." "NgrokSetup" "INFO"
    # A URL do webhook será definida após obter a URL pública do ngrok
    [System.Environment]::SetEnvironmentVariable("WEBHOOK_URL", "")
} else {
    Log-Event "Configurando variáveis de ambiente para modo local..." "LocalSetup" "INFO"
    $hostIp = if ($env:HOST_IP) { $env:HOST_IP } else { Get-PrimaryPrivateIPv4 }
    [System.Environment]::SetEnvironmentVariable("WEBHOOK_URL", "http://$hostIp:5678")
}

# 5. Subir todos os serviços
if ($useNgrok) {
    Log-Event "Iniciando todos os serviços com ngrok via docker-compose..." "Docker" "INFO"
    Run-Command "docker compose -f docker-compose.yml --profile ngrok up -d" "Falha ao subir os serviços com ngrok." "Docker"
    Log-Event "✓ Comando 'docker compose up' com profile ngrok executado. Aguardando estabilização..." "Docker" "SUCCESS"
    
    # Aguardar ngrok ficar disponível e obter URL pública
    Log-Event "Aguardando ngrok ficar disponível..." "NgrokSetup" "INFO"
    if (Wait-For-Port "localhost" 4040 120) {
        Log-Event "✓ API de inspeção do ngrok disponível na porta 4040." "NgrokSetup" "SUCCESS"
        
        # Obter URL pública do ngrok
        $ngrokResult = Get-NgrokPublicUrl -MaxRetries 30 -RetryIntervalSeconds 2
        
        if ($ngrokResult.Success) {
            $publicUrl = $ngrokResult.Url
            Log-Event "✅ URL pública do ngrok obtida: $publicUrl" "NgrokSetup" "SUCCESS"
            
            # Atualizar variável de ambiente
            [System.Environment]::SetEnvironmentVariable("WEBHOOK_URL", $publicUrl)
            
            # Reiniciar serviços que dependem da WEBHOOK_URL
            Log-Event "Reiniciando serviços para aplicar URL pública do webhook..." "NgrokSetup" "INFO"
            Run-Command "docker restart evolution_aula n8n_editor-1" "Falha ao reiniciar serviços para aplicar webhook URL." "Docker"
            Log-Event "✓ Serviços reiniciados com URL pública do webhook." "NgrokSetup" "SUCCESS"
            
            Write-Host-Color "`n🌐 WEBHOOK PÚBLICO CONFIGURADO:" "Green"
            Write-Host-Color "   URL: $publicUrl" "White"
            Write-Host-Color "   Evolution: $publicUrl/webhook/evolution" "White"
            
        } else {
            Log-Event "❌ Falha ao obter URL pública do ngrok: $($ngrokResult.ErrorMessage)" "NgrokSetup" "ERROR"
            Write-Host-Color "`n⚠️  Falha ao configurar ngrok. Continuando em modo local..." "Yellow"
            $useNgrok = $false
            $hostIp = if ($env:HOST_IP) { $env:HOST_IP } else { Get-PrimaryPrivateIPv4 }
            [System.Environment]::SetEnvironmentVariable("WEBHOOK_URL", "http://$hostIp:5678")
        }
    } else {
        Log-Event "❌ API de inspeção do ngrok não ficou disponível." "NgrokSetup" "ERROR"
        Write-Host-Color "`n⚠️  Ngrok não ficou disponível. Continuando em modo local..." "Yellow"
        $useNgrok = $false
        $hostIp = if ($env:HOST_IP) { $env:HOST_IP } else { Get-PrimaryPrivateIPv4 }
        [System.Environment]::SetEnvironmentVariable("WEBHOOK_URL", "http://$hostIp:5678")
    }
} else {
    Log-Event "Iniciando todos os serviços via docker-compose..." "Docker" "INFO"
    Run-Command "docker compose -f docker-compose.yml up -d" "Falha ao subir os serviços com docker-compose." "Docker"
    Log-Event "✓ Comando 'docker compose up' executado. Aguardando estabilização..." "Docker" "SUCCESS"
}

# 5. Aguardar serviços críticos
Log-Event "Aguardando serviços críticos ficarem saudáveis..." "HealthCheck" "INFO"

# PostgreSQL
if (-not (Wait-For-Service-Healthy "postgres_aula" 180)) {
    Abort-Install "Serviço 'postgres_aula' não ficou saudável dentro do tempo limite." "Postgres"
}

# Redis
if (-not (Wait-For-Service-Healthy "redis_aula" 120)) {
    Log-Event "Redis não ficou saudável após reinicialização." "Redis-Diagnostics" "ERROR"
} else {
    # Verifica conectividade lógica
    if (-not (Test-RedisConnectivity)) {
        Log-Event "Redis saudável, porém teste de conectividade falhou." "Redis-Diagnostics" "ERROR"
    }
}

# MinIO
if (-not (Wait-For-Service-Healthy "minio_aula" 180)) {
    Abort-Install "Serviço 'minio_aula' não ficou saudável dentro do tempo limite." "MinIO"
}

# 6. Diagnóstico completo do Redis
Log-Event "Executando diagnósticos específicos do Redis..." "Redis-Diagnostics" "INFO"
if (-not (Test-RedisConnectivity)) {
    Log-Event "⚠️ Problemas detectados no Redis. Tentando reinicialização..." "Redis-Diagnostics" "WARN"
    Run-Command "docker restart redis_aula" "Falha ao reiniciar o Redis" "Redis"
    if (-not (Wait-For-Service-Healthy "redis_aula" 120)) {
        Log-Event "Redis não ficou saudável após reinicialização." "Redis-Diagnostics" "ERROR"
    }
}

# 7. Setup do PostgreSQL
$pgContainer = docker ps --filter "name=postgres_aula" --format "{{.Names}}"
if (-not $pgContainer) { 
    Abort-Install "Container 'postgres_aula' não está rodando." "Postgres"
}

Log-Event "Iniciando setup avançado do PostgreSQL..." "Postgres" "INFO"

# Instalar pgvector (idempotente)
Log-Event "Verificando existência da extensão pgvector..." "Postgres" "INFO"
$vectorExists = Invoke-Expression "docker exec postgres_aula psql -U postgres -tAc `"SELECT 1 FROM pg_extension WHERE extname='vector'`" 2>&1"

if ($vectorExists -match "1") {
    Log-Event "Extensão pgvector já instalada. Pulando compilação." "Postgres" "SUCCESS"
} else {
    Log-Event "Extensão pgvector não encontrada. Iniciando processo de compilação..." "Postgres" "WARN"
    Run-Command "docker exec postgres_aula apt-get update" "Falha ao executar apt-get update" "Postgres"
    Run-Command "docker exec postgres_aula apt-get install -y git build-essential postgresql-server-dev-15 curl" "Falha ao instalar dependências" "Postgres"
    $pgvectorBashCommand = @'
git clone https://github.com/pgvector/pgvector.git /usr/src/pgvector && cd /usr/src/pgvector && make install
'@
    Run-Command "docker exec postgres_aula bash -c '$pgvectorBashCommand'" "Falha ao compilar pgvector" "Postgres"
    Run-Command "docker exec postgres_aula psql -U postgres -c 'CREATE EXTENSION IF NOT EXISTS vector;'" "Falha ao criar extensão vector" "Postgres"
    Log-Event "✓ Extensão pgvector instalada e ativada." "Postgres" "SUCCESS"
}

# Criar bancos de dados
Log-Event "Criando bancos de dados necessários..." "Postgres" "INFO"
$databases = @("chatwoot", "evolution", "n8n_fila")
foreach ($db in $databases) {
    $checkCmd = "docker exec postgres_aula psql -U postgres -tc `"SELECT 1 FROM pg_database WHERE datname = '$db'`" 2>&1"
    $exists = Invoke-Expression $checkCmd
    if ($exists -notmatch "1") {
        Run-Command "docker exec postgres_aula psql -U postgres -c 'CREATE DATABASE $db;'" "Falha ao criar banco '$db'" "Postgres"
        Log-Event "✓ Banco de dados '$db' criado." "Postgres" "SUCCESS"
    } else {
        Log-Event "✓ Banco de dados '$db' já existe." "Postgres" "INFO"
    }
}

# Configurar pg_hba.conf
Log-Event "Configurando pg_hba.conf para permitir conexões..." "Postgres" "INFO"
# Correção CRÍTICA do comando PostgreSQL com sintaxe robusta
$bashCommand = @'
grep -q "host all all all trust" /var/lib/postgresql/data/pg_hba.conf || echo "host all all all trust" >> /var/lib/postgresql/data/pg_hba.conf
'@
$pgHbaCommand = "docker exec postgres_aula bash -c '$bashCommand'"
Run-Command $pgHbaCommand "Falha ao ajustar pg_hba.conf" "Postgres"
Run-Command "docker exec postgres_aula psql -U postgres -c 'SELECT pg_reload_conf();'" "Falha ao recarregar configuração" "Postgres"
Log-Event "✓ Configuração de acesso do PostgreSQL atualizada." "Postgres" "SUCCESS"

# 8. Reiniciar n8n
Log-Event "Reiniciando n8n para garantir conexão com banco..." "n8n" "INFO"
Run-Command "docker restart n8n_editor-1" "Falha ao reiniciar n8n" "n8n"
Log-Event "✓ n8n reiniciado com sucesso." "n8n" "SUCCESS"

# 9. Verificar Evolution API
Log-Event "Verificando Evolution API..." "Evolution" "INFO"
if (Wait-For-Port "localhost" 8080 180) {
    Log-Event "✓ Evolution API respondendo na porta 8080." "Evolution" "SUCCESS"
    # Garantir que a API esteja plenamente pronta antes de prosseguir
    if (-not (Wait-For-Port "localhost" 8080 60)) {
        Log-Event "Evolution API deixou de responder após verificação inicial." "Evolution" "WARN"
    }
    
    # Testar conectividade com Redis
    Test-EvolutionRedisConnection
    
    # Monitorar logs por problemas do Redis
    Log-Event "Monitorando logs da Evolution API por 30 segundos..." "Evolution-Monitor" "INFO"
    $logMonitorJob = Start-Job -ScriptBlock {
        $redisErrors = 0
        $startTime = Get-Date
        while ((Get-Date) -lt $startTime.AddSeconds(30)) {
            $logs = docker logs evolution_aula --tail 10 --since 5s 2>&1
            foreach ($log in $logs) {
                if ($log -like "*redis disconnected*" -or $log -like "*Redis connection*" -or $log -like "*Error*Redis*") {
                    $redisErrors++
                }
            }
            Start-Sleep -Seconds 5
        }
        return $redisErrors
    }
    
    $redisErrorCount = Receive-Job $logMonitorJob -Wait
    Remove-Job $logMonitorJob
    
    if ($redisErrorCount -gt 0) {
        Log-Event "⚠️ Detectados $redisErrorCount erros relacionados ao Redis nos logs." "Evolution-Monitor" "WARN"
        Log-Event "🔧 Isso confirma o bug conhecido da Evolution API (issues #1289, #652, #1017)." "Evolution-Monitor" "WARN"
    } else {
        Log-Event "✓ Nenhum erro de Redis detectado nos logs recentes." "Evolution-Monitor" "SUCCESS"
    }
    
    # Diagnóstico robusto do QR Code da Evolution API
    Log-Event "Executando diagnóstico robusto do QR Code da Evolution API..." "QRCode-Diagnostics" "INFO"
    
    # Executar teste de prontidão do QR Code
    $qrCodeReady = Test-EvolutionQRCodeReadiness -EvolutionContainer "evolution_aula" -ApiKey $envCredentials['AUTHENTICATION_API_KEY']
    
    if (-not $qrCodeReady) {
        Log-Event "⚠️ Problemas detectados no QR Code da Evolution API. Iniciando reparo automático..." "QRCode-Auto-Repair" "WARN"
        
        # Executar reparo automático
        $repairResult = Repair-EvolutionQRCode -EvolutionContainer "evolution_aula" -ApiKey $envCredentials['AUTHENTICATION_API_KEY']
        
        if ($repairResult) {
            Log-Event "✅ Reparo automático do QR Code concluído com sucesso!" "QRCode-Auto-Repair" "SUCCESS"
        } else {
            Log-Event "❌ Reparo automático do QR Code falhou. Intervenção manual necessária." "QRCode-Auto-Repair" "ERROR"
        }
    } else {
        Log-Event "✅ QR Code da Evolution API está funcionando corretamente!" "QRCode-Diagnostics" "SUCCESS"
    }
} else {
    Log-Event "⚠️ Evolution API não respondeu na porta 8080." "Evolution" "WARN"
}

# 10. Setup do Chatwoot
$chatwootContainer = docker ps --filter "name=chatwoot-rails-1" --format "{{.Names}}"
if (-not $chatwootContainer) { 
    Abort-Install "Container 'chatwoot-rails-1' não está rodando." "Chatwoot"
}

Log-Event "Iniciando setup do Chatwoot..." "Chatwoot" "INFO"
if (-not (Wait-For-Port "localhost" 3000 300)) {
    Abort-Install "Chatwoot não respondeu na porta 3000." "Chatwoot"
}

# Executar migrações
Run-Command "docker exec chatwoot-rails-1 /usr/local/bundle/bin/bundle exec rails db:chatwoot_prepare" "Falha na migração do Chatwoot" "Chatwoot"
Log-Event "✓ Migração do Chatwoot concluída." "Chatwoot" "SUCCESS"

# Criar usuário admin se as credenciais foram geradas
if ($credentialsGenerated -or $envCredentials['CHATWOOT_ADMIN_EMAIL']) {
    Log-Event "Criando usuário administrador do Chatwoot..." "Chatwoot" "INFO"
    $adminEmail = $envCredentials['CHATWOOT_ADMIN_EMAIL']
    $adminPassword = $envCredentials['CHATWOOT_ADMIN_PASSWORD']
    
    # CORREÇÃO: Usar um "here-string" expansível (@"..."@) e corrigir a sintaxe de fechamento.
    # As aspas duplas internas (\") são para o comando bash, e são escapadas para o PowerShell.
    $createUserCmd = @"
docker exec chatwoot-rails-1 bash -c "RAILS_ENV=production bundle exec rails runner \"
  begin
    u = User.find_by(email: '$adminEmail')
    if u.nil?
      u = User.create!(
        email: '$adminEmail',
        password: '$adminPassword',
        password_confirmation: '$adminPassword',
        name: 'Administrator'
      )
      u.add_role(:administrator)
      puts 'Admin user created successfully'
    else
      u.update!(password: '$adminPassword', password_confirmation: '$adminPassword')
      u.add_role(:administrator) unless u.has_role?(:administrator)
      puts 'Admin user updated successfully'
    end
  rescue => e
    puts 'Error: ' + e.message
  end
\""
"@
    
    try {
        $output = Invoke-Expression $createUserCmd 2>&1
        if ($output -match "successfully") {
            Log-Event "✓ Usuário administrador do Chatwoot configurado." "Chatwoot" "SUCCESS"
        } else {
            Log-Event "⚠️ Possível problema ao criar usuário admin: $output" "Chatwoot" "WARN"
        }
    } catch {
        Log-Event "⚠️ Erro ao criar usuário admin do Chatwoot: $($_.Exception.Message)" "Chatwoot" "WARN"
    }
}

# 11. Diagnóstico Final
Log-Event "Executando diagnóstico final dos serviços..." "Final-Check" "INFO"

$finalStatus = @{
    "postgres_aula" = "❌"
    "redis_aula" = "❌" 
    "evolution_aula" = "❌"
    "chatwoot-rails-1" = "❌"
    "n8n_editor-1" = "❌"
    "minio_aula" = "❌"
}

$runningContainers = docker ps --format "{{.Names}}"
foreach ($container in $runningContainers) {
    if ($finalStatus.ContainsKey($container)) {
        $finalStatus[$container] = "✅"
    }
}

# Verificar problemas específicos
$evolutionHasRedisIssue = $false
if ($finalStatus["evolution_aula"] -eq "✅") {
    $recentLogs = docker logs evolution_aula --tail 20 2>&1
    if ($recentLogs -match "redis disconnected" -or $recentLogs -match "Error.*Redis") {
        $evolutionHasRedisIssue = $true
        $finalStatus["evolution_aula"] = "⚠️"
    }
}

# 12. Gerar Dashboard HTML Completo
Log-Event "Gerando dashboard HTML interativo..." "Dashboard" "INFO"

# Verificar se o módulo Generate-ServiceDashboard está disponível
if (Get-Command "Generate-ServiceDashboard" -ErrorAction SilentlyContinue) {
    try {
        # Usar o módulo para gerar o dashboard
        $dashboardPath = "services-dashboard.html"
        Generate-ServiceDashboard -Output $dashboardPath -UseNgrok:$useNgrok -EvolutionHasRedisIssue:$evolutionHasRedisIssue -EnvCredentials $envCredentials
        Log-Event "✓ Dashboard HTML gerado via módulo: $dashboardPath" "Dashboard" "SUCCESS"
    } catch {
        Write-Host-Color "⚠️ Erro ao usar módulo Generate-ServiceDashboard: $($_.Exception.Message)" "Yellow"
        Log-Event "Erro no módulo Generate-ServiceDashboard: $($_.Exception.Message)" "Dashboard" "ERROR"
        Write-Host-Color "📋 Gerando dashboard com método alternativo..." "Yellow"
        
        # Fallback para método original (código simplificado)
        $dashboardPath = "services-dashboard.html"
        $simpleHtml = @"
<!DOCTYPE html>
<html><head><title>Dashboard - N8N Evolution</title></head>
<body>
<h1>N8N Evolution - Dashboard</h1>
<p>Dashboard gerado em modo de fallback. Para funcionalidade completa, verifique o módulo Generate-ServiceDashboard.psm1</p>
<ul>
<li><a href="http://localhost:5678" target="_blank">N8N Editor</a></li>
<li><a href="http://localhost:8080/manager" target="_blank">Evolution API Manager</a></li>
<li><a href="http://localhost:3000" target="_blank">Chatwoot</a></li>
<li><a href="http://localhost:9001" target="_blank">MinIO Console</a></li>
</ul>
</body></html>
"@
        $simpleHtml | Out-File -FilePath $dashboardPath -Encoding UTF8 -NoNewline
        Log-Event "Dashboard HTML gerado em modo fallback: $dashboardPath" "Dashboard" "SUCCESS"
    }
} else {
    Write-Host-Color "⚠️ Módulo Generate-ServiceDashboard não encontrado" "Yellow"
    Log-Event "Módulo Generate-ServiceDashboard não disponível" "Dashboard" "WARNING"
    Write-Host-Color "📋 Gerando dashboard com método alternativo..." "Yellow"
    
    # Fallback para método original (código simplificado)
    $dashboardPath = "services-dashboard.html"
    $simpleHtml = @"
<!DOCTYPE html>
<html><head><title>Dashboard - N8N Evolution</title></head>
<body>
<h1>N8N Evolution - Dashboard</h1>
<p>Dashboard gerado em modo de fallback. Para funcionalidade completa, verifique o módulo Generate-ServiceDashboard.psm1</p>
<ul>
<li><a href="http://localhost:5678" target="_blank">N8N Editor</a></li>
<li><a href="http://localhost:8080/manager" target="_blank">Evolution API Manager</a></li>
<li><a href="http://localhost:3000" target="_blank">Chatwoot</a></li>
<li><a href="http://localhost:9001" target="_blank">MinIO Console</a></li>
</ul>
</body></html>
"@
    $simpleHtml | Out-File -FilePath $dashboardPath -Encoding UTF8 -NoNewline
    Log-Event "Dashboard HTML gerado em modo fallback: $dashboardPath" "Dashboard" "SUCCESS"
}

# Configuração completa dos serviços com TODAS as credenciais (mantida para compatibilidade)
$services = @(
    @{
        N='n8n'
        Url='http://localhost:5678'
        Desc='Editor de workflows de automação. Requer configuração manual de Webhook.'
        U=''
        Pwd=''
        Container='n8n_editor-1'
        Health='http://localhost:5678'
        ExtraInfo=@{
            'N8N_ENCRYPTION_KEY' = $envCredentials['N8N_ENCRYPTION_KEY']
            'Webhook Config Info' = 'EVOLUTION_API -> WEBHOOK TARGET URL: http://host.docker.internal:5678/api/n8n-evolution'
            'Webhook Setup' = 'Configure um nó Webhook no n8n com path: /api/n8n-evolution'
        }
    },
    @{
        N='Evolution API'
        Url='http://localhost:8080'
        Desc='API WhatsApp e Integração'
        U=''
        Pwd=$envCredentials['AUTHENTICATION_API_KEY']
        Container='evolution_aula'
        Health='http://localhost:8080'
        ExtraInfo=@{
            'Manager' = 'http://localhost:8080/manager'
            'Observação' = 'A documentação da Evolution API não é servida em /docs. Consulte a documentação oficial do projeto.'
        }
    },
    @{
        N='Chatwoot'
        Url='http://localhost:3000'
        Desc='Plataforma de atendimento omnichannel'
        U=$envCredentials['CHATWOOT_ADMIN_EMAIL']
        Pwd=$envCredentials['CHATWOOT_ADMIN_PASSWORD']
        Container='chatwoot-rails-1'
        Health='http://localhost:3000'
        ExtraInfo=@{
            'SECRET_KEY_BASE' = $envCredentials['SECRET_KEY_BASE']
        }
    },
    @{
        N='MinIO Console'
        Url='http://localhost:9001'
        Desc='Console de armazenamento S3'
        U=$envCredentials['MINIO_ROOT_USER']
        Pwd=$envCredentials['MINIO_ROOT_PASSWORD']
        Container='minio_aula'
        Health='http://localhost:9001'
        ExtraInfo=@{
            'S3 Endpoint' = 'http://localhost:9000'
        }
    },
    @{
        N='PostgreSQL'
        Url='localhost:5432'
        Desc='Banco de dados principal'
        U='postgres'
        Pwd=$envCredentials['POSTGRES_PASSWORD']
        Container='postgres_aula'
        Health=''
        ExtraInfo=@{
            'Databases' = 'chatwoot, evolution, n8n_fila'
        }
    },
    @{
        N='Redis'
        Url='localhost:6379'
        Desc='Cache e sessões'
        U=''
        Pwd=''
        Container='redis_aula'
        Health=''
        ExtraInfo=@{
            'Database' = '6 (Evolution API)'
        }
    }
)

# 13. Resumo Final no Console
Write-Host-Color "`n╔════════════════════════════════════════════════════════════╗" "Magenta"
Write-Host-Color "║      AMBIENTE DE AUTOMAÇÃO INICIADO COM SUCESSO!        ║" "Magenta"
Write-Host-Color "╚════════════════════════════════════════════════════════════╝" "Magenta"

Write-Host-Color "`n📋 STATUS DOS SERVIÇOS:" "White"
Write-Host-Color "  PostgreSQL + pgvector: $($finalStatus['postgres_aula'])" "White"
Write-Host-Color "  Redis: $($finalStatus['redis_aula'])" "White"
Write-Host-Color "  Evolution API: $($finalStatus['evolution_aula'])" "White"
Write-Host-Color "  Chatwoot: $($finalStatus['chatwoot-rails-1'])" "White"
Write-Host-Color "  n8n: $($finalStatus['n8n_editor-1'])" "White"
Write-Host-Color "  MinIO: $($finalStatus['minio_aula'])" "White"

if ($evolutionHasRedisIssue) {
    Write-Host-Color "`n⚠️  PROBLEMA CONHECIDO DETECTADO:" "Yellow"
    Write-Host-Color "   Evolution API apresenta instabilidade com Redis (issues #1289, #652, #1017)" "Yellow"
    Write-Host-Color "   A API funciona mas pode apresentar falhas na criação de instâncias WhatsApp" "Yellow"
    Write-Host-Color "   Consulte REDIS-TROUBLESHOOTING.md para mais detalhes" "Yellow"
}

Write-Host-Color "`n🌐 ACESSE OS SERVIÇOS:" "Cyan"
Write-Host-Color "  🎯 Dashboard Completo: services-dashboard.html" "Green"

if ($useNgrok -and $env:WEBHOOK_URL -and $env:WEBHOOK_URL -ne "") {
    Write-Host-Color "  📈 n8n (Público): $env:WEBHOOK_URL" "Green"
    Write-Host-Color "  📈 n8n (Local): http://localhost:5678" "White"
    Write-Host-Color "  📱 Evolution API (Local): http://localhost:8080/manager" "White"
    Write-Host-Color "  💬 Chatwoot (Local): http://localhost:3000" "White"
    Write-Host-Color "  📦 MinIO Console (Local): http://localhost:9001" "White"
    Write-Host-Color "  🔧 Ngrok Inspector: http://localhost:4040" "Cyan"
    Write-Host-Color "`n🌐 WEBHOOKS PÚBLICOS CONFIGURADOS:" "Green"
    Write-Host-Color "  📡 N8N Webhook Base: $env:WEBHOOK_URL" "Green"
    Write-Host-Color "  📱 Evolution Webhook: $env:WEBHOOK_URL/webhook/evolution" "Green"
} else {
    Write-Host-Color "  📈 n8n: http://localhost:5678" "White"
    Write-Host-Color "  📱 Evolution API: http://localhost:8080/manager" "White"
    Write-Host-Color "  💬 Chatwoot: http://localhost:3000" "White"
    Write-Host-Color "  📦 MinIO Console: http://localhost:9001" "White"
}

Write-Host-Color "`n🔑 CREDENCIAIS GERADAS (salvas em .env):" "Yellow"
Write-Host-Color "╔════════════════════════════════════════════════════════════════════╗" "Yellow"
Write-Host-Color "║ PostgreSQL                                                         ║" "Yellow"
Write-Host-Color "║   Usuário: postgres                                                ║" "Yellow"
Write-Host-Color "║   Senha: $($envCredentials['POSTGRES_PASSWORD'])" "Yellow"
Write-Host-Color "║                                                                    ║" "Yellow"
Write-Host-Color "║ MinIO                                                              ║" "Yellow"
Write-Host-Color "║   Usuário: $($envCredentials['MINIO_ROOT_USER'])" "Yellow"
Write-Host-Color "║   Senha: $($envCredentials['MINIO_ROOT_PASSWORD'])" "Yellow"
Write-Host-Color "║                                                                    ║" "Yellow"
Write-Host-Color "║ Evolution API                                                      ║" "Yellow"
Write-Host-Color "║   API Key: $($envCredentials['AUTHENTICATION_API_KEY'])" "Yellow"
Write-Host-Color "║                                                                    ║" "Yellow"
Write-Host-Color "║ Chatwoot                                                           ║" "Yellow"
Write-Host-Color "║   Email: $($envCredentials['CHATWOOT_ADMIN_EMAIL'])" "Yellow"
Write-Host-Color "║   Senha: $($envCredentials['CHATWOOT_ADMIN_PASSWORD'])" "Yellow"
Write-Host-Color "║   Secret: $($envCredentials['SECRET_KEY_BASE'].Substring(0, 20))..." "Yellow"
Write-Host-Color "║                                                                    ║" "Yellow"
Write-Host-Color "║ n8n                                                                ║" "Yellow"
Write-Host-Color "║   Encryption Key: $($envCredentials['N8N_ENCRYPTION_KEY'].Substring(0, 20))..." "Yellow"
Write-Host-Color "╚════════════════════════════════════════════════════════════════════╝" "Yellow"

Write-Host-Color "`nARQUIVOS CRIADOS:" "White"
Write-Host-Color "  install.log - Logs completos da instalação" "Green"
Write-Host-Color "  services-dashboard.html - Dashboard interativo" "Green"
Write-Host-Color "  .env - Arquivo com todas as credenciais" "Green"

# 14. Detectar primeira instalação e executar post-setup-automation.ps1 automaticamente
$firstInstallMarker = Join-Path $PSScriptRoot ".first-install-completed"
$isFirstInstall = $credentialsGenerated -and (-not (Test-Path $firstInstallMarker))

if ($isFirstInstall) {
    Write-Host-Color "`n🛠️ PRIMEIRA INSTALAÇÃO DETECTADA - Executando configuração automática avançada..." "Cyan"
    Log-Event "Primeira instalação detectada. Executando post-setup-automation.ps1 automaticamente..." "Auto-Setup" "INFO"
    
    # Aguardar 10 segundos para garantir que todos os serviços estejam estáveis
    Write-Host-Color "Aguardando estabilização dos serviços - 10 segundos..." "Yellow"
    Start-Sleep -Seconds 10
    
    try {
        $postSetupScript = Join-Path $PSScriptRoot "post-setup-automation.ps1"
        if (Test-Path $postSetupScript) {
            Write-Host-Color "🚀 Executando configuração automática..." "Green"
            
            # Executar o script post-setup-automation.ps1
            & $postSetupScript
            
            if ($LASTEXITCODE -eq 0) {
                Write-Host-Color "✅ Configuração automática concluída com sucesso!" "Green"
                Log-Event "post-setup-automation.ps1 executado com sucesso" "Auto-Setup" "SUCCESS"
                
                # Marcar primeira instalação como concluída
                $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
                "$timestamp - Primeira instalação e configuração automática concluída" | Out-File $firstInstallMarker -Encoding UTF8
                
                Write-Host-Color "`n🥳 SISTEMA TOTALMENTE CONFIGURADO!" "Green"
                Write-Host-Color "📋 ÚNICA ETAPA MANUAL RESTANTE:" "Yellow"
                Write-Host-Color "   • Configurar webhook N8N ↔️ Evolution API manualmente" "Yellow"
                Write-Host-Color "   • Acesse: http://localhost:5678 (N8N) e http://localhost:8080/manager (Evolution)" "Yellow"
                Write-Host-Color "   • Configure webhook path: /api/n8n-evolution" "Yellow"
                
            } else {
                Write-Host-Color "⚠️  Configuração automática falhou. Execute manualmente: .\post-setup-automation.ps1" "Yellow"
                Log-Event "post-setup-automation.ps1 falhou com código: $LASTEXITCODE" "Auto-Setup" "ERROR"
            }
        } else {
            Write-Host-Color "⚠️  Script post-setup-automation.ps1 não encontrado. Configuração manual necessária." "Yellow"
            Log-Event "post-setup-automation.ps1 não encontrado em: $postSetupScript" "Auto-Setup" "ERROR"
        }
    } catch {
        Write-Host-Color "❌ Erro ao executar configuração automática: $($_.Exception.Message)" "Red"
        Log-Event "Erro ao executar post-setup-automation.ps1: $($_.Exception.Message)" "Auto-Setup" "ERROR"
        Write-Host-Color "📋 Execute manualmente: .\post-setup-automation.ps1" "Yellow"
    }
} else {
    Write-Host-Color "`n🔄 INSTALAÇÃO EXISTENTE DETECTADA" "Cyan"
    if (Test-Path $firstInstallMarker) {
        $markerContent = Get-Content $firstInstallMarker -Raw
        Write-Host-Color "🗓️ Primeira configuração já foi executada: $($markerContent.Trim())" "Green"
        Write-Host-Color "🛠️ Para reconfigurar, execute manualmente: .\post-setup-automation.ps1" "White"
    } else {
        Write-Host-Color "🛠️ Para configuração completa, execute: .\post-setup-automation.ps1" "Yellow"
    }
}

# 15. Abrir Dashboard Automaticamente
Write-Host-Color "`n🖱️ Abrindo dashboard no navegador..." "Green"
try {
    $dashboardFullPath = Resolve-Path $dashboardPath
    Open-UrlInChrome -Url "file://$dashboardFullPath" -Component "Dashboard"
    Open-UrlInChrome -Url "http://localhost:8080/manager" -Component "EvolutionManager"
    Log-Event "Dashboard e Evolution Manager abertos automaticamente no Chrome." "Dashboard" "SUCCESS"
    Write-Host-Color "Dashboard disponível em: $dashboardFullPath" "Green"
} catch {
    Write-Host-Color "⚠️ Não foi possível abrir automaticamente: $($_.Exception.Message)" "Yellow"
    Write-Host-Color "  Acesse manualmente: $dashboardFullPath e http://localhost:8080/manager" "Yellow"
}

Write-Host-Color "`n✨ Instalação concluída com sucesso!" "Green"
Write-Host-Color "📋 Verifique o arquivo install.log para detalhes completos." "White"

if ($evolutionHasRedisIssue) {
    Write-Host-Color "`n📋 PRÓXIMOS PASSOS RECOMENDADOS:" "Cyan"
    Write-Host-Color "   1. Monitore os logs da Evolution API: docker logs -f evolution_aula" "Cyan"
    Write-Host-Color "   2. Teste versões alternativas se necessário" "Cyan"
    Write-Host-Color "   3. Reporte problemas persistentes no GitHub" "Cyan"
}

# Mostrar instruções simplificadas apenas se não for primeira instalação
if (-not $isFirstInstall) {
    Write-Host-Color "`n🛠️ CONFIGURAÇÃO MANUAL DISPONÍVEL:" "Yellow"
    Write-Host-Color "   • Para configuração avançada: .\post-setup-automation.ps1" "Yellow"
    Write-Host-Color "   • Para workflows específicos: .\Onboard-Workflow.ps1 -WorkflowName \"NomeDoWorkflow\"" "Yellow"
}

# Fim do script
Log-Event "Script finalizado com sucesso!" "Orchestrator" "SUCCESS"