# Start-Environment.ps1 - Orquestrador Zero-Touch Robusto com Logging Aprimorado e Entrega Completa de Credenciais
# Versão: 3.0 - Módulo centralizado e arquitetura robusta

# =====================================================
# IMPORTAR MÓDULO DE UTILITÁRIOS DE AUTOMAÇÃO
# =====================================================

$utilityModuleName = "AutomationUtils"
# Assume que o módulo está na mesma pasta do script PS, ou em um subdiretório
# Se PowerShellModules estiver no mesmo nível que os scripts PS:
$modulePath = Join-Path $PSScriptRoot ".\PowerShellModules\$utilityModuleName.psm1"
# Se PowerShellModules estiver um diretório acima dos scripts PS:
# $modulePath = Join-Path $PSScriptRoot "..\PowerShellModules\$utilityModuleName.psm1"

if (Test-Path $modulePath) {
    try {
        Import-Module $modulePath -Force -ErrorAction Stop
        if (-not (Get-Command Log-Event -ErrorAction SilentlyContinue)) { # Verificação de fallback robusta
            Write-Error "ERRO FATAL: Módulo '$utilityModuleName' importado, mas a função Log-Event não foi encontrada. O módulo pode estar corrompido. Verifique '$modulePath'."
            exit 1
        }
        Log-Event "Módulo '$utilityModuleName' carregado com sucesso." "ModuleLoader" "SUCCESS"
    } catch {
        Write-Error "ERRO FATAL: Falha crítica ao importar o módulo '$utilityModuleName' de '$modulePath'. As funções utilitárias são essenciais. Verifique a instalação do módulo."
        exit 1
    }
} else {
    Write-Error "ERRO FATAL: Módulo de utilidades do PowerShell não encontrado em '$modulePath'. O projeto não pode continuar sem funções de suporte essenciais."
    exit 1
}

# =====================================================
# INÍCIO DA EXECUÇÃO PRINCIPAL
# =====================================================

Write-Host-Color "`n╔══════════════════════════════════════════════════════════╗" "Cyan"
Write-Host-Color "║      N8N EVOLUTION ENVIRONMENT - INSTALADOR ROBUSTO      ║" "Cyan"
Write-Host-Color "║           Versão 2.0 - Diagnósticos Avançados            ║" "Cyan"
Write-Host-Color "╚══════════════════════════════════════════════════════════╝" "Cyan"

# =====================================================
# DETECÇÃO DE INSTALAÇÃO EXISTENTE E MENU DE OPÇÕES
# =====================================================

$firstInstallMarker = Join-Path $PSScriptRoot ".first-install-completed"
$envFile = ".env"
$hasExistingInstallation = (Test-Path $envFile) -or (Test-Path $firstInstallMarker)

if ($hasExistingInstallation) {
    Log-Event "Instalação existente detectada. Exibindo menu de opções..." "Menu" "INFO"

    Write-Host-Color "`n🔍 INSTALAÇÃO EXISTENTE DETECTADA" "Yellow"
    Write-Host-Color "═══════════════════════════════════════════════════════════" "Yellow"

    # Verificar status dos serviços principais
    $servicesRunning = @()
    $servicesDown = @()
    $criticalServices = @("postgres_aula", "redis_aula", "evolution_aula", "n8n_editor-1")

    foreach ($service in $criticalServices) {
        try {
            $status = docker inspect --format "{{.State.Status}}" $service 2>$null
            if ($status -eq "running") {
                $servicesRunning += $service
            } else {
                $servicesDown += $service
            }
        } catch {
            $servicesDown += $service
        }
    }

    Write-Host-Color "`n📊 Status dos Serviços:" "White"
    if ($servicesRunning.Count -gt 0) {
        Write-Host-Color "   ✅ Rodando: $($servicesRunning -join ', ')" "Green"
    }
    if ($servicesDown.Count -gt 0) {
        Write-Host-Color "   ❌ Parados: $($servicesDown -join ', ')" "Red"
    }

    Write-Host-Color "`n🎛️  OPÇÕES DISPONÍVEIS:" "Cyan"
    Write-Host-Color "═══════════════════════════════════════════════════════════" "Cyan"

    Write-Host-Color "`n📋 GERENCIAMENTO DE INFRAESTRUTURA:" "White"
    Write-Host "   [1] Continue and restart services - Verificar status e funcionalidade" -ForegroundColor White
    Write-Host "   [2] Complete removal - Remoção completa do sistema" -ForegroundColor White
    Write-Host "   [3] Complete removal and reinstall - Remoção completa + nova instalação" -ForegroundColor White
    Write-Host "   [4] Apply post-installation automation - Executar processos automatizados" -ForegroundColor White

    Write-Host-Color "`n🔄 GERENCIAMENTO DE WORKFLOWS:" "White"
    Write-Host "   [5] Scan workflows directory - Verificar workflows que precisam de ajustes" -ForegroundColor White

    Write-Host-Color "`n═══════════════════════════════════════════════════════════" "Cyan"
    Write-Host-Color "⏰ Seleção automática em 5 segundos: [1] Continue and restart services" "Yellow"
    Write-Host-Color "💡 Pressione qualquer tecla para interromper e escolher manualmente" "Gray"

    # Countdown timer com interrupção
    $timerCompleted = Show-CountdownTimer -Seconds 5 -Message "Prosseguindo automaticamente com opção [1] em"

    $selectedOption = "1"  # Padrão

    if (-not $timerCompleted) {
        # Usuário interrompeu - solicitar escolha manual
        Write-Host-Color "`n🎯 Escolha uma opção [1-5]: " "Yellow" -NoNewline
        $userInput = Read-Host

        if ($userInput -match '^[1-5]$') {
            $selectedOption = $userInput
        } else {
            Write-Host-Color "⚠️ Opção inválida. Usando padrão [1] Continue and restart services" "Yellow"
            $selectedOption = "1"
        }
    }

    Log-Event "Opção selecionada: $selectedOption" "Menu" "INFO"

    # Processar opção selecionada
    switch ($selectedOption) {
        "1" {
            Write-Host-Color "`n🔄 Executando: Continue and restart services" "Green"
            Log-Event "Usuário selecionou: Continue and restart services" "Menu" "INFO"
            # Continuar com o fluxo normal do script
        }
        "2" {
            Write-Host-Color "`n🗑️ Executando: Complete removal" "Red"
            Log-Event "Usuário selecionou: Complete removal" "Menu" "INFO"

            Write-Host-Color "`n⚠️ ATENÇÃO: Esta operação irá remover TODOS os dados e configurações!" "Red"
            Write-Host-Color "Tem certeza? Digite 'CONFIRMAR' para prosseguir: " "Yellow" -NoNewline
            $confirmation = Read-Host

            if ($confirmation -eq "CONFIRMAR") {
                Write-Host-Color "`n🧹 Iniciando remoção completa..." "Red"

                # Parar e remover todos os contêineres
                Log-Event "Parando todos os contêineres..." "Removal" "INFO"
                docker-compose down -v --remove-orphans 2>&1 | ForEach-Object { Log-Event $_ "Removal" "DEBUG" }

                # Remover arquivos de configuração
                $filesToRemove = @(".env", "services-dashboard.html", $firstInstallMarker, "install.log")
                foreach ($file in $filesToRemove) {
                    if (Test-Path $file) {
                        Remove-Item $file -Force
                        Log-Event "Arquivo removido: $file" "Removal" "INFO"
                    }
                }

                Write-Host-Color "`n✅ Remoção completa concluída!" "Green"
                Log-Event "Remoção completa concluída com sucesso" "Removal" "SUCCESS"
                exit 0
            } else {
                Write-Host-Color "`n❌ Remoção cancelada pelo usuário" "Yellow"
                exit 0
            }
        }
        "3" {
            Write-Host-Color "`n🔄 Executando: Complete removal and reinstall" "Yellow"
            Log-Event "Usuário selecionou: Complete removal and reinstall" "Menu" "INFO"

            Write-Host-Color "`n⚠️ ATENÇÃO: Esta operação irá remover TODOS os dados e fazer nova instalação!" "Red"
            Write-Host-Color "Tem certeza? Digite 'CONFIRMAR' para prosseguir: " "Yellow" -NoNewline
            $confirmation = Read-Host

            if ($confirmation -eq "CONFIRMAR") {
                Write-Host-Color "`n🧹 Iniciando remoção completa e reinstalação..." "Yellow"

                # Parar e remover todos os contêineres
                Log-Event "Parando todos os contêineres para reinstalação..." "Reinstall" "INFO"
                docker-compose down -v --remove-orphans 2>&1 | ForEach-Object { Log-Event $_ "Reinstall" "DEBUG" }

                # Remover arquivos de configuração (exceto logs para auditoria)
                $filesToRemove = @(".env", "services-dashboard.html", $firstInstallMarker)
                foreach ($file in $filesToRemove) {
                    if (Test-Path $file) {
                        Remove-Item $file -Force
                        Log-Event "Arquivo removido para reinstalação: $file" "Reinstall" "INFO"
                    }
                }

                Write-Host-Color "`n🚀 Iniciando nova instalação..." "Green"
                Log-Event "Remoção concluída. Iniciando nova instalação..." "Reinstall" "SUCCESS"

                # Forçar nova instalação
                $hasExistingInstallation = $false
                # Continuar com o fluxo normal do script
            } else {
                Write-Host-Color "`n❌ Reinstalação cancelada pelo usuário" "Yellow"
                exit 0
            }
        }
        "4" {
            Write-Host-Color "`n🔧 Executando: Apply post-installation automation" "Cyan"
            Log-Event "Usuário selecionou: Apply post-installation automation" "Menu" "INFO"

            $postSetupScript = Join-Path $PSScriptRoot "post-setup-automation.ps1"
            if (Test-Path $postSetupScript) {
                Write-Host-Color "`n🚀 Executando post-setup-automation.ps1..." "Green"
                try {
                    & $postSetupScript
                    if ($LASTEXITCODE -eq 0) {
                        Write-Host-Color "`n✅ Automação pós-instalação concluída com sucesso!" "Green"
                    } else {
                        Write-Host-Color "`n⚠️ Automação pós-instalação concluída com avisos (código: $LASTEXITCODE)" "Yellow"
                    }
                } catch {
                    Write-Host-Color "`n❌ Erro ao executar automação: $($_.Exception.Message)" "Red"
                }
            } else {
                Write-Host-Color "`n❌ Script post-setup-automation.ps1 não encontrado!" "Red"
            }
            exit 0
        }
        "5" {
            Write-Host-Color "`n🔍 Executando: Scan workflows directory" "Cyan"
            Log-Event "Usuário selecionou: Scan workflows directory" "Menu" "INFO"

            # Escanear workflows
            $workflowScanResult = Scan-WorkflowsDirectory -WorkflowsPath "workflows"

            if ($workflowScanResult.Success) {
                Write-Host-Color "`n📊 Resultado do escaneamento:" "White"
                Write-Host-Color "   Total de workflows encontrados: $($workflowScanResult.TotalWorkflows)" "White"
                Write-Host-Color "   Workflows que precisam de ajustes: $($workflowScanResult.WorkflowsNeedingAdjustment.Count)" "Yellow"

                if ($workflowScanResult.WorkflowsNeedingAdjustment.Count -gt 0) {
                    # Mostrar menu de seleção de workflows
                    $selectedWorkflows = Show-WorkflowSelectionMenu -WorkflowsNeedingAdjustment $workflowScanResult.WorkflowsNeedingAdjustment

                    if ($selectedWorkflows.Count -gt 0) {
                        Write-Host-Color "`n🔧 Aplicando ajustes nos workflows selecionados..." "Green"

                        foreach ($workflow in $selectedWorkflows) {
                            Write-Host-Color "`n📋 Processando: $($workflow.Name)" "Cyan"

                            $onboardScript = Join-Path $PSScriptRoot "Onboard-Workflow.ps1"
                            if (Test-Path $onboardScript) {
                                try {
                                    & $onboardScript -WorkflowName $workflow.Name
                                    if ($LASTEXITCODE -eq 0) {
                                        Write-Host-Color "   ✅ $($workflow.Name) processado com sucesso" "Green"
                                    } else {
                                        Write-Host-Color "   ⚠️ $($workflow.Name) processado com avisos" "Yellow"
                                    }
                                } catch {
                                    Write-Host-Color "   ❌ Erro ao processar $($workflow.Name): $($_.Exception.Message)" "Red"
                                }
                            } else {
                                Write-Host-Color "   ❌ Script Onboard-Workflow.ps1 não encontrado!" "Red"
                            }
                        }

                        Write-Host-Color "`n✅ Processamento de workflows concluído!" "Green"
                    }
                } else {
                    Write-Host-Color "`n✅ Todos os workflows estão prontos para uso!" "Green"
                }
            } else {
                Write-Host-Color "`n❌ Erro ao escanear workflows: $($workflowScanResult.ErrorMessage)" "Red"
            }
            exit 0
        }
    }

    Write-Host-Color "`n" "White"
}

# 1. Validação de pré-requisitos
Log-Event "Iniciando validação de pré-requisitos..." "Orchestrator" "INFO"
if (-not (Get-Command docker -ErrorAction SilentlyContinue)) { 
    Abort-Install "Docker não está instalado ou não está no PATH." "Prerequisites"
}
if (-not (Get-Command docker-compose -ErrorAction SilentlyContinue) -and -not (docker compose version 2>$null)) {
    Abort-Install "Docker Compose não está instalado ou não está no PATH." "Prerequisites"
}
if ($PSVersionTable.PSVersion.Major -lt 5) { 
    Abort-Install "PowerShell 5.1 ou superior é necessário." "Prerequisites"
}
Log-Event "✓ Validação de pré-requisitos concluída com sucesso." "Prerequisites" "SUCCESS"

# 2. Gestão de Configuração (.env) - COMPLETA
Log-Event "Iniciando gerenciamento de configuração .env..." "Config" "INFO"
$envFile = ".env"
$credentialsGenerated = $false

if (-not (Test-Path $envFile)) {
    Log-Event "Arquivo .env não encontrado. Gerando novas credenciais completas..." "Config" "WARN"
    $credentialsGenerated = $true
    
    # Obter IP do host de forma robusta
    if ($env:HOST_IP -and $env:HOST_IP.Trim()) {
        $hostIp = $env:HOST_IP.Trim()
        Log-Event "HOST_IP pré-definido detectado: $hostIp" "Config" "INFO"
    } else {
        $hostIp = Get-PrimaryPrivateIPv4
        Log-Event "HOST_IP detectado automaticamente: $hostIp" "Config" "INFO"
    }
    
    # Gerar todas as credenciais necessárias
    $postgresPassword = Generate-RandomPassword 16
    $minioPassword = Generate-RandomPassword 20
    $evolutionApiKey = Generate-RandomPassword 32
    $secretKeyBase = Generate-RandomPassword 64
    $n8nEncryptionKey = Generate-SecureKey 32
    $chatwootAdminEmail = "<EMAIL>"
    $chatwootAdminPassword = Generate-RandomPassword 16
    
    # Criar arquivo .env completo
    @"
# Credenciais do PostgreSQL
POSTGRES_PASSWORD=$postgresPassword

# Credenciais do MinIO
MINIO_ROOT_USER=admin
MINIO_ROOT_PASSWORD=$minioPassword

# Evolution API
AUTHENTICATION_API_KEY=$evolutionApiKey

# Chatwoot
SECRET_KEY_BASE=$secretKeyBase
CHATWOOT_ADMIN_EMAIL=$chatwootAdminEmail
CHATWOOT_ADMIN_PASSWORD=$chatwootAdminPassword

# n8n
N8N_ENCRYPTION_KEY=$n8nEncryptionKey

# Sistema
HOST_IP=$hostIp

# Configuração da Evolution API para QR Code
CONFIG_SESSION_PHONE_VERSION=2.3000.1020885143
"@ | Out-File -FilePath $envFile -Encoding utf8
    Log-Event "✓ Arquivo .env criado com todas as credenciais!" "Config" "SUCCESS"
} else {
    Log-Event "Arquivo .env existente encontrado. Verificando integridade..." "Config" "INFO"
    
    # Verificar e adicionar credenciais faltantes
    $envContent = Get-Content $envFile -Raw
    $updates = @()
    
    if ($envContent -notmatch "(?m)^SECRET_KEY_BASE\s*=\s*\S+") {
        $newSecret = Generate-RandomPassword 64
        Add-Content -Path $envFile -Value "SECRET_KEY_BASE=$newSecret"
        $updates += "SECRET_KEY_BASE"
    }
    
    if ($envContent -notmatch "(?m)^N8N_ENCRYPTION_KEY\s*=\s*\S+") {
        $n8nKey = Generate-SecureKey 32
        Add-Content -Path $envFile -Value "N8N_ENCRYPTION_KEY=$n8nKey"
        $updates += "N8N_ENCRYPTION_KEY"
    }
    
    if ($envContent -notmatch "(?m)^CHATWOOT_ADMIN_EMAIL\s*=\s*\S+") {
        Add-Content -Path $envFile -Value "CHATWOOT_ADMIN_EMAIL=<EMAIL>"
        $updates += "CHATWOOT_ADMIN_EMAIL"
    }
    
    if ($envContent -notmatch "(?m)^CHATWOOT_ADMIN_PASSWORD\s*=\s*\S+") {
        $chatwootPwd = Generate-RandomPassword 16
        Add-Content -Path $envFile -Value "CHATWOOT_ADMIN_PASSWORD=$chatwootPwd"
        $updates += "CHATWOOT_ADMIN_PASSWORD"
    }

    if ($envContent -notmatch "(?m)^CONFIG_SESSION_PHONE_VERSION\s*=\s*\S+") {
        Add-Content -Path $envFile -Value "CONFIG_SESSION_PHONE_VERSION=2.3000.1020885143"
        $updates += "CONFIG_SESSION_PHONE_VERSION"
    }
    
    if ($updates.Count -gt 0) {
        Log-Event "Variáveis adicionadas ao .env: $($updates -join ', ')" "Config" "WARN"
    }
}

Log-Event "Carregando variáveis de ambiente do arquivo .env..." "Config" "INFO"
# Carrega todas as variáveis do .env para a sessão atual
$envCredentials = @{}
Get-Content $envFile | ForEach-Object {
    if ($_ -match "^([^=]+)=(.*)$") {
        $key = $matches[1].Trim()
        $value = $matches[2].Trim()
        [System.Environment]::SetEnvironmentVariable($key, $value)
        $envCredentials[$key] = $value
    }
}
Log-Event "✓ Variáveis de ambiente carregadas com sucesso." "Config" "SUCCESS"

# Exibir chave da Evolution API para o usuário colar no Manager
if ($envCredentials.ContainsKey('AUTHENTICATION_API_KEY')) {
    $apiKeyDisplay = $envCredentials['AUTHENTICATION_API_KEY']
    Log-Event "⚠ Copie esta chave para o Manager da Evolution API: $apiKeyDisplay" "Evolution" "WARN"
    Write-Host "[Evolution API] Chave de autenticação: $apiKeyDisplay" -ForegroundColor Yellow
}

# 3. Criação da Rede Docker
Log-Event "Verificando rede Docker 'app_network'..." "Docker" "INFO"
$inspectOutput = docker network inspect app_network --format "{{.Driver}}" 2>$null

if ($LASTEXITCODE -ne 0) {
    # Rede não existe – criar com driver bridge para compatibilidade
    Log-Event "Rede 'app_network' não encontrada. Criando..." "Docker" "WARN"
    Run-Command "docker network create --driver bridge app_network" "Falha ao criar a rede Docker 'app_network'." "Docker"
    Log-Event "✓ Rede 'app_network' criada com sucesso (driver bridge)." "Docker" "SUCCESS"
} else {
    $driver = $inspectOutput.Trim()
    Log-Event "Rede 'app_network' encontrada com driver '$driver'." "Docker" "INFO"
    if ($driver -ne "bridge") {
        Log-Event "⚠ Driver da rede difere de 'bridge'. Certifique-se de que isso é intencional para evitar problemas de conectividade." "Docker" "WARN"
    }
}

# 4. Subir todos os serviços
Log-Event "Iniciando todos os serviços via docker-compose..." "Docker" "INFO"
Run-Command "docker compose -f docker-compose.yml up -d" "Falha ao subir os serviços com docker-compose." "Docker"
Log-Event "✓ Comando 'docker compose up' executado. Aguardando estabilização..." "Docker" "SUCCESS"

# 5. Aguardar serviços críticos
Log-Event "Aguardando serviços críticos ficarem saudáveis..." "HealthCheck" "INFO"

# PostgreSQL
if (-not (Wait-For-Service-Healthy "postgres_aula" 180)) {
    Abort-Install "Serviço 'postgres_aula' não ficou saudável dentro do tempo limite." "Postgres"
}

# Redis
if (-not (Wait-For-Service-Healthy "redis_aula" 120)) {
    Log-Event "Redis não ficou saudável após reinicialização." "Redis-Diagnostics" "ERROR"
} else {
    # Verifica conectividade lógica
    if (-not (Test-RedisConnectivity)) {
        Log-Event "Redis saudável, porém teste de conectividade falhou." "Redis-Diagnostics" "ERROR"
    }
}

# MinIO
if (-not (Wait-For-Service-Healthy "minio_aula" 180)) {
    Abort-Install "Serviço 'minio_aula' não ficou saudável dentro do tempo limite." "MinIO"
}

# 6. Diagnóstico completo do Redis
Log-Event "Executando diagnósticos específicos do Redis..." "Redis-Diagnostics" "INFO"
if (-not (Test-RedisConnectivity)) {
    Log-Event "⚠ Problemas detectados no Redis. Tentando reinicialização..." "Redis-Diagnostics" "WARN"
    Run-Command "docker restart redis_aula" "Falha ao reiniciar o Redis" "Redis"
    if (-not (Wait-For-Service-Healthy "redis_aula" 120)) {
        Log-Event "Redis não ficou saudável após reinicialização." "Redis-Diagnostics" "ERROR"
    }
}

# 7. Setup do PostgreSQL
$pgContainer = docker ps --filter "name=postgres_aula" --format "{{.Names}}"
if (-not $pgContainer) { 
    Abort-Install "Container 'postgres_aula' não está rodando." "Postgres"
}

Log-Event "Iniciando setup avançado do PostgreSQL..." "Postgres" "INFO"

# Instalar pgvector (idempotente)
Log-Event "Verificando existência da extensão pgvector..." "Postgres" "INFO"
$vectorExists = Invoke-Expression "docker exec postgres_aula psql -U postgres -tAc \`"SELECT 1 FROM pg_extension WHERE extname='vector'\`" 2>&1"

if ($vectorExists -match "1") {
    Log-Event "Extensão pgvector já instalada. Pulando compilação." "Postgres" "SUCCESS"
} else {
    Log-Event "Extensão pgvector não encontrada. Iniciando processo de compilação..." "Postgres" "WARN"
    Run-Command "docker exec postgres_aula apt-get update" "Falha ao executar apt-get update" "Postgres"
    Run-Command "docker exec postgres_aula apt-get install -y git build-essential postgresql-server-dev-15 curl" "Falha ao instalar dependências" "Postgres"
    $pgvectorBashCommand = @'
git clone https://github.com/pgvector/pgvector.git /usr/src/pgvector && cd /usr/src/pgvector && make install
'@
    Run-Command "docker exec postgres_aula bash -c '$pgvectorBashCommand'" "Falha ao compilar pgvector" "Postgres"
    Run-Command "docker exec postgres_aula psql -U postgres -c 'CREATE EXTENSION IF NOT EXISTS vector;'" "Falha ao criar extensão vector" "Postgres"
    Log-Event "✓ Extensão pgvector instalada e ativada." "Postgres" "SUCCESS"
}

# Criar bancos de dados
Log-Event "Criando bancos de dados necessários..." "Postgres" "INFO"
$databases = @("chatwoot", "evolution", "n8n_fila")
foreach ($db in $databases) {
    $checkCmd = "docker exec postgres_aula psql -U postgres -tc `"SELECT 1 FROM pg_database WHERE datname = '$db'`" 2>&1"
    $exists = Invoke-Expression $checkCmd
    if ($exists -notmatch "1") {
        Run-Command "docker exec postgres_aula psql -U postgres -c 'CREATE DATABASE $db;'" "Falha ao criar banco '$db'" "Postgres"
        Log-Event "✓ Banco de dados '$db' criado." "Postgres" "SUCCESS"
    } else {
        Log-Event "✓ Banco de dados '$db' já existe." "Postgres" "INFO"
    }
}

# Configurar pg_hba.conf
Log-Event "Configurando pg_hba.conf para permitir conexões..." "Postgres" "INFO"
# Correção CRÍTICA do comando PostgreSQL com sintaxe robusta
$bashCommand = @'
grep -q "host all all all trust" /var/lib/postgresql/data/pg_hba.conf || echo "host all all all trust" >> /var/lib/postgresql/data/pg_hba.conf
'@
$pgHbaCommand = "docker exec postgres_aula bash -c '$bashCommand'"
Run-Command $pgHbaCommand "Falha ao ajustar pg_hba.conf" "Postgres"
Run-Command "docker exec postgres_aula psql -U postgres -c 'SELECT pg_reload_conf();'" "Falha ao recarregar configuração" "Postgres"
Log-Event "✓ Configuração de acesso do PostgreSQL atualizada." "Postgres" "SUCCESS"

# 8. Reiniciar n8n
Log-Event "Reiniciando n8n para garantir conexão com banco..." "n8n" "INFO"
Run-Command "docker restart n8n_editor-1" "Falha ao reiniciar n8n" "n8n"
Log-Event "✓ n8n reiniciado com sucesso." "n8n" "SUCCESS"

# 9. Verificar Evolution API
Log-Event "Verificando Evolution API..." "Evolution" "INFO"
if (Wait-For-Port "localhost" 8080 180) {
    Log-Event "✓ Evolution API respondendo na porta 8080." "Evolution" "SUCCESS"
    # Garantir que a API esteja plenamente pronta antes de prosseguir
    if (-not (Wait-For-Port "localhost" 8080 60)) {
        Log-Event "Evolution API deixou de responder após verificação inicial." "Evolution" "WARN"
    }
    
    # Testar conectividade com Redis
    Test-EvolutionRedisConnection
    
    # Monitorar logs por problemas do Redis
    Log-Event "Monitorando logs da Evolution API por 30 segundos..." "Evolution-Monitor" "INFO"
    $logMonitorJob = Start-Job -ScriptBlock {
        $redisErrors = 0
        $startTime = Get-Date
        while ((Get-Date) -lt $startTime.AddSeconds(30)) {
            $logs = docker logs evolution_aula --tail 10 --since 5s 2>&1
            foreach ($log in $logs) {
                if ($log -like "*redis disconnected*" -or $log -like "*Redis connection*" -or $log -like "*Error*Redis*") {
                    $redisErrors++
                }
            }
            Start-Sleep -Seconds 5
        }
        return $redisErrors
    }
    
    $redisErrorCount = Receive-Job $logMonitorJob -Wait
    Remove-Job $logMonitorJob
    
    if ($redisErrorCount -gt 0) {
        Log-Event "⚠ Detectados $redisErrorCount erros relacionados ao Redis nos logs." "Evolution-Monitor" "WARN"
        Log-Event "🔍 Isso confirma o bug conhecido da Evolution API (issues #1289, #652, #1017)." "Evolution-Monitor" "WARN"
    } else {
        Log-Event "✓ Nenhum erro de Redis detectado nos logs recentes." "Evolution-Monitor" "SUCCESS"
    }
    
    # Diagnóstico robusto do QR Code da Evolution API
    Log-Event "Executando diagnóstico robusto do QR Code da Evolution API..." "QRCode-Diagnostics" "INFO"
    
    # Executar teste de prontidão do QR Code
    $qrCodeReady = Test-EvolutionQRCodeReadiness -EvolutionContainer "evolution_aula" -ApiKey $envCredentials['AUTHENTICATION_API_KEY']
    
    if (-not $qrCodeReady) {
        Log-Event "⚠️ Problemas detectados no QR Code da Evolution API. Iniciando reparo automático..." "QRCode-Auto-Repair" "WARN"
        
        # Executar reparo automático
        $repairResult = Repair-EvolutionQRCode -EvolutionContainer "evolution_aula" -ApiKey $envCredentials['AUTHENTICATION_API_KEY']
        
        if ($repairResult) {
            Log-Event "✅ Reparo automático do QR Code concluído com sucesso!" "QRCode-Auto-Repair" "SUCCESS"
        } else {
            Log-Event "❌ Reparo automático do QR Code falhou. Intervenção manual necessária." "QRCode-Auto-Repair" "ERROR"
        }
    } else {
        Log-Event "✅ QR Code da Evolution API está funcionando corretamente!" "QRCode-Diagnostics" "SUCCESS"
    }
} else {
    Log-Event "⚠ Evolution API não respondeu na porta 8080." "Evolution" "WARN"
}

# 10. Setup do Chatwoot
$chatwootContainer = docker ps --filter "name=chatwoot-rails-1" --format "{{.Names}}"
if (-not $chatwootContainer) { 
    Abort-Install "Container 'chatwoot-rails-1' não está rodando." "Chatwoot"
}

Log-Event "Iniciando setup do Chatwoot..." "Chatwoot" "INFO"
if (-not (Wait-For-Port "localhost" 3000 300)) {
    Abort-Install "Chatwoot não respondeu na porta 3000." "Chatwoot"
}

# Executar migrações
Run-Command "docker exec chatwoot-rails-1 /usr/local/bundle/bin/bundle exec rails db:chatwoot_prepare" "Falha na migração do Chatwoot" "Chatwoot"
Log-Event "✓ Migração do Chatwoot concluída." "Chatwoot" "SUCCESS"

# Criar usuário admin se as credenciais foram geradas
if ($credentialsGenerated -or $envCredentials['CHATWOOT_ADMIN_EMAIL']) {
    Log-Event "Criando usuário administrador do Chatwoot..." "Chatwoot" "INFO"
    $adminEmail = $envCredentials['CHATWOOT_ADMIN_EMAIL']
    $adminPassword = $envCredentials['CHATWOOT_ADMIN_PASSWORD']
    
    $createUserCmd = @"
docker exec chatwoot-rails-1 bash -c "RAILS_ENV=production bundle exec rails runner \"
  begin
    u = User.find_by(email: '$adminEmail')
    if u.nil?
      u = User.create!(
        email: '$adminEmail',
        password: '$adminPassword',
        password_confirmation: '$adminPassword',
        name: 'Administrator'
      )
      u.add_role(:administrator)
      puts 'Admin user created successfully'
    else
      u.update!(password: '$adminPassword', password_confirmation: '$adminPassword')
      u.add_role(:administrator) unless u.has_role?(:administrator)
      puts 'Admin user updated successfully'
    end
  rescue => e
    puts 'Error: ' + e.message
  end
\" "
"@
    
    try {
        $output = Invoke-Expression $createUserCmd 2>&1
        if ($output -match "successfully") {
            Log-Event "✓ Usuário administrador do Chatwoot configurado." "Chatwoot" "SUCCESS"
        } else {
            Log-Event "⚠ Possível problema ao criar usuário admin: $output" "Chatwoot" "WARN"
        }
    } catch {
        Log-Event "⚠ Erro ao criar usuário admin do Chatwoot: $($_.Exception.Message)" "Chatwoot" "WARN"
    }
}

# 11. Diagnóstico Final
Log-Event "Executando diagnóstico final dos serviços..." "Final-Check" "INFO"

$finalStatus = @{
    "postgres_aula" = "❌"
    "redis_aula" = "❌" 
    "evolution_aula" = "❌"
    "chatwoot-rails-1" = "❌"
    "n8n_editor-1" = "❌"
    "minio_aula" = "❌"
}

$runningContainers = docker ps --format "{{.Names}}"
foreach ($container in $runningContainers) {
    if ($finalStatus.ContainsKey($container)) {
        $finalStatus[$container] = "✅"
    }
}

# Verificar problemas específicos
$evolutionHasRedisIssue = $false
if ($finalStatus["evolution_aula"] -eq "✅") {
    $recentLogs = docker logs evolution_aula --tail 20 2>&1
    if ($recentLogs -match "redis disconnected" -or $recentLogs -match "Error.*Redis") {
        $evolutionHasRedisIssue = $true
        $finalStatus["evolution_aula"] = "⚠️"
    }
}

# 12. Gerar Dashboard HTML Completo
Log-Event "Gerando dashboard HTML interativo..." "Dashboard" "INFO"

# Função Safe-HtmlEncode agora está disponível via módulo AutomationUtils

# Configuração completa dos serviços com TODAS as credenciais
$services = @(
    @{
        N='n8n'
        Url='http://localhost:5678'
        Desc='Editor de workflows de automação. Requer configuração manual de Webhook.'
        U=''
        Pwd=''
        Container='n8n_editor-1'
        Health='http://localhost:5678'
        ExtraInfo=@{
            'N8N_ENCRYPTION_KEY' = $envCredentials['N8N_ENCRYPTION_KEY']
            'Webhook Config Info' = 'EVOLUTION_API -> WEBHOOK TARGET URL: http://host.docker.internal:5678/api/n8n-evolution'
            'Webhook Setup' = 'Configure um nó Webhook no n8n com path: /api/n8n-evolution'
        }
    },
    @{
        N='Evolution API'
        Url='http://localhost:8080'
        Desc='API WhatsApp e Integração'
        U=''
        Pwd=$envCredentials['AUTHENTICATION_API_KEY']
        Container='evolution_aula'
        Health='http://localhost:8080'
        ExtraInfo=@{
            'Manager' = 'http://localhost:8080/manager'
            'Observação' = 'A documentação da Evolution API não é servida em /docs. Consulte a documentação oficial do projeto.'
        }
    },
    @{
        N='Chatwoot'
        Url='http://localhost:3000'
        Desc='Plataforma de atendimento omnichannel'
        U=$envCredentials['CHATWOOT_ADMIN_EMAIL']
        Pwd=$envCredentials['CHATWOOT_ADMIN_PASSWORD']
        Container='chatwoot-rails-1'
        Health='http://localhost:3000'
        ExtraInfo=@{
            'SECRET_KEY_BASE' = $envCredentials['SECRET_KEY_BASE']
        }
    },
    @{
        N='MinIO Console'
        Url='http://localhost:9001'
        Desc='Console de armazenamento S3'
        U=$envCredentials['MINIO_ROOT_USER']
        Pwd=$envCredentials['MINIO_ROOT_PASSWORD']
        Container='minio_aula'
        Health='http://localhost:9001'
        ExtraInfo=@{
            'S3 Endpoint' = 'http://localhost:9000'
        }
    },
    @{
        N='PostgreSQL'
        Url='localhost:5432'
        Desc='Banco de dados principal'
        U='postgres'
        Pwd=$envCredentials['POSTGRES_PASSWORD']
        Container='postgres_aula'
        Health=''
        ExtraInfo=@{
            'Databases' = 'chatwoot, evolution, n8n_fila'
        }
    },
    @{
        N='Redis'
        Url='localhost:6379'
        Desc='Cache e sessões'
        U=''
        Pwd=''
        Container='redis_aula'
        Health=''
        ExtraInfo=@{
            'Database' = '6 (Evolution API)'
        }
    }
)

# Template HTML robusto e completo
$htmlTemplate = @'
<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>Dashboard - N8N Evolution Environment</title>
  <style>
    * { box-sizing: border-box; }
    body { 
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; 
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
      margin: 0; 
      padding: 20px; 
      min-height: 100vh; 
    }
    .container { max-width: 1400px; margin: 0 auto; }
    .header { 
      background: rgba(255,255,255,0.95); 
      border-radius: 16px; 
      padding: 40px; 
      text-align: center; 
      margin-bottom: 30px; 
      box-shadow: 0 10px 40px rgba(0,0,0,0.15); 
    }
    .header h1 { 
      color: #1a202c; 
      margin: 0 0 10px 0; 
      font-size: 2.8em; 
      font-weight: 700;
    }
    .header p { 
      color: #4a5568; 
      margin: 0; 
      font-size: 1.2em; 
    }
    .warning-banner {
      background: #FEF3C7;
      border: 2px solid #F59E0B;
      border-radius: 12px;
      padding: 20px;
      margin-bottom: 30px;
      display: none;
    }
    .warning-banner h3 {
      color: #92400E;
      margin: 0 0 10px 0;
    }
    .warning-banner p {
      color: #78350F;
      margin: 5px 0;
    }
    .services-grid { 
      display: grid; 
      grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)); 
      gap: 25px; 
      margin-bottom: 30px; 
    }
    .service-card { 
      background: rgba(255,255,255,0.98); 
      border-radius: 16px; 
      padding: 30px; 
      box-shadow: 0 10px 30px rgba(0,0,0,0.12); 
      transition: all 0.3s ease; 
      border: 1px solid rgba(0,0,0,0.05);
    }
    .service-card:hover { 
      transform: translateY(-5px); 
      box-shadow: 0 15px 40px rgba(0,0,0,0.18); 
    }
    .service-header { 
      display: flex; 
      align-items: center; 
      margin-bottom: 20px; 
      gap: 15px;
    }
    .status-dot { 
      width: 14px; 
      height: 14px; 
      border-radius: 50%; 
      background: #cbd5e0; 
      transition: all 0.3s ease; 
      flex-shrink: 0;
    }
    .status-dot.online { 
      background: #48bb78; 
      box-shadow: 0 0 10px rgba(72,187,120,0.5); 
    }
    .status-dot.offline { 
      background: #f56565; 
      box-shadow: 0 0 10px rgba(245,101,101,0.5); 
    }
    .status-dot.checking { 
      background: #ed8936; 
      animation: pulse 1.5s infinite; 
    }
    @keyframes pulse { 
      0%, 100% { opacity: 1; transform: scale(1); } 
      50% { opacity: 0.6; transform: scale(0.9); } 
    }
    .service-name { 
      font-size: 1.5em; 
      font-weight: 600; 
      color: #1a202c; 
      margin: 0; 
    }
    .service-desc { 
      color: #4a5568; 
      margin: 10px 0 20px 0; 
      line-height: 1.5; 
    }
    .service-url { 
      display: inline-block; 
      background: #edf2f7; 
      padding: 12px 20px; 
      border-radius: 8px; 
      text-decoration: none; 
      color: #2b6cb0; 
      font-family: 'SF Mono', Monaco, monospace; 
      margin-bottom: 20px; 
      transition: all 0.3s ease; 
      font-size: 0.95em;
      border: 1px solid #e2e8f0;
    }
    .service-url:hover { 
      background: #e2e8f0; 
      transform: translateX(3px);
    }
    .credentials { 
      background: #f7fafc;
      border-radius: 10px;
      padding: 15px;
      margin-top: 15px;
      border: 1px solid #e2e8f0;
    }
    .cred-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 10px;
    }
    .cred-btn { 
      background: #4299e1; 
      color: white; 
      border: none; 
      padding: 8px 16px; 
      border-radius: 6px; 
      cursor: pointer; 
      font-size: 0.9em; 
      transition: all 0.3s ease; 
      font-weight: 500;
    }
    .cred-btn:hover { 
      background: #3182ce; 
      transform: scale(1.05);
    }
    .cred-info { 
      font-family: 'SF Mono', Monaco, monospace; 
      background: white; 
      padding: 15px; 
      border-radius: 8px; 
      font-size: 0.85em; 
      border: 1px solid #e2e8f0;
      line-height: 1.8;
      margin-top: 10px;
    }
    .cred-info strong { 
      color: #2d3748; 
      font-weight: 600;
    }
    .cred-info .cred-value {
      color: #4a5568;
      word-break: break-all;
      padding: 2px 4px;
      background: #f7fafc;
      border-radius: 4px;
      margin-left: 5px;
    }
    .extra-info {
      margin-top: 10px;
      padding-top: 10px;
      border-top: 1px solid #e2e8f0;
    }
    .extra-info-item {
      margin: 5px 0;
      font-size: 0.9em;
    }
    .extra-info-label {
      color: #718096;
      font-weight: 500;
    }
    .extra-info-value {
      color: #4a5568;
      font-family: 'SF Mono', Monaco, monospace;
      background: #f7fafc;
      padding: 2px 6px;
      border-radius: 4px;
      font-size: 0.85em;
    }
    .controls { 
      text-align: center; 
      margin-bottom: 30px; 
    }
    .refresh-btn { 
      background: #48bb78; 
      color: white; 
      border: none; 
      padding: 14px 35px; 
      border-radius: 30px; 
      cursor: pointer; 
      font-size: 1.1em; 
      transition: all 0.3s ease; 
      font-weight: 500;
      box-shadow: 0 5px 15px rgba(72,187,120,0.3);
    }
    .refresh-btn:hover { 
      background: #38a169; 
      transform: scale(1.05); 
      box-shadow: 0 8px 20px rgba(72,187,120,0.4);
    }
    .refresh-btn:disabled {
      background: #a0aec0;
      cursor: not-allowed;
      transform: scale(1);
      box-shadow: none;
    }
    .footer { 
      text-align: center; 
      color: rgba(255,255,255,0.9); 
      margin-top: 40px; 
      font-size: 0.95em;
    }
    .hidden { display: none !important; }
    .timestamp {
      text-align: center;
      color: rgba(255,255,255,0.7);
      font-size: 0.85em;
      margin-top: 10px;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>🚀 N8N Evolution Environment</h1>
      <p>Dashboard de Monitoramento e Credenciais</p>
    </div>
    
    <div class="warning-banner" id="warningBanner">
      <h3>⚠️ Problema Conhecido Detectado</h3>
      <p><strong>Evolution API:</strong> Instabilidade com Redis detectada (issues #1289, #652, #1017)</p>
      <p>A API funciona mas pode apresentar falhas na criação de instâncias WhatsApp.</p>
    </div>
    
    <div class="controls">
      <button class="refresh-btn" onclick="checkAllServices()" id="refreshBtn">
        🔄 Verificar Status dos Serviços
      </button>
    </div>
    
    <div class="services-grid" id="servicesGrid">
      <!-- Services will be inserted here -->
    </div>
    
    <div class="footer">
      <p>💡 Dashboard gerado automaticamente pelo Start-Environment.ps1</p>
      <p class="timestamp">Gerado em: <span id="timestamp"></span></p>
    </div>
  </div>

  <script>
    const services = [
      SERVICE_DATA_PLACEHOLDER
    ];

    function createServiceCard(service) {
      const hasCredentials = service.username || service.password || Object.keys(service.extraInfo || {}).length > 0;
      
      let credentialsHtml = '';
      if (hasCredentials) {
        let credContent = '';
        if (service.username) {
          credContent += `<div>Usuário: <span class="cred-value">${service.username}</span></div>`;
        }
        if (service.password) {
          credContent += `<div>Senha: <span class="cred-value">${service.password}</span></div>`;
        }
        
        let extraInfoHtml = '';
        if (service.extraInfo && Object.keys(service.extraInfo).length > 0) {
          extraInfoHtml = '<div class="extra-info">';
          for (const [key, value] of Object.entries(service.extraInfo)) {
            if (value) {
              extraInfoHtml += `
                <div class="extra-info-item">
                  <span class="extra-info-label">${key}:</span>
                  <span class="extra-info-value">${value}</span>
                </div>`;
            }
          }
          extraInfoHtml += '</div>';
        }
        
        credentialsHtml = `
        <div class="credentials">
            <div class="cred-header">
              <span style="font-weight: 500; color: #2d3748;">Credenciais e Informações</span>
              <button class="cred-btn" onclick="toggleCredentials(this)">
                🔑 Mostrar
              </button>
            </div>
          <div class="cred-info hidden">
              ${credContent}
              ${extraInfoHtml}
          </div>
        </div>
        `;
      }

      return `
                        <div class="service-card" data-service="$($service.container)">
          <div class="service-header">
                          <span class="status-dot" id="status-$($service.container)"></span>
            <h3 class="service-name">$($service.name)</h3>
          </div>
          <p class="service-desc">$($service.description)</p>
                          <a href="$($service.url)" target="_blank" class="service-url">$($service.url)</a>
          ${credentialsHtml}
        </div>
      `;
    }

    function toggleCredentials(button) {
      const credInfo = button.parentElement.nextElementSibling;
      if (credInfo.classList.contains('hidden')) {
        credInfo.classList.remove('hidden');
        button.textContent = '🙈 Ocultar';
      } else {
        credInfo.classList.add('hidden');
        button.textContent = '🔑 Mostrar';
      }
    }

    async function checkServiceStatus(service) {
      const statusDot = document.getElementById(`status-${service.container}`);
      statusDot.className = 'status-dot checking';
      
      try {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 5000);
        
        const response = await fetch(service.health || service.url, {
          method: 'HEAD',
          mode: 'no-cors',
          signal: controller.signal
        });
        
        clearTimeout(timeoutId);
        statusDot.className = 'status-dot online';
        
        // Check for Evolution API Redis issues
        if (service.container === 'evolution_aula' && window.evolutionHasRedisIssue) {
          statusDot.className = 'status-dot offline';
          document.getElementById('warningBanner').style.display = 'block';
        }
      } catch (error) {
        statusDot.className = 'status-dot offline';
      }
    }

    async function checkAllServices() {
      const button = document.getElementById('refreshBtn');
      button.textContent = '⏳ Verificando...';
      button.disabled = true;
      
      const promises = services.map(service => checkServiceStatus(service));
      await Promise.all(promises);
      
      button.textContent = '🔄 Verificar Status dos Serviços';
      button.disabled = false;
    }

    function formatTimestamp() {
      const now = new Date();
      return now.toLocaleString('pt-BR', {
        dateStyle: 'full',
        timeStyle: 'medium'
      });
    }

    function initializeDashboard() {
      const grid = document.getElementById('servicesGrid');
      grid.innerHTML = services.map(service => createServiceCard(service)).join('');
      
      document.getElementById('timestamp').textContent = formatTimestamp();
      
      // Set Evolution Redis issue flag if present
      window.evolutionHasRedisIssue = EVOLUTION_REDIS_ISSUE_PLACEHOLDER;
      
      checkAllServices();
      
      // Auto-refresh every 30 seconds
      setInterval(checkAllServices, 30000);
    }

    document.addEventListener('DOMContentLoaded', initializeDashboard);
  </script>
</body>
</html>
'@

# Gerar dados JavaScript dos serviços
$jsServices = @()
foreach ($service in $services) {
    $serviceName = Safe-HtmlEncode $service.N
    $serviceUrl = Safe-HtmlEncode $service.Url
    $serviceDesc = Safe-HtmlEncode $service.Desc
    $serviceUser = Safe-HtmlEncode $service.U
    $servicePwd = Safe-HtmlEncode $service.Pwd
    $serviceContainer = Safe-HtmlEncode $service.Container
    $serviceHealth = Safe-HtmlEncode ($service.Health -or $service.Url)
    
    # Processar informações extras
    $extraInfoJs = "null"
    if ($service.ExtraInfo -and $service.ExtraInfo.Count -gt 0) {
        $extraItems = @()
        foreach ($key in $service.ExtraInfo.Keys) {
            $safeKey = Safe-HtmlEncode $key
            $safeValue = Safe-HtmlEncode $service.ExtraInfo[$key]
            $extraItems += "'$safeKey': '$safeValue'"
        }
        $extraInfoJs = "{ $($extraItems -join ', ') }"
    }
    
    $jsServices += @"
{
        name: '$serviceName',
        url: '$serviceUrl',
        description: '$serviceDesc',
        username: '$serviceUser',
        password: '$servicePwd',
        container: '$serviceContainer',
        health: '$serviceHealth',
        extraInfo: $extraInfoJs
      }
"@
}

$serviceDataJs = $jsServices -join ",`n      "
$evolutionRedisIssueJs = if ($evolutionHasRedisIssue) { "true" } else { "false" }

$finalHtml = $htmlTemplate -replace 'SERVICE_DATA_PLACEHOLDER', $serviceDataJs
$finalHtml = $finalHtml -replace 'EVOLUTION_REDIS_ISSUE_PLACEHOLDER', $evolutionRedisIssueJs

# Salvar dashboard
$dashboardPath = "services-dashboard.html"
$finalHtml | Out-File -FilePath $dashboardPath -Encoding UTF8 -NoNewline

Log-Event "✓ Dashboard HTML gerado: $dashboardPath" "Dashboard" "SUCCESS"

# 13. Resumo Final no Console
Write-Host-Color "`n╔══════════════════════════════════════════════════════════╗" "Magenta"
Write-Host-Color "║      AMBIENTE DE AUTOMAÇÃO INICIADO COM SUCESSO!        ║" "Magenta"
Write-Host-Color "╚══════════════════════════════════════════════════════════╝" "Magenta"

Write-Host-Color "`n📊 STATUS DOS SERVIÇOS:" "White"
Write-Host-Color "  PostgreSQL + pgvector: $($finalStatus['postgres_aula'])" "White"
Write-Host-Color "  Redis: $($finalStatus['redis_aula'])" "White"
Write-Host-Color "  Evolution API: $($finalStatus['evolution_aula'])" "White"
Write-Host-Color "  Chatwoot: $($finalStatus['chatwoot-rails-1'])" "White"
Write-Host-Color "  n8n: $($finalStatus['n8n_editor-1'])" "White"
Write-Host-Color "  MinIO: $($finalStatus['minio_aula'])" "White"

if ($evolutionHasRedisIssue) {
    Write-Host-Color "`n⚠️  PROBLEMA CONHECIDO DETECTADO:" "Yellow"
    Write-Host-Color "   Evolution API apresenta instabilidade com Redis (issues #1289, #652, #1017)" "Yellow"
    Write-Host-Color "   A API funciona mas pode apresentar falhas na criação de instâncias WhatsApp" "Yellow"
    Write-Host-Color "   Consulte REDIS-TROUBLESHOOTING.md para mais detalhes" "Yellow"
}

Write-Host-Color "`n🌐 ACESSE OS SERVIÇOS:" "Cyan"
Write-Host-Color "  🎯 Dashboard Completo: services-dashboard.html" "Green"
Write-Host-Color "  📊 n8n: http://localhost:5678" "White"
Write-Host-Color "  📱 Evolution API: http://localhost:8080/docs" "White"
Write-Host-Color "  💬 Chatwoot: http://localhost:3000" "White"
Write-Host-Color "  📦 MinIO Console: http://localhost:9001" "White"

Write-Host-Color "`n🔐 CREDENCIAIS GERADAS (salvas em .env):" "Yellow"
Write-Host-Color "╔════════════════════════════════════════════════════════════╗" "Yellow"
Write-Host-Color "║ PostgreSQL                                                 ║" "Yellow"
Write-Host-Color "║   Usuário: postgres                                        ║" "Yellow"
Write-Host-Color "║   Senha: $($envCredentials['POSTGRES_PASSWORD'])" "Yellow"
Write-Host-Color "║                                                            ║" "Yellow"
Write-Host-Color "║ MinIO                                                      ║" "Yellow"
Write-Host-Color "║   Usuário: $($envCredentials['MINIO_ROOT_USER'])" "Yellow"
Write-Host-Color "║   Senha: $($envCredentials['MINIO_ROOT_PASSWORD'])" "Yellow"
Write-Host-Color "║                                                            ║" "Yellow"
Write-Host-Color "║ Evolution API                                              ║" "Yellow"
Write-Host-Color "║   API Key: $($envCredentials['AUTHENTICATION_API_KEY'])" "Yellow"
Write-Host-Color "║                                                            ║" "Yellow"
Write-Host-Color "║ Chatwoot                                                   ║" "Yellow"
Write-Host-Color "║   Email: $($envCredentials['CHATWOOT_ADMIN_EMAIL'])" "Yellow"
Write-Host-Color "║   Senha: $($envCredentials['CHATWOOT_ADMIN_PASSWORD'])" "Yellow"
Write-Host-Color "║   Secret: $($envCredentials['SECRET_KEY_BASE'].Substring(0, 20))..." "Yellow"
Write-Host-Color "║                                                            ║" "Yellow"
Write-Host-Color "║ n8n                                                        ║" "Yellow"
Write-Host-Color "║   Encryption Key: $($envCredentials['N8N_ENCRYPTION_KEY'].Substring(0, 20))..." "Yellow"
Write-Host-Color "╚════════════════════════════════════════════════════════════╝" "Yellow"

Write-Host-Color "`n📂 ARQUIVOS CRIADOS:" "White"
Write-Host-Color "  ✓ install.log - Logs completos da instalação" "Green"
Write-Host-Color "  ✓ services-dashboard.html - Dashboard interativo" "Green"
Write-Host-Color "  ✓ .env - Arquivo com todas as credenciais" "Green"

# 14. Detectar primeira instalação e executar post-setup-automation.ps1 automaticamente
# Nota: $firstInstallMarker já foi definido no início do script
$isFirstInstall = $credentialsGenerated -and (-not (Test-Path $firstInstallMarker)) -and (-not $hasExistingInstallation)

if ($isFirstInstall) {
    Write-Host-Color "`n🔧 PRIMEIRA INSTALAÇÃO DETECTADA - Executando configuração automática avançada..." "Cyan"
    Log-Event "Primeira instalação detectada. Executando post-setup-automation.ps1 automaticamente..." "Auto-Setup" "INFO"
    
    # Aguardar 10 segundos para garantir que todos os serviços estejam estáveis
    Write-Host-Color "⏳ Aguardando estabilização dos serviços (10 segundos)..." "Yellow"
    Start-Sleep -Seconds 10
    
    try {
        $postSetupScript = Join-Path $PSScriptRoot "post-setup-automation.ps1"
        if (Test-Path $postSetupScript) {
            Write-Host-Color "🚀 Executando configuração automática..." "Green"
            
            # Executar o script post-setup-automation.ps1
            & $postSetupScript
            
            if ($LASTEXITCODE -eq 0) {
                Write-Host-Color "✅ Configuração automática concluída com sucesso!" "Green"
                Log-Event "post-setup-automation.ps1 executado com sucesso" "Auto-Setup" "SUCCESS"
                
                # Marcar primeira instalação como concluída
                "$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss') - Primeira instalação e configuração automática concluída" | Out-File $firstInstallMarker -Encoding UTF8
                
                Write-Host-Color "`n🎉 SISTEMA TOTALMENTE CONFIGURADO!" "Green"
                Write-Host-Color "📋 ÚNICA ETAPA MANUAL RESTANTE:" "Yellow"
                Write-Host-Color "   • Configurar webhook N8N ↔ Evolution API manualmente" "Yellow"
                Write-Host-Color "   • Acesse: http://localhost:5678 (N8N) e http://localhost:8080 (Evolution)" "Yellow"
                Write-Host-Color "   • Configure webhook path: /api/n8n-evolution" "Yellow"
                
            } else {
                Write-Host-Color "⚠️ Configuração automática falhou. Execute manualmente: .\post-setup-automation.ps1" "Yellow"
                Log-Event "post-setup-automation.ps1 falhou com código: $LASTEXITCODE" "Auto-Setup" "ERROR"
            }
        } else {
            Write-Host-Color "⚠️ Script post-setup-automation.ps1 não encontrado. Configuração manual necessária." "Yellow"
            Log-Event "post-setup-automation.ps1 não encontrado em: $postSetupScript" "Auto-Setup" "ERROR"
        }
    } catch {
        Write-Host-Color "❌ Erro ao executar configuração automática: $($_.Exception.Message)" "Red"
        Log-Event "Erro ao executar post-setup-automation.ps1: $($_.Exception.Message)" "Auto-Setup" "ERROR"
        Write-Host-Color "📋 Execute manualmente: .\post-setup-automation.ps1" "Yellow"
    }
} else {
    # Instalação existente já foi tratada no menu inicial
    # ou é uma nova instalação que passou pelo menu
    if ($hasExistingInstallation) {
        Write-Host-Color "`n🔄 Continuando com instalação existente conforme seleção do menu..." "Cyan"
        Log-Event "Continuando fluxo normal após seleção do menu" "Menu" "INFO"
    } else {
        Write-Host-Color "`n🆕 Nova instalação detectada - configuração manual disponível" "Cyan"
        Write-Host-Color "🔧 Para configuração completa após instalação, execute: .\post-setup-automation.ps1" "Yellow"
    }
}

# 15. Abrir Dashboard Automaticamente
Write-Host-Color "`n🌐 Abrindo dashboard no navegador..." "Green"
try {
    $dashboardFullPath = Resolve-Path $dashboardPath
    Open-UrlInChrome -Url "file://$dashboardFullPath" -Component "Dashboard"
    Open-UrlInChrome -Url "http://localhost:8080/manager" -Component "EvolutionManager"
    Log-Event "✓ Dashboard e Evolution Manager abertos automaticamente no Chrome." "Dashboard" "SUCCESS"
    Write-Host-Color "✓ Dashboard disponível em: $dashboardFullPath" "Green"
} catch {
    Write-Host-Color "⚠ Não foi possível abrir automaticamente: $($_.Exception.Message)" "Yellow"
    Write-Host-Color "  Acesse manualmente: $dashboardFullPath e http://localhost:8080/manager" "Yellow"
}

Write-Host-Color "`n✨ Instalação concluída com sucesso!" "Green"
Write-Host-Color "📋 Verifique o arquivo install.log para detalhes completos." "White"

if ($evolutionHasRedisIssue) {
    Write-Host-Color "`n📋 PRÓXIMOS PASSOS RECOMENDADOS:" "Cyan"
    Write-Host-Color "   1. Monitore os logs da Evolution API: docker logs -f evolution_aula" "Cyan"
    Write-Host-Color "   2. Teste versões alternativas se necessário" "Cyan"
    Write-Host-Color "   3. Reporte problemas persistentes no GitHub" "Cyan"
}

# Mostrar instruções simplificadas apenas se não for primeira instalação
if (-not $isFirstInstall -and -not $hasExistingInstallation) {
    Write-Host-Color "`n🔧 CONFIGURAÇÃO MANUAL DISPONÍVEL:" "Yellow"
    Write-Host-Color "   • Para configuração avançada: .\post-setup-automation.ps1" "Yellow"
    Write-Host-Color "   • Para workflows específicos: .\Onboard-Workflow.ps1 -WorkflowName 'NomeDoWorkflow'" "Yellow"
    Write-Host-Color "   • Para menu completo na próxima execução: o sistema detectará automaticamente" "Gray"
}

# Fim do script
Log-Event "✓ Script finalizado com sucesso!" "Orchestrator" "SUCCESS"