﻿# Start-Environment.ps1 - Orquestrador Zero-Touch Robusto com Logging Aprimorado e Entrega Completa de Credenciais
# VersÃ£o: 3.0 - MÃ³dulo centralizado e arquitetura robusta

# =====================================================
# IMPORTAR MÃ“DULO DE UTILITÃRIOS DE AUTOMAÃ‡ÃƒO
# =====================================================

$utilityModuleName = "AutomationUtils"
# Assume que o mÃ³dulo estÃ¡ na mesma pasta do script PS, ou em um subdiretÃ³rio
# Se PowerShellModules estiver no mesmo nÃ­vel que os scripts PS:
$modulePath = Join-Path $PSScriptRoot ".\PowerShellModules\$utilityModuleName.psm1"
# Se PowerShellModules estiver um diretÃ³rio acima dos scripts PS:
# $modulePath = Join-Path $PSScriptRoot "..\PowerShellModules\$utilityModuleName.psm1"

if (Test-Path $modulePath) {
    try {
        Import-Module $modulePath -Force -ErrorAction Stop
        if (-not (Get-Command Log-Event -ErrorAction SilentlyContinue)) { # VerificaÃ§Ã£o de fallback robusta
            Write-Error "ERRO FATAL: MÃ³dulo '$utilityModuleName' importado, mas a funÃ§Ã£o Log-Event nÃ£o foi encontrada. O mÃ³dulo pode estar corrompido. Verifique '$modulePath'."
            exit 1
        }
        Log-Event "MÃ³dulo '$utilityModuleName' carregado com sucesso." "ModuleLoader" "SUCCESS"
    } catch {
        Write-Error "ERRO FATAL: Falha crÃ­tica ao importar o mÃ³dulo '$utilityModuleName' de '$modulePath'. As funÃ§Ãµes utilitÃ¡rias sÃ£o essenciais. Verifique a instalaÃ§Ã£o do mÃ³dulo."
        exit 1
    }
} else {
    Write-Error "ERRO FATAL: MÃ³dulo de utilidades do PowerShell nÃ£o encontrado em '$modulePath'. O projeto nÃ£o pode continuar sem funÃ§Ãµes de suporte essenciais."
    exit 1
}

# Importar mÃ³dulo de geraÃ§Ã£o de dashboard
$dashboardModulePath = Join-Path $PSScriptRoot ".\PowerShellModules\Generate-ServiceDashboard.psm1"
if (Test-Path $dashboardModulePath) {
    try {
        Import-Module $dashboardModulePath -Force -ErrorAction Stop
        Log-Event "MÃ³dulo 'Generate-ServiceDashboard' carregado com sucesso." "ModuleLoader" "SUCCESS"
    } catch {
        Write-Warning "Aviso: Falha ao importar mÃ³dulo Generate-ServiceDashboard. Dashboard HTML serÃ¡ gerado de forma alternativa."
        Log-Event "Falha ao carregar mÃ³dulo Generate-ServiceDashboard: $($_.Exception.Message)" "ModuleLoader" "WARN"
    }
} else {
    Write-Warning "MÃ³dulo Generate-ServiceDashboard nÃ£o encontrado. Dashboard HTML serÃ¡ gerado de forma alternativa."
    Log-Event "MÃ³dulo Generate-ServiceDashboard nÃ£o encontrado em '$dashboardModulePath'" "ModuleLoader" "WARN"
}

# =====================================================
# INÃCIO DA EXECUÃ‡ÃƒO PRINCIPAL
# =====================================================

Write-Host-Color "`nâ•”â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•—" "Cyan"
Write-Host-Color "â•‘      N8N EVOLUTION ENVIRONMENT - INSTALADOR ROBUSTO      â•‘" "Cyan"
Write-Host-Color "â•‘           VersÃ£o 2.0 - DiagnÃ³sticos AvanÃ§ados            â•‘" "Cyan"
Write-Host-Color "â•šâ•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•" "Cyan"

# =====================================================
# MENU DE INICIALIZAÃ‡ÃƒO
# =====================================================

Write-Host-Color "`nðŸš€ Selecione o modo de inicializaÃ§Ã£o:" "Yellow"
Write-Host-Color "â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•" "Yellow"
Write-Host "[1] ðŸ  Modo Local (padrÃ£o)" -ForegroundColor White
Write-Host "    â€¢ Acesso apenas via localhost/IP local" -ForegroundColor Gray
Write-Host "    â€¢ Ideal para desenvolvimento local" -ForegroundColor Gray
Write-Host "`n[2] ðŸŒ Modo PÃºblico (ngrok)" -ForegroundColor White
Write-Host "    â€¢ Webhook pÃºblico via tÃºnel ngrok" -ForegroundColor Gray
Write-Host "    â€¢ Acesso externo para webhooks" -ForegroundColor Gray
Write-Host "    â€¢ Requer token ngrok configurado" -ForegroundColor Gray

$useNgrok = $false
$userChoice = $null

# Verificar se hÃ¡ entrada do usuÃ¡rio ou usar countdown
Write-Host "`nEscolha [1-2] ou aguarde para modo padrÃ£o: " -NoNewline -ForegroundColor Yellow

# Implementar countdown com verificaÃ§Ã£o de tecla
$countdownCompleted = Show-CountdownTimer -Seconds 10 -Message "Iniciando modo local em"

if (-not $countdownCompleted) {
    # UsuÃ¡rio pressionou uma tecla, ler a escolha
    $userChoice = Read-Host "`nDigite sua escolha [1-2]"
    
    switch ($userChoice) {
        "1" {
            Write-Host-Color "`nâœ… Modo Local selecionado" "Green"
            $useNgrok = $false
        }
        "2" {
            Write-Host-Color "`nðŸŒ Modo PÃºblico (ngrok) selecionado" "Green"
            $useNgrok = $true
            
            # Verificar se NGROK_AUTHTOKEN estÃ¡ configurado
            if (-not $env:NGROK_AUTHTOKEN -or $env:NGROK_AUTHTOKEN.Trim() -eq "") {
                Write-Host-Color "`nâš ï¸  ATENÃ‡ÃƒO: Token do ngrok nÃ£o configurado!" "Red"
                Write-Host "Para usar o modo pÃºblico, vocÃª precisa:" -ForegroundColor Yellow
                Write-Host "1. Criar uma conta em https://ngrok.com" -ForegroundColor White
                Write-Host "2. Obter seu token de autenticaÃ§Ã£o" -ForegroundColor White
                Write-Host "3. Adicionar NGROK_AUTHTOKEN=seu_token ao arquivo .env" -ForegroundColor White
                Write-Host "`nContinuando em modo local..." -ForegroundColor Gray
                $useNgrok = $false
            }
        }
        default {
            Write-Host-Color "`nâš ï¸  OpÃ§Ã£o invÃ¡lida. Usando modo local padrÃ£o." "Yellow"
            $useNgrok = $false
        }
    }
} else {
    # Countdown completou, usar modo padrÃ£o
    Write-Host-Color "âœ… Modo Local (padrÃ£o) selecionado automaticamente" "Green"
    $useNgrok = $false
}

Log-Event "Modo de inicializaÃ§Ã£o selecionado: $(if ($useNgrok) { 'PÃºblico (ngrok)' } else { 'Local' })" "Orchestrator" "INFO"

# =====================================================
# VALIDAÃ‡ÃƒO DE PRÃ‰-REQUISITOS
# =====================================================

# 1. ValidaÃ§Ã£o de prÃ©-requisitos
Log-Event "Iniciando validaÃ§Ã£o de prÃ©-requisitos..." "Orchestrator" "INFO"
if (-not (Get-Command docker -ErrorAction SilentlyContinue)) { 
    Abort-Install "Docker nÃ£o estÃ¡ instalado ou nÃ£o estÃ¡ no PATH." "Prerequisites"
}
if (-not (Get-Command docker-compose -ErrorAction SilentlyContinue) -and -not (docker compose version 2>$null)) {
    Abort-Install "Docker Compose nÃ£o estÃ¡ instalado ou nÃ£o estÃ¡ no PATH." "Prerequisites"
}
if ($PSVersionTable.PSVersion.Major -lt 5) { 
    Abort-Install "PowerShell 5.1 ou superior Ã© necessÃ¡rio." "Prerequisites"
}
Log-Event "âœ“ ValidaÃ§Ã£o de prÃ©-requisitos concluÃ­da com sucesso." "Prerequisites" "SUCCESS"

# 2. GestÃ£o de ConfiguraÃ§Ã£o (.env) - COMPLETA
Log-Event "Iniciando gerenciamento de configuraÃ§Ã£o .env..." "Config" "INFO"
$envFile = ".env"
$credentialsGenerated = $false

if (-not (Test-Path $envFile)) {
    Log-Event "Arquivo .env nÃ£o encontrado. Gerando novas credenciais completas..." "Config" "WARN"
    $credentialsGenerated = $true
    
    # Obter IP do host de forma robusta
    if ($env:HOST_IP -and $env:HOST_IP.Trim()) {
        $hostIp = $env:HOST_IP.Trim()
        Log-Event "HOST_IP prÃ©-definido detectado: $hostIp" "Config" "INFO"
    } else {
        $hostIp = Get-PrimaryPrivateIPv4
        Log-Event "HOST_IP detectado automaticamente: $hostIp" "Config" "INFO"
    }
    
    # Gerar todas as credenciais necessÃ¡rias
    $postgresPassword = Generate-RandomPassword 16
    $minioPassword = Generate-RandomPassword 20
    $evolutionApiKey = Generate-RandomPassword 32
    $secretKeyBase = Generate-RandomPassword 64
    $n8nEncryptionKey = Generate-SecureKey 32
    $chatwootAdminEmail = "<EMAIL>"
    $chatwootAdminPassword = Generate-RandomPassword 16
    
    # Criar arquivo .env completo
    @"
# Credenciais do PostgreSQL
POSTGRES_PASSWORD=$postgresPassword

# Credenciais do MinIO
MINIO_ROOT_USER=admin
MINIO_ROOT_PASSWORD=$minioPassword

# Evolution API
AUTHENTICATION_API_KEY=$evolutionApiKey

# Chatwoot
SECRET_KEY_BASE=$secretKeyBase
CHATWOOT_ADMIN_EMAIL=$chatwootAdminEmail
CHATWOOT_ADMIN_PASSWORD=$chatwootAdminPassword

# n8n
N8N_ENCRYPTION_KEY=$n8nEncryptionKey

# Sistema
HOST_IP=$hostIp

# ConfiguraÃ§Ã£o da Evolution API para QR Code
CONFIG_SESSION_PHONE_VERSION=2.3000.1020885143
"@ | Out-File -FilePath $envFile -Encoding utf8
    Log-Event "âœ“ Arquivo .env criado com todas as credenciais!" "Config" "SUCCESS"
} else {
    Log-Event "Arquivo .env existente encontrado. Verificando integridade..." "Config" "INFO"
    
    # Verificar e adicionar credenciais faltantes
    $envContent = Get-Content $envFile -Raw
    $updates = @()
    
    if ($envContent -notmatch "(?m)^SECRET_KEY_BASE\s*=\s*\S+") {
        $newSecret = Generate-RandomPassword 64
        Add-Content -Path $envFile -Value "SECRET_KEY_BASE=$newSecret"
        $updates += "SECRET_KEY_BASE"
    }
    
    if ($envContent -notmatch "(?m)^N8N_ENCRYPTION_KEY\s*=\s*\S+") {
        $n8nKey = Generate-SecureKey 32
        Add-Content -Path $envFile -Value "N8N_ENCRYPTION_KEY=$n8nKey"
        $updates += "N8N_ENCRYPTION_KEY"
    }
    
    if ($envContent -notmatch "(?m)^CHATWOOT_ADMIN_EMAIL\s*=\s*\S+") {
        Add-Content -Path $envFile -Value "CHATWOOT_ADMIN_EMAIL=<EMAIL>"
        $updates += "CHATWOOT_ADMIN_EMAIL"
    }
    
    if ($envContent -notmatch "(?m)^CHATWOOT_ADMIN_PASSWORD\s*=\s*\S+") {
        $chatwootPwd = Generate-RandomPassword 16
        Add-Content -Path $envFile -Value "CHATWOOT_ADMIN_PASSWORD=$chatwootPwd"
        $updates += "CHATWOOT_ADMIN_PASSWORD"
    }

    if ($envContent -notmatch "(?m)^CONFIG_SESSION_PHONE_VERSION\s*=\s*\S+") {
        Add-Content -Path $envFile -Value "CONFIG_SESSION_PHONE_VERSION=2.3000.1020885143"
        $updates += "CONFIG_SESSION_PHONE_VERSION"
    }
    
    if ($updates.Count -gt 0) {
        Log-Event "VariÃ¡veis adicionadas ao .env: $($updates -join ', ')" "Config" "WARN"
    }
}

Log-Event "Carregando variÃ¡veis de ambiente do arquivo .env..." "Config" "INFO"
# Carrega todas as variÃ¡veis do .env para a sessÃ£o atual
$envCredentials = @{}
Get-Content $envFile | ForEach-Object {
    if ($_ -match "^([^=]+)=(.*)$") {
        $key = $matches[1].Trim()
        $value = $matches[2].Trim()
        [System.Environment]::SetEnvironmentVariable($key, $value)
        $envCredentials[$key] = $value
    }
}
Log-Event "âœ“ VariÃ¡veis de ambiente carregadas com sucesso." "Config" "SUCCESS"

# Exibir chave da Evolution API para o usuÃ¡rio colar no Manager
if ($envCredentials.ContainsKey('AUTHENTICATION_API_KEY')) {
    $apiKeyDisplay = $envCredentials['AUTHENTICATION_API_KEY']
    Log-Event "âš  Copie esta chave para o Manager da Evolution API: $apiKeyDisplay" "Evolution" "WARN"
    Write-Host "[Evolution API] Chave de autenticaÃ§Ã£o: $apiKeyDisplay" -ForegroundColor Yellow
}

# 3. CriaÃ§Ã£o da Rede Docker
Log-Event "Verificando rede Docker 'app_network'..." "Docker" "INFO"
$inspectOutput = docker network inspect app_network --format "{{.Driver}}" 2>$null

if ($LASTEXITCODE -ne 0) {
    # Rede nÃ£o existe â€“ criar com driver bridge para compatibilidade
    Log-Event "Rede 'app_network' nÃ£o encontrada. Criando..." "Docker" "WARN"
    Run-Command "docker network create --driver bridge app_network" "Falha ao criar a rede Docker 'app_network'." "Docker"
    Log-Event "âœ“ Rede 'app_network' criada com sucesso (driver bridge)." "Docker" "SUCCESS"
} else {
    $driver = $inspectOutput.Trim()
    Log-Event "Rede 'app_network' encontrada com driver '$driver'." "Docker" "INFO"
    if ($driver -ne "bridge") {
        Log-Event "âš  Driver da rede difere de 'bridge'. Certifique-se de que isso Ã© intencional para evitar problemas de conectividade." "Docker" "WARN"
    }
}

# 4. Configurar variÃ¡veis de ambiente para webhooks
if ($useNgrok) {
    Log-Event "Configurando variÃ¡veis de ambiente para modo ngrok..." "NgrokSetup" "INFO"
    # A URL do webhook serÃ¡ definida apÃ³s obter a URL pÃºblica do ngrok
    [System.Environment]::SetEnvironmentVariable("WEBHOOK_URL", "")
} else {
    Log-Event "Configurando variÃ¡veis de ambiente para modo local..." "LocalSetup" "INFO"
    $hostIp = if ($env:HOST_IP) { $env:HOST_IP } else { Get-PrimaryPrivateIPv4 }
    [System.Environment]::SetEnvironmentVariable("WEBHOOK_URL", "http://$hostIp:5678")
}

# 5. Subir todos os serviÃ§os
if ($useNgrok) {
    Log-Event "Iniciando todos os serviÃ§os com ngrok via docker-compose..." "Docker" "INFO"
    Run-Command "docker compose -f docker-compose.yml --profile ngrok up -d" "Falha ao subir os serviÃ§os com ngrok." "Docker"
    Log-Event "âœ“ Comando 'docker compose up' com profile ngrok executado. Aguardando estabilizaÃ§Ã£o..." "Docker" "SUCCESS"
    
    # Aguardar ngrok ficar disponÃ­vel e obter URL pÃºblica
    Log-Event "Aguardando ngrok ficar disponÃ­vel..." "NgrokSetup" "INFO"
    if (Wait-For-Port "localhost" 4040 120) {
        Log-Event "âœ“ API de inspeÃ§Ã£o do ngrok disponÃ­vel na porta 4040." "NgrokSetup" "SUCCESS"
        
        # Obter URL pÃºblica do ngrok
        $ngrokResult = Get-NgrokPublicUrl -MaxRetries 30 -RetryIntervalSeconds 2
        
        if ($ngrokResult.Success) {
            $publicUrl = $ngrokResult.Url
            Log-Event "âœ… URL pÃºblica do ngrok obtida: $publicUrl" "NgrokSetup" "SUCCESS"
            
            # Atualizar variÃ¡vel de ambiente
            [System.Environment]::SetEnvironmentVariable("WEBHOOK_URL", $publicUrl)
            
            # Reiniciar serviÃ§os que dependem da WEBHOOK_URL
            Log-Event "Reiniciando serviÃ§os para aplicar URL pÃºblica do webhook..." "NgrokSetup" "INFO"
            Run-Command "docker restart evolution_aula n8n_editor-1" "Falha ao reiniciar serviÃ§os para aplicar webhook URL." "Docker"
            Log-Event "âœ“ ServiÃ§os reiniciados com URL pÃºblica do webhook." "NgrokSetup" "SUCCESS"
            
            Write-Host-Color "`nðŸŒ WEBHOOK PÃšBLICO CONFIGURADO:" "Green"
            Write-Host-Color "   URL: $publicUrl" "White"
            Write-Host-Color "   Evolution: $publicUrl/webhook/evolution" "White"
            
        } else {
            Log-Event "âŒ Falha ao obter URL pÃºblica do ngrok: $($ngrokResult.ErrorMessage)" "NgrokSetup" "ERROR"
            Write-Host-Color "`nâš ï¸  Falha ao configurar ngrok. Continuando em modo local..." "Yellow"
            $useNgrok = $false
            $hostIp = if ($env:HOST_IP) { $env:HOST_IP } else { Get-PrimaryPrivateIPv4 }
            [System.Environment]::SetEnvironmentVariable("WEBHOOK_URL", "http://$hostIp:5678")
        }
    } else {
        Log-Event "âŒ API de inspeÃ§Ã£o do ngrok nÃ£o ficou disponÃ­vel." "NgrokSetup" "ERROR"
        Write-Host-Color "`nâš ï¸  Ngrok nÃ£o ficou disponÃ­vel. Continuando em modo local..." "Yellow"
        $useNgrok = $false
        $hostIp = if ($env:HOST_IP) { $env:HOST_IP } else { Get-PrimaryPrivateIPv4 }
        [System.Environment]::SetEnvironmentVariable("WEBHOOK_URL", "http://$hostIp:5678")
    }
} else {
    Log-Event "Iniciando todos os serviÃ§os via docker-compose..." "Docker" "INFO"
    Run-Command "docker compose -f docker-compose.yml up -d" "Falha ao subir os serviÃ§os com docker-compose." "Docker"
    Log-Event "âœ“ Comando 'docker compose up' executado. Aguardando estabilizaÃ§Ã£o..." "Docker" "SUCCESS"
}

# 5. Aguardar serviÃ§os crÃ­ticos
Log-Event "Aguardando serviÃ§os crÃ­ticos ficarem saudÃ¡veis..." "HealthCheck" "INFO"

# PostgreSQL
if (-not (Wait-For-Service-Healthy "postgres_aula" 180)) {
    Abort-Install "ServiÃ§o 'postgres_aula' nÃ£o ficou saudÃ¡vel dentro do tempo limite." "Postgres"
}

# Redis
if (-not (Wait-For-Service-Healthy "redis_aula" 120)) {
    Log-Event "Redis nÃ£o ficou saudÃ¡vel apÃ³s reinicializaÃ§Ã£o." "Redis-Diagnostics" "ERROR"
} else {
    # Verifica conectividade lÃ³gica
    if (-not (Test-RedisConnectivity)) {
        Log-Event "Redis saudÃ¡vel, porÃ©m teste de conectividade falhou." "Redis-Diagnostics" "ERROR"
    }
}

# MinIO
if (-not (Wait-For-Service-Healthy "minio_aula" 180)) {
    Abort-Install "ServiÃ§o 'minio_aula' nÃ£o ficou saudÃ¡vel dentro do tempo limite." "MinIO"
}

# 6. DiagnÃ³stico completo do Redis
Log-Event "Executando diagnÃ³sticos especÃ­ficos do Redis..." "Redis-Diagnostics" "INFO"
if (-not (Test-RedisConnectivity)) {
    Log-Event "âš  Problemas detectados no Redis. Tentando reinicializaÃ§Ã£o..." "Redis-Diagnostics" "WARN"
    Run-Command "docker restart redis_aula" "Falha ao reiniciar o Redis" "Redis"
    if (-not (Wait-For-Service-Healthy "redis_aula" 120)) {
        Log-Event "Redis nÃ£o ficou saudÃ¡vel apÃ³s reinicializaÃ§Ã£o." "Redis-Diagnostics" "ERROR"
    }
}

# 7. Setup do PostgreSQL
$pgContainer = docker ps --filter "name=postgres_aula" --format "{{.Names}}"
if (-not $pgContainer) { 
    Abort-Install "Container 'postgres_aula' nÃ£o estÃ¡ rodando." "Postgres"
}

Log-Event "Iniciando setup avanÃ§ado do PostgreSQL..." "Postgres" "INFO"

# Instalar pgvector (idempotente)
Log-Event "Verificando existÃªncia da extensÃ£o pgvector..." "Postgres" "INFO"
$vectorExists = Invoke-Expression "docker exec postgres_aula psql -U postgres -tAc \`"SELECT 1 FROM pg_extension WHERE extname='vector'\`" 2>&1"

if ($vectorExists -match "1") {
    Log-Event "ExtensÃ£o pgvector jÃ¡ instalada. Pulando compilaÃ§Ã£o." "Postgres" "SUCCESS"
} else {
    Log-Event "ExtensÃ£o pgvector nÃ£o encontrada. Iniciando processo de compilaÃ§Ã£o..." "Postgres" "WARN"
    Run-Command "docker exec postgres_aula apt-get update" "Falha ao executar apt-get update" "Postgres"
    Run-Command "docker exec postgres_aula apt-get install -y git build-essential postgresql-server-dev-15 curl" "Falha ao instalar dependÃªncias" "Postgres"
    $pgvectorBashCommand = @'
git clone https://github.com/pgvector/pgvector.git /usr/src/pgvector && cd /usr/src/pgvector && make install
'@
    Run-Command "docker exec postgres_aula bash -c '$pgvectorBashCommand'" "Falha ao compilar pgvector" "Postgres"
    Run-Command "docker exec postgres_aula psql -U postgres -c 'CREATE EXTENSION IF NOT EXISTS vector;'" "Falha ao criar extensÃ£o vector" "Postgres"
    Log-Event "âœ“ ExtensÃ£o pgvector instalada e ativada." "Postgres" "SUCCESS"
}

# Criar bancos de dados
Log-Event "Criando bancos de dados necessÃ¡rios..." "Postgres" "INFO"
$databases = @("chatwoot", "evolution", "n8n_fila")
foreach ($db in $databases) {
    $checkCmd = "docker exec postgres_aula psql -U postgres -tc `"SELECT 1 FROM pg_database WHERE datname = '$db'`" 2>&1"
    $exists = Invoke-Expression $checkCmd
    if ($exists -notmatch "1") {
        Run-Command "docker exec postgres_aula psql -U postgres -c 'CREATE DATABASE $db;'" "Falha ao criar banco '$db'" "Postgres"
        Log-Event "âœ“ Banco de dados '$db' criado." "Postgres" "SUCCESS"
    } else {
        Log-Event "âœ“ Banco de dados '$db' jÃ¡ existe." "Postgres" "INFO"
    }
}

# Configurar pg_hba.conf
Log-Event "Configurando pg_hba.conf para permitir conexÃµes..." "Postgres" "INFO"
# CorreÃ§Ã£o CRÃTICA do comando PostgreSQL com sintaxe robusta
$bashCommand = @'
grep -q "host all all all trust" /var/lib/postgresql/data/pg_hba.conf || echo "host all all all trust" >> /var/lib/postgresql/data/pg_hba.conf
'@
$pgHbaCommand = "docker exec postgres_aula bash -c '$bashCommand'"
Run-Command $pgHbaCommand "Falha ao ajustar pg_hba.conf" "Postgres"
Run-Command "docker exec postgres_aula psql -U postgres -c 'SELECT pg_reload_conf();'" "Falha ao recarregar configuraÃ§Ã£o" "Postgres"
Log-Event "âœ“ ConfiguraÃ§Ã£o de acesso do PostgreSQL atualizada." "Postgres" "SUCCESS"

# 8. Reiniciar n8n
Log-Event "Reiniciando n8n para garantir conexÃ£o com banco..." "n8n" "INFO"
Run-Command "docker restart n8n_editor-1" "Falha ao reiniciar n8n" "n8n"
Log-Event "âœ“ n8n reiniciado com sucesso." "n8n" "SUCCESS"

# 9. Verificar Evolution API
Log-Event "Verificando Evolution API..." "Evolution" "INFO"
if (Wait-For-Port "localhost" 8080 180) {
    Log-Event "âœ“ Evolution API respondendo na porta 8080." "Evolution" "SUCCESS"
    # Garantir que a API esteja plenamente pronta antes de prosseguir
    if (-not (Wait-For-Port "localhost" 8080 60)) {
        Log-Event "Evolution API deixou de responder apÃ³s verificaÃ§Ã£o inicial." "Evolution" "WARN"
    }
    
    # Testar conectividade com Redis
    Test-EvolutionRedisConnection
    
    # Monitorar logs por problemas do Redis
    Log-Event "Monitorando logs da Evolution API por 30 segundos..." "Evolution-Monitor" "INFO"
    $logMonitorJob = Start-Job -ScriptBlock {
        $redisErrors = 0
        $startTime = Get-Date
        while ((Get-Date) -lt $startTime.AddSeconds(30)) {
            $logs = docker logs evolution_aula --tail 10 --since 5s 2>&1
            foreach ($log in $logs) {
                if ($log -like "*redis disconnected*" -or $log -like "*Redis connection*" -or $log -like "*Error*Redis*") {
                    $redisErrors++
                }
            }
            Start-Sleep -Seconds 5
        }
        return $redisErrors
    }
    
    $redisErrorCount = Receive-Job $logMonitorJob -Wait
    Remove-Job $logMonitorJob
    
    if ($redisErrorCount -gt 0) {
        Log-Event "âš  Detectados $redisErrorCount erros relacionados ao Redis nos logs." "Evolution-Monitor" "WARN"
        Log-Event "ðŸ” Isso confirma o bug conhecido da Evolution API (issues #1289, #652, #1017)." "Evolution-Monitor" "WARN"
    } else {
        Log-Event "âœ“ Nenhum erro de Redis detectado nos logs recentes." "Evolution-Monitor" "SUCCESS"
    }
    
    # DiagnÃ³stico robusto do QR Code da Evolution API
    Log-Event "Executando diagnÃ³stico robusto do QR Code da Evolution API..." "QRCode-Diagnostics" "INFO"
    
    # Executar teste de prontidÃ£o do QR Code
    $qrCodeReady = Test-EvolutionQRCodeReadiness -EvolutionContainer "evolution_aula" -ApiKey $envCredentials['AUTHENTICATION_API_KEY']
    
    if (-not $qrCodeReady) {
        Log-Event "âš ï¸ Problemas detectados no QR Code da Evolution API. Iniciando reparo automÃ¡tico..." "QRCode-Auto-Repair" "WARN"
        
        # Executar reparo automÃ¡tico
        $repairResult = Repair-EvolutionQRCode -EvolutionContainer "evolution_aula" -ApiKey $envCredentials['AUTHENTICATION_API_KEY']
        
        if ($repairResult) {
            Log-Event "âœ… Reparo automÃ¡tico do QR Code concluÃ­do com sucesso!" "QRCode-Auto-Repair" "SUCCESS"
        } else {
            Log-Event "âŒ Reparo automÃ¡tico do QR Code falhou. IntervenÃ§Ã£o manual necessÃ¡ria." "QRCode-Auto-Repair" "ERROR"
        }
    } else {
        Log-Event "âœ… QR Code da Evolution API estÃ¡ funcionando corretamente!" "QRCode-Diagnostics" "SUCCESS"
    }
} else {
    Log-Event "âš  Evolution API nÃ£o respondeu na porta 8080." "Evolution" "WARN"
}

# 10. Setup do Chatwoot
$chatwootContainer = docker ps --filter "name=chatwoot-rails-1" --format "{{.Names}}"
if (-not $chatwootContainer) { 
    Abort-Install "Container 'chatwoot-rails-1' nÃ£o estÃ¡ rodando." "Chatwoot"
}

Log-Event "Iniciando setup do Chatwoot..." "Chatwoot" "INFO"
if (-not (Wait-For-Port "localhost" 3000 300)) {
    Abort-Install "Chatwoot nÃ£o respondeu na porta 3000." "Chatwoot"
}

# Executar migraÃ§Ãµes
Run-Command "docker exec chatwoot-rails-1 /usr/local/bundle/bin/bundle exec rails db:chatwoot_prepare" "Falha na migraÃ§Ã£o do Chatwoot" "Chatwoot"
Log-Event "âœ“ MigraÃ§Ã£o do Chatwoot concluÃ­da." "Chatwoot" "SUCCESS"

# Criar usuÃ¡rio admin se as credenciais foram geradas
if ($credentialsGenerated -or $envCredentials['CHATWOOT_ADMIN_EMAIL']) {
    Log-Event "Criando usuÃ¡rio administrador do Chatwoot..." "Chatwoot" "INFO"
    $adminEmail = $envCredentials['CHATWOOT_ADMIN_EMAIL']
    $adminPassword = $envCredentials['CHATWOOT_ADMIN_PASSWORD']
    
    $createUserCmd = @'
docker exec chatwoot-rails-1 bash -c "RAILS_ENV=production bundle exec rails runner \"
  begin
    u = User.find_by(email: '$adminEmail')
    if u.nil?
      u = User.create!(
        email: '$adminEmail',
        password: '$adminPassword',
        password_confirmation: '$adminPassword',
        name: 'Administrator'
      )
      u.add_role(:administrator)
      puts 'Admin user created successfully'
    else
      u.update!(password: '$adminPassword', password_confirmation: '$adminPassword')
      u.add_role(:administrator) unless u.has_role?(:administrator)
      puts 'Admin user updated successfully'
    end
  rescue => e
    puts 'Error: ' + e.message
  end
\" "
'@
    
    try {
        $output = Invoke-Expression $createUserCmd 2>&1
        if ($output -match "successfully") {
            Log-Event "âœ“ UsuÃ¡rio administrador do Chatwoot configurado." "Chatwoot" "SUCCESS"
        } else {
            Log-Event "âš  PossÃ­vel problema ao criar usuÃ¡rio admin: $output" "Chatwoot" "WARN"
        }
    } catch {
        Log-Event "âš  Erro ao criar usuÃ¡rio admin do Chatwoot: $($_.Exception.Message)" "Chatwoot" "WARN"
    }
}

# 11. DiagnÃ³stico Final
Log-Event "Executando diagnÃ³stico final dos serviÃ§os..." "Final-Check" "INFO"

$finalStatus = @{
    "postgres_aula" = "âŒ"
    "redis_aula" = "âŒ" 
    "evolution_aula" = "âŒ"
    "chatwoot-rails-1" = "âŒ"
    "n8n_editor-1" = "âŒ"
    "minio_aula" = "âŒ"
}

$runningContainers = docker ps --format "{{.Names}}"
foreach ($container in $runningContainers) {
    if ($finalStatus.ContainsKey($container)) {
        $finalStatus[$container] = "âœ…"
    }
}

# Verificar problemas especÃ­ficos
$evolutionHasRedisIssue = $false
if ($finalStatus["evolution_aula"] -eq "âœ…") {
    $recentLogs = docker logs evolution_aula --tail 20 2>&1
    if ($recentLogs -match "redis disconnected" -or $recentLogs -match "Error.*Redis") {
        $evolutionHasRedisIssue = $true
        $finalStatus["evolution_aula"] = "âš ï¸"
    }
}

# 12. Gerar Dashboard HTML Completo
Log-Event "Gerando dashboard HTML interativo..." "Dashboard" "INFO"

# Verificar se o mÃ³dulo Generate-ServiceDashboard estÃ¡ disponÃ­vel
if (Get-Command "Generate-ServiceDashboard" -ErrorAction SilentlyContinue) {
    try {
        # Usar o mÃ³dulo para gerar o dashboard
        $dashboardPath = "services-dashboard.html"
        Generate-ServiceDashboard -Output $dashboardPath -UseNgrok:$useNgrok -EvolutionHasRedisIssue:$evolutionHasRedisIssue -EnvCredentials $envCredentials
        Log-Event "âœ“ Dashboard HTML gerado via mÃ³dulo: $dashboardPath" "Dashboard" "SUCCESS"
    } catch {
        Write-Host-Color "âš ï¸ Erro ao usar mÃ³dulo Generate-ServiceDashboard: $($_.Exception.Message)" "Yellow"
        Log-Event "Erro no mÃ³dulo Generate-ServiceDashboard: $($_.Exception.Message)" "Dashboard" "ERROR"
        Write-Host-Color "ðŸ“‹ Gerando dashboard com mÃ©todo alternativo..." "Yellow"
        
        # Fallback para mÃ©todo original (cÃ³digo simplificado)
        $dashboardPath = "services-dashboard.html"
        $simpleHtml = @"
<!DOCTYPE html>
<html><head><title>Dashboard - N8N Evolution</title></head>
<body>
<h1>N8N Evolution - Dashboard</h1>
<p>Dashboard gerado em modo de fallback. Para funcionalidade completa, verifique o mÃ³dulo Generate-ServiceDashboard.psm1</p>
<ul>
<li><a href="http://localhost:5678" target="_blank">N8N Editor</a></li>
<li><a href="http://localhost:8080/manager" target="_blank">Evolution API Manager</a></li>
<li><a href="http://localhost:3000" target="_blank">Chatwoot</a></li>
<li><a href="http://localhost:9001" target="_blank">MinIO Console</a></li>
</ul>
</body></html>
"@
        $simpleHtml | Out-File -FilePath $dashboardPath -Encoding UTF8 -NoNewline
        Log-Event "Dashboard HTML gerado em modo fallback: $dashboardPath" "Dashboard" "SUCCESS"
    }
} else {
    Write-Host-Color "âš ï¸ MÃ³dulo Generate-ServiceDashboard nÃ£o encontrado" "Yellow"
    Log-Event "MÃ³dulo Generate-ServiceDashboard nÃ£o disponÃ­vel" "Dashboard" "WARNING"
    Write-Host-Color "ðŸ“‹ Gerando dashboard com mÃ©todo alternativo..." "Yellow"
    
    # Fallback para mÃ©todo original (cÃ³digo simplificado)
    $dashboardPath = "services-dashboard.html"
    $simpleHtml = @"
<!DOCTYPE html>
<html><head><title>Dashboard - N8N Evolution</title></head>
<body>
<h1>N8N Evolution - Dashboard</h1>
<p>Dashboard gerado em modo de fallback. Para funcionalidade completa, verifique o mÃ³dulo Generate-ServiceDashboard.psm1</p>
<ul>
<li><a href="http://localhost:5678" target="_blank">N8N Editor</a></li>
<li><a href="http://localhost:8080/manager" target="_blank">Evolution API Manager</a></li>
<li><a href="http://localhost:3000" target="_blank">Chatwoot</a></li>
<li><a href="http://localhost:9001" target="_blank">MinIO Console</a></li>
</ul>
</body></html>
"@
    $simpleHtml | Out-File -FilePath $dashboardPath -Encoding UTF8 -NoNewline
    Log-Event "Dashboard HTML gerado em modo fallback: $dashboardPath" "Dashboard" "SUCCESS"
}

# ConfiguraÃ§Ã£o completa dos serviÃ§os com TODAS as credenciais (mantida para compatibilidade)
$services = @(
    @{
        N='n8n'
        Url='http://localhost:5678'
        Desc='Editor de workflows de automaÃ§Ã£o. Requer configuraÃ§Ã£o manual de Webhook.'
        U=''
        Pwd=''
        Container='n8n_editor-1'
        Health='http://localhost:5678'
        ExtraInfo=@{
            'N8N_ENCRYPTION_KEY' = $envCredentials['N8N_ENCRYPTION_KEY']
            'Webhook Config Info' = 'EVOLUTION_API -> WEBHOOK TARGET URL: http://host.docker.internal:5678/api/n8n-evolution'
            'Webhook Setup' = 'Configure um nÃ³ Webhook no n8n com path: /api/n8n-evolution'
        }
    },
    @{
        N='Evolution API'
        Url='http://localhost:8080'
        Desc='API WhatsApp e IntegraÃ§Ã£o'
        U=''
        Pwd=$envCredentials['AUTHENTICATION_API_KEY']
        Container='evolution_aula'
        Health='http://localhost:8080'
        ExtraInfo=@{
            'Manager' = 'http://localhost:8080/manager'
            'ObservaÃ§Ã£o' = 'A documentaÃ§Ã£o da Evolution API nÃ£o Ã© servida em /docs. Consulte a documentaÃ§Ã£o oficial do projeto.'
        }
    },
    @{
        N='Chatwoot'
        Url='http://localhost:3000'
        Desc='Plataforma de atendimento omnichannel'
        U=$envCredentials['CHATWOOT_ADMIN_EMAIL']
        Pwd=$envCredentials['CHATWOOT_ADMIN_PASSWORD']
        Container='chatwoot-rails-1'
        Health='http://localhost:3000'
        ExtraInfo=@{
            'SECRET_KEY_BASE' = $envCredentials['SECRET_KEY_BASE']
        }
    },
    @{
        N='MinIO Console'
        Url='http://localhost:9001'
        Desc='Console de armazenamento S3'
        U=$envCredentials['MINIO_ROOT_USER']
        Pwd=$envCredentials['MINIO_ROOT_PASSWORD']
        Container='minio_aula'
        Health='http://localhost:9001'
        ExtraInfo=@{
            'S3 Endpoint' = 'http://localhost:9000'
        }
    },
    @{
        N='PostgreSQL'
        Url='localhost:5432'
        Desc='Banco de dados principal'
        U='postgres'
        Pwd=$envCredentials['POSTGRES_PASSWORD']
        Container='postgres_aula'
        Health=''
        ExtraInfo=@{
            'Databases' = 'chatwoot, evolution, n8n_fila'
        }
    },
    @{
        N='Redis'
        Url='localhost:6379'
        Desc='Cache e sessÃµes'
        U=''
        Pwd=''
        Container='redis_aula'
        Health=''
        ExtraInfo=@{
            'Database' = '6 (Evolution API)'
        }
    }
)

# 13. Resumo Final no Console
Write-Host-Color "`nâ•”â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•—" "Magenta"
Write-Host-Color "â•‘      AMBIENTE DE AUTOMAÃ‡ÃƒO INICIADO COM SUCESSO!        â•‘" "Magenta"
Write-Host-Color "â•šâ•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•" "Magenta"

Write-Host-Color "`nðŸ“Š STATUS DOS SERVIÃ‡OS:" "White"
Write-Host-Color "  PostgreSQL + pgvector: $($finalStatus['postgres_aula'])" "White"
Write-Host-Color "  Redis: $($finalStatus['redis_aula'])" "White"
Write-Host-Color "  Evolution API: $($finalStatus['evolution_aula'])" "White"
Write-Host-Color "  Chatwoot: $($finalStatus['chatwoot-rails-1'])" "White"
Write-Host-Color "  n8n: $($finalStatus['n8n_editor-1'])" "White"
Write-Host-Color "  MinIO: $($finalStatus['minio_aula'])" "White"

if ($evolutionHasRedisIssue) {
    Write-Host-Color "`nâš ï¸  PROBLEMA CONHECIDO DETECTADO:" "Yellow"
    Write-Host-Color "   Evolution API apresenta instabilidade com Redis (issues #1289, #652, #1017)" "Yellow"
    Write-Host-Color "   A API funciona mas pode apresentar falhas na criaÃ§Ã£o de instÃ¢ncias WhatsApp" "Yellow"
    Write-Host-Color "   Consulte REDIS-TROUBLESHOOTING.md para mais detalhes" "Yellow"
}

Write-Host-Color "`nðŸŒ ACESSE OS SERVIÃ‡OS:" "Cyan"
Write-Host-Color "  ðŸŽ¯ Dashboard Completo: services-dashboard.html" "Green"

if ($useNgrok -and $env:WEBHOOK_URL -and $env:WEBHOOK_URL -ne "") {
    Write-Host-Color "  ðŸ“Š n8n (PÃºblico): $env:WEBHOOK_URL" "Green"
    Write-Host-Color "  ðŸ“Š n8n (Local): http://localhost:5678" "White"
    Write-Host-Color "  ðŸ“± Evolution API (Local): http://localhost:8080/docs" "White"
    Write-Host-Color "  ðŸ’¬ Chatwoot (Local): http://localhost:3000" "White"
    Write-Host-Color "  ðŸ“¦ MinIO Console (Local): http://localhost:9001" "White"
    Write-Host-Color "  ðŸ” Ngrok Inspector: http://localhost:4040" "Cyan"
    Write-Host-Color "`nðŸŒ WEBHOOKS PÃšBLICOS CONFIGURADOS:" "Green"
    Write-Host-Color "  ðŸ“¡ N8N Webhook Base: $env:WEBHOOK_URL" "Green"
    Write-Host-Color "  ðŸ“± Evolution Webhook: $env:WEBHOOK_URL/webhook/evolution" "Green"
} else {
    Write-Host-Color "  ðŸ“Š n8n: http://localhost:5678" "White"
    Write-Host-Color "  ðŸ“± Evolution API: http://localhost:8080/docs" "White"
    Write-Host-Color "  ðŸ’¬ Chatwoot: http://localhost:3000" "White"
    Write-Host-Color "  ðŸ“¦ MinIO Console: http://localhost:9001" "White"
}

Write-Host-Color "`nðŸ” CREDENCIAIS GERADAS (salvas em .env):" "Yellow"
Write-Host-Color "â•”â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•—" "Yellow"
Write-Host-Color "â•‘ PostgreSQL                                                 â•‘" "Yellow"
Write-Host-Color "â•‘   UsuÃ¡rio: postgres                                        â•‘" "Yellow"
Write-Host-Color "â•‘   Senha: $($envCredentials['POSTGRES_PASSWORD'])" "Yellow"
Write-Host-Color "â•‘                                                            â•‘" "Yellow"
Write-Host-Color "â•‘ MinIO                                                      â•‘" "Yellow"
Write-Host-Color "â•‘   UsuÃ¡rio: $($envCredentials['MINIO_ROOT_USER'])" "Yellow"
Write-Host-Color "â•‘   Senha: $($envCredentials['MINIO_ROOT_PASSWORD'])" "Yellow"
Write-Host-Color "â•‘                                                            â•‘" "Yellow"
Write-Host-Color "â•‘ Evolution API                                              â•‘" "Yellow"
Write-Host-Color "â•‘   API Key: $($envCredentials['AUTHENTICATION_API_KEY'])" "Yellow"
Write-Host-Color "â•‘                                                            â•‘" "Yellow"
Write-Host-Color "â•‘ Chatwoot                                                   â•‘" "Yellow"
Write-Host-Color "â•‘   Email: $($envCredentials['CHATWOOT_ADMIN_EMAIL'])" "Yellow"
Write-Host-Color "â•‘   Senha: $($envCredentials['CHATWOOT_ADMIN_PASSWORD'])" "Yellow"
Write-Host-Color "â•‘   Secret: $($envCredentials['SECRET_KEY_BASE'].Substring(0, 20))..." "Yellow"
Write-Host-Color "â•‘                                                            â•‘" "Yellow"
Write-Host-Color "â•‘ n8n                                                        â•‘" "Yellow"
Write-Host-Color "â•‘   Encryption Key: $($envCredentials['N8N_ENCRYPTION_KEY'].Substring(0, 20))..." "Yellow"
Write-Host-Color "â•šâ•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•" "Yellow"

Write-Host-Color "`nARQUIVOS CRIADOS:" "White"
Write-Host-Color "  install.log - Logs completos da instalaÃ§Ã£o" "Green"
Write-Host-Color "  services-dashboard.html - Dashboard interativo" "Green"
Write-Host-Color "  .env - Arquivo com todas as credenciais" "Green"

# 14. Detectar primeira instalaÃ§Ã£o e executar post-setup-automation.ps1 automaticamente
$firstInstallMarker = Join-Path $PSScriptRoot ".first-install-completed"
$isFirstInstall = $credentialsGenerated -and (-not (Test-Path $firstInstallMarker))

if ($isFirstInstall) {
    Write-Host-Color "`nðŸ”§ PRIMEIRA INSTALAÃ‡ÃƒO DETECTADA - Executando configuraÃ§Ã£o automÃ¡tica avanÃ§ada..." "Cyan"
    Log-Event "Primeira instalaÃ§Ã£o detectada. Executando post-setup-automation.ps1 automaticamente..." "Auto-Setup" "INFO"
    
    # Aguardar 10 segundos para garantir que todos os serviÃ§os estejam estÃ¡veis
    Write-Host-Color "Aguardando estabilizacao dos servicos - 10 segundos..." "Yellow"
    Start-Sleep -Seconds 10
    
    try {
        $postSetupScript = Join-Path $PSScriptRoot "post-setup-automation.ps1"
        if (Test-Path $postSetupScript) {
            Write-Host-Color "ðŸš€ Executando configuraÃ§Ã£o automÃ¡tica..." "Green"
            
            # Executar o script post-setup-automation.ps1
            & $postSetupScript
            
            if ($LASTEXITCODE -eq 0) {
                Write-Host-Color "âœ… ConfiguraÃ§Ã£o automÃ¡tica concluÃ­da com sucesso!" "Green"
                Log-Event "post-setup-automation.ps1 executado com sucesso" "Auto-Setup" "SUCCESS"
                
                # Marcar primeira instalaÃ§Ã£o como concluÃ­da
                $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
                "$timestamp - Primeira instalacao e configuracao automatica concluida" | Out-File $firstInstallMarker -Encoding UTF8
                
                Write-Host-Color "`nðŸŽ‰ SISTEMA TOTALMENTE CONFIGURADO!" "Green"
                Write-Host-Color "ðŸ“‹ ÃšNICA ETAPA MANUAL RESTANTE:" "Yellow"
                Write-Host-Color "   â€¢ Configurar webhook N8N â†” Evolution API manualmente" "Yellow"
                Write-Host-Color "   â€¢ Acesse: http://localhost:5678 (N8N) e http://localhost:8080 (Evolution)" "Yellow"
                Write-Host-Color "   â€¢ Configure webhook path: /api/n8n-evolution" "Yellow"
                
            } else {
                Write-Host-Color "âš ï¸ ConfiguraÃ§Ã£o automÃ¡tica falhou. Execute manualmente: .\post-setup-automation.ps1" "Yellow"
                Log-Event "post-setup-automation.ps1 falhou com cÃ³digo: $LASTEXITCODE" "Auto-Setup" "ERROR"
            }
        } else {
            Write-Host-Color "âš ï¸ Script post-setup-automation.ps1 nÃ£o encontrado. ConfiguraÃ§Ã£o manual necessÃ¡ria." "Yellow"
            Log-Event "post-setup-automation.ps1 nÃ£o encontrado em: $postSetupScript" "Auto-Setup" "ERROR"
        }
    } catch {
        Write-Host-Color "âŒ Erro ao executar configuraÃ§Ã£o automÃ¡tica: $($_.Exception.Message)" "Red"
        Log-Event "Erro ao executar post-setup-automation.ps1: $($_.Exception.Message)" "Auto-Setup" "ERROR"
        Write-Host-Color "ðŸ“‹ Execute manualmente: .\post-setup-automation.ps1" "Yellow"
    }
} else {
    Write-Host-Color "`nðŸ”„ INSTALAÃ‡ÃƒO EXISTENTE DETECTADA" "Cyan"
    if (Test-Path $firstInstallMarker) {
        $markerContent = Get-Content $firstInstallMarker -Raw
        Write-Host-Color "ðŸ“… Primeira configuraÃ§Ã£o jÃ¡ foi executada: $($markerContent.Trim())" "Green"
        Write-Host-Color "ðŸ”§ Para reconfigurar, execute manualmente: .\post-setup-automation.ps1" "White"
    } else {
        Write-Host-Color "ðŸ”§ Para configuraÃ§Ã£o completa, execute: .\post-setup-automation.ps1" "Yellow"
    }
}

# 15. Abrir Dashboard Automaticamente
Write-Host-Color "`nðŸŒ Abrindo dashboard no navegador..." "Green"
try {
    $dashboardFullPath = Resolve-Path $dashboardPath
    Open-UrlInChrome -Url "file://$dashboardFullPath" -Component "Dashboard"
    Open-UrlInChrome -Url "http://localhost:8080/manager" -Component "EvolutionManager"
    Log-Event "Dashboard e Evolution Manager abertos automaticamente no Chrome." "Dashboard" "SUCCESS"
    Write-Host-Color "Dashboard disponÃ­vel em: $dashboardFullPath" "Green"
} catch {
    Write-Host-Color "âš  NÃ£o foi possÃ­vel abrir automaticamente: $($_.Exception.Message)" "Yellow"
    Write-Host-Color "  Acesse manualmente: $dashboardFullPath e http://localhost:8080/manager" "Yellow"
}

Write-Host-Color "`nâœ¨ InstalaÃ§Ã£o concluÃ­da com sucesso!" "Green"
Write-Host-Color "ðŸ“‹ Verifique o arquivo install.log para detalhes completos." "White"

if ($evolutionHasRedisIssue) {
    Write-Host-Color "`nðŸ“‹ PRÃ“XIMOS PASSOS RECOMENDADOS:" "Cyan"
    Write-Host-Color "   1. Monitore os logs da Evolution API: docker logs -f evolution_aula" "Cyan"
    Write-Host-Color "   2. Teste versÃµes alternativas se necessÃ¡rio" "Cyan"
    Write-Host-Color "   3. Reporte problemas persistentes no GitHub" "Cyan"
}

# Mostrar instruÃ§Ãµes simplificadas apenas se nÃ£o for primeira instalaÃ§Ã£o
if (-not $isFirstInstall) {
    Write-Host-Color "`nðŸ”§ CONFIGURAÃ‡ÃƒO MANUAL DISPONÃVEL:" "Yellow"
    Write-Host-Color "   â€¢ Para configuraÃ§Ã£o avanÃ§ada: .\post-setup-automation.ps1" "Yellow"
    Write-Host-Color "   â€¢ Para workflows especÃ­ficos: .\Onboard-Workflow.ps1 -WorkflowName \"NomeDoWorkflow\"" "Yellow"
}

# Fim do script

}}
Log-Event "OK - Script finalizado com sucesso!" "Orchestrator" "SUCCESS"