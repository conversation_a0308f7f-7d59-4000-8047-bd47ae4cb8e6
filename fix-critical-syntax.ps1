# fix-critical-syntax.ps1 - Corrigir problemas críticos de sintaxe
Write-Host "Corrigindo problemas críticos de sintaxe..." -ForegroundColor Yellow

# Ler o arquivo linha por linha
$lines = Get-Content "Start-Environment.ps1" -Encoding UTF8

# Corrigir linhas específicas problemáticas
for ($i = 0; $i -lt $lines.Count; $i++) {
    $lineNum = $i + 1
    $line = $lines[$i]
    
    # Linha 595 - Problema com Log-Event
    if ($lineNum -eq 595) {
        $lines[$i] = '        Log-Event "Dashboard HTML gerado em modo fallback: $dashboardPath" "Dashboard" "SUCCESS"'
        Write-Host "Corrigida linha 595" -ForegroundColor Green
    }
    
    # Linha 811 - Problema com caracteres especiais
    if ($lineNum -eq 811) {
        $lines[$i] = '        Write-Host-Color "Erro ao executar configuração automática: $($_.Exception.Message)" "Red"'
        Write-Host "Corrigida linha 811" -ForegroundColor Green
    }
    
    # Linha 813 - Problema com caracteres especiais
    if ($lineNum -eq 813) {
        $lines[$i] = '        Write-Host-Color "Execute manualmente: .\post-setup-automation.ps1" "Yellow"'
        Write-Host "Corrigida linha 813" -ForegroundColor Green
    }
    
    # Linha 857 - Problema com terminação de string
    if ($lineNum -eq 857) {
        $lines[$i] = 'Log-Event "Script finalizado com sucesso!" "Orchestrator" "SUCCESS"'
        Write-Host "Corrigida linha 857" -ForegroundColor Green
    }
    
    # Remover emojis problemáticos de todas as linhas
    $line = $lines[$i]
    $line = $line -replace 'ðŸ"§', ''
    $line = $line -replace 'ðŸš€', ''
    $line = $line -replace 'ðŸ"„', ''
    $line = $line -replace 'ðŸ"…', ''
    $line = $line -replace 'ðŸ"‹', ''
    $line = $line -replace 'âŒ', 'ERRO:'
    $line = $line -replace 'âš ï¸', 'ATENÇÃO:'
    $line = $line -replace 'âœ…', 'OK'
    $line = $line -replace 'âœ"', 'OK'
    
    $lines[$i] = $line
}

# Salvar arquivo corrigido
$lines | Out-File "Start-Environment.ps1" -Encoding UTF8

Write-Host "Correções aplicadas!" -ForegroundColor Green

# Testar sintaxe básica
Write-Host "Testando sintaxe..." -ForegroundColor Yellow
try {
    powershell -ExecutionPolicy Bypass -Command "Get-Content 'Start-Environment.ps1' | Out-Null; Write-Host 'Arquivo pode ser lido' -ForegroundColor Green"
} catch {
    Write-Host "Erro ao ler arquivo: $($_.Exception.Message)" -ForegroundColor Red
}
