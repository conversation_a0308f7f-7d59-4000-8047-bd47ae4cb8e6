{"name": "[REVIEW] Human Content Review Engine", "nodes": [{"parameters": {"httpMethod": "POST", "path": "content-review", "options": {}}, "id": "f47ac10b-58cc-4372-a567-0e02b2c3d479", "name": "👁️ Webhook - Review Request", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300], "webhookId": "content-review"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "c1", "leftValue": "={{ $json.action }}", "rightValue": "submit_for_review", "operator": {"type": "string", "operation": "equals"}}, {"id": "c2", "leftValue": "={{ $json.action }}", "rightValue": "provide_feedback", "operator": {"type": "string", "operation": "equals"}}, {"id": "c3", "leftValue": "={{ $json.action }}", "rightValue": "get_queue", "operator": {"type": "string", "operation": "equals"}}, {"id": "c4", "leftValue": "={{ $json.action }}", "rightValue": "auto_approve_check", "operator": {"type": "string", "operation": "equals"}}], "combinator": "or"}, "options": {}}, "id": "8f14e45f-ceea-467a-9b87-7df96c6f135e", "name": "🔀 Route Action", "type": "n8n-nodes-base.switch", "typeVersion": 3, "position": [460, 300]}, {"parameters": {"operation": "select", "schema": {"value": "agent"}, "table": {"value": "human_ai_cycle_config"}, "where": {"values": [{"column": "config_key", "condition": "equal", "value": "review_thresholds"}]}, "options": {}}, "id": "6ba7b810-9dad-11d1-80b4-00c04fd430c8", "name": "⚙️ Get Review Config", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [680, 120], "credentials": {"postgres": {"id": "postgres-main", "name": "PostgreSQL - Main DB"}}}, {"parameters": {"jsCode": "// 🤖 Verificar se conteúdo pode ser aprovado automaticamente\nconst input = $input.first().json;\nconst config = $('⚙️ Get Review Config').first().json.config_value;\n\n// Validações básicas\nif (!input.influencer_id || !input.content_type || !input.content_data) {\n  throw new Error('influencer_id, content_type e content_data são obrigatórios');\n}\n\nif (!input.ai_confidence_score || input.ai_confidence_score < 0 || input.ai_confidence_score > 1) {\n  throw new Error('ai_confidence_score deve estar entre 0 e 1');\n}\n\n// Verificar se pode ser aprovado automaticamente\nconst canAutoApprove = input.ai_confidence_score >= config.auto_approve_threshold;\n\n// Determinar prioridade baseada na confiança\nlet priority = 'medium';\nif (input.ai_confidence_score < config.low_confidence) {\n  priority = 'high';\n} else if (input.ai_confidence_score < config.medium_confidence) {\n  priority = 'medium';\n} else if (input.ai_confidence_score < config.high_confidence) {\n  priority = 'low';\n}\n\n// Se marcado como urgente, manter prioridade\nif (input.priority_level === 'urgent') {\n  priority = 'urgent';\n}\n\n// Calcular deadline baseado na prioridade\nconst slaConfig = $('⚙️ Get Review Config').all().find(c => c.json.config_key === 'review_sla')?.json.config_value;\nconst slaMinutes = slaConfig ? slaConfig[priority] : 480; // Default 8 horas\nconst deadline = new Date(Date.now() + slaMinutes * 60 * 1000);\n\nreturn {\n  ...input,\n  can_auto_approve: canAutoApprove,\n  priority_level: priority,\n  review_deadline: deadline.toISOString(),\n  status: canAutoApprove ? 'auto_approved' : 'pending'\n};"}, "id": "8f14e45f-ceea-467a-9b87-7df96c6f135f", "name": "🤖 Auto-Approve Check", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [900, 120]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "c1", "leftValue": "={{ $json.can_auto_approve }}", "rightValue": true, "operator": {"type": "boolean", "operation": "equal"}}], "combinator": "and"}, "options": {}}, "id": "8f14e45f-ceea-467a-9b87-7df96c6f1360", "name": "✅ Can Auto-Approve?", "type": "n8n-nodes-base.switch", "typeVersion": 3, "position": [1120, 120]}, {"parameters": {"operation": "insert", "schema": {"value": "agent"}, "table": {"value": "published_content"}, "columns": {"mappingMode": "defineBelow", "values": [{"column": "influencer_id", "value": "={{ $json.influencer_id }}"}, {"column": "platform", "value": "={{ $json.content_data.platform || 'unknown' }}"}, {"column": "content_text", "value": "={{ $json.content_data.text || '' }}"}, {"column": "content_visual_url", "value": "={{ $json.content_data.visual_url || null }}"}, {"column": "status", "value": "auto_approved"}, {"column": "published_at", "value": "={{ new Date().toISOString() }}"}]}, "options": {}}, "id": "6ba7b811-9dad-11d1-80b4-00c04fd430c8", "name": "✅ Auto-Approve Content", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [1340, 80], "credentials": {"postgres": {"id": "postgres-main", "name": "PostgreSQL - Main DB"}}}, {"parameters": {"operation": "insert", "schema": {"value": "agent"}, "table": {"value": "content_review_queue"}, "columns": {"mappingMode": "defineBelow", "values": [{"column": "influencer_id", "value": "={{ $json.influencer_id }}"}, {"column": "content_type", "value": "={{ $json.content_type }}"}, {"column": "content_data", "value": "={{ JSON.stringify($json.content_data) }}"}, {"column": "ai_confidence_score", "value": "={{ $json.ai_confidence_score }}"}, {"column": "priority_level", "value": "={{ $json.priority_level }}"}, {"column": "status", "value": "pending"}, {"column": "review_deadline", "value": "={{ $json.review_deadline }}"}, {"column": "created_by", "value": "ai_content_generator"}]}, "options": {}}, "id": "6ba7b812-9dad-11d1-80b4-00c04fd430c8", "name": "📝 Add to Review Queue", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [1340, 160], "credentials": {"postgres": {"id": "postgres-main", "name": "PostgreSQL - Main DB"}}}, {"parameters": {"operation": "select", "schema": {"value": "agent"}, "table": {"value": "content_review_queue"}, "where": {"values": [{"column": "status", "condition": "in", "value": "pending,in_review"}]}, "sort": {"values": [{"column": "priority_level", "direction": "ASC"}, {"column": "created_at", "direction": "ASC"}]}, "limit": 50, "options": {}}, "id": "6ba7b813-9dad-11d1-80b4-00c04fd430c8", "name": "📋 Get Review Queue", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [680, 300], "credentials": {"postgres": {"id": "postgres-main", "name": "PostgreSQL - Main DB"}}}, {"parameters": {"jsCode": "// 📋 Formatar fila de revisão com informações do influenciador\nconst queueItems = $input.all();\n\n// Buscar informações dos influenciadores\nconst influencerIds = [...new Set(queueItems.map(item => item.json.influencer_id))];\n\nreturn queueItems.map(item => {\n  const data = item.json;\n  \n  // Parse do content_data se for string\n  let contentData = data.content_data;\n  if (typeof contentData === 'string') {\n    try {\n      contentData = JSON.parse(contentData);\n    } catch (e) {\n      contentData = { text: contentData };\n    }\n  }\n  \n  // Calcular tempo na fila\n  const ageMinutes = Math.floor((Date.now() - new Date(data.created_at).getTime()) / (1000 * 60));\n  \n  // Status do deadline\n  const deadline = new Date(data.review_deadline);\n  const now = new Date();\n  let deadlineStatus = 'on_time';\n  \n  if (deadline < now) {\n    deadlineStatus = 'overdue';\n  } else if (deadline < new Date(now.getTime() + 60 * 60 * 1000)) { // 1 hora\n    deadlineStatus = 'urgent';\n  }\n  \n  return {\n    id: data.id,\n    influencer_id: data.influencer_id,\n    content_type: data.content_type,\n    content_preview: {\n      text: contentData.text ? contentData.text.substring(0, 100) + '...' : '',\n      platform: contentData.platform || 'unknown',\n      has_visual: !!contentData.visual_url\n    },\n    ai_confidence_score: data.ai_confidence_score,\n    priority_level: data.priority_level,\n    status: data.status,\n    assigned_reviewer: data.assigned_reviewer,\n    review_deadline: data.review_deadline,\n    deadline_status: deadlineStatus,\n    age_minutes: ageMinutes,\n    created_at: data.created_at\n  };\n});"}, "id": "8f14e45f-ceea-467a-9b87-7df96c6f1361", "name": "📋 Format Queue Response", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [900, 300]}, {"parameters": {"jsCode": "// 💬 Validar e processar feedback humano\nconst input = $input.first().json;\n\n// Validações básicas\nif (!input.review_queue_id) {\n  throw new Error('review_queue_id é obrigatório');\n}\n\nif (!input.reviewer_name) {\n  throw new Error('reviewer_name é obrigatório');\n}\n\nif (!input.feedback_type || !['approval', 'rejection', 'revision_request'].includes(input.feedback_type)) {\n  throw new Error('feedback_type deve ser: approval, rejection ou revision_request');\n}\n\n// Validar scores se fornecidos\nconst scores = ['quality_score', 'brand_alignment_score', 'engagement_potential_score'];\nfor (const score of scores) {\n  if (input[score] && (input[score] < 1 || input[score] > 10)) {\n    throw new Error(`${score} deve estar entre 1 e 10`);\n  }\n}\n\n// Determinar novo status baseado no feedback\nlet newStatus;\nswitch (input.feedback_type) {\n  case 'approval':\n    newStatus = 'approved';\n    break;\n  case 'rejection':\n    newStatus = 'rejected';\n    break;\n  case 'revision_request':\n    newStatus = 'needs_revision';\n    break;\n}\n\nreturn {\n  ...input,\n  new_status: newStatus,\n  review_time_minutes: input.review_time_minutes || null,\n  ip_address: input.ip_address || null\n};"}, "id": "8f14e45f-ceea-467a-9b87-7df96c6f1362", "name": "💬 Validate <PERSON>back", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [680, 420]}, {"parameters": {"operation": "insert", "schema": {"value": "agent"}, "table": {"value": "human_feedback"}, "columns": {"mappingMode": "defineBelow", "values": [{"column": "review_queue_id", "value": "={{ $json.review_queue_id }}"}, {"column": "reviewer_name", "value": "={{ $json.reviewer_name }}"}, {"column": "feedback_type", "value": "={{ $json.feedback_type }}"}, {"column": "feedback_text", "value": "={{ $json.feedback_text || null }}"}, {"column": "suggested_changes", "value": "={{ $json.suggested_changes ? JSON.stringify($json.suggested_changes) : null }}"}, {"column": "quality_score", "value": "={{ $json.quality_score || null }}"}, {"column": "brand_alignment_score", "value": "={{ $json.brand_alignment_score || null }}"}, {"column": "engagement_potential_score", "value": "={{ $json.engagement_potential_score || null }}"}, {"column": "review_time_minutes", "value": "={{ $json.review_time_minutes || null }}"}, {"column": "created_by", "value": "={{ $json.reviewer_name }}"}, {"column": "ip_address", "value": "={{ $json.ip_address || null }}"}]}, "options": {}}, "id": "6ba7b814-9dad-11d1-80b4-00c04fd430c8", "name": "💾 Save Feedback", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [900, 420], "credentials": {"postgres": {"id": "postgres-main", "name": "PostgreSQL - Main DB"}}}, {"parameters": {"operation": "update", "schema": {"value": "agent"}, "table": {"value": "content_review_queue"}, "where": {"values": [{"column": "id", "condition": "equal", "value": "={{ $json.review_queue_id }}"}]}, "columns": {"mappingMode": "defineBelow", "values": [{"column": "status", "value": "={{ $json.new_status }}"}, {"column": "assigned_reviewer", "value": "={{ $json.reviewer_name }}"}, {"column": "updated_by", "value": "={{ $json.reviewer_name }}"}]}, "options": {}}, "id": "6ba7b815-9dad-11d1-80b4-00c04fd430c8", "name": "🔄 Update Queue Status", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [1120, 420], "credentials": {"postgres": {"id": "postgres-main", "name": "PostgreSQL - Main DB"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "c1", "leftValue": "={{ $json.feedback_type }}", "rightValue": "approval", "operator": {"type": "string", "operation": "equals"}}], "combinator": "and"}, "options": {}}, "id": "8f14e45f-ceea-467a-9b87-7df96c6f1363", "name": "✅ Is Approved?", "type": "n8n-nodes-base.switch", "typeVersion": 3, "position": [1340, 420]}, {"parameters": {"operation": "select", "schema": {"value": "agent"}, "table": {"value": "content_review_queue"}, "where": {"values": [{"column": "id", "condition": "equal", "value": "={{ $json.review_queue_id }}"}]}, "options": {}}, "id": "6ba7b816-9dad-11d1-80b4-00c04fd430c8", "name": "📄 Get Content Data", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [1560, 380], "credentials": {"postgres": {"id": "postgres-main", "name": "PostgreSQL - Main DB"}}}, {"parameters": {"operation": "insert", "schema": {"value": "agent"}, "table": {"value": "published_content"}, "columns": {"mappingMode": "defineBelow", "values": [{"column": "influencer_id", "value": "={{ $json.influencer_id }}"}, {"column": "platform", "value": "={{ JSON.parse($json.content_data).platform || 'unknown' }}"}, {"column": "content_text", "value": "={{ JSON.parse($json.content_data).text || '' }}"}, {"column": "content_visual_url", "value": "={{ JSON.parse($json.content_data).visual_url || null }}"}, {"column": "status", "value": "approved"}, {"column": "published_at", "value": "={{ new Date().toISOString() }}"}]}, "options": {}}, "id": "6ba7b817-9dad-11d1-80b4-00c04fd430c8", "name": "📤 Publish Approved Content", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [1780, 380], "credentials": {"postgres": {"id": "postgres-main", "name": "PostgreSQL - Main DB"}}}, {"parameters": {"respondWith": "json", "responseBody": "={{ $json }}", "options": {}}, "id": "f47ac10b-58cc-4372-a567-0e02b2c3d480", "name": "📤 Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1560, 300]}, {"parameters": {"jsCode": "// 📤 Formatar resposta final\nconst action = $('👁️ Webhook - Review Request').first().json.action;\nconst input = $input.first().json;\n\nlet response = {\n  success: true,\n  action: action,\n  timestamp: new Date().toISOString()\n};\n\nswitch (action) {\n  case 'submit_for_review':\n    response.message = input.can_auto_approve ? \n      'Conteúdo aprovado automaticamente' : \n      'Conteúdo adicionado à fila de revisão';\n    response.data = {\n      auto_approved: input.can_auto_approve,\n      priority: input.priority_level,\n      deadline: input.review_deadline,\n      queue_id: input.id || null\n    };\n    break;\n    \n  case 'get_queue':\n    response.data = {\n      queue_items: input,\n      total_items: input.length,\n      urgent_items: input.filter(item => item.deadline_status === 'urgent' || item.deadline_status === 'overdue').length\n    };\n    break;\n    \n  case 'provide_feedback':\n    response.message = 'Feedback registrado com sucesso';\n    response.data = {\n      feedback_type: input.feedback_type,\n      new_status: input.new_status,\n      published: input.feedback_type === 'approval'\n    };\n    break;\n    \n  case 'auto_approve_check':\n    response.data = {\n      can_auto_approve: input.can_auto_approve,\n      confidence_score: input.ai_confidence_score,\n      priority: input.priority_level\n    };\n    break;\n}\n\nreturn response;"}, "id": "8f14e45f-ceea-467a-9b87-7df96c6f1364", "name": "📋 Format Final Response", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1340, 300]}, {"parameters": {"jsCode": "// 🚨 Tratamento de erro\nconst error = $input.first().json;\n\nreturn {\n  success: false,\n  error: {\n    message: error.message || 'Erro interno do servidor',\n    code: error.code || 'INTERNAL_ERROR',\n    details: error.details || null\n  },\n  timestamp: new Date().toISOString()\n};"}, "id": "8f14e45f-ceea-467a-9b87-7df96c6f1365", "name": "🚨 <PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1340, 540]}, {"parameters": {"operation": "insert", "schema": {"value": "agent"}, "table": {"value": "system_logs"}, "columns": {"mappingMode": "defineBelow", "values": [{"column": "event_type", "value": "content_review_error"}, {"column": "event_data", "value": "={{ JSON.stringify({error: $json.error, action: $('👁️ Webhook - Review Request').first().json.action}) }}"}, {"column": "severity", "value": "error"}]}, "options": {}}, "id": "6ba7b818-9dad-11d1-80b4-00c04fd430c8", "name": "📝 Log Error", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [1560, 540], "credentials": {"postgres": {"id": "postgres-main", "name": "PostgreSQL - Main DB"}}}], "connections": {"👁️ Webhook - Review Request": {"main": [[{"node": "🔀 Route Action", "type": "main", "index": 0}]]}, "🔀 Route Action": {"main": [[{"node": "⚙️ Get Review Config", "type": "main", "index": 0}], [{"node": "💬 Validate <PERSON>back", "type": "main", "index": 0}], [{"node": "📋 Get Review Queue", "type": "main", "index": 0}], [{"node": "⚙️ Get Review Config", "type": "main", "index": 0}]]}, "⚙️ Get Review Config": {"main": [[{"node": "🤖 Auto-Approve Check", "type": "main", "index": 0}]]}, "🤖 Auto-Approve Check": {"main": [[{"node": "✅ Can Auto-Approve?", "type": "main", "index": 0}]]}, "✅ Can Auto-Approve?": {"main": [[{"node": "✅ Auto-Approve Content", "type": "main", "index": 0}], [{"node": "📝 Add to Review Queue", "type": "main", "index": 0}]]}, "✅ Auto-Approve Content": {"main": [[{"node": "📋 Format Final Response", "type": "main", "index": 0}]]}, "📝 Add to Review Queue": {"main": [[{"node": "📋 Format Final Response", "type": "main", "index": 0}]]}, "📋 Get Review Queue": {"main": [[{"node": "📋 Format Queue Response", "type": "main", "index": 0}]]}, "📋 Format Queue Response": {"main": [[{"node": "📋 Format Final Response", "type": "main", "index": 0}]]}, "💬 Validate Feedback": {"main": [[{"node": "💾 Save Feedback", "type": "main", "index": 0}]]}, "💾 Save Feedback": {"main": [[{"node": "🔄 Update Queue Status", "type": "main", "index": 0}]]}, "🔄 Update Queue Status": {"main": [[{"node": "✅ Is Approved?", "type": "main", "index": 0}]]}, "✅ Is Approved?": {"main": [[{"node": "📄 Get Content Data", "type": "main", "index": 0}], [{"node": "📋 Format Final Response", "type": "main", "index": 0}]]}, "📄 Get Content Data": {"main": [[{"node": "📤 Publish Approved Content", "type": "main", "index": 0}]]}, "📤 Publish Approved Content": {"main": [[{"node": "📋 Format Final Response", "type": "main", "index": 0}]]}, "📋 Format Final Response": {"main": [[{"node": "📤 Response", "type": "main", "index": 0}]]}, "🚨 Error Handler": {"main": [[{"node": "📝 Log Error", "type": "main", "index": 0}]]}, "📝 Log Error": {"main": [[{"node": "📤 Response", "type": "main", "index": 0}]]}}, "pinData": {}, "settings": {"executionOrder": "v1"}, "staticData": null, "tags": [{"createdAt": "2024-12-19T10:00:00.000Z", "updatedAt": "2024-12-19T10:00:00.000Z", "id": "review", "name": "review"}, {"createdAt": "2024-12-19T10:00:00.000Z", "updatedAt": "2024-12-19T10:00:00.000Z", "id": "human-ai", "name": "human-ai"}], "triggerCount": 1, "updatedAt": "2024-12-19T10:00:00.000Z", "versionId": "1.0.0"}