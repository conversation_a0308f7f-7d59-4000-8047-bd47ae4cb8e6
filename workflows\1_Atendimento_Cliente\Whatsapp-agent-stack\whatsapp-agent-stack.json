{"name": "Agente Inteligente v8.0 (The Tool-Bearer Edition)", "nodes": [{"parameters": {}, "id": "e8d66df2-466d-4bb4-b258-3d1f0ddaf631", "name": "TRIGGER: WhatsApp (Webhook)", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [-3700, -140], "webhookId": "whatsapp-inbox-v71", "path": "whatsapp-inbox-v71"}, {"parameters": {"content": "### 🚀 Agente Inteligente v8.0 (The Tool-Bearer Edition)\n\n**Propósito**: Esta versão expande o Agente Inteligente para se tornar uma plataforma de produtividade pessoal, adicionando ferramentas seguras para gerenciar arquivos, agenda e e-mails, acessíveis diretamente do WhatsApp por usuários autorizados.\n\n**Recursos da v8.0 (Integração com Ferramentas e Segurança):**\n- **✅ Camada de Autorização Robusta:** Um novo nó `DB: Check Authorization` verifica se o remetente é o próprio dono do número (chat com você mesmo) ou se está listado como um contato autorizado em uma nova coluna `permissions` no banco de dados. Um `IF: Is User Authorized?` age como um portão de segurança, bloqueando imediatamente qualquer tentativa de uso de ferramentas sensíveis por usuários não autorizados.\n- **✅ Integração com Armazenamento (Google Drive):** Intenção `find_document`: O agente agora entende pedidos como \"busque o relatório do 3º trimestre\". Fluxo: `AI: Extract Search Query` -> `TOOL: Search Google Drive` -> `TOOL: Send WhatsApp Document`. Ele lida com o caso de o documento não ser encontrado.\n- **✅ Capacidade de Gerenciar PDFs:**\n  - **Geração (`generate_pdf`):** Um novo nó `TOOL: Generate PDF (API Call)` demonstra como o agente pode pegar um texto (ex: um resumo de e-mail) e convertê-lo em um PDF para envio. Nota: Isso requer um microsserviço externo, como documentado no nó.\n  - **Leitura (Entrada):** O fluxo de entrada de mídia agora reconhece `application/pdf`, enviando-o para uma fila de processamento `pdf_analysis_queue`, similar ao tratamento de áudio.\n- **✅ Gestão Completa de Agenda (Google Calendar):**\n  - **Criar (`create_appointment`):** Entende \"marcar reunião com João amanhã às 15h\". Usa `AI: Extract Appointment Details` e `TOOL: Create Google Calendar Event`.\n  - **Ler (`read_calendar`):** Entende \"quais meus compromissos de hoje?\". Usa `TOOL: List Google Calendar Events` e formata a resposta.\n  - **Apagar (`delete_appointment`):** Entende \"cancele a reunião das 15h\". Usa `TOOL: Search Google Calendar Events` e depois `TOOL: Delete Google Calendar Event` para precisão.\n- **✅ Integração com E-mail (Gmail):**\n  - **Ler (`read_email`):** Entende \"resuma meus e-mails não lidos de hoje\". Usa `TOOL: Search Emails (Gmail)` e `AI: Summarize Emails`.\n  - **Enviar (`send_email`):** Entende \"envie um e-<NAME_EMAIL>...\". Usa `AI: Extract Email Details` e `TOOL: Send Email (Gmail)`.", "height": 980}, "id": "e5860dd3-30ad-467f-94a2-ef6c2e39908d", "name": "DOCUMENTAÇÃO DO WORKFLOW v8.0", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-3740, -1360]}, {"parameters": {"values": {"string": [{"name": "channel", "value": "whatsapp"}]}, "options": {}}, "id": "3167eb27-edca-4fd4-acdc-6b5860361a68", "name": "AGENT: Normalize WhatsApp Payload", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [-3460, -140]}, {"parameters": {"conditions": {"boolean": [{"value1": "={{ !$json.fromMe }}"}]}}, "id": "ab75d8d3-5569-45da-901d-531e2850fc25", "name": "IF: Is User Message?", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [-2820, 500]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT id, nome, grau_de_conexao, tags, consentimento_enriquecimento, permissions FROM agent.contatos WHERE telefone = '{{$json.senderNumber}}' LIMIT 1;", "options": {}}, "id": "0693a1ee-a912-4fcf-aa18-7b95085e7ca7", "name": "DB: Verificar Contato", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [-2580, 500], "credentials": {"postgres": {"id": "YOUR_POSTGRES_CREDENTIAL_ID", "name": "PostgreSQL DB"}}}, {"parameters": {"conditions": {"number": [{"value1": "={{ $items.length }}"}]}}, "id": "df2dae10-64cd-4e8c-859a-5f6068d34195", "name": "IF: Contato Existe?", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [-2380, 500]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "INSERT INTO agent.contatos (telefone, nome, tags, permissions) VALUES ('{{$json.senderNumber}}', '{{$json.pushName || `Contato ${$json.senderNumber}`}}', '[\"new_contact\"]', '[]'::jsonb) RETURNING id, 1 as grau_de_conexao, '[\"new_contact\"]' as tags, false as consentimento_enriquecimento, '[]'::jsonb as permissions;", "options": {}}, "id": "b0cf31ea-be12-40f4-a034-7fc44a4113ae", "name": "DB: <PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [-2380, 700], "credentials": {"postgres": {"id": "YOUR_POSTGRES_CREDENTIAL_ID", "name": "PostgreSQL DB"}}}, {"parameters": {}, "id": "893d5dfd-b4ef-4de8-9e5c-bd08d3e2de81", "name": "MERGE: Contact Info", "type": "n8n-nodes-base.merge", "typeVersion": 2, "position": [-2180, 500]}, {"parameters": {"values": {"string": [{"name": "<PERSON><PERSON>ey", "value": "={{ $json.isGroup ? 'ctx:group:' + $json.senderId : 'ctx:contact:' + $json.contactDbId }}"}]}, "options": {}}, "id": "766ef8b3-bd60-4e3f-a3d1-933e4cc0068a", "name": "AGENT: Generate Context Key", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [-1780, 500]}, {"parameters": {"operation": "get", "key": "={{ $json.contextKey }}"}, "id": "c1f7ca45-1c39-44ef-a6b3-6cf1b4cd3b54", "name": "CACHE: <PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.redis", "typeVersion": 1, "position": [-1580, 500], "continueOnFail": true, "credentials": {"redis": {"id": "YOUR_REDIS_CREDENTIAL_ID", "name": "Redis DB"}}}, {"parameters": {"agent": "simple", "model": "gpt-4o-mini", "text": "Summarize the key points of the following conversation in no more than 150 words. Focus on unresolved topics, user's main goal, and the overall sentiment. Conversation history: \n\n{{ $json.data }}", "options": {}}, "id": "4d1ac7cb-f24b-4ec9-adcd-e2652b31f71a", "name": "AI: <PERSON><PERSON>rize Context", "type": "@n8n/n8n-nodes-langchain.chain", "typeVersion": 1, "position": [-1380, 500], "continueOnFail": true, "credentials": {"openAiApi": {"id": "YOUR_OPENAI_CREDENTIAL_ID", "name": "OpenAI Account"}}}, {"parameters": {"values": {"string": [{"name": "summarizedContext", "value": "={{ $json.data || $json.value || 'Nenhum contexto.' }}"}]}, "options": {}}, "id": "1aa17f30-ad36-47b1-b9f7-cc17edb88e0e", "name": "AGENT: Set Summarized Context", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [-1180, 500]}, {"parameters": {"routing": {"rules": {"values": [{"operation": "regex", "value1": "={{$json.messageType}}", "value2": "conversation|extendedTextMessage"}, {"operation": "contains", "value1": "={{$json.messageType}}", "value2": "image"}, {"operation": "contains", "value1": "={{$json.messageType}}", "value2": "audio"}, {"operation": "contains", "value1": "={{$json.messageType}}", "value2": "video"}, {"operation": "equal", "value1": "={{$json.messageType}}", "value2": "document"}]}, "fieldToMatch": ""}, "options": {}}, "id": "3127dd4f-8cc7-400f-be9d-7cae128148e6", "name": "Rotear por Tipo de Mensagem", "type": "n8n-nodes-base.switch", "typeVersion": 2, "position": [-980, 500]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "INSERT INTO agent.mensagens (id_mensagem_wpp, id_contato, id_grupo, timestamp, tipo, direcao, conteudo)\nVALUES ('{{$json.messageId}}', {{$json.contactDbId}}, {{$json.isGroup ? `'${$json.senderId}'` : 'null'}}, NOW(), '{{$json.messageType}}', 'entrada', '{{$json.textContent.replace(/'/g, \"''\")}}');", "options": {}}, "id": "ab65a9ba-23de-42cd-9f79-c5c2a1dd0ce2", "name": "DB: Log Mensagem de Texto", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [-740, -160], "credentials": {"postgres": {"id": "YOUR_POSTGRES_CREDENTIAL_ID", "name": "PostgreSQL DB"}}}, {"parameters": {"agent": "conversationalAgent", "model": "gpt-4o", "tools": [], "text": "User Message: \"{{ $('AI: Redigir PII').item.json.data }}\"", "options": {"memoryKey": "chat_history", "inputKey": "input", "systemMessage": "={{ $('MERGE: Final Prompts').item.json.prompt }}", "promptVariables": {"values": [{"name": "context_summary", "value": "={{ $('AGENT: Set Summarized Context').item.json.summarizedContext }}"}, {"name": "user_emotion", "value": "={{ $('AGENT: Parse Emotion Analysis').item.json.user_emotion }}"}, {"name": "agent_tone", "value": "={{ $('AGENT: Parse Emotion Analysis').item.json.agent_tone }}"}, {"name": "pro_sentiment_score", "value": "={{ $('TOOL: Pro Sentiment Analysis (Google NLP)').item.json.documentSentiment?.score || 'N/A' }}"}, {"name": "personalized_suggestions", "value": "={{ $('TOOL: Call ML API').item.json.suggestions || ' ' }}"}, {"name": "group_guidelines", "value": "={{ $('MERGE: Final Prompts').item.json.group_guidelines || 'N/A' }}"}]}}}, "id": "76ed22d8-fd96-419b-acab-7b79ab444ec2", "name": "AI: Primary Agent (OpenAI)", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1, "position": [1780, -160], "continueOnFail": true, "credentials": {"openAiApi": {"id": "YOUR_OPENAI_CREDENTIAL_ID", "name": "OpenAI Account"}}}, {"parameters": {"method": "POST", "url": "={{ $credentials.EvolutionApi.baseUrl }}/message/sendText/{{$json.instanceName}}", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "apikey", "value": "={{ $credentials.EvolutionApi.apiKey }}"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "number", "value": "={{ $json.senderId }}"}, {"name": "options", "value": "={{ { \"delay\": 1200, \"presence\": \"composing\" } }}", "type": "json"}, {"name": "textMessage", "value": "={{ { \"text\": $json.formatted_ai_output } }}"}]}, "options": {}}, "id": "97463fd3-d9d1-4e78-95ff-cae11ca5d848", "name": "TOOL: Send WhatsApp Text Reply", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [2180, -280], "credentials": {"httpHeaderAuth": {"id": "YOUR_EVOLUTION_API_CREDENTIAL_ID", "name": "EvolutionApi"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "INSERT INTO agent.mensagens (id_mensagem_wpp, id_contato, id_grupo, timestamp, tipo, direcao, conteudo)\nVALUES ('{{$json.messageId}}_reply', {{$json.contactDbId}}, {{$json.isGroup ? `'${$json.senderId}'` : 'null'}}, NOW(), 'text_reply', 'saida', '{{$json.formatted_ai_output.replace(/'/g, \"''\")}}');", "options": {}}, "id": "7275d27d-9473-45a8-b590-f213df65d836", "name": "DB: Log Resposta da IA", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [2380, -160], "credentials": {"postgres": {"id": "YOUR_POSTGRES_CREDENTIAL_ID", "name": "PostgreSQL DB"}}}, {"parameters": {"operation": "set", "key": "={{ $json.contextKey }}", "value": "={{ ($('CACHE: Obter Contexto').item.json.data ? $('CACHE: Obter Contexto').item.json.data + '\\n' : '') + 'User: ' + $json.textContent + '\\nAgent: ' + $json.formatted_ai_output }}", "options": {"expiration": "={{ $json.ttl_seconds }}"}}, "id": "d748f572-132d-4dd6-9e01-d7481bbefc6c", "name": "CACHE: <PERSON><PERSON>", "type": "n8n-nodes-base.redis", "typeVersion": 1, "position": [2580, -160], "credentials": {"redis": {"id": "YOUR_REDIS_CREDENTIAL_ID", "name": "Redis DB"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "INSERT INTO agent.mensagens (id_mensagem_wpp, id_contato, id_grupo, timestamp, tipo, direcao, conteudo)\nVALUES ('{{$json.messageId}}', {{$json.contactDbId}}, {{$json.isGroup ? `'${$json.senderId}'` : 'null'}}, NOW(), '{{$json.messageType}}', 'entrada', '{{$json.mediaUrl}}')\nRETURNING id;", "options": {}}, "id": "ba72ed12-a8c6-4cc2-9d32-d8c9a33bb75f", "name": "DB: <PERSON>g Mensagem de Mídia", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [-740, 1520], "credentials": {"postgres": {"id": "YOUR_POSTGRES_CREDENTIAL_ID", "name": "PostgreSQL DB"}}}, {"parameters": {"queueName": "={{ $json.queue }}", "message": "={{ JSON.stringify({ message_db_id: $json.id, media_url: $json.mediaUrl, media_type: $json.messageType, sender_jid: $json.senderId, instance: $json.instanceName }) }}", "options": {"priority": "={{$json.priority || 0}}"}}, "id": "b8f0ca43-b9da-419b-b236-8da9035f8d08", "name": "TOOL: Enviar para Fila Específica", "type": "n8n-nodes-base.rabbitMqSender", "typeVersion": 1, "position": [-100, 1520], "credentials": {"rabbitMq": {"id": "YOUR_RABBITMQ_CREDENTIAL_ID", "name": "RabbitMQ Local"}}}, {"parameters": {"method": "POST", "url": "={{ $credentials.EvolutionApi.baseUrl }}/message/sendText/{{$json.instanceName}}", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "apikey", "value": "={{ $credentials.EvolutionApi.apiKey }}"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "number", "value": "={{ $json.senderId }}"}, {"name": "options", "value": "={{ { \"delay\": 1200, \"presence\": \"composing\" } }}", "type": "json"}, {"name": "textMessage", "value": "={{ { \"text\": `<PERSON><PERSON><PERSON> sua mídia (${$json.messageType.replace('Message','')}). <PERSON><PERSON> estou analisando e te respondo em um instante! 🧠` } }}", "type": "json"}]}, "options": {}}, "id": "bcf5e714-b4a1-432d-949c-f90c422896da", "name": "TOOL: <PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [100, 1520], "credentials": {"httpHeaderAuth": {"id": "YOUR_EVOLUTION_API_CREDENTIAL_ID", "name": "EvolutionApi"}}}, {"parameters": {"queueName": "processing_results_queue"}, "id": "c62b467b-1d7b-4dfa-b183-cc94f71fcb4e", "name": "TRIGGER: <PERSON><PERSON><PERSON>tados", "type": "n8n-nodes-base.rabbitMqTrigger", "typeVersion": 1, "position": [-3700, 2100], "credentials": {"rabbitMq": {"id": "YOUR_RABBITMQ_CREDENTIAL_ID", "name": "RabbitMQ Local"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "UPDATE agent.mensagens \nSET analise_midia = '{{ JSON.stringify($json.body.result).replace(/'/g, \"''\") }}'\nWHERE id = {{$json.body.message_db_id}}\nRETURNING (SELECT telefone FROM agent.contatos c WHERE c.id = id_contato) as \"senderNumber\",\n(SELECT id FROM agent.contatos c WHERE c.id = id_contato) as \"contactDbId\",\n(SELECT id_grupo FROM agent.mensagens m WHERE m.id = {{$json.body.message_db_id}}) as \"groupId\";", "options": {}}, "id": "d040a3d4-b789-4e01-b6a6-976cc13d1be5", "name": "DB: Atualizar com Análise", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [-3460, 2100], "credentials": {"postgres": {"id": "YOUR_POSTGRES_CREDENTIAL_ID", "name": "PostgreSQL DB"}}}, {"parameters": {}, "id": "2d9b6ccf-8b9a-4c28-98e8-dd438699efcd", "name": "<PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.errorTrigger", "typeVersion": 1, "position": [-2280, -120]}, {"parameters": {"values": {"string": [{"name": "errorMessage", "value": "={{ $execution.error.message }}"}, {"name": "errorNode", "value": "={{ $execution.error.node.name }}"}, {"name": "errorData", "value": "={{ JSON.stringify($input.item.json) }}"}]}, "options": {}}, "id": "ff3cdd6e-9b2f-488b-a437-0ed3ab2edbb5", "name": "ERROR: Format Message", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [-2080, -120]}, {"parameters": {"message": "=🚨 Erro no Workflow 'Agente Inteligente v8.0'\n\n**Nó:** `{{$json.errorNode}}`\n**Mensagem:** `{{$json.errorMessage}}`\n**Usuário:** `{{$json.senderId}}`", "options": {}}, "id": "2b6ff1aa-50b3-4fde-bc51-14fcbb9ab0d7", "name": "ALERT: Notify Admin (via Slack/Email)", "type": "n8n-nodes-base.noOp", "typeVersion": 1, "position": [-1880, -120], "notes": "NOTA: <PERSON><PERSON> é um nó placeholder. Substitua-o por um nó de 'Send Email', 'Slack', 'Discord', ou um 'HttpRequest' para o seu serviço de notificação preferido para tornar os alertas funcionais."}, {"parameters": {"functionCode": "const redis = $redis.get();\nconst key = `rate_limit:user:{{$json.senderId}}`;\n\n// Get user profile to set dynamic limit\nconst userTags = $json.tags || [];\nlet limit = 20; // Default limit\n\nif ($json.isGroup) {\n  limit = 40; // Higher limit for groups\n} else if (userTags.includes('vip')) {\n  limit = 100; // Very high limit for VIPs\n} else if ($json.grau_de_conexao >= 4) {\n  limit = 50; // Higher limit for connected users\n}\n\nconst count = await redis.incr(key);\nif (count === 1) {\n  await redis.expire(key, 60); // Expira em 60 segundos\n}\n\n$json.requestCount = count;\n$json.limit = limit;\n\nreturn items;", "options": {}}, "id": "d04a60f9-f458-45fe-8ac0-41ed499edba2", "name": "AGENT: Dynamic Rate Limiter Gate", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1980, 280], "credentials": {"redis": {"id": "YOUR_REDIS_CREDENTIAL_ID", "name": "Redis DB"}}}, {"parameters": {"conditions": {"number": [{"value1": "={{ $json.requestCount }}", "operation": "larger", "value2": "={{ $json.limit }}"}]}}, "id": "0dae1774-a740-4d43-bdce-6ebc45d315b9", "name": "IF: Rate Limit Exceeded?", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [-1980, -20]}, {"parameters": {"method": "POST", "url": "={{ $credentials.EvolutionApi.baseUrl }}/message/sendText/{{$json.instanceName}}", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "apikey", "value": "={{ $credentials.EvolutionApi.apiKey }}"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "number", "value": "={{ $json.senderId }}"}, {"name": "textMessage", "value": "={{ { \"text\": \"<PERSON><PERSON>, respira! 🧘‍♂️ Você enviou muitas mensagens rapidamente. Por favor, aguarde um minuto antes de continuar.\" } }}"}]}, "options": {}}, "id": "252fcd2e-1e96-4191-8ac3-3cd711e5a5f1", "name": "TOOL: Enviar Msg Rate Limit", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [-1980, -220], "credentials": {"httpHeaderAuth": {"id": "YOUR_EVOLUTION_API_CREDENTIAL_ID", "name": "EvolutionApi"}}}, {"parameters": {"routing": {"rules": {"values": [{"operation": "contains", "value1": "={{$json.messageType}}", "value2": "audio"}, {"operation": "contains", "value1": "={{$json.messageType}}", "value2": "image"}, {"operation": "contains", "value1": "={{$json.messageType}}", "value2": "video"}, {"operation": "contains", "value1": "={{$json.messageType}}", "value2": "pdf"}]}, "fieldToMatch": ""}, "options": {}}, "id": "f51c7694-a15d-49fe-bd2b-c8de81cd6631", "name": "Rotear por TIPO de Mídia", "type": "n8n-nodes-base.switch", "typeVersion": 2, "position": [-540, 1520]}, {"parameters": {"values": {"string": [{"name": "queue", "value": "audio_queue"}]}, "options": {}}, "id": "18f03ff9-b7b8-44bd-9c09-eb5942de856b", "name": "Set Audio Queue", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [-540, 1340]}, {"parameters": {"values": {"string": [{"name": "queue", "value": "image_queue"}]}, "options": {}}, "id": "4b6b6ec4-1e0e-436f-b27e-394ff03d922a", "name": "Set Image Queue", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [-540, 1680]}, {"parameters": {"values": {"string": [{"name": "queue", "value": "video_processing_queue"}]}, "options": {}}, "id": "ddb81d8d-ab51-4f40-848e-d748f95c46e3", "name": "Set Video Queue", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [-540, 1840]}, {"parameters": {"agent": "simple", "model": "gpt-4o-mini", "text": "Detect the primary language of the following text. Respond with only the two-letter ISO 639-1 code (e.g., pt, en, es). If you are unsure, respond with 'pt'.\n\nText: \"{{ $json.textContent }}\"", "options": {}}, "id": "ad6cae45-f094-4d10-86c0-43572d422a57", "name": "AI: Detectar Idioma", "type": "@n8n/n8n-nodes-langchain.chain", "typeVersion": 1, "position": [-140, -160], "credentials": {"openAiApi": {"id": "YOUR_OPENAI_CREDENTIAL_ID", "name": "OpenAI Account"}}}, {"parameters": {"routing": {"rules": {"values": [{"operation": "contains", "value1": "={{$json.data.toLowerCase()}}", "value2": "en"}, {"operation": "contains", "value1": "={{$json.data.toLowerCase()}}", "value2": "es"}]}, "fieldToMatch": "={{$json.data.toLowerCase()}}"}, "options": {}}, "id": "87c4f1c9-6dd5-455b-abf0-9303de776f8a", "name": "SWITCH: Route by Language", "type": "n8n-nodes-base.switch", "typeVersion": 2, "position": [60, -160]}, {"parameters": {"values": {"string": [{"name": "prompt", "value": "Seu nome é <PERSON>, um assistente de IA. Você está conversando em Português.\nO usuário está se sentindo '{{$json.user_emotion}}' (score: {{$json.pro_sentiment_score}}). Adapte seu tom para ser '{{$json.agent_tone}}'.\nUse o contexto sumarizado para manter a conversa: {{$json.summarizedContext}}.\n\nSe <PERSON><PERSON>, considere estas sugestões personalizadas para este usuário: {{$json.personalized_suggestions}}"}]}, "options": {}}, "id": "fc402b8d-de58-47f2-b7e9-a31dfb15ff71", "name": "Set Prompt: PT", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [260, -300]}, {"parameters": {"values": {"string": [{"name": "prompt", "value": "Your name is <PERSON>, an AI assistant. You are speaking in English.\nThe user is feeling '{{$json.user_emotion}}' (score: {{$json.pro_sentiment_score}}). Adapt your tone to be '{{$json.agent_tone}}'.\nUse the summarized context: {{$json.summarizedContext}}.\n\nIf relevant, consider these personalized suggestions for this user: {{$json.personalized_suggestions}}"}]}, "options": {}}, "id": "1894d070-d5bd-444f-b6bd-6b0daacb21ab", "name": "Set Prompt: EN", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [260, -120]}, {"parameters": {"values": {"string": [{"name": "prompt", "value": "Tu nombre es Jarvis, un asistente de IA. Estás hablando en Español.\nEl usuario se siente '{{$json.user_emotion}}' (score: {{$json.pro_sentiment_score}}). Adapta tu tono para que sea '{{$json.agent_tone}}'.\nUsa el contexto resumido: {{$json.summarizedContext}}.\n\nSi es relevante, considera estas sugerencias personalizadas para este usuario: {{$json.personalized_suggestions}}"}]}, "options": {}}, "id": "df6b6ab7-380d-40ba-83f0-4660d5b62b14", "name": "Set Prompt: ES", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [260, 60]}, {"parameters": {}, "id": "beffccac-fc1c-43bc-9311-6be3f09f448c", "name": "MERGE: Language Prompts", "type": "n8n-nodes-base.merge", "typeVersion": 2, "position": [460, -160]}, {"parameters": {"conditions": {"boolean": [{"value1": "={{ $items[0].execution.error !== undefined }}", "operation": "equal", "value2": true}]}}, "id": "c60a2830-4663-4de7-920f-0740a6b6f005", "name": "IF: Primary AI Failed?", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [1780, 140]}, {"parameters": {"values": {"number": [{"name": "processingTimeMs", "value": "={{ Date.now() - Date.parse($startTime) }}"}], "string": [{"name": "channel", "value": "={{ $json.channel }}"}]}, "options": {}}, "id": "f6858e80-82a1-432d-9473-b7b5949d0347", "name": "METRICS: Log Execution Time", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [3260, -160]}, {"parameters": {"agent": "simple", "model": "gpt-4o-mini", "text": "Analyze the user's message and classify its primary intent. Choose one: [greeting, question, complaint, request_info, chitchat, appreciation, request_call, get_weather, get_news, schedule_reminder, grant_consent, find_document, generate_pdf, create_appointment, read_calendar, delete_appointment, read_email, send_email]. If unsure, respond with 'general_inquiry'.\n\nMessage: \"{{ $json.textContent }}\"", "options": {}}, "id": "76652c41-8608-4b2a-a957-c8fc4ca18a4a", "name": "AI: Detectar Intenção", "type": "@n8n/n8n-nodes-langchain.chain", "typeVersion": 1, "position": [820, -160], "credentials": {"openAiApi": {"id": "YOUR_OPENAI_CREDENTIAL_ID", "name": "OpenAI Account"}}}, {"parameters": {"routing": {"rules": {"values": [{"operation": "contains", "value1": "={{$json.data}}", "value2": "complaint"}, {"operation": "contains", "value1": "={{$json.data}}", "value2": "request_call"}, {"operation": "contains", "value1": "={{$json.data}}", "value2": "get_weather"}, {"operation": "contains", "value1": "={{$json.data}}", "value2": "get_news"}, {"operation": "contains", "value1": "={{$json.data}}", "value2": "schedule_reminder"}, {"operation": "contains", "value1": "={{$json.data}}", "value2": "grant_consent"}, {"operation": "regex", "value1": "={{$json.data}}", "value2": "find_document|generate_pdf|create_appointment|read_calendar|delete_appointment|read_email|send_email"}]}, "fieldToMatch": ""}, "options": {}}, "id": "c044cf4d-9dd9-4d6d-8bdc-b7102e3ef293", "name": "SWITCH: Route by Intent", "type": "n8n-nodes-base.switch", "typeVersion": 2, "position": [1000, -160]}, {"parameters": {"method": "POST", "url": "={{ $credentials.chatwootApi.baseUrl }}/api/v1/accounts/1/conversations", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "api_access_token", "value": "={{ $credentials.chatwootApi.apiKey }}"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "inbox_id", "value": "YOUR_INBOX_ID"}, {"name": "contact_id", "value": "={{$json.chatwoot_contact_id_placeholder}}"}, {"name": "message", "value": "={{ { \"content\": \"User complaint received:\\n\\n`\" + $json.textContent + \"`\\n\\nConversation Context:\\n\" + $json.summarizedContext, \"message_type\": \"incoming\" } }}", "type": "json"}]}, "options": {}}, "id": "f626a5ca-cc3d-4c3a-bbde-11f26487e445", "name": "TOOL: <PERSON><PERSON> Chat<PERSON>ot Ticket", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [1180, -660], "credentials": {"chatwootApi": {"id": "YOUR_CHATWOOT_CREDENTIAL_ID", "name": "Chatwoot API"}}}, {"parameters": {"agent": "simple", "model": "gpt-4o", "text": "From the following text, identify and replace any Personal Identifiable Information (PII) like names, emails, phone numbers, and national ID numbers with placeholders like [NAME], [EMAIL], [PHONE], [ID_NUMBER]. Return only the processed text.\n\nOriginal Text:\n{{$json.textContent}}", "options": {}}, "id": "0d20914d-3d46-4e00-bcda-3f140de027ed", "name": "AI: Redigir PII", "type": "@n8n/n8n-nodes-langchain.chain", "typeVersion": 1, "position": [1380, -380], "credentials": {"openAiApi": {"id": "YOUR_OPENAI_CREDENTIAL_ID", "name": "OpenAI Account"}}}, {"parameters": {"method": "POST", "url": "=https://api.twilio.com/2010-04-01/Accounts/{{ $credentials.twilio.accountSid }}/Calls.json", "authentication": "predefinedCredential", "credentialType": "t<PERSON><PERSON><PERSON><PERSON>", "sendBody": true, "bodyParameters": {"parameters": [{"name": "To", "value": "={{ $json.senderNumber }}"}, {"name": "From", "value": "YOUR_TWILIO_PHONE_NUMBER"}, {"name": "Twiml", "value": "<Response><Say language=\"pt-BR\"><PERSON><PERSON><PERSON>, você solicitou uma chamada. Um de nossos atendentes irá falar com você em breve.</Say></Response>"}]}, "options": {}}, "id": "ab83a48e-ffef-4be7-ae86-5381a1792df0", "name": "TOOL: <PERSON><PERSON><PERSON> (Twilio)", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [1180, 300], "credentials": {"twilioApi": {"id": "YOUR_TWILIO_CREDENTIAL_ID", "name": "Twilio API"}}}, {"parameters": {"agent": "simple", "model": "gpt-4o-mini", "text": "Analyze the user's message for its emotion and tone. Respond with a JSON object with two keys: `emotion` and `agent_tone`. \nFor 'emotion', choose one from: [joyful, neutral, sad, angry, surprised, analytical]. \nFor 'agent_tone', choose the best corresponding response tone from: [energetic_and_friendly, calm_and_neutral, empathetic_and_supportive, calm_and_conciliatory, curious_and_engaging, formal_and_informative].\n\nExample response: {\"emotion\": \"angry\", \"agent_tone\": \"calm_and_conciliatory\"}\n\nUser Message: \"{{ $json.textContent }}\"", "options": {}}, "id": "07e3cf44-482a-43d7-8656-3decc1e9389f", "name": "AI: <PERSON><PERSON><PERSON> & Tom", "type": "@n8n/n8n-nodes-langchain.chain", "typeVersion": 1, "position": [-540, -160], "credentials": {"openAiApi": {"id": "YOUR_OPENAI_CREDENTIAL_ID", "name": "OpenAI Account"}}}, {"parameters": {"functionCode": "try {\n  const analysis = JSON.parse(items[0].json.data);\n  items[0].json.user_emotion = analysis.emotion;\n  items[0].json.agent_tone = analysis.agent_tone;\n} catch (e) {\n  items[0].json.user_emotion = 'neutral';\n  items[0].json.agent_tone = 'calm_and_neutral';\n}\n\nreturn items;", "options": {}}, "id": "c6204565-5c1a-4643-af0c-f38b25da11ce", "name": "AGENT: Parse Emotion Analysis", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-540, 40]}, {"parameters": {"routing": {"rules": {"values": [{"operation": "equal", "value1": "={{$json.channel}}", "value2": "whatsapp"}, {"operation": "equal", "value1": "={{$json.channel}}", "value2": "sms"}]}, "fieldToMatch": "={{$json.channel}}"}, "options": {}}, "id": "edffc3b0-ebc3-4d40-bdde-be76d49ca71a", "name": "ROUTE: Reply Format by Channel", "type": "n8n-nodes-base.switch", "typeVersion": 2, "position": [1980, -160]}, {"parameters": {"method": "POST", "url": "https://api.elevenlabs.io/v1/text-to-speech/YOUR_VOICE_ID_HERE", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "xi-api-key", "value": "={{ $credentials.elevenLabs.apiKey }}"}, {"name": "Accept", "value": "audio/mpeg"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "text", "value": "={{$json.ai_output}}"}, {"name": "model_id", "value": "eleven_multilingual_v2"}, {"name": "voice_settings", "value": "={{ { \"stability\": 0.5, \"similarity_boost\": 0.75 } }}", "type": "json"}]}, "options": {"response": {"response": {"returnFull": false, "returnBody": true, "returnHeaders": false, "responseFormat": "file"}}}}, "id": "d041c2c3-41ec-43c3-b3c9-f1ab755869a8", "name": "TOOL: Text-to-Speech (ElevenLabs)", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [1980, -400], "credentials": {"httpHeaderAuth": {"id": "YOUR_ELEVENLABS_CREDENTIAL_ID", "name": "ElevenLabs"}}}, {"parameters": {"method": "POST", "url": "={{ $credentials.EvolutionApi.baseUrl }}/message/sendAudio/{{$json.instanceName}}", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "apikey", "value": "={{ $credentials.EvolutionApi.apiKey }}"}]}, "sendBody": true, "contentType": "multipart-form-data", "bodyParameters": {"parameters": [{"name": "number", "value": "={{ $json.senderId }}"}, {"name": "audioMessage[mimetype]", "value": "audio/mpeg"}, {"name": "audioMessage[audio]", "value": "={{ $binary.data }}"}]}, "options": {}}, "id": "4b7b285b-ccf6-4767-af91-c124ec6fc931", "name": "TOOL: Send WhatsApp Audio Reply", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [2180, -400], "credentials": {"httpHeaderAuth": {"id": "YOUR_EVOLUTION_API_CREDENTIAL_ID", "name": "EvolutionApi"}}}, {"parameters": {"agent": "simple", "model": "gpt-4o-mini", "text": "Based on this conversation exchange, extract key insights about the user. Focus on their stated interests, problems, goals, or products mentioned. Return the insights as a JSON object with a single key `new_insights`, which is an array of strings. If no new insights are found, return an empty array. \n\nUser: {{$json.textContent}}\nAgent: {{$json.ai_output}}\n\nExample Response: {\"new_insights\": [\"interested_in_ia\", \"has_problem_with_billing\"]}", "options": {}}, "id": "d1e70418-4a69-4e78-be7c-c67d71626fce", "name": "AI: Extrair Insights do Contato", "type": "@n8n/n8n-nodes-langchain.chain", "typeVersion": 1, "position": [2780, -160], "credentials": {"openAiApi": {"id": "YOUR_OPENAI_CREDENTIAL_ID", "name": "OpenAI Account"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "UPDATE agent.contatos \nSET insights = COALESCE(insights, '[]'::jsonb) || '{{ $json.data.new_insights ? JSON.stringify($json.data.new_insights) : \"[]\" }}'::jsonb, \nlast_update = NOW()\nWHERE id = {{$json.contactDbId}};", "options": {}}, "id": "7bf3b934-297c-4fc9-b684-2a62372fde1c", "name": "DB: Atualizar Perfil do Contato", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [2980, -160], "credentials": {"postgres": {"id": "YOUR_POSTGRES_CREDENTIAL_ID", "name": "PostgreSQL DB"}}}, {"parameters": {"queueName": "data_enrichment_queue", "message": "={{ JSON.stringify({ contact_db_id: $json.contactDbId, sender_id: $json.senderId }) }}", "options": {}}, "id": "bd0538a7-5ef4-4f0b-acac-ed04cddeff65", "name": "TOOL: Delegate Full Profile Enrichment", "type": "n8n-nodes-base.rabbitMqSender", "typeVersion": 1, "position": [3220, 200], "credentials": {"rabbitMq": {"id": "YOUR_RABBITMQ_CREDENTIAL_ID", "name": "RabbitMQ Local"}}}, {"parameters": {"routing": {"rules": {"values": [{"operation": "stringContains", "value1": "={{$json.body.media_type}}", "value2": "audio"}]}, "fieldToMatch": ""}, "options": {}}, "id": "06b02660-f975-4700-bc56-3c5ef82672cc", "name": "SWITCH: Resultado é Transcrição?", "type": "n8n-nodes-base.switch", "typeVersion": 2, "position": [-3260, 2100]}, {"parameters": {"functionCode": "// Prepara os dados da transcrição para serem processados como uma mensagem de texto normal.\nconst original_payload = items[0].json.body;\n\n// Remapeia os campos para o formato esperado pelo `AGENT: Parse Common Payload`\nconst newBody = {};\nnewBody.instance = original_payload.instance;\nnewBody.data = {\n    messageType: 'conversation',\n    message: {\n        conversation: original_payload.result.transcription\n    },\n    key: {\n        remoteJid: original_payload.sender_jid,\n        id: `transcription_${original_payload.message_db_id}`,\n        fromMe: false\n    }\n};\n\nitems[0].json.body = newBody;\n\nreturn items;", "options": {}}, "id": "b7edcfcf-9d41-48cd-b145-c4a4506b3e89", "name": "AGENT: Preparar Loop de Transcrição", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-3260, 2300]}, {"parameters": {"rule": {"interval": [{"triggerAtHour": 2}]}}, "id": "ab81b5ff-813c-43f1-bd56-788b13c77d4c", "name": "TRIGGER: Daily Job (2 AM)", "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1.1, "position": [-3700, 2520]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "-- Exemplo: Gera estatísticas diárias quantitativas e por tipo de grupo\nSELECT \n  (SELECT COUNT(*) FROM agent.contatos) as total_contacts,\n  (SELECT COUNT(*) FROM agent.mensagens WHERE timestamp >= NOW() - INTERVAL '1 day') as messages_last_24h,\n  (SELECT AVG((analise_sentimento->>'score')::numeric) FROM agent.mensagens WHERE timestamp >= NOW() - INTERVAL '1 day' AND analise_sentimento->>'score' IS NOT NULL) as avg_sentiment_score,\n  (SELECT jsonb_object_agg(g.group_type, g.count) FROM (SELECT group_type, count(*) as count FROM agent.group_metadata GROUP BY group_type) g) as contacts_by_group_type,\n  (SELECT COUNT(*) FROM agent.contatos WHERE consentimento_enriquecimento = TRUE) as consented_users\n;", "options": {}}, "id": "bd16999a-f4ef-4b45-8ce0-d5a22df67431", "name": "DB: <PERSON><PERSON><PERSON>ti<PERSON>", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [-3460, 2520], "credentials": {"postgres": {"id": "YOUR_POSTGRES_CREDENTIAL_ID", "name": "PostgreSQL DB"}}}, {"parameters": {"message": "Este é um nó placeholder que representa a atualização de dashboards no Metabase/Grafana. Geralmente, esses dashboards são configurados para ler diretamente do banco de dados (PostgreSQL), então este nó apenas serve para marcar a conclusão da etapa de geração de dados no fluxo.", "options": {}}, "id": "80ffcdff-ba2e-4f11-9a70-8b010f3ccbd0", "name": "TOOL: Atualizar BI & Dashboards", "type": "n8n-nodes-base.noOp", "typeVersion": 1, "position": [-2780, 2660], "notes": "Os dashboards no BI devem ser configurados para ler as tabelas `agent.mensagens` e `agent.contatos` atualizadas."}, {"parameters": {"agent": "simple", "model": "gpt-4o", "text": "The following is a JSON array of recent conversation logs. Analyze these interactions from the past 7 days and identify 3-5 emerging trends, common user problems, or frequent questions. Output a brief report in markdown format, ready to be sent to a manager.\n\nConversation Logs:\n{{ $json.logs_summary }}", "options": {}}, "id": "ac6a3286-dafe-4835-bd84-406ef5a8b9ee", "name": "AI: Analyze Interaction Trends", "type": "@n8n/n8n-nodes-langchain.chain", "typeVersion": 1, "position": [-3260, 2660], "credentials": {"openAiApi": {"id": "YOUR_OPENAI_CREDENTIAL_ID", "name": "OpenAI Account"}}}, {"parameters": {"mode": "mergeByPosition", "options": {}}, "id": "3bbdfa7a-6246-46c5-8f6b-acb023e1f744", "name": "MERGE: All Channels", "type": "n8n-nodes-base.merge", "typeVersion": 2, "position": [-3260, 500]}, {"parameters": {"values": {"string": [{"name": "ai_output", "value": "={{ $('AI: Primary Agent (OpenAI)').item.json.data.output }}"}]}, "options": {}}, "id": "6a908a8e-2895-4eb8-b6de-b78801d9cd6c", "name": "AGENT: Set Primary AI Output", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [1780, -10]}, {"parameters": {"method": "GET", "url": "https://api.openweathermap.org/data/2.5/weather", "sendHeaders": true, "headerParameters": {"parameters": []}, "sendQuery": true, "queryParameters": {"parameters": [{"name": "q", "value": "={{ $json.entity }}"}, {"name": "appid", "value": "={{ $credentials.openWeatherMap.apiKey }}"}, {"name": "units", "value": "metric"}, {"name": "lang", "value": "pt_br"}]}, "options": {}}, "id": "ba010b91-9de0-410a-bd7c-47fc96324d83", "name": "TOOL: Call Weather API (Primary)", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [1380, 140], "continueOnFail": true, "credentials": {"openWeatherMapApi": {"id": "YOUR_OPENWEATHERMAP_CREDENTIAL_ID", "name": "OpenWeatherMap"}}}, {"parameters": {"method": "GET", "url": "https://newsapi.org/v2/top-headlines", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "X-Api-Key", "value": "={{ $credentials.newsApi.apiKey }}"}]}, "sendQuery": true, "queryParameters": {"parameters": [{"name": "q", "value": "={{ $json.entity }}"}, {"name": "pageSize", "value": 3}, {"name": "language", "value": "pt"}]}, "options": {}}, "id": "2da14392-8877-4402-ae29-063a568b9bb2", "name": "TOOL: Call News API", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [1380, 620], "credentials": {"newsApi": {"id": "YOUR_NEWSAPI_CREDENTIAL_ID", "name": "NewsAPI"}}}, {"parameters": {"agent": "simple", "model": "gpt-4o-mini", "text": "From the user's request, extract the reminder text and parse the date/time information into an ISO 8601 string. The current date is {{$now.toFormat('yyyy-MM-dd')}}. \n\nRequest: \"{{ $json.textContent }}\"\n\nReturn ONLY a JSON object with two keys: `reminder_text` and `date_time_iso`.", "options": {}}, "id": "d0e2e5ac-794d-4bb1-abfd-8dfc0a3cdde9", "name": "AI: Extract Date & Details", "type": "@n8n/n8n-nodes-langchain.chain", "typeVersion": 1, "position": [1180, 940], "credentials": {"openAiApi": {"id": "YOUR_OPENAI_CREDENTIAL_ID", "name": "OpenAI Account"}}}, {"parameters": {"method": "POST", "url": "={{ $credentials.EvolutionApi.baseUrl }}/message/sendText/{{$json.instanceName}}", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "apikey", "value": "={{ $credentials.EvolutionApi.apiKey }}"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "number", "value": "={{ $json.senderId }}"}, {"name": "textMessage", "value": "={{ { \"text\": $json.ack_message } }}"}]}, "options": {}}, "id": "eb3442fd-76cc-46a2-bc49-74d7df63013d", "name": "TOOL: Send Acknowledge Message", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [2180, 780], "credentials": {"httpHeaderAuth": {"id": "YOUR_EVOLUTION_API_CREDENTIAL_ID", "name": "EvolutionApi"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "INSERT INTO agent.reminders (contact_id, sender_id, channel, reminder_time, message, status) \nVALUES ({{$json.contactDbId}}, '{{$json.senderId}}', '{{$json.channel}}', '{{$json.parsedDetails.date_time_iso}}'::timestamptz, '{{$json.parsedDetails.reminder_text.replace(/'/g, \"''\")}}', 'scheduled');", "options": {}}, "id": "55f5dd00-fd31-482a-a9a7-9657edb56952", "name": "DB: <PERSON>", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [1380, 1240], "credentials": {"postgres": {"id": "YOUR_POSTGRES_CREDENTIAL_ID", "name": "PostgreSQL DB"}}}, {"parameters": {"agent": "simple", "model": "anthropic.claude-3-haiku-********-v1:0", "text": "{{$json.textContent}}", "options": {"systemMessage": "You are a backup AI assistant. The primary AI failed. Please provide a concise and helpful response to the user's message."}}, "id": "da14cd0a-2007-422f-8cf5-bbca0e57f00d", "name": "AI: <PERSON>up Agent (Anthropic)", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1, "position": [1580, 280], "credentials": {"anthropicApi": {"id": "YOUR_ANTHROPIC_CREDENTIAL_ID", "name": "Anthropic Account"}}}, {"parameters": {"values": {"string": [{"name": "ai_output", "value": "={{ $('AI: Backup Agent (Anthropic)').item.json.data.output }}"}]}, "options": {}}, "id": "90e44201-1e24-42b7-a3a8-a3f85822f7cd", "name": "AGENT: Set Backup AI Output", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [1780, 280]}, {"parameters": {}, "id": "ad64c9ab-443b-489e-8c3e-d82084b66df2", "name": "MERGE: AI Responses", "type": "n8n-nodes-base.merge", "typeVersion": 2, "position": [1780, 500]}, {"parameters": {"method": "POST", "url": "https://language.googleapis.com/v1/documents:analyzeSentiment?key={{$credentials.googleApi.apiKey}}", "sendHeaders": true, "headerParameters": {"parameters": []}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "document", "value": "={{ { \"type\": \"PLAIN_TEXT\", \"content\": $json.textContent, \"language\": $json.language } }}", "type": "json"}]}, "options": {}}, "id": "d040ed55-e51c-4b61-bbce-14cf3ec37ec1", "name": "TOOL: Pro Sentiment Analysis (Google NLP)", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [-540, 240], "continueOnFail": true, "credentials": {"googleApi": {"id": "YOUR_GOOGLE_API_CREDENTIAL_ID", "name": "Google API"}}}, {"parameters": {}, "id": "b3e36e78-0cf7-4f68-96ad-d0ca17d0c3dd", "name": "TRIGGER: Telegram (Webhook)", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [-3700, 220], "webhookId": "telegram-inbox-v7", "path": "telegram-inbox-v7"}, {"parameters": {"values": {"string": [{"name": "channel", "value": "telegram"}]}, "options": {}}, "id": "40b15b6d-a6fd-4be0-8041-5ac72f5d9434", "name": "AGENT: Normalize Telegram Payload", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [-3460, 220]}, {"parameters": {}, "id": "b88b0f7e-fdba-4d2c-91bb-bdccac656cd8", "name": "TRIGGER: SMS (Twilio Webhook)", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [-3700, 500], "webhookId": "sms-inbox-v7", "path": "sms-inbox-v7", "httpMethod": "POST"}, {"parameters": {"values": {"string": [{"name": "channel", "value": "sms"}]}, "options": {}}, "id": "ad6ffeb5-44d4-4a25-a8c9-04430e3abf16", "name": "AGENT: Normalize SMS Payload", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [-3460, 500]}, {"parameters": {"conditions": {"boolean": [{"value1": "={{true}}"}]}, "options": {}}, "id": "d8ed3168-154a-4df3-8ca4-ffc1dfdfedec", "name": "IGNORE - Was ROUTE: By Channel", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [-3020, 920]}, {"parameters": {"agent": "simple", "model": "gpt-4o-mini", "text": "The user sent an audio message. The transcribed text is below. Detect the user's primary intent from the text. Choose one: [greeting, question, complaint, request_info, chitchat, appreciation, request_call, schedule_reminder]. If unsure, respond with 'general_inquiry'.\n\nTranscribed Text: \"{{ $json.body.result.transcription }}\"", "options": {}}, "id": "c1fcf4e8-8bfe-4c54-9721-fffc2df70068", "name": "AI: <PERSON><PERSON><PERSON> In<PERSON>t from Audio", "type": "@n8n/n8n-nodes-langchain.chain", "typeVersion": 1, "position": [-3020, 2300], "credentials": {"openAiApi": {"id": "YOUR_OPENAI_CREDENTIAL_ID", "name": "OpenAI Account"}}}, {"parameters": {"rule": {"mode": "everyX", "unit": "minutes", "value": 1}}, "id": "067dd523-a1df-4b60-8f92-7de1c0c3be7c", "name": "TRIGGER: Check Reminders (Every Minute)", "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1.1, "position": [-3700, 2780]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT id, contact_id, channel, message, sender_id, instance_name\nFROM agent.reminders\nWHERE reminder_time <= NOW() AND status = 'scheduled';", "options": {}}, "id": "5f993358-1549-43c9-aa92-7f2824bb8d43", "name": "DB: <PERSON><PERSON> Due Reminders", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [-3460, 2780], "credentials": {"postgres": {"id": "YOUR_POSTGRES_CREDENTIAL_ID", "name": "PostgreSQL DB"}}}, {"parameters": {"batchSize": 1, "options": {}}, "id": "ab75d045-3da2-43bb-bbd5-a30932cd560b", "name": "Loop Over Reminders", "type": "n8n-nodes-base.splitInBatches", "typeVersion": 2, "position": [-3260, 2780]}, {"parameters": {"method": "POST", "url": "={{ $credentials.EvolutionApi.baseUrl }}/message/sendText/{{$json.instance_name}}", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "apikey", "value": "={{ $credentials.EvolutionApi.apiKey }}"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "number", "value": "={{$json.sender_id}}"}, {"name": "textMessage", "value": "={{ { \"text\": \"🔔 Lembrete: \" + $json.message } }}"}]}, "options": {}}, "id": "2da1ef4b-01ee-4899-ae1e-450f6bf3dd7e", "name": "TOOL: <PERSON> Reminder (WhatsApp)", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [-3020, 2780], "credentials": {"httpHeaderAuth": {"id": "YOUR_EVOLUTION_API_CREDENTIAL_ID", "name": "EvolutionApi"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "UPDATE agent.reminders SET status = 'sent' WHERE id = {{$json.id}};", "options": {}}, "id": "7665ac2f-7a43-47be-b1b7-fc7ac85514ee", "name": "DB: <PERSON> as <PERSON><PERSON>", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [-2820, 2780], "credentials": {"postgres": {"id": "YOUR_POSTGRES_CREDENTIAL_ID", "name": "PostgreSQL DB"}}}, {"parameters": {"agent": "simple", "model": "gpt-4o-mini", "text": "The user wants the weather. Extract the city name from the following text. Respond with only the city name. If no city is mentioned, assume 'São Paulo'.\n\nText: \"{{ $json.textContent }}\"", "options": {}}, "id": "bd0538ec-5c22-4a01-b845-a9a2a0ccb5bc", "name": "AI: Extract City", "type": "@n8n/n8n-nodes-langchain.chain", "typeVersion": 1, "position": [1180, -480], "credentials": {"openAiApi": {"id": "YOUR_OPENAI_CREDENTIAL_ID", "name": "OpenAI Account"}}}, {"parameters": {"values": {"string": [{"name": "entity", "value": "={{$json.data}}"}, {"name": "ack_message", "value": "=Claro! Buscando a previsão do tempo para {{$json.data}}..."}]}, "options": {}}, "id": "c620e7e1-88f2-45a8-9edc-e3745a709033", "name": "Set Entity (for <PERSON><PERSON>)", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [1180, -160]}, {"parameters": {"functionCode": "const data = items[0].json.body;\n\n// Handle potential API error where data might be a string\nif (typeof data === 'string') {\n  items[0].json.ai_output = data;\n  return items;\n}\n\nconst temp = data.main.temp;\nconst feels_like = data.main.feels_like;\nconst description = data.weather[0].description;\nconst city = data.name;\n\nitems[0].json.ai_output = `Aqui está o tempo para ${city}: ${temp}°C com sensação de ${feels_like}°C. O céu está ${description}. 🌤️`;\n\nreturn items;", "options": {}}, "id": "db81804d-4554-4cf1-bdce-6c3de764e5aa", "name": "AGENT: Format Weather Response", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1780, 640]}, {"parameters": {"functionCode": "const articles = items[0].json.body?.articles;\nlet response = \"Aqui estão as 3 principais notícias sobre esse assunto: \\n\\n\";\n\nif (!articles || articles.length === 0) {\n  items[0].json.ai_output = \"<PERSON><PERSON><PERSON><PERSON>, não encontrei nenhuma notícia recente sobre esse tópico.\";\n} else {\n  for (const article of articles) {\n    response += `*${article.title}*\\n`;\n    response += `${article.url}\\n\\n`;\n  }\n  items[0].json.ai_output = response;\n}\n\nreturn items;", "options": {}}, "id": "b328a992-0f5a-4e2b-be24-7b4477484df7", "name": "AGENT: Format News Response", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1580, 620]}, {"parameters": {"agent": "simple", "model": "gpt-4o-mini", "text": "The user wants news. Extract the main topic from the following text. Respond with only the topic. If no topic is clear, respond with 'Brazil'.\n\nText: \"{{ $json.textContent }}\"", "options": {}}, "id": "a9efedcb-49cc-433b-8267-beecab8364ed", "name": "AI: Extract News Topic", "type": "@n8n/n8n-nodes-langchain.chain", "typeVersion": 1, "position": [1180, 460], "credentials": {"openAiApi": {"id": "YOUR_OPENAI_CREDENTIAL_ID", "name": "OpenAI Account"}}}, {"parameters": {"functionCode": "const userTags = items[0].json.tags || [];\nlet ttl = 3600; // 1 hour default\n\nif (userTags.includes('vip')) {\n  ttl = 86400; // 24 hours for VIPs\n} else if (userTags.includes('active_support')) {\n  ttl = 14400; // 4 hours for active tickets\n} else if (userTags.includes('new_contact')) {\n  ttl = 28800; // 8 hours for new contacts to allow more interaction\n}\n\nitems[0].json.ttl_seconds = ttl;\n\nreturn items;", "options": {}}, "id": "be5ca223-9fbd-40d0-bb3e-6ce523fd03d0", "name": "AGENT: Set Dynamic TTL", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [2380, -10]}, {"parameters": {"method": "POST", "url": "https://your-ml-api.example.com/v1/predict/suggestions", "sendBody": true, "bodyParameters": {"parameters": [{"name": "contact_id", "value": "={{ $json.contactDbId }}"}, {"name": "messages_history", "value": "={{ $('CACHE: Obter Contexto').item.json.data || '' }}"}]}, "options": {}}, "id": "ad64c39c-eb85-48fd-bcac-725cf9c4da7b", "name": "TOOL: Call ML API for Suggestions", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [1380, -600], "continueOnFail": true, "notes": "Chama uma API de Machine Learning para obter sugestões personalizadas de produtos ou ações com base no histórico do usuário."}, {"parameters": {"functionCode": "const redis = $redis.get();\nconst key = `manual_override:user:{{$json.senderId}}`;\n\nconst override = await redis.get(key);\n\n// A flag será 'true' se o override estiver ativo.\nitems[0].json.manualOverrideActive = (override === 'true');\n\nreturn items;", "options": {}}, "id": "23d7da01-4475-47e0-9430-85fcc13bb7f5", "name": "DB: Check for Manual Override Flag", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-3020, 280], "credentials": {"redis": {"id": "YOUR_REDIS_CREDENTIAL_ID", "name": "Redis DB"}}}, {"parameters": {"conditions": {"boolean": [{"value1": "={{$json.manualOverrideActive}}", "operation": "equal", "value2": true}]}}, "id": "ba72ed12-a8c6-4cc2-9d32-d8c9a33bb75d", "name": "IF: Manual Override Active?", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [-3020, 60]}, {"parameters": {"message": "Fim do fluxo. Atendimento manual ativo.", "options": {}}, "id": "54498308-4122-4a0e-ae5c-9da5be08cdd4", "name": "Stop (Manual Override)", "type": "n8n-nodes-base.noOp", "typeVersion": 1, "position": [-3020, -140]}, {"parameters": {"conditions": {"number": [{"value1": "={{ $response.statusCode }}", "operation": "in", "value2": "401,403"}]}}, "id": "e9ef1d3b-39dd-483b-9687-be4c1a8563dd", "name": "IF: <PERSON><PERSON>?", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [1380, -840], "notes": "Este padrão deve ser replicado após cada nó HttpRequest crítico. Ele verifica se a API retornou um erro de autenticação."}, {"parameters": {"message": "=🚨 FALHA DE AUTENTICAÇÃO CRÍTICA\n\n**Nó:** `{{$node.name}}`\n**API:** {{$json.api_name_placeholder}}\n\nA chave de API pode ter expirado ou estar inválida. Por favor, verifique as credenciais no n8n.", "options": {}}, "id": "f5556d42-7608-4c2b-a857-c8fc4ca18a4f", "name": "ALERT: Critical Auth Failure (via Slack/Email)", "type": "n8n-nodes-base.noOp", "typeVersion": 1, "position": [1380, -1060], "notes": "NOTA: <PERSON><PERSON> é um nó placeholder. Substitua-o por um nó de 'Send Email', 'Slack', ou um 'HttpRequest' para notificar os administradores sobre a falha crítica de autenticação."}, {"parameters": {"method": "POST", "url": "=https://api.twilio.com/2010-04-01/Accounts/{{ $credentials.twilio.accountSid }}/Messages.json", "authentication": "predefinedCredential", "credentialType": "t<PERSON><PERSON><PERSON><PERSON>", "sendBody": true, "bodyParameters": {"parameters": [{"name": "To", "value": "={{$json.senderId}}"}, {"name": "From", "value": "YOUR_TWILIO_PHONE_NUMBER"}, {"name": "Body", "value": "={{$json.formatted_ai_output}}"}]}, "options": {}}, "id": "27f7de11-fd31-482a-a9a7-9657edb56952", "name": "TOOL: Send SMS Reply", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [2180, 40], "credentials": {"twilioApi": {"id": "YOUR_TWILIO_CREDENTIAL_ID", "name": "Twilio API"}}}, {"parameters": {"model": "gpt-4-vision-preview", "imagePropertyName": "mediaUrl", "text": "User Message: {{$json.textContent}}\n\nTask: Analyze the attached image. First, describe the image content (objects, scene, people). Then, if there is text, transcribe it. Finally, combine the visual description and the OCR text to answer the user's implicit question in their message, if any.", "options": {}}, "id": "e0e2e5ac-794d-4bb1-abfd-8dfc0a3cdde8", "name": "AI: Analyze Image with Vision", "type": "@n8n/n8n-nodes-langchain.vision", "typeVersion": 1, "position": [-740, 280], "credentials": {"openAiApi": {"id": "YOUR_OPENAI_CREDENTIAL_ID", "name": "OpenAI Account"}}}, {"parameters": {"functionCode": "items[0].json.ai_output = items[0].json.text;\nreturn items;", "options": {}}, "id": "c4f4ddeba-d20f-4318-9ca5-e51c863a35d7", "name": "AGENT: Format Vision Response", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-740, 500]}, {"parameters": {"values": {"string": [{"name": "parsedDetails", "value": "={{ JSON.parse($json.data) }}"}, {"name": "ack_message", "value": "=Tudo certo! Agendei seu lembrete: \"{{JSON.parse($json.data).reminder_text}}\" para {{$self.timestamp}}"}]}, "options": {}}, "id": "a88dd2cb-ec6e-41a6-b072-a0c45163a34a", "name": "AGENT: <PERSON><PERSON>", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [1380, 1080]}, {"parameters": {"functionCode": "// SECURITY NODE - Use a dedicated library in production!\n// This node provides basic sanitization against common vectors.\n// It's a first line of defense, not a complete solution.\n\nconst raw_text = items[0].json.textContent;\nif (!raw_text) {\n  items[0].json.sanitizedTextContent = '';\n  return items;\n}\n\n// 1. Agressively remove anything that looks like a script tag.\nlet sanitized = raw_text.replace(/<script\\b[^>]*>([\\s\\S]*?)<\\/script>/gmi, '');\n\n// 2. Agressively remove common SQL injection patterns. \n// FOR PRODUCTION: ALWAYS use parameterized queries or an ORM instead of string concatenation.\nsanitized = sanitized.replace(/(--|;|\\/\\*|' OR '1'='1|' or '1'='1)/gi, '');\n\n// 3. Remove other potentially harmful HTML tags, leaving only basic formatting.\n// A more robust solution would use a library like `sanitize-html`.\nsanitized = sanitized.replace(/<(?!b|i|strong|em|p|br|u)[^>]+>/gi, '');\n\n// 4. Escape special characters to prevent injection in other contexts.\nsanitized = sanitized.replace(/[&<>'\"/]/g, function (s) {\n    return {\n        '&': '&amp;',\n        '<': '&lt;',\n        '>': '&gt;',\n        '\"': '&quot;',\n        \"'\": '&#39;',\n        \"/\": '&#x2F;'\n    }[s];\n});\n\n\nitems[0].json.sanitizedTextContent = sanitized;\n\nif (raw_text !== sanitized) {\n  console.warn(`Potential malicious input sanitized from user ${items[0].json.senderId}. Original: ${raw_text}`)\n  // Here you could also send a security alert.\n  items[0].json.security_alert = true;\n}\n\nreturn items;", "options": {}}, "id": "c8117565-5c1a-4643-af0c-f38b25da11ce", "name": "AGENT: Sanitize Input", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1980, 80], "notes": "ALERTA DE SEGURANÇA: Esta sanitização é básica. Para ambientes de produção, use bibliotecas dedicadas como 'sanitize-html' e sempre use queries parametrizadas no banco de dados."}, {"parameters": {"functionCode": "const intent = items[0].json.data;\nlet priority = 0; // Normal priority\n\nif (intent === 'complaint') {\n  priority = 9; // High priority\n} else if (intent === 'request_info') {\n  priority = 5; // Medium priority\n}\n\nitems[0].json.priority = priority;\nreturn items;", "options": {}}, "id": "14eb3a1b-c75c-4861-ac09-acbeff331acb", "name": "AGENT: Set Task Priority", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-340, 1520]}, {"parameters": {"routing": {"rules": {"values": [{"operation": "equal", "value1": "={{$json.report_channel_placeholder || 'email'}}", "value2": "email"}, {"operation": "equal", "value1": "={{$json.report_channel_placeholder || 'email'}}", "value2": "slack"}]}, "fieldToMatch": ""}, "options": {}}, "id": "b0354020-854a-406d-92b7-66e29e1f3c8e", "name": "ROUTE: Send Report by Channel", "type": "n8n-nodes-base.switch", "typeVersion": 2, "position": [-3020, 2520]}, {"parameters": {"method": "POST", "url": "=https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK", "sendBody": true, "bodyParameters": {"parameters": [{"name": "text", "value": "=*Relatório Diário do Agente Inteligente v8.0*=\n\n*Resumo Quantitativo:*\n- Total de Contatos: `{{$('DB: Gerar Relatório Quantitativo').item.json.total_contacts}}`\n- Mensa<PERSON> (24h): `{{$('DB: Gerar Relatório Quantitativo').item.json.messages_last_24h}}`\n- Sentimento Médio (24h): `{{$('DB: Gerar Relatório Quantitativo').item.json.avg_sentiment_score.toFixed(2)}}`\n\n*Tendências da Semana:*\n{{$json.data}}\n\n*Insights de Correlação:*\n- {{($('DB: Gerar Relatório de Correlações').item.json.data[0].correlation_insight)}}"}]}, "options": {}}, "id": "1cf385da-09f6-4e15-9e25-8717e9c6d35a", "name": "TOOL: Send Report (Slack)", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [-3020, 2820]}, {"parameters": {"values": {"string": [{"name": "operation", "value": "set"}, {"name": "key", "value": "consent:data_enrichment:{{$json.senderId}}"}, {"name": "value", "value": "true"}]}, "options": {"expiration": 31536000}}, "id": "1a56d050-14a7-4e07-8439-aa0571a7341d", "name": "DB: Record Consent in Cache", "type": "n8n-nodes-base.redis", "typeVersion": 1, "position": [1180, 1400], "credentials": {"redis": {"id": "YOUR_REDIS_CREDENTIAL_ID", "name": "Redis DB"}}}, {"parameters": {"operation": "get", "key": "consent:data_enrichment:{{$json.senderId}}"}, "id": "f6c525ba-3a2f-42f8-a6b3-b7102e3ef29c", "name": "DB: <PERSON> (Enrichment)", "type": "n8n-nodes-base.redis", "typeVersion": 1, "position": [2980, 220], "credentials": {"redis": {"id": "YOUR_REDIS_CREDENTIAL_ID", "name": "Redis DB"}}}, {"parameters": {"conditions": {"boolean": [{"value1": "={{$json.data === 'true'}}"}]}}, "id": "882c7703-a87b-4efb-859b-a3814b471e5a", "name": "IF: Consent Given for Enrichment?", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [3220, 20]}, {"parameters": {"functionCode": "// Se a chamada de API falhar (erro de execução) ou retornar um código de status de rate limit (429), este nó tentará novamente.\nconst shouldRetry = $items[0].execution?.error || $items[0].json.response?.statusCode === 429;\n\nif (shouldRetry) {\n  const retries = $json.retries_api_call || 0;\n  if (retries < 3) {\n    $json.retries_api_call = retries + 1;\n    const waitTime = 2000 * Math.pow(2, retries); // Exponential backoff: 2s, 4s, 8s\n    console.log(`API ${$node.name} falhou. Tentativa #${$json.retries_api_call} em ${waitTime / 1000}s.`);\n    await $wait(waitTime); \n    return $retry(); // Tenta a execução do nó anterior novamente\n  } else {\n    console.error(`API ${$node.name} falhou após 3 tentativas.`);\n    // Deixa o erro passar para ser tratado pelo IF de falha de API\n  }\n}\n\nreturn $items;", "options": {}}, "id": "e88ee2cb-ec6e-41a6-b072-a0c45163a34a", "name": "CODE: Retry Logic for API Calls", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1380, 420], "notes": "Este nó genérico implementa uma lógica de retry com 'exponential backoff'. Pode ser conectado após qualquer chamada HTTP crítica para aumentar a resiliência a falhas temporárias ou de rate-limiting."}, {"parameters": {"agent": "simple", "model": "gpt-4o-mini", "text": "Format the following AI response based on the target channel: '{{$json.channel}}'. \nFor WhatsApp and Instagram, use emojis, short paragraphs and markdown like *bold* and _italic_.\nFor SMS, be very concise, without markdown and preferably under 160 characters. \nFor Telegram and Email, use full markdown for formatting (bold, italics, lists, etc).\n\nOriginal AI Response:\n\n{{$json.ai_output}}", "options": {}}, "id": "b1354020-854a-406d-92b7-66e29e1f3c8e", "name": "AI: Format Response by Channel", "type": "@n8n/n8n-nodes-langchain.chain", "typeVersion": 1, "position": [1980, 100], "credentials": {"openAiApi": {"id": "YOUR_OPENAI_CREDENTIAL_ID", "name": "OpenAI Account"}}}, {"parameters": {"values": {"string": [{"name": "formatted_ai_output", "value": "={{$json.data}}"}]}, "options": {}}, "id": "f31a6c0e-6422-40f0-bc49-098da34f6fc0", "name": "AGENT: Set Formatted AI Output", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [2180, -100]}, {"parameters": {"method": "POST", "url": "https://YOUR_PROMETHEUS_PUSHGATEWAY_URL/metrics/job/n8n_agent/instance/{{$json.instanceName}}", "sendBody": true, "contentType": "text", "body": "n8n_request_latency_ms{channel=\"{{$json.channel}}\",sender=\"{{$json.senderId}}\",group=\"{{$json.isGroup}}\"} {{$json.processingTimeMs}}\nn8n_message_count{channel=\"{{$json.channel}}\",is_group=\"{{$json.isGroup}}\"} 1\n{{ $items[0].execution.error ? 'n8n_workflow_failures_total{channel=\"' + $json.channel + '\"} 1\\n' : '' }}\n{{ $('IF: Primary AI Failed?').item.json.ai_backup_used ? 'n8n_ai_backup_uses_total{channel=\"' + $json.channel + '\"} 1\\n' : '' }}", "options": {}}, "id": "a57c7bd8-280d-40ba-83f0-4660d5b62b1c", "name": "TOOL: Publish Metrics to Prometheus", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [3260, 60]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "INSERT INTO agent.api_usage (api_name, timestamp, success, cost_usd_placeholder) VALUES ('{{$json.api_name_placeholder}}', NOW(), {{$json.success_placeholder}}, {{$json.cost_usd_placeholder || 0.001}});", "options": {}}, "id": "0894e070-d5bd-444f-b6bd-6b0daacb21ac", "name": "DB: Log API Usage", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [2380, 100], "credentials": {"postgres": {"id": "YOUR_POSTGRES_CREDENTIAL_ID", "name": "PostgreSQL DB"}}}, {"parameters": {}, "id": "ac502c8d-de58-47f2-b7e9-a31dfb15ff71", "name": "TRIGGER: Instagram Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [-3700, 740], "webhookId": "instagram-inbox-v7", "path": "instagram-inbox-v7"}, {"parameters": {"values": {"string": [{"name": "channel", "value": "instagram"}]}, "options": {}}, "id": "3316e5ff-f424-4213-830a-5b3d35f95559", "name": "AGENT: Normalize Instagram Payload", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [-3460, 740]}, {"parameters": {}, "id": "92555430-d5d4-4212-8427-5056333acbcf", "name": "TRIGGER: <PERSON><PERSON> (SendGrid)", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [-3700, 980], "webhookId": "email-inbox-v7", "path": "email-inbox-v7"}, {"parameters": {"values": {"string": [{"name": "channel", "value": "email"}]}, "options": {}}, "id": "b2e3b4f4-3c01-4512-963a-0d93703e0d25", "name": "AGENT: Normalize Email Payload", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [-3460, 980]}, {"parameters": {"method": "GET", "url": "https://api.weatherbit.io/v2.0/current", "sendQuery": true, "queryParameters": {"parameters": [{"name": "city", "value": "={{$json.entity}}"}, {"name": "key", "value": "={{$credentials.weatherbit.apiKey}}"}]}, "options": {}}, "id": "4f10d9cd-8116-4729-b185-0e7945e955ee", "name": "TOOL: Call Weather API (Fallback)", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [1580, 80], "credentials": {"weatherbitApi": {"id": "YOUR_WEATHERBIT_CREDENTIAL_ID", "name": "Weatherbit API"}}}, {"parameters": {"conditions": {"boolean": [{"value1": "={{ $items[0].execution?.error !== undefined || ($items[0].json.response?.statusCode && $items[0].json.response?.statusCode >=400) }}"}]}}, "id": "d1e70418-4a69-4e78-be7c-c67d71626fdd", "name": "IF: Primary Weather API Failed?", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [1380, -160]}, {"parameters": {"operation": "get", "key": "=weather:{{ $json.entity.toLowerCase().replace(/\\s/g, '_') }}"}, "id": "ab2df521-06ba-42ef-8d70-e644a705b714", "name": "CACHE: Check API Response", "type": "n8n-nodes-base.redis", "typeVersion": 1, "position": [1180, -340], "credentials": {"redis": {"id": "YOUR_REDIS_CREDENTIAL_ID", "name": "Redis DB"}}}, {"parameters": {"conditions": {"boolean": [{"value1": "={{ $json.data != null }}"}]}}, "id": "ed98a5b9-a3f7-4b5f-bc61-7b2d7a10a9fc", "name": "IF: API Response in Cache?", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [1180, -160]}, {"parameters": {"functionCode": "// Este nó foi adicionado para centralizar a extração de dados dos diferentes webhooks (WhatsApp, Telegram, SMS etc.)\n// e criar um payload comum para o restante do workflow.\n\nconst item = items[0];\nconst channel = item.json.channel;\nconst body = item.json.body;\n\nswitch(channel) {\n  case 'whatsapp':\n    item.json.senderId = body.data?.key?.remoteJid || '';\n    item.json.senderNumber = (body.data?.key?.remoteJid || '').split('@')[0];\n    item.json.textContent = body.data?.message?.conversation || body.data?.message?.extendedTextMessage?.text || body.data?.message?.imageMessage?.caption || body.data?.message?.videoMessage?.caption || '';\n    // Handle PDF documents from Evolution API\n    if (body.data?.message?.documentMessage?.mimetype === 'application/pdf') {\n        item.json.messageType = 'document';\n    } else {\n        item.json.messageType = Object.keys(body.data?.message || {})[0] || 'unknown';\n    }\n    item.json.isGroup = (body.data?.key?.remoteJid || '').includes('@g.us');\n    item.json.messageId = body.data?.key?.id;\n    item.json.instanceName = body.instance;\n    item.json.pushName = body.data?.pushName;\n    item.json.fromMe = body.data?.key?.fromMe || false;\n    item.json.mediaUrl = body.data?.message?.imageMessage?.url || body.data?.message?.audioMessage?.url || body.data?.message?.videoMessage?.url || body.data?.message?.documentMessage?.url || '';\n    item.json.groupName = body.data?.chat?.name || '';\n    break;\n  case 'telegram':\n    item.json.senderId = body.message?.from?.id?.toString() || '';\n    item.json.senderNumber = body.message?.from?.id?.toString() || '';\n    item.json.textContent = body.message?.text || '';\n    item.json.messageType = 'conversation';\n    item.json.isGroup = (body.message?.chat?.type === 'group' || body.message?.chat?.type === 'supergroup');\n    item.json.messageId = body.message?.message_id?.toString();\n    item.json.instanceName = 'telegram_bot';\n    item.json.pushName = body.message?.from?.first_name;\n    item.json.fromMe = false;\n    break;\n  case 'sms':\n    item.json.senderId = body.From || '';\n    item.json.senderNumber = body.From || '';\n    item.json.textContent = body.Body || '';\n    item.json.messageType = 'conversation';\n    item.json.isGroup = false;\n    item.json.messageId = body.MessageSid;\n    item.json.instanceName = 'twilio_sms';\n    item.json.pushName = body.From;\n    item.json.fromMe = false;\n    break;\n  // Adicionar outros canais como Instagram e Email aqui\n  default:\n    console.error(`Canal desconhecido: ${channel}`);\n    return [];\n}\n\n// Garante que a ID de contato do DB esteja no item\nitem.json.contactDbId = item.json.id;\n\nreturn item;", "options": {"keepOnlySet": false}}, "id": "e67e37de-c8cb-4e3a-9e9f-e3c35b67dabc", "name": "AGENT: Parse Common Payload", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-3020, 500]}, {"parameters": {"method": "POST", "url": "=https://api.hubapi.com/crm/v3/objects/contacts", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer {{$credentials.hubspot.apiKey}}"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "properties", "value": "={{ { \"firstname\": $json.nome, \"phone\": $json.senderNumber, \"n8n_insights\": $json.insights.join('; ') } }}", "type": "json"}]}, "options": {}}, "id": "d54d9cde-f1f2-4e3a-b4e5-f5c4e3e3b1c2", "name": "TOOL: Sync CRM Data (HubSpot)", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [3220, -160], "credentials": {"httpHeaderAuth": {"id": "YOUR_HUBSPOT_CREDENTIAL_ID", "name": "Hubspot API"}}, "notes": "Sincroniza/Cria um contato no HubSpot com as informações e insights mais recentes."}, {"parameters": {"operation": "execute<PERSON>uery", "query": "-- Consulta conceitual para encontrar correlações. A implementação real pode variar.\nSELECT \n  corr((analise_sentimento->>'score')::numeric, (SELECT COUNT(*) FROM jsonb_array_elements_text(tags) WHERE value = 'complaint')) as sentiment_vs_complaint_corr,\n  'O sentimento tende a ' || CASE WHEN corr((analise_sentimento->>'score')::numeric, (SELECT COUNT(*) FROM jsonb_array_elements_text(tags) WHERE value = 'complaint')) < 0 THEN 'diminuir' ELSE 'aumentar' END || ' quando o usuário faz uma reclamação.' as correlation_insight\nFROM agent.mensagens m\nJOIN agent.contatos c ON m.id_contato = c.id\nWHERE m.timestamp > NOW() - INTERVAL '7 day';", "options": {}}, "id": "e78f4b1d-c9db-4a1e-b8d9-e4a5d3f2e4b3", "name": "DB: <PERSON><PERSON><PERSON>latório de Correlações", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [-3460, 2660], "credentials": {"postgres": {"id": "YOUR_POSTGRES_CREDENTIAL_ID", "name": "PostgreSQL DB"}}}, {"parameters": {"conditions": {"boolean": [{"value1": "={{$json.isGroup}}"}]}}, "id": "f2c4e2a5-b1d3-4f5e-9a9b-e8b9a1d3c5e3", "name": "IF: Is Group Message?", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [-740, -480]}, {"parameters": {"agent": "simple", "model": "gpt-4o-mini", "text": "Analyze the group name and classify it into one of the following categories: [Work, Family, Friends, Study, Project, Hobby, Religious, Community, General]. Respond with only the category name.\n\nGroup Name: \"{{$json.groupName}}\"", "options": {}}, "id": "d4e2a5b1-c3d5-4a4e-a1d2-b3c4e2a5b1c3", "name": "AI: Classify Group Type", "type": "@n8n/n8n-nodes-langchain.chain", "typeVersion": 1, "position": [-540, -480], "credentials": {"openAiApi": {"id": "YOUR_OPENAI_CREDENTIAL_ID", "name": "OpenAI Account"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT guidelines, response_style_vector_placeholder FROM agent.group_settings WHERE group_type = '{{$json.data}}' OR group_id = '{{$json.senderId}}' ORDER BY group_id DESC LIMIT 1;", "options": {}}, "id": "e5b1c3d5-a4e2-4f3e-b8d9-e4a5d3f2e4b3", "name": "DB: Fetch Group Settings & Guidelines", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [-340, -480], "credentials": {"postgres": {"id": "YOUR_POSTGRES_CREDENTIAL_ID", "name": "PostgreSQL DB"}}, "notes": "Busca diretrizes específicas para o tipo de grupo (ex: 'Work') ou para o ID do grupo exato, dando preferência às configurações específicas do grupo."}, {"parameters": {"functionCode": "const base_prompt = $items[1].json.prompt;\nconst guidelines_data = $items[0].json.data?.[0];\n\nif (guidelines_data?.guidelines) {\n  $items[1].json.prompt = base_prompt + `\\n\\n**Diretrizes Específicas do Grupo:** ${guidelines_data.guidelines}`;\n  $items[1].json.group_guidelines = guidelines_data.guidelines;\n} else {\n   $items[1].json.group_guidelines = 'Nenhuma diretriz específica encontrada.';\n}\n\nreturn $items[1].json;", "options": {}}, "id": "f5e9a9b8-c7d6-4a5e-b2d3-e1d2b3c4d5e6", "name": "AI: Adapt Response to Group Style", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [60, -480]}, {"parameters": {}, "id": "g8d7f6e5-b4c3-4d2e-a1b2-c3d4e5f6g7h8", "name": "MERGE: Final Prompts", "type": "n8n-nodes-base.merge", "typeVersion": 2, "position": [620, -320]}, {"parameters": {"operation": "set", "key": "=weather:{{ $json.entity.toLowerCase().replace(/\\s/g, '_') }}", "value": "={{$json.body}}", "options": {"expiration": 3600}}, "id": "b3d4f5e6-g7h8-4a1e-b2c3-d4e5f6g7h8i9", "name": "CACHE: Save API Response", "type": "n8n-nodes-base.redis", "typeVersion": 1, "position": [1580, -160], "credentials": {"redis": {"id": "YOUR_REDIS_CREDENTIAL_ID", "name": "Redis DB"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "-- A coluna 'permissions' deve ser um jsonb array, ex: '[\"tool_user\"]'\n-- Este query retorna 'true' se o remetente for o 'owner_jid' (chat consigo mesmo) ou tiver a permissão 'tool_user'.\nSELECT\n  '{{$json.senderNumber}}' = (SELECT value FROM agent.settings WHERE key = 'owner_jid' LIMIT 1)\n  OR\n  'tool_user' = ANY(SELECT jsonb_array_elements_text(permissions) FROM agent.contatos WHERE telefone = '{{$json.senderNumber}}')\n  AS \"is_authorized\";", "options": {}}, "id": "402e1b1d-c9db-4a1e-b8d9-e4a5d3f2e4b3", "name": "DB: Check Authorization", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [1220, 1420], "credentials": {"postgres": {"id": "YOUR_POSTGRES_CREDENTIAL_ID", "name": "PostgreSQL DB"}}, "notes": "Verifica se o usuário pode usar ferramentas sensíveis. Ele é autorizado se estiver na lista de permissões ou se for o dono do número (configurado em 'agent.settings')."}, {"parameters": {"conditions": {"boolean": [{"value1": "={{$json.is_authorized}}"}]}}, "id": "e4a5d3f2-e4b3-4f5e-9a9b-e8b9a1d3c5e3", "name": "IF: Is User Authorized?", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [1420, 1420]}, {"parameters": {"values": {"string": [{"name": "ai_output", "value": "🚫 Acesso Negado. Você não tem permissão para usar esta função."}]}, "options": {}}, "id": "d4e2a5b1-c3d5-4a4e-a1d2-b3c4e2a5b1c4", "name": "AGENT: Set Unauthorized Message", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [1420, 1620]}, {"parameters": {"resource": "file", "operation": "search", "query": "={{$('AI: Extract Search Query').item.json.data}}", "options": {}}, "id": "a9efedcb-49cc-433b-8267-beecab8364ed", "name": "TOOL: Search Google Drive", "type": "n8n-nodes-base.googleDrive", "typeVersion": 3.2, "position": [1820, 1420], "credentials": {"googleApi": {"id": "YOUR_GOOGLE_DRIVE_CREDENTIAL_ID", "name": "Google Drive API"}}}, {"parameters": {"routing": {"rules": {"values": [{"operation": "contains", "value1": "={{$('76652c41-8608-4b2a-a957-c8fc4ca18a4a').item.json.data}}", "value2": "find_document"}, {"operation": "contains", "value1": "={{$('76652c41-8608-4b2a-a957-c8fc4ca18a4a').item.json.data}}", "value2": "generate_pdf"}, {"operation": "contains", "value1": "={{$('76652c41-8608-4b2a-a957-c8fc4ca18a4a').item.json.data}}", "value2": "read_calendar"}]}, "fieldToMatch": ""}, "options": {}}, "id": "e5b1c3d5-a4e2-4f3e-b8d9-e4a5d3f2e4b4", "name": "SWITCH: Route by Tool Intent", "type": "n8n-nodes-base.switch", "typeVersion": 2, "position": [1620, 1420]}, {"parameters": {"agent": "simple", "model": "gpt-4o-mini", "text": "The user wants to find a document. Extract the name or keywords for the document search from the following text. Respond with just the search query.\n\nText: \"{{ $json.textContent }}\"", "options": {}}, "id": "c620e7e1-88f2-45a8-9edc-e3745a709034", "name": "AI: Extract Search Query", "type": "@n8n/n8n-nodes-langchain.chain", "typeVersion": 1, "position": [1620, 1600], "credentials": {"openAiApi": {"id": "YOUR_OPENAI_CREDENTIAL_ID", "name": "OpenAI Account"}}}, {"parameters": {"conditions": {"number": [{"value1": "={{$items.length}}", "operation": "larger", "value2": 0}]}}, "id": "f5e9a9b8-c7d6-4a5e-b2d3-e1d2b3c4d5e7", "name": "IF: Document Found?", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [2020, 1420]}, {"parameters": {"method": "POST", "url": "={{ $credentials.EvolutionApi.baseUrl }}/message/sendDocument/{{$json.instanceName}}", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "apikey", "value": "={{ $credentials.EvolutionApi.apiKey }}"}]}, "sendBody": true, "contentType": "multipart-form-data", "bodyParameters": {"parameters": [{"name": "number", "value": "={{ $json.senderId }}"}, {"name": "documentMessage[mimetype]", "value": "={{$binary.data.mimeType}}"}, {"name": "documentMessage[fileName]", "value": "={{$binary.data.fileName}}"}, {"name": "documentMessage[document]", "value": "={{$binary.data}}"}]}, "options": {}}, "id": "a9d8c7b6-a5a4-4987-a4b3-c2d1e0f987a6", "name": "TOOL: Send WhatsApp Document", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [2220, 1320], "credentials": {"httpHeaderAuth": {"id": "YOUR_EVOLUTION_API_CREDENTIAL_ID", "name": "EvolutionApi"}}}, {"parameters": {"values": {"string": [{"name": "ai_output", "value": "😕 <PERSON><PERSON><PERSON><PERSON>, não consegui encontrar um documento com esse nome no Drive."}]}, "options": {}}, "id": "g8d7f6e5-b4c3-4d2e-a1b2-c3d4e5f6g7h9", "name": "AGENT: Set Not Found Message", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [2020, 1620]}, {"parameters": {"message": "**Requisição Externa Necessária**\nEste nó é um placeholder para uma chamada de API a um microsserviço que gera PDFs. \n\n**Exemplo de API:**\nURL: `https://your-pdf-service.com/generate`\nMethod: `POST`\nBody: `{ \"content\": \"{{$json.extracted_text}}\" }`\n\nO serviço deve retornar o PDF como um arquivo binário, que pode ser conectado ao nó `TOOL: Send WhatsApp Document`.", "options": {}}, "id": "b3d4f5e6-g7h8-4a1e-b2c3-d4e5f6g7h8j0", "name": "TOOL: Generate PDF (API Call)", "type": "n8n-nodes-base.noOp", "typeVersion": 1, "position": [1820, 1740]}, {"parameters": {"values": {"string": [{"name": "queue", "value": "pdf_analysis_queue"}]}, "options": {}}, "id": "h9i8g7f6-e5d4-4b3a-a2b1-c0d9e8f7g6h5", "name": "Set PDF Queue", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [-740, 1800]}, {"parameters": {"authentication": "oAuth2", "calendar": "primary", "operation": "getEvents", "simplify": true, "options": {}}, "id": "k1j2h3g4-f5e6-4d7c-b8a9-f0e9d8c7b6a5", "name": "TOOL: List Google Calendar Events", "type": "n8n-nodes-base.googleCalendar", "typeVersion": 1, "position": [1820, 2000], "credentials": {"googleCalendarOAuth2Api": {"id": "YOUR_GOOGLE_CALENDAR_CREDENTIAL_ID", "name": "Google Calendar API"}}}, {"parameters": {"authentication": "oAuth2", "resource": "email", "operation": "send", "to": "={{$('AI: Extract Email Details').item.json.data.recipient}}", "subject": "={{$('AI: Extract Email Details').item.json.data.subject}}", "text": "={{$('AI: Extract Email Details').item.json.data.body}}", "options": {}}, "id": "m3n4b5v6-c7x8-4z9a-b1c2-d3e4f5g6h7i8", "name": "TOOL: Send Email (Gmail)", "type": "n8n-nodes-base.googleMail", "typeVersion": 4.1, "position": [1620, 2560], "credentials": {"googleApi": {"id": "YOUR_GMAIL_CREDENTIAL_ID", "name": "Gmail API"}}}, {"parameters": {"agent": "simple", "model": "gpt-4o-mini", "text": "The user wants to send an email. From the following text, extract the recipient's email address, the subject line, and the body of the email. Return a single JSON object with the keys `recipient`, `subject`, and `body`.\n\nText: \"{{ $json.textContent }}\"", "options": {}}, "id": "p9o8i7u6-y5t4-4r3e-w2q1-a0s9d8f7g6h5", "name": "AI: Extract Email Details", "type": "@n8n/n8n-nodes-langchain.chain", "typeVersion": 1, "position": [1420, 2560], "credentials": {"openAiApi": {"id": "YOUR_OPENAI_CREDENTIAL_ID", "name": "OpenAI Account"}}}, {"parameters": {"authentication": "oAuth2", "resource": "email", "operation": "search", "match": {"field": "label", "value": "UNREAD"}, "limit": 5, "options": {}}, "id": "l2k3j4h5-g6f7-4e8d-9c0b-a1b2c3d4e5f6", "name": "TOOL: Search Emails (Gmail)", "type": "n8n-nodes-base.googleMail", "typeVersion": 4.1, "position": [1420, 2280], "credentials": {"googleApi": {"id": "YOUR_GMAIL_CREDENTIAL_ID", "name": "Gmail API"}}}, {"parameters": {"agent": "simple", "model": "gpt-4o", "text": "The user wants a summary of their unread emails. Here is the data for the last few emails. Summarize them concisely in a single block of text, formatted for WhatsApp with markdown (e.g., *bold* for sender, _italic_ for subject).\n\nEmails Data (JSON):\n{{ JSON.stringify($input.item.json) }}", "options": {}}, "id": "q1w2e3r4-t5y6-4u7i-o8p9-a0s9d8f7g6h5", "name": "AI: <PERSON><PERSON><PERSON><PERSON>", "type": "@n8n/n8n-nodes-langchain.chain", "typeVersion": 1, "position": [1620, 2280], "credentials": {"openAiApi": {"id": "YOUR_OPENAI_CREDENTIAL_ID", "name": "OpenAI Account"}}}], "connections": {"TRIGGER: WhatsApp (Webhook)": {"main": [[{"node": "AGENT: Normalize WhatsApp Payload", "type": "main", "index": 0}]]}, "AGENT: Normalize WhatsApp Payload": {"main": [[{"node": "MERGE: All Channels", "type": "main", "index": 0}]]}, "IF: Is User Message?": {"main": [[{"node": "DB: Verificar Contato", "type": "main", "index": 0}]]}, "DB: Verificar Contato": {"main": [[{"node": "IF: Contato Existe?", "type": "main", "index": 0}]]}, "IF: Contato Existe?": {"main": [[{"node": "MERGE: Contact Info", "type": "main", "index": 0}], [{"node": "DB: <PERSON><PERSON><PERSON>", "type": "main", "index": 0}]]}, "DB: Criar Contato": {"main": [[{"node": "MERGE: Contact Info", "type": "main", "index": 1}]]}, "MERGE: Contact Info": {"main": [[{"node": "AGENT: Sanitize Input", "type": "main", "index": 0}]]}, "AGENT: Generate Context Key": {"main": [[{"node": "CACHE: <PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "main", "index": 0}]]}, "CACHE: Obter Contexto": {"main": [[{"node": "AI: <PERSON><PERSON>rize Context", "type": "main", "index": 0}]]}, "Rotear por Tipo de Mensagem": {"main": [[{"node": "DB: Log Mensagem de Texto", "type": "main", "index": 0}], [{"node": "e0e2e5ac-794d-4bb1-abfd-8dfc0a3cdde8", "type": "main", "index": 0}], [{"node": "DB: <PERSON>g Mensagem de Mídia", "type": "main", "index": 0}], [{"node": "DB: <PERSON>g Mensagem de Mídia", "type": "main", "index": 0}], [{"node": "DB: <PERSON>g Mensagem de Mídia", "type": "main", "index": 0}]]}, "DB: Log Mensagem de Texto": {"main": [[{"node": "IF: Is Group Message?", "type": "main", "index": 0}]]}, "AI: Primary Agent (OpenAI)": {"main": [[{"node": "IF: Primary AI Failed?", "type": "main", "index": 0}]]}, "TOOL: Send WhatsApp Text Reply": {"main": [[{"node": "DB: Log Resposta da IA", "type": "main", "index": 0}]]}, "DB: Log Resposta da IA": {"main": [[{"node": "AGENT: Set Dynamic TTL", "type": "main", "index": 0}]]}, "CACHE: Salvar Contexto": {"main": [[{"node": "AI: Extrair Insights do Contato", "type": "main", "index": 0}]]}, "DB: Log Mensagem de Mídia": {"main": [[{"node": "Rotear por TIPO de Mídia", "type": "main", "index": 0}]]}, "TOOL: Enviar para Fila Específica": {"main": [[{"node": "TOOL: <PERSON><PERSON><PERSON>", "type": "main", "index": 0}]]}, "TRIGGER: Ouvir Fila de Resultados": {"main": [[{"node": "DB: Atualizar com Análise", "type": "main", "index": 0}]]}, "DB: Atualizar com Análise": {"main": [[{"node": "SWITCH: Resultado é Transcrição?", "type": "main", "index": 0}]]}, "Error Trigger": {"main": [[{"node": "ERROR: Format Message", "type": "main", "index": 0}]]}, "ERROR: Format Message": {"main": [[{"node": "ALERT: Notify Admin (via Slack/Email)", "type": "main", "index": 0}]]}, "AGENT: Dynamic Rate Limiter Gate": {"main": [[{"node": "IF: Rate Limit Exceeded?", "type": "main", "index": 0}]]}, "IF: Rate Limit Exceeded?": {"main": [[{"node": "TOOL: Enviar Msg Rate Limit", "type": "main", "index": 0}], [{"node": "AGENT: Generate Context Key", "type": "main", "index": 0}]]}, "Rotear por TIPO de Mídia": {"main": [[{"node": "Set Audio Queue", "type": "main", "index": 0}], [{"node": "Set Image Queue", "type": "main", "index": 0}], [{"node": "Set Video Queue", "type": "main", "index": 0}], [{"node": "h9i8g7f6-e5d4-4b3a-a2b1-c0d9e8f7g6h5", "type": "main", "index": 0}]]}, "Set Audio Queue": {"main": [[{"node": "AGENT: Set Task Priority", "type": "main", "index": 0}]]}, "Set Image Queue": {"main": [[{"node": "AGENT: Set Task Priority", "type": "main", "index": 0}]]}, "Set Video Queue": {"main": [[{"node": "AGENT: Set Task Priority", "type": "main", "index": 0}]]}, "AI: Summarize Context": {"main": [[{"node": "AGENT: Set Summarized Context", "type": "main", "index": 0}]]}, "AGENT: Set Summarized Context": {"main": [[{"node": "Rotear por Tipo de Mensagem", "type": "main", "index": 0}]]}, "AI: Detectar Idioma": {"main": [[{"node": "SWITCH: Route by Language", "type": "main", "index": 0}]]}, "SWITCH: Route by Language": {"main": [[{"node": "Set Prompt: PT", "type": "main", "index": 0}], [{"node": "Set Prompt: EN", "type": "main", "index": 0}], [{"node": "Set Prompt: ES", "type": "main", "index": 0}]]}, "Set Prompt: PT": {"main": [[{"node": "MERGE: Language Prompts", "type": "main", "index": 0}]]}, "Set Prompt: EN": {"main": [[{"node": "MERGE: Language Prompts", "type": "main", "index": 1}]]}, "Set Prompt: ES": {"main": [[{"node": "MERGE: Language Prompts", "type": "main", "index": 2}]]}, "MERGE: Language Prompts": {"main": [[{"node": "g8d7f6e5-b4c3-4d2e-a1b2-c3d4e5f6g7h8", "type": "main", "index": 1}]]}, "IF: Primary AI Failed?": {"main": [[{"node": "AGENT: Set Primary AI Output", "type": "main", "index": 0}], [{"node": "AI: <PERSON>up Agent (Anthropic)", "type": "main", "index": 0}]]}, "AI: Detectar Intenção": {"main": [[{"node": "SWITCH: Route by Intent", "type": "main", "index": 0}]]}, "SWITCH: Route by Intent": {"main": [[{"node": "TOOL: <PERSON><PERSON> Chat<PERSON>ot Ticket", "type": "main", "index": 0}], [{"node": "TOOL: <PERSON><PERSON><PERSON> (Twilio)", "type": "main", "index": 0}], [{"node": "bd0538ec-5c22-4a01-b845-a9a2a0ccb5bc", "type": "main", "index": 0}], [{"node": "a9efedcb-49cc-433b-8267-beecab8364ed", "type": "main", "index": 0}], [{"node": "AI: Extract Date & Details", "type": "main", "index": 0}], [{"node": "DB: Record Consent in Cache", "type": "main", "index": 0}], [{"node": "402e1b1d-c9db-4a1e-b8d9-e4a5d3f2e4b3", "type": "main", "index": 0}], [{"node": "ad64c39c-eb85-48fd-bcac-725cf9c4da7b", "type": "main", "index": 0}]]}, "TOOL: Create Chatwoot Ticket": {"main": [[{"node": "e9ef1d3b-39dd-483b-9687-be4c1a8563dd", "type": "main", "index": 0}]]}, "AI: Redigir PII": {"main": [[{"node": "AI: Primary Agent (OpenAI)", "type": "main", "index": 0}]]}, "AI: Analisar Emoção & Tom": {"main": [[{"node": "TOOL: Pro Sentiment Analysis (Google NLP)", "type": "main", "index": 0}]]}, "AGENT: Parse Emotion Analysis": {"main": [[{"node": "AI: Detectar Idioma", "type": "main", "index": 0}]]}, "ROUTE: Reply Format by Channel": {"main": [[{"node": "TOOL: Send WhatsApp Text Reply", "type": "main", "index": 0}], [{"node": "27f7de11-fd31-482a-a9a7-9657edb56952", "type": "main", "index": 0}]]}, "TOOL: Text-to-Speech (ElevenLabs)": {"main": [[{"node": "TOOL: Send WhatsApp Audio Reply", "type": "main", "index": 0}]]}, "TOOL: Send WhatsApp Audio Reply": {"main": [[{"node": "DB: Log Resposta da IA", "type": "main", "index": 0}]]}, "AI: Extrair Insights do Contato": {"main": [[{"node": "DB: Atualizar Perfil do Contato", "type": "main", "index": 0}]]}, "DB: Atualizar Perfil do Contato": {"main": [[{"node": "DB: <PERSON> (Enrichment)", "type": "main", "index": 0}, {"node": "d54d9cde-f1f2-4e3a-b4e5-f5c4e3e3b1c2", "type": "main", "index": 0}]]}, "SWITCH: Resultado é Transcrição?": {"main": [[{"node": "AGENT: Preparar Loop de Transcrição", "type": "main", "index": 0}]]}, "AGENT: Preparar Loop de Transcrição": {"main": [[{"node": "MERGE: All Channels", "type": "main", "index": 1}]]}, "TRIGGER: Daily Job (2 AM)": {"main": [[{"node": "DB: <PERSON><PERSON><PERSON>ti<PERSON>", "type": "main", "index": 0}]]}, "DB: Gerar Relatório Quantitativo": {"main": [[{"node": "e78f4b1d-c9db-4a1e-b8d9-e4a5d3f2e4b3", "type": "main", "index": 0}]]}, "AI: Analyze Interaction Trends": {"main": [[{"node": "ROUTE: Send Report by Channel", "type": "main", "index": 0}]]}, "MERGE: All Channels": {"main": [[{"node": "e67e37de-c8cb-4e3a-9e9f-e3c35b67dabc", "type": "main", "index": 0}]]}, "AGENT: Set Primary AI Output": {"main": [[{"node": "MERGE: AI Responses", "type": "main", "index": 0}]]}, "TOOL: Call Weather API (Primary)": {"main": [[{"node": "b3d4f5e6-g7h8-4a1e-b2c3-d4e5f6g7h8i9", "type": "main", "index": 0}, {"node": "e88ee2cb-ec6e-41a6-b072-a0c45163a34a", "type": "main", "index": 0}]]}, "TOOL: Call News API": {"main": [[{"node": "AGENT: Format News Response", "type": "main", "index": 0}]]}, "AI: Extract Date & Details": {"main": [[{"node": "a88dd2cb-ec6e-41a6-b072-a0c45163a34a", "type": "main", "index": 0}]]}, "DB: Save Reminder": {"main": [[{"node": "eb3442fd-76cc-46a2-bc49-74d7df63013d", "type": "main", "index": 0}]]}, "AI: Backup Agent (Anthropic)": {"main": [[{"node": "AGENT: Set Backup AI Output", "type": "main", "index": 0}]]}, "AGENT: Set Backup AI Output": {"main": [[{"node": "MERGE: AI Responses", "type": "main", "index": 1}]]}, "MERGE: AI Responses": {"main": [[{"node": "ROUTE: Reply Format by Channel", "type": "main", "index": 0}, {"node": "db81804d-4554-4cf1-bdce-6c3de764e5aa", "type": "main", "index": 0}, {"node": "b1354020-854a-406d-92b7-66e29e1f3c8e", "type": "main", "index": 0}]]}, "TOOL: Pro Sentiment Analysis (Google NLP)": {"main": [[{"node": "AGENT: Parse Emotion Analysis", "type": "main", "index": 0}]]}, "TRIGGER: Telegram (Webhook)": {"main": [[{"node": "AGENT: Normalize Telegram Payload", "type": "main", "index": 0}]]}, "AGENT: Normalize Telegram Payload": {"main": [[{"node": "MERGE: All Channels", "type": "main", "index": 2}]]}, "TRIGGER: SMS (Twilio Webhook)": {"main": [[{"node": "AGENT: Normalize SMS Payload", "type": "main", "index": 0}]]}, "AGENT: Normalize SMS Payload": {"main": [[{"node": "MERGE: All Channels", "type": "main", "index": 3}]]}, "AI: Detect Intent from Audio": {"main": [[{"node": "SWITCH: Route by Intent", "type": "main", "index": 0}]]}, "TRIGGER: Check Reminders (Every Minute)": {"main": [[{"node": "DB: <PERSON><PERSON> Due Reminders", "type": "main", "index": 0}]]}, "DB: Fetch Due Reminders": {"main": [[{"node": "Loop Over Reminders", "type": "main", "index": 0}]]}, "Loop Over Reminders": {"main": [[{"node": "TOOL: <PERSON> Reminder (WhatsApp)", "type": "main", "index": 0}]]}, "TOOL: Send Reminder (WhatsApp)": {"main": [[{"node": "DB: <PERSON> as <PERSON><PERSON>", "type": "main", "index": 0}]]}, "AI: Extract City": {"main": [[{"node": "c620e7e1-88f2-45a8-9edc-e3745a709033", "type": "main", "index": 0}]]}, "Set Entity (for Tool)": {"main": [[{"node": "eb3442fd-76cc-46a2-bc49-74d7df63013d", "type": "main", "index": 0}, {"node": "CACHE: Check API Response", "type": "main", "index": 0}, {"node": "TOOL: Call News API", "type": "main", "index": 0}]]}, "AGENT: Format Weather Response": {"main": [[{"node": "MERGE: AI Responses", "type": "main", "index": 2}]]}, "AGENT: Format News Response": {"main": [[{"node": "MERGE: AI Responses", "type": "main", "index": 3}]]}, "AI: Extract News Topic": {"main": [[{"node": "c620e7e1-88f2-45a8-9edc-e3745a709033", "type": "main", "index": 1}]]}, "AGENT: Set Dynamic TTL": {"main": [[{"node": "CACHE: <PERSON><PERSON>", "type": "main", "index": 0}]]}, "TOOL: Call ML API for Suggestions": {"main": [[{"node": "AI: Redigir PII", "type": "main", "index": 0}]]}, "DB: Check for Manual Override Flag": {"main": [[{"node": "IF: Manual Override Active?", "type": "main", "index": 0}]]}, "IF: Manual Override Active?": {"main": [[{"node": "Stop (Manual Override)", "type": "main", "index": 0}], [{"node": "IF: Is User Message?", "type": "main", "index": 0}]]}, "IF: Auth Error?": {"main": [[{"node": "ALERT: Critical Auth Failure (via Slack/Email)", "type": "main", "index": 0}]]}, "TOOL: Send SMS Reply": {"main": [[{"node": "DB: Log Resposta da IA", "type": "main", "index": 0}]]}, "AI: Analyze Image with Vision": {"main": [[{"node": "c4f4ddeba-d20f-4318-9ca5-e51c863a35d7", "type": "main", "index": 0}]]}, "AGENT: Format Vision Response": {"main": [[{"node": "MERGE: AI Responses", "type": "main", "index": 4}]]}, "AGENT: Parse Reminder Details": {"main": [[{"node": "DB: <PERSON>", "type": "main", "index": 0}]]}, "AGENT: Sanitize Input": {"main": [[{"node": "AGENT: Dynamic Rate Limiter Gate", "type": "main", "index": 0}]]}, "AGENT: Set Task Priority": {"main": [[{"node": "TOOL: Enviar para Fila Específica", "type": "main", "index": 0}]]}, "ROUTE: Send Report by Channel": {"main": [[{"node": "1cf385da-09f6-4e15-9e25-8717e9c6d35a", "type": "main", "index": 0}], [{"node": "1cf385da-09f6-4e15-9e25-8717e9c6d35a", "type": "main", "index": 0}]]}, "DB: Record Consent in Cache": {"main": [[{"node": "eb3442fd-76cc-46a2-bc49-74d7df63013d", "type": "main", "index": 0}]]}, "DB: Check Consent (Enrichment)": {"main": [[{"node": "IF: Consent Given for Enrichment?", "type": "main", "index": 0}]]}, "IF: Consent Given for Enrichment?": {"main": [[{"node": "TOOL: Delegate Full Profile Enrichment", "type": "main", "index": 0}], [{"node": "METRICS: Log Execution Time", "type": "main", "index": 0}]]}, "CODE: Retry Logic for API Calls": {"main": [[{"node": "d1e70418-4a69-4e78-be7c-c67d71626fdd", "type": "main", "index": 0}]]}, "AI: Format Response by Channel": {"main": [[{"node": "AGENT: Set Formatted AI Output", "type": "main", "index": 0}]]}, "AGENT: Set Formatted AI Output": {"main": [[{"node": "ROUTE: Reply Format by Channel", "type": "main", "index": 0}]]}, "METRICS: Log Execution Time": {"main": [[{"node": "a57c7bd8-280d-40ba-83f0-4660d5b62b1c", "type": "main", "index": 0}]]}, "TRIGGER: Instagram Webhook": {"main": [[{"node": "AGENT: Normalize Instagram Payload", "type": "main", "index": 0}]]}, "AGENT: Normalize Instagram Payload": {"main": [[{"node": "MERGE: All Channels", "type": "main", "index": 4}]]}, "TRIGGER: Email Webhook (SendGrid)": {"main": [[{"node": "AGENT: Normalize Email Payload", "type": "main", "index": 0}]]}, "AGENT: Normalize Email Payload": {"main": [[{"node": "MERGE: All Channels", "type": "main", "index": 5}]]}, "TOOL: Call Weather API (Fallback)": {"main": [[{"node": "AGENT: Format Weather Response", "type": "main", "index": 0}]]}, "IF: Primary Weather API Failed?": {"main": [[{"node": "TOOL: Call Weather API (Fallback)", "type": "main", "index": 0}], [{"node": "AGENT: Format Weather Response", "type": "main", "index": 0}]]}, "CACHE: Check API Response": {"main": [[{"node": "IF: API Response in Cache?", "type": "main", "index": 0}]]}, "IF: API Response in Cache?": {"main": [[{"node": "TOOL: Call Weather API (Primary)", "type": "main", "index": 0}], [{"node": "AGENT: Format Weather Response", "type": "main", "index": 0}]]}, "AGENT: Parse Common Payload": {"main": [[{"node": "DB: Check for Manual Override Flag", "type": "main", "index": 0}]]}, "TOOL: Sync CRM Data (HubSpot)": {"main": [[{"node": "DB: <PERSON> (Enrichment)", "type": "main", "index": 0}]]}, "DB: Gerar Relatório de Correlações": {"main": [[{"node": "AI: Analyze Interaction Trends", "type": "main", "index": 0}]]}, "IF: Is Group Message?": {"main": [[{"node": "d4e2a5b1-c3d5-4a4e-a1d2-b3c4e2a5b1c3", "type": "main", "index": 0}], [{"node": "AI: <PERSON><PERSON><PERSON> & Tom", "type": "main", "index": 0}]]}, "AI: Classify Group Type": {"main": [[{"node": "e5b1c3d5-a4e2-4f3e-b8d9-e4a5d3f2e4b3", "type": "main", "index": 0}]]}, "DB: Fetch Group Settings & Guidelines": {"main": [[{"node": "AI: <PERSON><PERSON><PERSON> & Tom", "type": "main", "index": 0}]]}, "AI: Adapt Response to Group Style": {"main": [[{"node": "g8d7f6e5-b4c3-4d2e-a1b2-c3d4e5f6g7h8", "type": "main", "index": 0}]]}, "MERGE: Final Prompts": {"main": [[{"node": "AI: Detectar Intenção", "type": "main", "index": 0}]]}, "CACHE: Save API Response": {"main": [[{"node": "AGENT: Format Weather Response", "type": "main", "index": 0}]]}, "DB: Check Authorization": {"main": [[{"node": "IF: Is User Authorized?", "type": "main", "index": 0}]]}, "IF: Is User Authorized?": {"main": [[{"node": "e5b1c3d5-a4e2-4f3e-b8d9-e4a5d3f2e4b4", "type": "main", "index": 0}], [{"node": "d4e2a5b1-c3d5-4a4e-a1d2-b3c4e2a5b1c4", "type": "main", "index": 0}]]}, "AGENT: Set Unauthorized Message": {"main": [[{"node": "MERGE: AI Responses", "type": "main", "index": 5}]]}, "TOOL: Search Google Drive": {"main": [[{"node": "IF: Document Found?", "type": "main", "index": 0}]]}, "SWITCH: Route by Tool Intent": {"main": [[{"node": "c620e7e1-88f2-45a8-9edc-e3745a709034", "type": "main", "index": 0}], [{"node": "b3d4f5e6-g7h8-4a1e-b2c3-d4e5f6g7h8j0", "type": "main", "index": 0}], [{"node": "k1j2h3g4-f5e6-4d7c-b8a9-f0e9d8c7b6a5", "type": "main", "index": 0}]]}, "AI: Extract Search Query": {"main": [[{"node": "a9efedcb-49cc-433b-8267-beecab8364ed", "type": "main", "index": 0}]]}, "IF: Document Found?": {"main": [[{"node": "a9d8c7b6-a5a4-4987-a4b3-c2d1e0f987a6", "type": "main", "index": 0}], [{"node": "g8d7f6e5-b4c3-4d2e-a1b2-c3d4e5f6g7h9", "type": "main", "index": 0}]]}, "AGENT: Set Not Found Message": {"main": [[{"node": "MERGE: AI Responses", "type": "main", "index": 6}]]}, "Set PDF Queue": {"main": [[{"node": "AGENT: Set Task Priority", "type": "main", "index": 0}]]}}, "settings": {"errorWorkflow": "<PERSON><PERSON><PERSON>", "timezone": "America/Sao_Paulo", "saveDataSuccess": "all", "saveDataError": "all"}, "staticData": null, "triggerCount": 9, "active": true}