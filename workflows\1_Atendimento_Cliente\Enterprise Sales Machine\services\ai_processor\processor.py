# services/ai_processor/processor.py
import os
import pika
import time
import json
import logging
import requests
import psycopg2
from psycopg2.extras import Json
from dotenv import load_dotenv

# --- Configuração de Logging ---
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# --- Carregar Variáveis de Ambiente ---
load_dotenv()
DATABASE_URL = os.getenv("DATABASE_URL")
RABBITMQ_URL = os.getenv("RABBITMQ_URL")
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")

MEDIA_QUEUE = 'media_processing_queue'
RESULT_QUEUE = 'processing_results_queue'

# --- Placeholder para Funções de IA ---
def transcribe_audio(media_url):
    """Placeholder: Baixa e transcreve áudio com Whisper."""
    logging.info(f"Transcrevendo áudio de {media_url}")
    # Aqui entraria a lógica real:
    # 1. Baixar o arquivo de `media_url`
    # 2. Carregar o modelo do Whisper
    # 3. Transcrever
    # 4. Retornar o texto
    time.sleep(5) # Simula o processamento
    return f"Texto transcrito do áudio em {media_url}."

def analyze_image(media_url):
    """Placeholder: Analisa imagem com YOLO e Tesseract."""
    logging.info(f"Analisando imagem de {media_url}")
    # Lógica real:
    # 1. Baixar imagem
    # 2. OCR com Tesseract
    # 3. Detecção de objetos/marcas com YOLOv8
    time.sleep(3)
    return {
        "ocr_text": "Texto extraído da imagem.",
        "objects_detected": ["logo_empresa_x", "pessoa", "celular"]
    }

# --- Lógica do Worker ---
def process_message(channel, method, properties, body):
    try:
        data = json.loads(body)
        logging.info(f"Recebida mensagem para processamento: {data['message_id']}")
        
        media_type = data.get('media_type')
        media_url = data.get('media_url')
        
        analysis_result = {}

        if media_type == 'audio':
            analysis_result['transcription'] = transcribe_audio(media_url)
        elif media_type in ['image', 'meme']:
            analysis_result = analyze_image(media_url)
        # Adicionar 'video' aqui, que poderia extrair frames e analisá-los
        else:
            logging.warning(f"Tipo de mídia desconhecido: {media_type}")

        # --- Salvar o resultado no banco de dados ---
        # Ex: Atualizar uma tabela de 'analise_midia' ligada à mensagem original
        # conn = psycopg2.connect(DATABASE_URL)
        # cur = conn.cursor()
        # cur.execute("UPDATE agent.mensagens SET analise_midia = %s WHERE id = %s", (Json(analysis_result), data['message_id']))
        # conn.commit()
        # cur.close()
        # conn.close()
        
        logging.info(f"Análise completa para mensagem {data['message_id']}. Resultado: {analysis_result}")

        # Opcional: Enviar o resultado para outra fila para o n8n consumir
        # result_payload = json.dumps({'message_id': data['message_id'], 'result': analysis_result})
        # channel.basic_publish(exchange='', routing_key=RESULT_QUEUE, body=result_payload)
        
        channel.basic_ack(delivery_tag=method.delivery_tag)

    except Exception as e:
        logging.error(f"Erro ao processar mensagem: {e}")
        # Rejeita a mensagem mas não a re-enfileira para evitar loops de erro infinitos
        channel.basic_nack(delivery_tag=method.delivery_tag, requeue=False)

def main():
    while True:
        try:
            connection = pika.BlockingConnection(pika.URLParameters(RABBITMQ_URL))
            channel = connection.channel()
            
            # Declara as filas para garantir que existam
            channel.queue_declare(queue=MEDIA_QUEUE, durable=True)
            channel.queue_declare(queue=RESULT_QUEUE, durable=True)
            
            channel.basic_qos(prefetch_count=1) # Processa uma mensagem por vez
            channel.basic_consume(queue=MEDIA_QUEUE, on_message_callback=process_message)
            
            logging.info('Aguardando mensagens na fila de mídia. Para sair pressione CTRL+C')
            channel.start_consuming()

        except pika.exceptions.AMQPConnectionError:
            logging.warning("Conexão com RabbitMQ perdida. Tentando reconectar em 5 segundos...")
            time.sleep(5)
        except KeyboardInterrupt:
            logging.info("Encerrando o worker.")
            break

if __name__ == '__main__':
    main()