{
  "name": "[ESCALATION] Intelligent Human Escalation Agent v1.0",
  "nodes": [
    {
      "parameters": {
        "path": "escalate-intelligent",
        "httpMethod": "POST",
        "responseMode": "responseNode",
        "options": {
          "rawBody": true,
          "allowedOrigins": "*"
        }
      },
      "id": "webhook-trigger",
      "name": "🚀 Trigger: Escalation Request",
      "type": "n8n-nodes-base.webhook",
      "typeVersion": 1,
      "position": [240, 300],
      "webhookId": "escalate-intelligent"
    },
    {
      "parameters": {
        "jsCode": "// =====================================================\n// INTELLIGENT ESCALATION AGENT - REQUEST VALIDATOR\n// =====================================================\n\nconst Joi = require('joi');\nconst crypto = require('crypto');\n\n// Schema de validação rigorosa\nconst escalationSchema = Joi.object({\n  contact_id: Joi.number().integer().positive().required(),\n  escalation_reason: Joi.string().min(10).max(500).required(),\n  urgency_level: Joi.string().valid('low', 'medium', 'high', 'critical').default('medium'),\n  context_data: Joi.object().required(),\n  conversation_history: Joi.array().items(Joi.object()).max(50).optional(),\n  specialization_hint: Joi.string().valid('technical', 'billing', 'sales', 'support', 'compliance', 'legal', 'product', 'general').optional(),\n  metadata: Joi.object().optional(),\n  chatwoot_conversation_id: Joi.number().integer().positive().optional(),\n  agent_identity: Joi.string().max(100).optional()\n});\n\n// Função para sanitização\nfunction sanitizeInput(input) {\n  if (typeof input === 'string') {\n    return input\n      .replace(/<script[^>]*>.*?<\/script>/gi, '')\n      .replace(/<[^>]+>/g, '')\n      .trim();\n  }\n  return input;\n}\n\n// Função para gerar correlation ID\nfunction generateCorrelationId() {\n  return crypto.randomUUID();\n}\n\n// Função para calcular hash de contexto\nfunction generateContextHash(contextData, conversationHistory) {\n  const combined = JSON.stringify({\n    context: contextData,\n    history: conversationHistory || []\n  });\n  return crypto.createHash('sha256').update(combined).digest('hex');\n}\n\n// Processar requisição\nconst startTime = Date.now();\nconst correlationId = generateCorrelationId();\n\ntry {\n  // Extrair dados do webhook\n  const requestBody = $input.first().json.body;\n  const headers = $input.first().json.headers;\n  \n  // Log da requisição\n  console.log(`[${correlationId}] Escalation request received`, {\n    timestamp: new Date().toISOString(),\n    userAgent: headers['user-agent'],\n    contentLength: headers['content-length']\n  });\n  \n  // Validar schema\n  const { error, value: validatedData } = escalationSchema.validate(requestBody, {\n    abortEarly: false,\n    stripUnknown: true,\n    convert: true\n  });\n  \n  if (error) {\n    const validationErrors = error.details.map(detail => ({\n      field: detail.path.join('.'),\n      message: detail.message,\n      value: detail.context?.value\n    }));\n    \n    return [{\n      json: {\n        success: false,\n        error: 'VALIDATION_ERROR',\n        message: 'Request validation failed',\n        details: validationErrors,\n        correlation_id: correlationId,\n        timestamp: new Date().toISOString()\n      }\n    }];\n  }\n  \n  // Sanitizar dados\n  const sanitizedData = {\n    ...validatedData,\n    escalation_reason: sanitizeInput(validatedData.escalation_reason),\n    specialization_hint: validatedData.specialization_hint ? sanitizeInput(validatedData.specialization_hint) : null\n  };\n  \n  // Gerar hash de contexto para cache\n  const contextHash = generateContextHash(\n    sanitizedData.context_data,\n    sanitizedData.conversation_history\n  );\n  \n  // Calcular métricas de validação\n  const processingTime = Date.now() - startTime;\n  \n  // Preparar dados para próximo nó\n  const outputData = {\n    // Dados validados\n    ...sanitizedData,\n    \n    // Metadados de processamento\n    correlation_id: correlationId,\n    context_hash: contextHash,\n    validation_time_ms: processingTime,\n    received_at: new Date().toISOString(),\n    \n    // Headers úteis\n    user_agent: headers['user-agent'],\n    ip_address: headers['x-forwarded-for'] || headers['x-real-ip'] || 'unknown',\n    \n    // Status\n    validation_status: 'success',\n    next_step: 'ai_analysis'\n  };\n  \n  console.log(`[${correlationId}] Validation successful`, {\n    processingTime,\n    urgencyLevel: sanitizedData.urgency_level,\n    hasConversationHistory: !!sanitizedData.conversation_history\n  });\n  \n  return [{ json: outputData }];\n  \n} catch (error) {\n  console.error(`[${correlationId}] Validation error:`, error);\n  \n  return [{\n    json: {\n      success: false,\n      error: 'INTERNAL_ERROR',\n      message: 'Internal validation error',\n      correlation_id: correlationId,\n      timestamp: new Date().toISOString(),\n      details: {\n        error: error.message,\n        stack: error.stack\n      }\n    }\n  }];\n}"
      },
      "id": "request-validator",
      "name": "✅ Validator: Request Validation",
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [460, 300]
    },
    {
      "parameters": {
        "conditions": {
          "options": {
            "caseSensitive": true,
            "leftValue": "",
            "typeValidation": "strict"
          },
          "conditions": [
            {
              "id": "validation-success",
              "leftValue": "={{ $json.validation_status }}",
              "rightValue": "success",
              "operator": {
                "type": "string",
                "operation": "equals"
              }
            }
          ],
          "combinator": "and"
        },
        "options": {}
      },
      "id": "validation-check",
      "name": "🔍 Check: Validation Status",
      "type": "n8n-nodes-base.if",
      "typeVersion": 2,
      "position": [680, 300]
    },
    {
      "parameters": {
        "operation": "executeQuery",
        "query": "SELECT * FROM agent.ai_analysis_cache WHERE context_hash = $1 AND expires_at > NOW()",
        "options": {
          "queryParameters": "={{ [$json.context_hash] }}"
        }
      },
      "id": "cache-check",
      "name": "💾 Cache: Check AI Analysis",
      "type": "n8n-nodes-base.postgres",
      "typeVersion": 2.4,
      "position": [900, 200],
      "credentials": {
        "postgres": {
          "id": "postgres-main",
          "name": "PostgreSQL - Main Database"
        }
      }
    },
    {
      "parameters": {
        "conditions": {
          "options": {
            "caseSensitive": true,
            "leftValue": "",
            "typeValidation": "strict"
          },
          "conditions": [
            {
              "id": "cache-hit",
              "leftValue": "={{ $json.length }}",
              "rightValue": 0,
              "operator": {
                "type": "number",
                "operation": "gt"
              }
            }
          ],
          "combinator": "and"
        },
        "options": {}
      },
      "id": "cache-hit-check",
      "name": "🎯 Check: Cache Hit",
      "type": "n8n-nodes-base.if",
      "typeVersion": 2,
      "position": [1120, 200]
    },
    {
      "parameters": {
        "jsCode": "// =====================================================\n// AI CONTEXT ANALYZER - INTELLIGENT ANALYSIS\n// =====================================================\n\nconst startTime = Date.now();\nconst correlationId = $json.correlation_id;\n\n// Configuração de AI providers\nconst AI_CONFIG = {\n  primary: {\n    provider: 'openai',\n    model: 'gpt-4',\n    maxTokens: 1000,\n    temperature: 0.1\n  },\n  fallback: {\n    provider: 'anthropic',\n    model: 'claude-3-sonnet',\n    maxTokens: 1000,\n    temperature: 0.1\n  }\n};\n\n// Função para análise de sentimento\nfunction analyzeSentiment(text) {\n  const negativeWords = ['problema', 'erro', 'falha', 'ruim', 'péssimo', 'horrível', 'cancelar', 'reembolso'];\n  const positiveWords = ['bom', 'ótimo', 'excelente', 'satisfeito', 'obrigado', 'parabéns'];\n  const criticalWords = ['urgente', 'crítico', 'emergência', 'imediato', 'grave', 'sério'];\n  \n  const lowerText = text.toLowerCase();\n  \n  const negativeCount = negativeWords.filter(word => lowerText.includes(word)).length;\n  const positiveCount = positiveWords.filter(word => lowerText.includes(word)).length;\n  const criticalCount = criticalWords.filter(word => lowerText.includes(word)).length;\n  \n  if (criticalCount > 0) return 'critical';\n  if (negativeCount > positiveCount) return 'negative';\n  if (positiveCount > negativeCount) return 'positive';\n  return 'neutral';\n}\n\n// Função para detectar urgência\nfunction detectUrgency(reason, contextData, conversationHistory) {\n  const urgentKeywords = ['urgente', 'crítico', 'emergência', 'imediato', 'parou', 'não funciona'];\n  const highKeywords = ['importante', 'prioridade', 'rápido', 'hoje'];\n  \n  const text = `${reason} ${JSON.stringify(contextData)} ${JSON.stringify(conversationHistory || [])}`.toLowerCase();\n  \n  const urgentCount = urgentKeywords.filter(word => text.includes(word)).length;\n  const highCount = highKeywords.filter(word => text.includes(word)).length;\n  \n  if (urgentCount > 0) return 'critical';\n  if (highCount > 0) return 'high';\n  return 'medium';\n}\n\n// Função para identificar especialização\nfunction identifySpecialization(reason, contextData) {\n  const specializations = {\n    technical: ['erro', 'bug', 'falha', 'não funciona', 'sistema', 'aplicativo', 'site', 'login', 'senha'],\n    billing: ['cobrança', 'fatura', 'pagamento', 'cartão', 'boleto', 'valor', 'preço', 'desconto'],\n    sales: ['comprar', 'produto', 'serviço', 'plano', 'upgrade', 'venda', 'orçamento'],\n    support: ['ajuda', 'dúvida', 'como', 'tutorial', 'suporte', 'atendimento'],\n    compliance: ['lgpd', 'privacidade', 'dados', 'legal', 'contrato', 'termo'],\n    legal: ['jurídico', 'advogado', 'processo', 'lei', 'regulamento']\n  };\n  \n  const text = `${reason} ${JSON.stringify(contextData)}`.toLowerCase();\n  const scores = {};\n  \n  for (const [spec, keywords] of Object.entries(specializations)) {\n    scores[spec] = keywords.filter(word => text.includes(word)).length;\n  }\n  \n  const maxScore = Math.max(...Object.values(scores));\n  if (maxScore === 0) return ['general'];\n  \n  return Object.entries(scores)\n    .filter(([_, score]) => score === maxScore)\n    .map(([spec, _]) => spec);\n}\n\n// Função para extrair entidades\nfunction extractEntities(text) {\n  const entities = {\n    products: [],\n    issues: [],\n    amounts: []\n  };\n  \n  // Extrair valores monetários\n  const moneyRegex = /R\$\s*([0-9.,]+)/gi;\n  const moneyMatches = text.match(moneyRegex);\n  if (moneyMatches) {\n    entities.amounts = moneyMatches;\n  }\n  \n  // Extrair produtos/serviços comuns\n  const productKeywords = ['plano', 'produto', 'serviço', 'aplicativo', 'sistema', 'conta'];\n  entities.products = productKeywords.filter(keyword => \n    text.toLowerCase().includes(keyword)\n  );\n  \n  // Extrair tipos de problemas\n  const issueKeywords = ['erro', 'problema', 'falha', 'bug', 'lentidão', 'indisponibilidade'];\n  entities.issues = issueKeywords.filter(keyword => \n    text.toLowerCase().includes(keyword)\n  );\n  \n  return entities;\n}\n\ntry {\n  console.log(`[${correlationId}] Starting AI analysis`);\n  \n  // Extrair dados\n  const {\n    escalation_reason,\n    context_data,\n    conversation_history,\n    urgency_level: providedUrgency,\n    specialization_hint\n  } = $json;\n  \n  // Análise local (fallback)\n  const sentiment = analyzeSentiment(escalation_reason);\n  const detectedUrgency = detectUrgency(escalation_reason, context_data, conversation_history);\n  const specializations = identifySpecialization(escalation_reason, context_data);\n  const entities = extractEntities(escalation_reason);\n  \n  // Calcular score de confiança baseado em heurísticas\n  let confidenceScore = 60; // Base score\n  \n  // Aumentar confiança baseado em keywords específicas\n  if (sentiment === 'critical') confidenceScore += 20;\n  if (detectedUrgency === 'critical') confidenceScore += 15;\n  if (specializations.length === 1) confidenceScore += 10;\n  if (entities.issues.length > 0) confidenceScore += 5;\n  \n  // Limitar score\n  confidenceScore = Math.min(confidenceScore, 95);\n  \n  // Determinar urgência final\n  const finalUrgency = providedUrgency === 'medium' ? detectedUrgency : providedUrgency;\n  \n  // Gerar recomendações\n  const recommendations = [];\n  if (sentiment === 'critical') {\n    recommendations.push('Priorizar atendimento imediato');\n  }\n  if (entities.amounts.length > 0) {\n    recommendations.push('Verificar questões financeiras');\n  }\n  if (conversation_history && conversation_history.length > 10) {\n    recommendations.push('Cliente com histórico extenso - revisar contexto');\n  }\n  \n  // Resultado da análise\n  const aiAnalysis = {\n    sentiment,\n    urgency_detected: detectedUrgency,\n    urgency_final: finalUrgency,\n    specializations_detected: specializations,\n    specialization_hint,\n    confidence_score: confidenceScore,\n    entities,\n    recommendations,\n    analysis_method: 'heuristic_fallback',\n    model_used: 'local_heuristics',\n    processing_time_ms: Date.now() - startTime,\n    timestamp: new Date().toISOString()\n  };\n  \n  console.log(`[${correlationId}] AI analysis completed`, {\n    confidence: confidenceScore,\n    urgency: finalUrgency,\n    specializations,\n    processingTime: Date.now() - startTime\n  });\n  \n  // Preparar output\n  const output = {\n    ...$json,\n    ai_analysis: aiAnalysis,\n    ai_confidence_score: confidenceScore,\n    urgency_level: finalUrgency,\n    specialization_required: specializations,\n    customer_sentiment: sentiment,\n    analysis_status: 'success',\n    next_step: 'intelligent_routing'\n  };\n  \n  return [{ json: output }];\n  \n} catch (error) {\n  console.error(`[${correlationId}] AI analysis error:`, error);\n  \n  // Fallback para análise manual\n  const fallbackAnalysis = {\n    sentiment: 'neutral',\n    urgency_detected: $json.urgency_level || 'medium',\n    urgency_final: $json.urgency_level || 'medium',\n    specializations_detected: [$json.specialization_hint || 'general'],\n    confidence_score: 30,\n    entities: { products: [], issues: [], amounts: [] },\n    recommendations: ['Análise manual necessária'],\n    analysis_method: 'error_fallback',\n    model_used: 'none',\n    processing_time_ms: Date.now() - startTime,\n    error: error.message,\n    timestamp: new Date().toISOString()\n  };\n  \n  return [{\n    json: {\n      ...$json,\n      ai_analysis: fallbackAnalysis,\n      ai_confidence_score: 30,\n      urgency_level: $json.urgency_level || 'medium',\n      specialization_required: [$json.specialization_hint || 'general'],\n      customer_sentiment: 'neutral',\n      analysis_status: 'fallback',\n      next_step: 'intelligent_routing'\n    }\n  }];\n}"
      },
      "id": "ai-analyzer",
      "name": "🤖 AI: Context Analysis",
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [1340, 300]
    },
    {
      "parameters": {
        "jsCode": "// =====================================================\n// CACHE PROCESSOR - USAR ANÁLISE CACHEADA\n// =====================================================\n\nconst correlationId = $('request-validator').first().json.correlation_id;\nconst cachedAnalysis = $json[0]; // Resultado do cache\n\nconsole.log(`[${correlationId}] Using cached AI analysis`);\n\n// Incrementar hit count\nconst hitCount = (cachedAnalysis.hit_count || 0) + 1;\n\n// Preparar dados com análise cacheada\nconst requestData = $('request-validator').first().json;\nconst aiAnalysis = JSON.parse(cachedAnalysis.analysis_result);\n\n// Atualizar timestamps\naiAnalysis.cached_at = new Date().toISOString();\naiAnalysis.cache_hit_count = hitCount;\naiAnalysis.analysis_method = 'cached';\n\nconst output = {\n  ...requestData,\n  ai_analysis: aiAnalysis,\n  ai_confidence_score: aiAnalysis.confidence_score,\n  urgency_level: aiAnalysis.urgency_final,\n  specialization_required: aiAnalysis.specializations_detected,\n  customer_sentiment: aiAnalysis.sentiment,\n  analysis_status: 'cached',\n  cache_hit: true,\n  next_step: 'intelligent_routing'\n};\n\nreturn [{ json: output }];"
      },
      "id": "cache-processor",
      "name": "⚡ Cache: Use Cached Analysis",
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [1340, 100]
    },
    {
      "parameters": {
        "operation": "executeQuery",
        "query": "UPDATE agent.ai_analysis_cache SET hit_count = hit_count + 1, last_accessed = NOW() WHERE context_hash = $1",
        "options": {
          "queryParameters": "={{ [$('request-validator').first().json.context_hash] }}"
        }
      },
      "id": "cache-update",
      "name": "📊 Cache: Update Hit Count",
      "type": "n8n-nodes-base.postgres",
      "typeVersion": 2.4,
      "position": [1560, 100],
      "credentials": {
        "postgres": {
          "id": "postgres-main",
          "name": "PostgreSQL - Main Database"
        }
      }
    },
    {
      "parameters": {
        "operation": "executeQuery",
        "query": "INSERT INTO agent.ai_analysis_cache (context_hash, input_data, analysis_result, confidence_score, model_used, processing_time_ms, expires_at) VALUES ($1, $2, $3, $4, $5, $6, NOW() + INTERVAL '24 hours') ON CONFLICT (context_hash) DO UPDATE SET analysis_result = EXCLUDED.analysis_result, confidence_score = EXCLUDED.confidence_score, last_accessed = NOW()",
        "options": {
          "queryParameters": "={{ [$json.context_hash, JSON.stringify({context_data: $json.context_data, conversation_history: $json.conversation_history}), JSON.stringify($json.ai_analysis), $json.ai_confidence_score, $json.ai_analysis.model_used, $json.ai_analysis.processing_time_ms] }}"
        }
      },
      "id": "cache-store",
      "name": "💾 Cache: Store Analysis",
      "type": "n8n-nodes-base.postgres",
      "typeVersion": 2.4,
      "position": [1560, 300],
      "credentials": {
        "postgres": {
          "id": "postgres-main",
          "name": "PostgreSQL - Main Database"
        }
      }
    },
    {
      "parameters": {
        "jsCode": "// =====================================================\n// INTELLIGENT ROUTER - SMART QUEUE SELECTION\n// =====================================================\n\nconst correlationId = $json.correlation_id;\nconst startTime = Date.now();\n\nconsole.log(`[${correlationId}] Starting intelligent routing`);\n\n// Função para calcular SLA deadline\nfunction calculateSLADeadline(urgencyLevel) {\n  const now = new Date();\n  const slaHours = {\n    critical: 1,\n    high: 4,\n    medium: 24,\n    low: 72\n  };\n  \n  const hours = slaHours[urgencyLevel] || 24;\n  return new Date(now.getTime() + (hours * 60 * 60 * 1000));\n}\n\n// Função para calcular score de prioridade\nfunction calculatePriorityScore(urgency, confidence, sentiment) {\n  let score = 50; // Base score\n  \n  // Urgência\n  const urgencyScores = { critical: 40, high: 30, medium: 20, low: 10 };\n  score += urgencyScores[urgency] || 20;\n  \n  // Confiança da IA\n  score += Math.floor(confidence * 0.2);\n  \n  // Sentimento\n  const sentimentScores = { critical: 20, negative: 15, neutral: 5, positive: 0 };\n  score += sentimentScores[sentiment] || 5;\n  \n  return Math.min(score, 100);\n}\n\n// Função para selecionar fila baseada em especialização\nfunction selectQueue(specializations, urgency) {\n  // Mapeamento de especializações para filas\n  const queueMapping = {\n    technical: 'technical-support',\n    billing: 'billing-support',\n    sales: 'sales-support',\n    support: 'general-support',\n    compliance: 'compliance-legal',\n    legal: 'compliance-legal',\n    product: 'technical-support',\n    general: 'general-support'\n  };\n  \n  // Priorizar primeira especialização\n  const primarySpec = specializations[0] || 'general';\n  let selectedQueue = queueMapping[primarySpec] || 'general-support';\n  \n  // Para escalações críticas, usar fila especializada se disponível\n  if (urgency === 'critical' && primarySpec !== 'general') {\n    selectedQueue = queueMapping[primarySpec];\n  }\n  \n  return selectedQueue;\n}\n\n// Função para gerar razão de roteamento\nfunction generateRoutingReason(specializations, urgency, confidence, method) {\n  const reasons = [];\n  \n  if (method === 'cached') {\n    reasons.push('Análise cacheada utilizada');\n  } else {\n    reasons.push('Análise de IA realizada');\n  }\n  \n  reasons.push(`Urgência: ${urgency}`);\n  reasons.push(`Especialização: ${specializations.join(', ')}`);\n  reasons.push(`Confiança: ${confidence}%`);\n  \n  return reasons.join(' | ');\n}\n\ntry {\n  const {\n    urgency_level,\n    specialization_required,\n    ai_confidence_score,\n    customer_sentiment,\n    ai_analysis\n  } = $json;\n  \n  // Calcular SLA deadline\n  const slaDeadline = calculateSLADeadline(urgency_level);\n  \n  // Calcular score de prioridade\n  const priorityScore = calculatePriorityScore(\n    urgency_level,\n    ai_confidence_score,\n    customer_sentiment\n  );\n  \n  // Selecionar fila\n  const selectedQueue = selectQueue(specialization_required, urgency_level);\n  \n  // Gerar razão de roteamento\n  const routingReason = generateRoutingReason(\n    specialization_required,\n    urgency_level,\n    ai_confidence_score,\n    ai_analysis.analysis_method\n  );\n  \n  // Preparar dados de roteamento\n  const routingData = {\n    assigned_queue: selectedQueue,\n    sla_deadline: slaDeadline.toISOString(),\n    priority_score: priorityScore,\n    routing_algorithm: 'intelligent_ai',\n    routing_reason: routingReason,\n    routing_time_ms: Date.now() - startTime,\n    routed_at: new Date().toISOString()\n  };\n  \n  console.log(`[${correlationId}] Routing completed`, {\n    queue: selectedQueue,\n    priority: priorityScore,\n    sla: slaDeadline,\n    processingTime: Date.now() - startTime\n  });\n  \n  // Preparar output final\n  const output = {\n    ...$json,\n    ...routingData,\n    routing_status: 'success',\n    next_step: 'persist_escalation'\n  };\n  \n  return [{ json: output }];\n  \n} catch (error) {\n  console.error(`[${correlationId}] Routing error:`, error);\n  \n  // Fallback para fila geral\n  const fallbackRouting = {\n    assigned_queue: 'general-support',\n    sla_deadline: calculateSLADeadline('medium').toISOString(),\n    priority_score: 50,\n    routing_algorithm: 'fallback',\n    routing_reason: `Erro no roteamento: ${error.message}`,\n    routing_time_ms: Date.now() - startTime,\n    routed_at: new Date().toISOString()\n  };\n  \n  return [{\n    json: {\n      ...$json,\n      ...fallbackRouting,\n      routing_status: 'fallback',\n      next_step: 'persist_escalation'\n    }\n  }];\n}"
      },
      "id": "intelligent-router",
      "name": "🎯 Router: Intelligent Queue Selection",
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [1780, 200]
    },
    {
      "parameters": {
        "operation": "executeQuery",
        "query": "INSERT INTO agent.intelligent_escalations (correlation_id, contact_id, escalation_trigger, escalation_reason, urgency_level, ai_analysis, ai_confidence_score, ai_model_used, ai_processing_time_ms, specialization_required, context_data, conversation_history, customer_sentiment, assigned_queue, routing_algorithm, routing_reason, sla_deadline, priority_score, created_by, metadata) VALUES ($1, $2, $3, $4, $5::agent.urgency_level_enum, $6::jsonb, $7, $8, $9, $10::agent.specialization_enum[], $11::jsonb, $12::jsonb, $13, $14, $15, $16, $17::timestamptz, $18, $19, $20::jsonb) RETURNING id, correlation_id, created_at, sla_deadline",
        "options": {
          "queryParameters": "={{ [$json.correlation_id, $json.contact_id, 'webhook_request', $json.escalation_reason, $json.urgency_level, JSON.stringify($json.ai_analysis), $json.ai_confidence_score, $json.ai_analysis.model_used, $json.ai_analysis.processing_time_ms, $json.specialization_required, JSON.stringify($json.context_data), JSON.stringify($json.conversation_history || []), $json.customer_sentiment, $json.assigned_queue, $json.routing_algorithm, $json.routing_reason, $json.sla_deadline, $json.priority_score, 'intelligent_escalation_agent', JSON.stringify($json.metadata || {})] }}"
        }
      },
      "id": "persist-escalation",
      "name": "💾 DB: Persist Escalation",
      "type": "n8n-nodes-base.postgres",
      "typeVersion": 2.4,
      "position": [2000, 200],
      "credentials": {
        "postgres": {
          "id": "postgres-main",
          "name": "PostgreSQL - Main Database"
        }
      }
    },
    {
      "parameters": {
        "url": "={{ $('persist-escalation').first().json.chatwoot_conversation_id ? 'https://chatwoot.empresa.com/api/v1/accounts/1/conversations/' + $('persist-escalation').first().json.chatwoot_conversation_id : 'https://chatwoot.empresa.com/api/v1/accounts/1/conversations' }}",
        "authentication": "predefinedCredentialType",
        "nodeCredentialType": "chatwootApi",
        "sendHeaders": true,
        "headerParameters": {
          "parameters": [
            {
              "name": "Content-Type",
              "value": "application/json"
            }
          ]
        },
        "sendBody": true,
        "bodyParameters": {
          "parameters": [
            {
              "name": "inbox_id",
              "value": "={{ $json.assigned_queue === 'technical-support' ? 1 : $json.assigned_queue === 'billing-support' ? 2 : $json.assigned_queue === 'sales-support' ? 3 : 4 }}"
            },
            {
              "name": "contact_id",
              "value": "={{ $json.contact_id }}"
            },
            {
              "name": "status",
              "value": "open"
            },
            {
              "name": "priority",
              "value": "={{ $json.urgency_level === 'critical' ? 'urgent' : $json.urgency_level === 'high' ? 'high' : $json.urgency_level === 'medium' ? 'medium' : 'low' }}"
            },
            {
              "name": "custom_attributes",
              "value": "={{ { escalation_id: $('persist-escalation').first().json.id, ai_confidence: $json.ai_confidence_score, escalation_reason: $json.escalation_reason, correlation_id: $json.correlation_id, sla_deadline: $json.sla_deadline } }}"
            }
          ]
        },
        "options": {
          "timeout": 30000,
          "retry": {
            "enabled": true,
            "maxTries": 3
          }
        }
      },
      "id": "chatwoot-integration",
      "name": "💬 Chatwoot: Create/Update Conversation",
      "type": "n8n-nodes-base.httpRequest",
      "typeVersion": 4.2,
      "position": [2220, 100],
      "credentials": {
        "chatwootApi": {
          "id": "chatwoot-main",
          "name": "Chatwoot API - Main"
        }
      }
    },
    {
      "parameters": {
        "jsCode": "// =====================================================\n// SLACK NOTIFICATION BUILDER\n// =====================================================\n\nconst correlationId = $json.correlation_id;\nconst escalationData = $('persist-escalation').first().json;\n\n// Função para determinar canal baseado na urgência e especialização\nfunction getSlackChannel(urgency, specialization) {\n  if (urgency === 'critical') {\n    return '#critical-escalations';\n  }\n  \n  const channelMap = {\n    'technical-support': '#tech-escalations',\n    'billing-support': '#billing-escalations',\n    'sales-support': '#sales-escalations',\n    'compliance-legal': '#compliance-escalations',\n    'general-support': '#general-escalations'\n  };\n  \n  return channelMap[specialization] || '#general-escalations';\n}\n\n// Função para gerar emoji baseado na urgência\nfunction getUrgencyEmoji(urgency) {\n  const emojiMap = {\n    critical: '🚨',\n    high: '⚡',\n    medium: '⚠️',\n    low: '📝'\n  };\n  return emojiMap[urgency] || '📝';\n}\n\n// Função para gerar cor baseada na urgência\nfunction getUrgencyColor(urgency) {\n  const colorMap = {\n    critical: '#FF0000',\n    high: '#FF8C00',\n    medium: '#FFD700',\n    low: '#32CD32'\n  };\n  return colorMap[urgency] || '#FFD700';\n}\n\n// Preparar dados da notificação\nconst {\n  urgency_level,\n  assigned_queue,\n  escalation_reason,\n  ai_confidence_score,\n  sla_deadline,\n  customer_sentiment,\n  specialization_required\n} = $json;\n\nconst channel = getSlackChannel(urgency_level, assigned_queue);\nconst emoji = getUrgencyEmoji(urgency_level);\nconst color = getUrgencyColor(urgency_level);\n\n// Formatear deadline\nconst deadline = new Date(sla_deadline);\nconst deadlineFormatted = deadline.toLocaleString('pt-BR', {\n  timeZone: 'America/Sao_Paulo',\n  day: '2-digit',\n  month: '2-digit',\n  year: 'numeric',\n  hour: '2-digit',\n  minute: '2-digit'\n});\n\n// Construir mensagem\nconst message = {\n  channel: channel,\n  username: 'Escalation Agent',\n  icon_emoji: ':robot_face:',\n  attachments: [\n    {\n      color: color,\n      title: `${emoji} Nova Escalação - ${urgency_level.toUpperCase()}`,\n      title_link: `https://dashboard.empresa.com/escalations/${escalationData.id}`,\n      fields: [\n        {\n          title: 'Cliente ID',\n          value: $json.contact_id,\n          short: true\n        },\n        {\n          title: 'Fila Atribuída',\n          value: assigned_queue,\n          short: true\n        },\n        {\n          title: 'Especialização',\n          value: specialization_required.join(', '),\n          short: true\n        },\n        {\n          title: 'Sentimento',\n          value: customer_sentiment,\n          short: true\n        },\n        {\n          title: 'Confiança IA',\n          value: `${ai_confidence_score}%`,\n          short: true\n        },\n        {\n          title: 'SLA Deadline',\n          value: deadlineFormatted,\n          short: true\n        },\n        {\n          title: 'Motivo da Escalação',\n          value: escalation_reason.length > 200 ? \n            escalation_reason.substring(0, 200) + '...' : \n            escalation_reason,\n          short: false\n        }\n      ],\n      actions: [\n        {\n          type: 'button',\n          text: 'Aceitar Escalação',\n          style: 'primary',\n          url: `https://dashboard.empresa.com/escalations/${escalationData.id}/accept`\n        },\n        {\n          type: 'button',\n          text: 'Ver Detalhes',\n          url: `https://dashboard.empresa.com/escalations/${escalationData.id}`\n        },\n        {\n          type: 'button',\n          text: 'Chatwoot',\n          url: `https://chatwoot.empresa.com/app/accounts/1/conversations/${$json.chatwoot_conversation_id || 'new'}`\n        }\n      ],\n      footer: 'Intelligent Escalation Agent v1.0',\n      footer_icon: 'https://empresa.com/assets/bot-icon.png',\n      ts: Math.floor(Date.now() / 1000)\n    }\n  ]\n};\n\nconsole.log(`[${correlationId}] Slack notification prepared for ${channel}`);\n\nreturn [{\n  json: {\n    ...message,\n    escalation_id: escalationData.id,\n    correlation_id: correlationId\n  }\n}];"
      },
      "id": "slack-notification-builder",
      "name": "📢 Slack: Build Notification",
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [2220, 300]
    },
    {
      "parameters": {
        "authentication": "predefinedCredentialType",
        "nodeCredentialType": "slackApi",
        "resource": "message",
        "operation": "post",
        "channel": "={{ $json.channel }}",
        "text": "Nova escalação inteligente recebida",
        "attachments": "={{ $json.attachments }}",
        "otherOptions": {
          "username": "={{ $json.username }}",
          "icon_emoji": "={{ $json.icon_emoji }}"
        }
      },
      "id": "slack-send",
      "name": "📤 Slack: Send Notification",
      "type": "n8n-nodes-base.slack",
      "typeVersion": 2.1,
      "position": [2440, 300],
      "credentials": {
        "slackApi": {
          "id": "slack-main",
          "name": "Slack API - Main"
        }
      }
    },
    {
      "parameters": {
        "operation": "executeQuery",
        "query": "UPDATE agent.intelligent_escalations SET chatwoot_conversation_id = $1, slack_thread_ts = $2, status = 'assigned'::agent.escalation_status_enum, updated_at = NOW() WHERE id = $3 RETURNING *",
        "options": {
          "queryParameters": "={{ [$('chatwoot-integration').first().json.id || null, $('slack-send').first().json.ts || null, $('persist-escalation').first().json.id] }}"
        }
      },
      "id": "update-escalation",
      "name": "🔄 DB: Update Escalation Status",
      "type": "n8n-nodes-base.postgres",
      "typeVersion": 2.4,
      "position": [2660, 200],
      "credentials": {
        "postgres": {
          "id": "postgres-main",
          "name": "PostgreSQL - Main Database"
        }
      }
    },
    {
      "parameters": {
        "jsCode": "// =====================================================\n// METRICS COLLECTOR - PERFORMANCE TRACKING\n// =====================================================\n\nconst correlationId = $json.correlation_id;\nconst escalationData = $('update-escalation').first().json;\nconst startTime = new Date($('request-validator').first().json.received_at);\nconst endTime = new Date();\n\n// Calcular métricas de performance\nconst totalProcessingTime = endTime.getTime() - startTime.getTime();\nconst validationTime = $('request-validator').first().json.validation_time_ms || 0;\nconst aiAnalysisTime = $json.ai_analysis?.processing_time_ms || 0;\nconst routingTime = $json.routing_time_ms || 0;\n\n// Preparar métricas para inserção\nconst metrics = [\n  {\n    escalation_id: escalationData.id,\n    metric_type: 'performance',\n    metric_name: 'total_processing_time',\n    metric_value: totalProcessingTime,\n    unit: 'milliseconds',\n    component: 'workflow',\n    operation: 'end_to_end'\n  },\n  {\n    escalation_id: escalationData.id,\n    metric_type: 'performance',\n    metric_name: 'validation_time',\n    metric_value: validationTime,\n    unit: 'milliseconds',\n    component: 'validator',\n    operation: 'validate'\n  },\n  {\n    escalation_id: escalationData.id,\n    metric_type: 'performance',\n    metric_name: 'ai_analysis_time',\n    metric_value: aiAnalysisTime,\n    unit: 'milliseconds',\n    component: 'ai_analyzer',\n    operation: 'analyze'\n  },\n  {\n    escalation_id: escalationData.id,\n    metric_type: 'performance',\n    metric_name: 'routing_time',\n    metric_value: routingTime,\n    unit: 'milliseconds',\n    component: 'router',\n    operation: 'route'\n  },\n  {\n    escalation_id: escalationData.id,\n    metric_type: 'quality',\n    metric_name: 'ai_confidence_score',\n    metric_value: $json.ai_confidence_score,\n    unit: 'percentage',\n    component: 'ai_analyzer',\n    operation: 'analyze'\n  },\n  {\n    escalation_id: escalationData.id,\n    metric_type: 'business',\n    metric_name: 'priority_score',\n    metric_value: $json.priority_score,\n    unit: 'score',\n    component: 'router',\n    operation: 'prioritize'\n  }\n];\n\n// Adicionar métrica de cache se aplicável\nif ($json.cache_hit) {\n  metrics.push({\n    escalation_id: escalationData.id,\n    metric_type: 'efficiency',\n    metric_name: 'cache_hit',\n    metric_value: 1,\n    unit: 'boolean',\n    component: 'cache',\n    operation: 'lookup'\n  });\n}\n\nconsole.log(`[${correlationId}] Metrics collected`, {\n  totalTime: totalProcessingTime,\n  aiConfidence: $json.ai_confidence_score,\n  cacheHit: !!$json.cache_hit\n});\n\nreturn [{ json: { metrics, correlation_id: correlationId } }];"
      },
      "id": "metrics-collector",
      "name": "📊 Metrics: Collect Performance Data",
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [2880, 200]
    },
    {
      "parameters": {
        "operation": "executeQuery",
        "query": "INSERT INTO agent.escalation_metrics (escalation_id, metric_type, metric_name, metric_value, unit, component, operation, metadata) SELECT unnest($1::int[]), unnest($2::text[]), unnest($3::text[]), unnest($4::decimal[]), unnest($5::text[]), unnest($6::text[]), unnest($7::text[]), unnest($8::jsonb[])",
        "options": {
          "queryParameters": "={{ [\n            $json.metrics.map(m => m.escalation_id),\n            $json.metrics.map(m => m.metric_type),\n            $json.metrics.map(m => m.metric_name),\n            $json.metrics.map(m => m.metric_value),\n            $json.metrics.map(m => m.unit),\n            $json.metrics.map(m => m.component),\n            $json.metrics.map(m => m.operation),\n            $json.metrics.map(m => JSON.stringify({ correlation_id: $json.correlation_id, timestamp: new Date().toISOString() }))\n          ] }}"
        }
      },
      "id": "store-metrics",
      "name": "💾 DB: Store Metrics",
      "type": "n8n-nodes-base.postgres",
      "typeVersion": 2.4,
      "position": [3100, 200],
      "credentials": {
        "postgres": {
          "id": "postgres-main",
          "name": "PostgreSQL - Main Database"
        }
      }
    },
    {
      "parameters": {
        "respondWith": "json",
        "responseBody": "={{ {\n  success: true,\n  escalation_id: $('update-escalation').first().json.id,\n  correlation_id: $('update-escalation').first().json.correlation_id,\n  status: 'assigned',\n  assigned_queue: $('update-escalation').first().json.assigned_queue,\n  sla_deadline: $('update-escalation').first().json.sla_deadline,\n  priority_score: $('update-escalation').first().json.priority_score,\n  ai_confidence: $('update-escalation').first().json.ai_confidence_score,\n  chatwoot_conversation_id: $('update-escalation').first().json.chatwoot_conversation_id,\n  processing_time_ms: $('metrics-collector').first().json.metrics.find(m => m.metric_name === 'total_processing_time')?.metric_value,\n  message: 'Escalation processed successfully and assigned to specialized queue',\n  timestamp: new Date().toISOString()\n} }}",
        "options": {
          "responseCode": 201
        }
      },
      "id": "success-response",
      "name": "✅ Response: Success",
      "type": "n8n-nodes-base.respondToWebhook",
      "typeVersion": 1,
      "position": [3320, 200]
    },
    {
      "parameters": {
        "respondWith": "json",
        "responseBody": "={{ $json }}",
        "options": {
          "responseCode": 400
        }
      },
      "id": "error-response",
      "name": "❌ Response: Validation Error",
      "type": "n8n-nodes-base.respondToWebhook",
      "typeVersion": 1,
      "position": [680, 500]
    }
  ],
  "pinData": {},
  "connections": {
    "webhook-trigger": {
      "main": [
        [
          {
            "node": "request-validator",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "request-validator": {
      "main": [
        [
          {
            "node": "validation-check",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "validation-check": {
      "main": [
        [
          {
            "node": "cache-check",
            "type": "main",
            "index": 0
          }
        ],
        [
          {
            "node": "error-response",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "cache-check": {
      "main": [
        [
          {
            "node": "cache-hit-check",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "cache-hit-check": {
      "main": [
        [
          {
            "node": "cache-processor",
            "type": "main",
            "index": 0
          }
        ],
        [
          {
            "node": "ai-analyzer",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "ai-analyzer": {
      "main": [
        [
          {
            "node": "cache-store",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "cache-processor": {
      "main": [
        [
          {
            "node": "cache-update",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "cache-update": {
      "main": [
        [
          {
            "node": "intelligent-router",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "cache-store": {
      "main": [
        [
          {
            "node": "intelligent-router",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "intelligent-router": {
      "main": [
        [
          {
            "node": "persist-escalation",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "persist-escalation": {
      "main": [
        [
          {
            "node": "chatwoot-integration",
            "type": "main",
            "index": 0
          },
          {
            "node": "slack-notification-builder",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "chatwoot-integration": {
      "main": [
        [
          {
            "node": "update-escalation",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "slack-notification-builder": {
      "main": [
        [
          {
            "node": "slack-send",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "slack-send": {
      "main": [
        [
          {
            "node": "update-escalation",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "update-escalation": {
      "main": [
        [
          {
            "node": "metrics-collector",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "metrics-collector": {
      "main": [
        [
          {
            "node": "store-metrics",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "store-metrics": {
      "main": [
        [
          {
            "node": "success-response",
            "type": "main",
            "index": 0
          }
        ]
      ]
    }
  },
  "active": false,
  "settings": {
    "executionOrder": "v1",
    "saveManualExecutions": true,
    "callerPolicy": "workflowsFromSameOwner",
    "errorWorkflow": "error-handler-workflow"
  },
  "versionId": "1.0.0",
  "meta": {
    "templateCredsSetupCompleted": true,
    "instanceId": "intelligent-escalation-agent-v1"
  },
  "id": "intelligent-escalation-agent",
  "tags": [
    {
      "createdAt": "2024-01-15T10:00:00.000Z",
      "updatedAt": "2024-01-15T10:00:00.000Z",
      "id": "escalation",
      "name": "escalation"
    },
    {
      "createdAt": "2024-01-15T10:00:00.000Z",
      "updatedAt": "2024-01-15T10:00:00.000Z",
      "id": "ai-powered",
      "name": "ai-powered"
    },
    {
      "createdAt": "2024-01-15T10:00:00.000Z",
      "updatedAt": "2024-01-15T10:00:00.000Z",
      "id": "enterprise",
      "name": "enterprise"
    }
  ]
}