# 🚀 N8N + Evolution API + Chatwoot - Ambiente Completo de Automação

[![Docker](https://img.shields.io/badge/Docker-20.10+-blue.svg)](https://www.docker.com/)
[![PowerShell](https://img.shields.io/badge/PowerShell-5.1+-blue.svg)](https://docs.microsoft.com/en-us/powershell/)
[![License](https://img.shields.io/badge/License-MIT-green.svg)](LICENSE)

## 📋 Descrição

Stack completa de automação e atendimento ao cliente integrado, incluindo:

- **🤖 N8N** - Editor visual de workflows de automação
- **📱 Evolution API** - API robusta para integração WhatsApp
- **💬 Chatwoot** - Plataforma omnichannel de atendimento
- **🗄️ PostgreSQL** - Banco de dados principal com pgvector
- **⚡ Redis** - Cache e sessões otimizado
- **📦 MinIO** - Armazenamento S3 compatível

## ✨ Características Principais

### 🎯 **Dashboard Interativo**
- Interface web moderna com status em tempo real
- Credenciais organizadas e seguras
- Links diretos para todos os serviços
- Design responsivo e profissional

### 🔧 **Orquestração Robusta**
- Health-checks avançados para todos os serviços
- Dependências otimizadas entre containers
- Logs estruturados e diagnósticos automáticos
- Recuperação automática de falhas

### 🛡️ **Segurança**
- Senhas geradas automaticamente
- Configurações SSL/TLS prontas
- Isolamento de rede entre serviços
- Backup automático de dados

## 📋 **Pré-requisitos**

- **Windows 10/11** (Testado no Windows 10 build 26100)
- **Docker Desktop** 20.10+
- **PowerShell** 5.1+ (recomendado 7+)
- **Git** (para clonagem)

### **Instalação Inicial**
```powershell
git clone https://github.com/seu-usuario/N8N-evolution-V5.git
cd N8N-evolution-V5
```

## 🚀 **Fluxo de Instalação Automatizado**

### **📋 ETAPA 1: Start-Environment.ps1 - Infraestrutura Base**
```powershell
.\Start-Environment.ps1
```

**O que acontece automaticamente:**
- ✅ **Cria toda a infraestrutura** (PostgreSQL, Redis, MinIO, Evolution API, Chatwoot, N8N)
- ✅ **Gera credenciais automáticas** e salva no arquivo `.env`
- ✅ **Configura health checks** e dependências entre serviços
- ✅ **Cria dashboard HTML** interativo para monitoramento
- ✅ **Valida que todos os serviços** estão funcionando
- ✅ **NOVO: Detecta primeira instalação** e executa automaticamente o próximo passo

**Resultado da Etapa 1:**
```
✅ 7/7 serviços funcionais:
- PostgreSQL (com pgvector)
- Redis 
- MinIO (armazenamento S3)
- Evolution API (healthy)
- N8N (editor de workflows)
- Chatwoot Rails (backend)
- Chatwoot Sidekiq (processamento)
```

### **📋 ETAPA 2: post-setup-automation.ps1 - Configuração Avançada (AUTOMÁTICA)**

**🎯 NOVIDADE: Execução Automática na Primeira Instalação**

O script `Start-Environment.ps1` agora **detecta automaticamente** se é a primeira instalação e executa o `post-setup-automation.ps1` sem intervenção manual.

**O que acontece automaticamente:**
- ✅ **Cria instância Evolution API** com credenciais válidas
- ✅ **Configura MinIO S3** com buckets e políticas
- ✅ **Atualiza evolution.yml** com configurações corretas
- ✅ **Reinicia serviços** para aplicar configurações
- ✅ **Valida conectividade** de todos os componentes
- ✅ **Testa APIs** e endpoints

**Resultado da Etapa 2:**
```
✅ Evolution API: Instância criada e funcional
✅ MinIO: Configurado com S3 para Evolution
✅ PostgreSQL: Databases criadas e configuradas
✅ Sistema: 100% funcional e integrado
```

### **📋 ETAPA 3: Onboard-Workflow.ps1 - Workflows Específicos (OPCIONAL)**
```powershell
.\Onboard-Workflow.ps1 -WorkflowName "Enterprise Sales Machine"
```

**Quando usar:**
- Quando você tem workflows específicos que precisam de configuração SQL
- Para aplicar esquemas de banco de dados personalizados
- Para configurar workflows complexos com dependências

## 🎯 **Fluxo Simplificado para o Usuário**

### **🥇 PRIMEIRA INSTALAÇÃO (Totalmente Automática)**
```powershell
# 1. Execute apenas este comando:
.\Start-Environment.ps1

# 🎉 PRONTO! O sistema fará tudo automaticamente:
# - Cria infraestrutura
# - Gera credenciais  
# - Configura todos os serviços
# - Executa configuração avançada
# - Valida integração completa
```

**📋 ÚNICA ETAPA MANUAL RESTANTE:**
- Configurar webhook N8N ↔ Evolution API
- Acesse: http://localhost:5678 (N8N) e http://localhost:8080 (Evolution)  
- Configure webhook path: `/api/n8n-evolution`

### **🔄 INSTALAÇÕES SUBSEQUENTES**
```powershell
# 1. Para recriar ambiente:
.\Start-Environment.ps1

# 2. Para reconfigurar (se necessário):
.\post-setup-automation.ps1

# 3. Para workflows específicos:
.\Onboard-Workflow.ps1 -WorkflowName "NomeDoWorkflow"
```

## 🔧 **Detecção Inteligente de Primeira Instalação**

O sistema usa um arquivo marcador `.first-install-completed` para detectar se é a primeira execução:

- **✅ Primeira instalação:** Executa tudo automaticamente
- **🔄 Instalação existente:** Mostra opções manuais disponíveis
- **📅 Histórico:** Mantém registro de quando foi configurado

## 📊 **Status dos Serviços**

Acesse o dashboard interativo: `services-dashboard.html`

**URLs dos Serviços:**
- 🎯 **N8N:** http://localhost:5678
- 📱 **Evolution API:** http://localhost:8080/docs  
- 💬 **Chatwoot:** http://localhost:3000
- 📦 **MinIO Console:** http://localhost:9001

## 🔐 **Credenciais (Geradas Automaticamente)**

Todas as credenciais são salvas no arquivo `.env`:
- PostgreSQL: `postgres` / `[senha-gerada]`
- MinIO: `admin` / `[senha-gerada]`
- Evolution API: `[api-key-gerada]`
- Chatwoot: `<EMAIL>` / `[senha-gerada]`

## 🛠️ **Troubleshooting**

### **Se algo der errado:**
1. Verifique os logs: `install.log`
2. Execute manualmente: `.\post-setup-automation.ps1`
3. Para workflows: `.\Onboard-Workflow.ps1 -WorkflowName "Nome"`

### **Para reconfigurar completamente:**
```powershell
# Remover marcador de primeira instalação
Remove-Item .first-install-completed -Force

# Executar novamente
.\Start-Environment.ps1
```

## 📋 **Resumo do Fluxo Completo**

1. **Execute:** `.\Start-Environment.ps1`
2. **Aguarde:** Configuração automática completa
3. **Configure:** Webhook N8N ↔ Evolution (única etapa manual)
4. **Use:** Sistema 100% funcional!

**Tempo total:** ~5-10 minutos (primeira instalação)

## 🌐 Acesso aos Serviços

| Serviço | URL | Descrição |
|---------|-----|-----------|
| **Dashboard** | `services-dashboard.html` | Controle centralizado |
| **N8N** | http://localhost:5678 | Editor de workflows |
| **Evolution API** | http://localhost:8080 | API WhatsApp |
| **Evolution Manager** | http://localhost:8080/manager | Interface de gerenciamento |
| **Chatwoot** | http://localhost:3000 | Plataforma de atendimento |
| **MinIO Console** | http://localhost:9001 | Console de armazenamento |

## 📁 Estrutura do Projeto

```
├── Start-Environment.ps1              # Script principal de orquestração (v3.0)
├── post-setup-automation.ps1          # Script de configuração avançada (v3.0)
├── test-module.ps1                    # Script de validação do módulo
├── PowerShellModules/                 # 🆕 Módulos PowerShell centralizados
│   ├── AutomationUtils.psm1          # Módulo principal com funções utilitárias
│   └── README.md                      # Documentação da arquitetura modular
├── docker-compose.yml                 # Orquestração dos serviços
├── docker-compose.evolution-test.yml  # Arquivo para testar versões alternativas
├── services-dashboard.html            # Dashboard interativo (gerado)
├── REDIS-TROUBLESHOOTING.md          # Documentação de problemas conhecidos
├── .env                               # Variáveis de ambiente (gerado)
├── install.log                        # Logs da instalação (gerado)
└── README.md                          # Este arquivo
```

## 🔧 Configuração Avançada

### Variáveis de Ambiente

O arquivo `.env` é gerado automaticamente com senhas seguras. Principais variáveis:

```env
# Banco de Dados
POSTGRES_PASSWORD=<gerado_automaticamente>
POSTGRES_DB=postgres

# Autenticação
AUTHENTICATION_API_KEY=<gerado_automaticamente>

# MinIO
MINIO_ROOT_USER=admin
MINIO_ROOT_PASSWORD=<gerado_automaticamente>

# Chatwoot
SECRET_KEY_BASE=<gerado_automaticamente>
```

### Portas Utilizadas

| Serviço | Porta Interna | Porta Externa |
|---------|---------------|---------------|
| N8N | 5678 | 5678 |
| Evolution API | 8080 | 8080 |
| Chatwoot | 3000 | 3000 |
| PostgreSQL | 5432 | 5432 |
| Redis | 6379 | 6379 |
| MinIO API | 9000 | 9000 |
| MinIO Console | 9001 | 9001 |

## 🛠️ Comandos Úteis

### Gerenciamento dos Containers

```powershell
# Parar todos os serviços
docker compose down

# Parar e remover volumes (reset completo)
docker compose down -v

# Ver logs em tempo real
docker compose logs -f

# Reiniciar um serviço específico
docker compose restart <nome_do_serviço>

# Ver status dos containers
docker ps
```

### Diagnósticos

```powershell
# Testar conectividade Redis
docker exec redis_aula redis-cli ping

# Verificar logs da Evolution API
docker logs evolution_aula -f

# Testar API da Evolution
curl http://localhost:8080/

# Executar migração do Chatwoot manualmente
docker exec chatwoot-rails-1 bundle exec rails db:chatwoot_prepare
```

## 🐛 Solução de Problemas

### Problemas Conhecidos

1. **Evolution API com Redis**
   - Consulte `REDIS-TROUBLESHOOTING.md` para detalhes
   - Use `docker-compose.evolution-test.yml` para testar outras versões

2. **Erro SSL no Chatwoot**
   - Use http:// em vez de https:// para desenvolvimento
   - Limpe o cache do navegador

3. **Containers não sobem**
   - Verifique se as portas não estão em uso
   - Execute: `docker system prune -f`

### Logs e Diagnósticos

O script gera automaticamente:
- `install.log` - Log completo da instalação
- Dashboard com status em tempo real
- Diagnósticos automáticos em caso de falha

## 🤝 Contribuição

1. Faça um fork do projeto
2. Crie uma branch para sua feature (`git checkout -b feature/AmazingFeature`)
3. Commit suas mudanças (`git commit -m 'Add some AmazingFeature'`)
4. Push para a branch (`git push origin feature/AmazingFeature`)
5. Abra um Pull Request

## 📝 Changelog

### v3.0.0 (2025-01-09) - Arquitetura Modular Robusta
- 🆕 **Módulo PowerShell Centralizado**: AutomationUtils.psm1 com todas as funções utilitárias
- 🆕 **Reutilização Máxima de Código**: Eliminação completa de duplicação entre scripts
- 🆕 **Robustez Aprimorada**: Tratamento de erro consistente e validação de integridade
- 🆕 **Instruções Manuais Claras**: Guias detalhados para configuração de webhooks
- 🆕 **Script de Validação**: test-module.ps1 para verificar integridade do módulo
- 🆕 **Documentação Técnica**: README completo da arquitetura modular
- ✅ **Prevenção de Regressões**: Arquitetura que impede erros e inconsistências

### v1.0.0 (2025-01-09)
- ✅ Implementação inicial completa
- ✅ Dashboard interativo
- ✅ Orquestração robusta com health-checks
- ✅ Correção de problemas do Redis
- ✅ Geração automática de credenciais
- ✅ Documentação completa

## 📄 Licença

Este projeto está licenciado sob a MIT License - veja o arquivo [LICENSE](LICENSE) para detalhes.

## 🙏 Agradecimentos

- [N8N](https://n8n.io/) - Plataforma de automação
- [Evolution API](https://github.com/EvolutionAPI/evolution-api) - API WhatsApp
- [Chatwoot](https://www.chatwoot.com/) - Plataforma de atendimento
- Comunidade Docker e PowerShell

## 📞 Suporte

- 🐛 **Issues:** [GitHub Issues](https://github.com/Misael-art/-N8N-Evolution-Chatwoot---Misa/issues)
- 📖 **Documentação:** Consulte os arquivos `.md` do projeto
- 💬 **Discussões:** [GitHub Discussions](https://github.com/Misael-art/-N8N-Evolution-Chatwoot---Misa/discussions)

---

<div align="center">

**🌟 Se este projeto foi útil, considere dar uma estrela! ⭐**

</div> 