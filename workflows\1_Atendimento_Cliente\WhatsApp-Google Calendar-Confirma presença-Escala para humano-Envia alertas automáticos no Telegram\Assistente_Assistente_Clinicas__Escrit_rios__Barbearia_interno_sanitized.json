{"name": "Assistente Assistente Clinicas, Escritórios, Barbearia interno", "nodes": [{"parameters": {"model": {"__rl": true, "value": "gpt-4o-mini", "mode": "list", "cachedResultName": "gpt-4o-mini"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [160, 200], "id": "c20f1b5c-4ce7-44e7-a255-d320c3af3a00", "name": "OpenAI Chat Model1", "credentials": {}}, {"parameters": {"promptType": "define", "text": "={{ $json.message.text }}", "options": {"systemMessage": "=Hoje é {{$now}}\nPAPEL:  \nVocê é um assistente interno de reagendamento na clínica, acionado diretamente por um profissional via Telegram para gerenciar situações de remarcação de consultas ou incluir lembretes na lista de compras.\n\nOBJETIVO GERAL:  \n1. Reagendar consultas a pedido do profissional.  \n2. Adicionar lembretes na lista de compras quando solicitado.  \n\nRESUMO DE RESPONSABILIDADES:  \n1. Reagendamento de pacientes  \n   - Acesse o Google Calendar por meio da ferramenta \"MCP Google Calendar\" para identificar as consultas afetadas.  \n   - Extraia o número de telefone na descrição do evento.  \n   - Use a ferramenta \"Reagendar no WhatsApp\" para enviar mensagens de reagendamento aos pacientes.  \n   - Lembre-se de que você apenas envia a mensagem; a resposta do paciente é tratada por outro agente.  \n\n2. Lista de compras da clínica  \n   - Se o profissional solicitar pelo Telegram a inclusão de um item na lista de compras, utilize a ferramenta \"Google Tasks\" para adicionar o lembrete.  \n\nORIENTAÇÕES DE LINGUAGEM E PROCEDIMENTO:  \n- Use uma abordagem empática, profissional e acolhedora.  \n- Nunca envie mensagens para pacientes sem autorização explícita do profissional.  \n- Quando listar eventos ou tarefas, seja objetivo e organizado.  \n- Mantenha clareza e concisão em todas as interações.  \n\nFERRAMENTAS DISPONÍVEIS:  \n- Reagendar no WhatsApp  \n- Google Tasks  \n- MCP Google Calendar  \n\nINSTRUÇÕES FINAIS:  \n- Atenda exclusivamente às solicitações de reagendamento e inclusão de lembretes.  \n- A remarcação de consultas ocorre somente quando o profissional pede, utilizando o MCP Google Calendar para identificar os pacientes e o \"Reagendar no WhatsApp\" para enviar a mensagem.  \n- Para a lista de compras, sempre registre no \"Google Tasks\".  \n"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.8, "position": [280, -40], "id": "80e3cf22-069f-4e64-90dd-55b22d3bfb3b", "name": "Assistente clinica interno"}, {"parameters": {"task": "bDQ5ZlNVV2lPQ3pYT3NsNA", "title": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Title', ``, 'string') }}", "additionalFields": {"notes": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Notes', ``, 'string') }}", "status": "needsAction"}}, "type": "n8n-nodes-base.googleTasksTool", "typeVersion": 1, "position": [760, 200], "id": "b94c0a34-2eeb-4ff7-9c16-84dbc6b0153e", "name": "Google Tasks", "credentials": {}}, {"parameters": {"toolDescription": "Use essa ferramenta para enviar WhatsApp", "method": "POST", "url": "https://api.z-api.io/instances/REDACTED/token/REDACTED/send-button-list", "sendHeaders": true, "parametersHeaders": {"values": [{"name": "client-token", "valueProvider": "fieldValue", "value": "REDACTED"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "{\n  \"phone\": \"{telefone}\",\n  \"message\": \"<PERSON><PERSON><PERSON>! Aqui é da Clínica Moreira. Estamos entrando em contato para informar que, infelizmente, o <PERSON><PERSON> <PERSON> *não poderá realizar sua consulta na data agendada* devido a um imprevisto de última hora.\\n\\nPedimos desculpas pelo transtorno e agradecemos sua compreensão. Por favor, clique no botão abaixo para reagendarmos sua consulta o quanto antes.\",\n  \"buttonList\": {\n    \"buttons\": [\n      {\n        \"id\": \"{id_evento_google_calendar}\",\n        \"label\": \"Reagendar\"\n      }\n    ]\n  }\n}\n"}, "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "typeVersion": 1.1, "position": [620, 200], "id": "8a7706cc-59fc-48c3-bbaa-fcfb523f9033", "name": "Reagendar no WhatsApp"}, {"parameters": {"sseEndpoint": "https://n8n.fazer.ai/mcp/google-calendar/sse"}, "type": "@n8n/n8n-nodes-langchain.mcpClientTool", "typeVersion": 1, "position": [480, 200], "id": "caf133fa-235e-429f-89fb-8328308041ce", "name": "MCP Google Calendar"}, {"parameters": {"chatId": "1239148307", "text": "={{ $json.output }}", "additionalFields": {}}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [740, -40], "id": "4c6dd82f-26a5-45e3-a7e9-e43a75a63296", "name": "Responder Telegram", "webhookId": "21855174-4f7f-49f5-b8f4-a284d6ee4ddf", "credentials": {}}, {"parameters": {"updates": ["message"], "additionalFields": {}}, "type": "n8n-nodes-base.telegramTrigger", "typeVersion": 1.2, "position": [0, -40], "id": "118bcbb7-9f29-4e9e-b1a6-7232edf4a2d3", "name": "Receber Mensagem Telegram", "webhookId": "f2b29356-d5d3-4f5d-9ef1-273001c0a820", "credentials": {}}, {"parameters": {"sessionIdType": "customKey", "sessionKey": "100", "contextWindowLength": 10}, "type": "@n8n/n8n-nodes-langchain.memoryPostgresChat", "typeVersion": 1.3, "position": [340, 200], "id": "58a78eaa-6ce5-4713-9177-da137a2aefc6", "name": "Postgres Chat Memory", "credentials": {}}], "pinData": {}, "connections": {"OpenAI Chat Model1": {"ai_languageModel": [[{"node": "Assistente clinica interno", "type": "ai_languageModel", "index": 0}]]}, "Assistente clinica interno": {"main": [[{"node": "Responder Telegram", "type": "main", "index": 0}]]}, "Google Tasks": {"ai_tool": [[{"node": "Assistente clinica interno", "type": "ai_tool", "index": 0}]]}, "Reagendar no WhatsApp": {"ai_tool": [[{"node": "Assistente clinica interno", "type": "ai_tool", "index": 0}]]}, "MCP Google Calendar": {"ai_tool": [[{"node": "Assistente clinica interno", "type": "ai_tool", "index": 0}]]}, "Receber Mensagem Telegram": {"main": [[{"node": "Assistente clinica interno", "type": "main", "index": 0}]]}, "Postgres Chat Memory": {"ai_memory": [[{"node": "Assistente clinica interno", "type": "ai_memory", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1"}, "versionId": "ef2f393b-ca05-4ce6-b2af-8dee99352e85", "meta": {"templateCredsSetupCompleted": true, "instanceId": "0b49d177f34e66e4cb02bb7fd4f3a4af9f44ac0cbad00917db36a2465834ec4e"}, "id": "iWOzFdQEpjWgOsJO", "tags": [{"createdAt": "2025-04-12T19:24:26.346Z", "updatedAt": "2025-04-12T19:24:26.346Z", "id": "nzjmNiBtIYWd12Cd", "name": "credencial-lucas"}, {"createdAt": "2025-04-12T22:46:24.076Z", "updatedAt": "2025-04-12T22:46:24.076Z", "id": "6mN2rPIbFGcvOjzP", "name": "videoyoutube"}]}