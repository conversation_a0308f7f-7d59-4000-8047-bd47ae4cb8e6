# Generate-ServiceDashboard.psm1
# Módulo para geração de dashboard HTML dos serviços

function Generate-ServiceDashboard {
    param(
        [Parameter(Mandatory = $true)]
        [array]$Services,
        
        [Parameter(Mandatory = $false)]
        [string]$OutputPath = "services-dashboard.html",
        
        [Parameter(Mandatory = $false)]
        [bool]$EvolutionHasRedisIssue = $false,
        
        [Parameter(Mandatory = $false)]
        [hashtable]$EnvCredentials = @{}
    )
    
    # Template HTML base
    $htmlTemplate = @'
<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Dashboard de Serviços - N8N Evolution</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
      padding: 20px;
    }
    
    .container {
      max-width: 1400px;
      margin: 0 auto;
    }
    
    .header {
      text-align: center;
      color: white;
      margin-bottom: 30px;
    }
    
    .header h1 {
      font-size: 2.5rem;
      margin-bottom: 10px;
      text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    }
    
    .header p {
      font-size: 1.1rem;
      opacity: 0.9;
    }
    
    .controls {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 30px;
      background: rgba(255,255,255,0.1);
      padding: 15px 20px;
      border-radius: 10px;
      backdrop-filter: blur(10px);
    }
    
    .refresh-btn {
      background: #28a745;
      color: white;
      border: none;
      padding: 12px 24px;
      border-radius: 8px;
      cursor: pointer;
      font-size: 1rem;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      gap: 8px;
    }
    
    .refresh-btn:hover {
      background: #218838;
      transform: translateY(-2px);
    }
    
    .refresh-btn:disabled {
      background: #6c757d;
      cursor: not-allowed;
      transform: none;
    }
    
    .timestamp {
      color: white;
      font-size: 0.9rem;
      opacity: 0.8;
    }
    
    .warning-banner {
      background: #ff6b6b;
      color: white;
      padding: 15px;
      border-radius: 8px;
      margin-bottom: 20px;
      display: none;
      animation: pulse 2s infinite;
    }
    
    @keyframes pulse {
      0% { opacity: 1; }
      50% { opacity: 0.7; }
      100% { opacity: 1; }
    }
    
    .services-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
      gap: 20px;
    }
    
    .service-card {
      background: rgba(255,255,255,0.95);
      border-radius: 15px;
      padding: 25px;
      box-shadow: 0 8px 32px rgba(0,0,0,0.1);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255,255,255,0.2);
      transition: all 0.3s ease;
    }
    
    .service-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 12px 40px rgba(0,0,0,0.15);
    }
    
    .service-header {
      display: flex;
      align-items: center;
      gap: 12px;
      margin-bottom: 15px;
    }
    
    .status-dot {
      width: 16px;
      height: 16px;
      border-radius: 50%;
      transition: all 0.3s ease;
    }
    
    .status-dot.online {
      background: #28a745;
      box-shadow: 0 0 10px rgba(40, 167, 69, 0.5);
    }
    
    .status-dot.offline {
      background: #dc3545;
      box-shadow: 0 0 10px rgba(220, 53, 69, 0.5);
    }
    
    .status-dot.checking {
      background: #ffc107;
      animation: pulse 1s infinite;
    }
    
    .service-name {
      font-size: 1.3rem;
      font-weight: 600;
      color: #2c3e50;
    }
    
    .service-desc {
      color: #6c757d;
      line-height: 1.5;
      margin-bottom: 15px;
      font-size: 0.95rem;
    }
    
    .service-url {
      display: inline-block;
      background: #007bff;
      color: white;
      padding: 8px 16px;
      border-radius: 6px;
      text-decoration: none;
      font-size: 0.9rem;
      transition: all 0.3s ease;
      margin-bottom: 15px;
    }
    
    .service-url:hover {
      background: #0056b3;
      transform: translateY(-1px);
    }
    
    .credentials {
      margin-top: 15px;
    }
    
    .cred-toggle {
      background: #6c757d;
      color: white;
      border: none;
      padding: 6px 12px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 0.8rem;
      transition: all 0.3s ease;
    }
    
    .cred-toggle:hover {
      background: #5a6268;
    }
    
    .cred-info {
      margin-top: 10px;
      padding: 10px;
      background: #f8f9fa;
      border-radius: 6px;
      font-family: 'Courier New', monospace;
      font-size: 0.85rem;
      border-left: 4px solid #007bff;
    }
    
    .cred-info.hidden {
      display: none;
    }
    
    .extra-info {
      margin-top: 10px;
      padding: 10px;
      background: #e9ecef;
      border-radius: 6px;
      font-size: 0.85rem;
    }
    
    .extra-info strong {
      color: #495057;
    }
    
    @media (max-width: 768px) {
      .services-grid {
        grid-template-columns: 1fr;
      }
      
      .controls {
        flex-direction: column;
        gap: 15px;
        text-align: center;
      }
      
      .header h1 {
        font-size: 2rem;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>🚀 Dashboard de Serviços</h1>
      <p>Ambiente de Automação N8N + Evolution API</p>
    </div>
    
    <div class="controls">
      <button id="refreshBtn" class="refresh-btn" onclick="checkAllServices()">
        🔄 Verificar Status dos Serviços
      </button>
      <div class="timestamp">
        Última atualização: <span id="timestamp"></span>
      </div>
    </div>
    
    <div id="warningBanner" class="warning-banner">
      ⚠️ <strong>Problema Conhecido:</strong> Evolution API apresenta instabilidade com Redis. 
      A API funciona mas pode falhar na criação de instâncias WhatsApp.
    </div>
    
    <div id="servicesGrid" class="services-grid">
      <!-- Services will be populated by JavaScript -->
    </div>
  </div>

  <script>
    const services = [
      SERVICE_DATA_PLACEHOLDER
    ];

    function createServiceCard(service) {
      let credentialsHtml = '';
      if (service.username || service.password) {
        credentialsHtml = `
          <div class="credentials">
            <button class="cred-toggle" onclick="toggleCredentials(this)">🔑 Mostrar</button>
            <div class="cred-info hidden">
              <strong>Usuário:</strong> ${service.username || 'N/A'}<br>
              <strong>Senha:</strong> ${service.password || 'N/A'}
            </div>
          </div>
        `;
      }
      
      let extraInfoHtml = '';
      if (service.extraInfo && typeof service.extraInfo === 'object') {
        const extraItems = Object.entries(service.extraInfo)
          .map(([key, value]) => `<strong>${key}:</strong> ${value}`)
          .join('<br>');
        extraInfoHtml = `<div class="extra-info">${extraItems}</div>`;
      }

      return `
        <div class="service-card" data-service="${service.container}">
          <div class="service-header">
            <span class="status-dot" id="status-${service.container}"></span>
            <h3 class="service-name">${service.name}</h3>
          </div>
          <p class="service-desc">${service.description}</p>
          <a href="${service.url}" target="_blank" class="service-url">${service.url}</a>
          ${credentialsHtml}
          ${extraInfoHtml}
        </div>
      `;
    }

    function toggleCredentials(button) {
      const credInfo = button.parentElement.nextElementSibling;
      if (credInfo.classList.contains('hidden')) {
        credInfo.classList.remove('hidden');
        button.textContent = '🙈 Ocultar';
      } else {
        credInfo.classList.add('hidden');
        button.textContent = '🔑 Mostrar';
      }
    }

    async function checkServiceStatus(service) {
      const statusDot = document.getElementById(`status-${service.container}`);
      statusDot.className = 'status-dot checking';
      
      try {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 5000);
        
        const response = await fetch(service.health || service.url, {
          method: 'HEAD',
          mode: 'no-cors',
          signal: controller.signal
        });
        
        clearTimeout(timeoutId);
        statusDot.className = 'status-dot online';
        
        // Check for Evolution API Redis issues
        if (service.container === 'evolution_aula' && window.evolutionHasRedisIssue) {
          statusDot.className = 'status-dot offline';
          document.getElementById('warningBanner').style.display = 'block';
        }
      } catch (error) {
        statusDot.className = 'status-dot offline';
      }
    }

    async function checkAllServices() {
      const button = document.getElementById('refreshBtn');
      button.textContent = '⏳ Verificando...';
      button.disabled = true;
      
      const promises = services.map(service => checkServiceStatus(service));
      await Promise.all(promises);
      
      button.textContent = '🔄 Verificar Status dos Serviços';
      button.disabled = false;
    }

    function formatTimestamp() {
      const now = new Date();
      return now.toLocaleString('pt-BR', {
        dateStyle: 'full',
        timeStyle: 'medium'
      });
    }

    function initializeDashboard() {
      const grid = document.getElementById('servicesGrid');
      grid.innerHTML = services.map(service => createServiceCard(service)).join('');
      
      document.getElementById('timestamp').textContent = formatTimestamp();
      
      // Set Evolution Redis issue flag if present
      window.evolutionHasRedisIssue = EVOLUTION_REDIS_ISSUE_PLACEHOLDER;
      
      checkAllServices();
      
      // Auto-refresh every 30 seconds
      setInterval(checkAllServices, 30000);
    }

    document.addEventListener('DOMContentLoaded', initializeDashboard);
  </script>
</body>
</html>
'@

    # Função auxiliar para escapar HTML
    function Safe-HtmlEncode {
        param([string]$Text)
        if (-not $Text) { return "" }
        return $Text -replace '&', '&amp;' -replace '<', '&lt;' -replace '>', '&gt;' -replace '"', '&quot;' -replace "'", '&#39;'
    }

    # Gerar dados JavaScript dos serviços
    $jsServices = @()
    foreach ($service in $Services) {
        $serviceName = Safe-HtmlEncode $service.Name
        $serviceUrl = Safe-HtmlEncode $service.Url
        $serviceDesc = Safe-HtmlEncode $service.Desc
        $serviceUser = Safe-HtmlEncode $service.User
        $servicePwd = Safe-HtmlEncode $service.Pwd
        $serviceContainer = Safe-HtmlEncode $service.Container
        $serviceHealth = Safe-HtmlEncode ($service.Health -or $service.Url)
        
        # Processar informações extras
        $extraInfoJs = "null"
        if ($service.ExtraInfo -and $service.ExtraInfo.Count -gt 0) {
            $extraItems = @()
            foreach ($key in $service.ExtraInfo.Keys) {
                $safeKey = Safe-HtmlEncode $key
                $safeValue = Safe-HtmlEncode $service.ExtraInfo[$key]
                $extraItems += "'$safeKey': '$safeValue'"
            }
            $extraInfoJs = "{ $($extraItems -join ', ') }"
        }
        
        $jsServices += @"
{
        name: '$serviceName',
        url: '$serviceUrl',
        description: '$serviceDesc',
        username: '$serviceUser',
        password: '$servicePwd',
        container: '$serviceContainer',
        health: '$serviceHealth',
        extraInfo: $extraInfoJs
      }
"@
    }

    $serviceDataJs = $jsServices -join ",`n      "
    $evolutionRedisIssueJs = if ($EvolutionHasRedisIssue) { "true" } else { "false" }

    $finalHtml = $htmlTemplate -replace 'SERVICE_DATA_PLACEHOLDER', $serviceDataJs
    $finalHtml = $finalHtml -replace 'EVOLUTION_REDIS_ISSUE_PLACEHOLDER', $evolutionRedisIssueJs

    # Salvar dashboard
    try {
        $finalHtml | Out-File -FilePath $OutputPath -Encoding UTF8 -NoNewline
        Write-Host "✓ Dashboard HTML gerado: $OutputPath" -ForegroundColor Green
        return $true
    }
    catch {
        Write-Host "✗ Erro ao gerar dashboard: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Exportar a função
Export-ModuleMember -Function Generate-ServiceDashboard