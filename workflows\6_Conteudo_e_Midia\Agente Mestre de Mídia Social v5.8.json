{"name": "Agente Mestre de Mídia Social v5.8.1 (Edição Final, Corrigida e REALMENTE Completa)", "nodes": [{"parameters": {"workflowInputs": {"values": [{"name": "user_id", "type": "string", "value": "user_v5.8.1_final_test"}, {"name": "network", "type": "string", "value": "TikTok"}, {"name": "task", "type": "string", "value": "create_post"}, {"name": "task_details", "type": "json", "json": "{ \"topic\": \"Latest AI Trends\", \"cache_key\": \"tiktok_post_aitrends_v581\" }"}]}}, "type": "n8n-nodes-base.executeWorkflowTrigger", "typeVersion": 1.1, "position": [-2000, 600], "id": "START_NODE", "name": "Start"}, {"parameters": {"content": "### 🚀 Agente Mestre de Mídia Social v5.8.1 (A Edição Final, Corrigida e REALMENTE Completa)\n\n**Propósito**: Versão que implementa cache completo para todas as redes e tarefas, corrige o fluxo de criação de post do TikTok para usar um AGENTE e a API diretamente, e adiciona análise de sentimento faltante.\n\n**Arquitetura**: \n1. **Entrada e Validação Global**.\n2. **Rote<PERSON> por Rede (Principal)**.\n3. **Módulos de Rede Social (x5)**:\n    - **Roteador de Tarefa (Interno)**: Direciona para a tarefa correta.\n    - **Fluxos de Tarefa Dedicados**: CADA tarefa tem seu próprio workflow completo (Validação -> Auth -> An<PERSON>lis<PERSON> de Sentimento (p/ Create Post) -> <PERSON>ache Check -> (Miss: Agente -> Tool -> Cache <PERSON>) -> Log).\n4. **Notificação e Saída Unificada**.", "height": 580, "width": 540, "color": 10}, "type": "n8n-nodes-base.stickyNote", "position": [-2040, -220], "typeVersion": 1, "id": "V5_NOTE_ARCH", "name": "Nota da Arquitetura v5.8.1"}, {"parameters": {"routing": {"rules": {"values": [{"output": 0, "operation": "equal", "value1": "={{ $json.network }}", "value2": "LinkedIn"}, {"output": 1, "operation": "equal", "value1": "={{ $json.network }}", "value2": "Instagram"}, {"output": 2, "operation": "equal", "value1": "={{ $json.network }}", "value2": "X"}, {"output": 3, "operation": "equal", "value1": "={{ $json.network }}", "value2": "TikTok"}, {"output": 4, "operation": "equal", "value1": "={{ $json.network }}", "value2": "Facebook"}]}, "fieldToMatch": "={{ $json.network }}"}}, "id": "V5_ROUTE_NETWORK", "name": "Route by Network", "type": "n8n-nodes-base.switch", "typeVersion": 2, "position": [-1520, 600]}, {"parameters": {"values": {"string": []}, "options": {"action": "merge"}}, "id": "V5_MERGE_FINAL", "name": "Final Merge Point", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [1860, 600]}, {"parameters": {"values": {"string": []}, "options": {}}, "id": "V5_ERROR_FORMAT", "name": "[ERROR] Format Error Message", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [-1280, 1800]}, {"parameters": {}, "id": "V5_ERROR_TRIGGER", "name": "<PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.errorTrigger", "typeVersion": 1, "position": [-1520, 1800]}, {"parameters": {"values": {"string": []}, "options": {}}, "id": "V5_ERROR_NOTIFY", "name": "[ALERT] Notify Admin via Chat/Email", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [-1080, 1800]}, {"parameters": {"values": {"string": []}, "options": {}}, "id": "V5_OUTPUT_USER", "name": "[OUTPUT] Send Result to User", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [2100, 600]}, {"parameters": {"values": {"string": []}, "options": {}}, "id": "V5_FEEDBACK_CHATWOOT", "name": "[FEEDBACK] Request Feedback via Chatwoot", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [2340, 600]}, {"parameters": {"mode": "everyX", "unit": "hours"}, "id": "V51_SCHEDULE_TRIGGER", "name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1.1, "position": [-2000, 380]}, {"parameters": {"values": {"string": []}, "options": {}}, "id": "V55_VALIDATE_INFO", "name": "[TOOL] Validate External Info", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [-1760, 600]}, {"parameters": {"values": {"string": [{"name": "network", "value": "LinkedIn"}, {"name": "task", "value": "generate_performance_report"}, {"name": "task_details", "value": "={{ { \"time_period_days\": 7, \"cache_key\": \"li_report_7d_v581\" } }}"}]}, "options": {}}, "id": "V53_PREPARE_SCHEDULE", "name": "Prepare Scheduled Task", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [-1760, 380]}, {"parameters": {"values": {"string": []}, "options": {}}, "id": "V55_ANALYZE_MEDIA", "name": "[AI_TOOL] Analyze Media", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [-1760, 780]}, {"parameters": {"values": {"string": []}, "options": {}}, "id": "V55_EVOLUTION_API_NOTIFY", "name": "[TOOL] Send Result via Evolution API", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [1620, 600]}, {"parameters": {"routing": {"rules": {"values": [{"output": 0, "operation": "equal", "value1": "={{ $json.task }}", "value2": "create_post"}, {"output": 1, "operation": "equal", "value1": "={{ $json.task }}", "value2": "generate_performance_report"}, {"output": 2, "operation": "equal", "value1": "={{ $json.task }}", "value2": "respond_to_comment"}]}, "fieldToMatch": "={{ $json.task }}"}}, "id": "V54_ROUTE_LINKEDIN_TASK", "name": "Route LinkedIn Task", "type": "n8n-nodes-base.switch", "typeVersion": 2, "position": [-1280, 380]}, {"parameters": {"conditions": {"boolean": [{"value1": "={{ $json.task_details.topic }}", "operation": "isset"}]}}, "id": "V54_VALIDATE_LINKEDIN_POST", "name": "Validate: LinkedIn Post", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [-1040, 380]}, {"parameters": {"conditions": {"boolean": [{"value1": "={{ $json.task_details.time_period_days }}", "operation": "isset"}]}}, "id": "V54_VALIDATE_LINKEDIN_REPORT", "name": "Validate: LinkedIn Report", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [-1040, 260]}, {"parameters": {"conditions": {"boolean": [{"value1": "={{ $json.task_details.comment_id }}", "operation": "isset"}]}}, "id": "V56_VALIDATE_LINKEDIN_COMMENT", "name": "Validate: LinkedIn Comment", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [-1040, 500]}, {"parameters": {"values": {"string": []}, "options": {}}, "id": "V54_AUTH_LINKEDIN", "name": "[CACHE] Get LinkedIn Token", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [-800, 320]}, {"parameters": {"conditions": {"boolean": [{"value1": "={{ $json.token_expired }}", "operation": "equal", "value2": true}]}}, "id": "V54_CHECK_TOKEN_LINKEDIN", "name": "Check if LinkedIn Token Expired?", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [-580, 320]}, {"parameters": {"values": {"string": []}, "options": {}}, "id": "V54_RENEW_TOKEN_LINKEDIN", "name": "[AUTH] Renew LinkedIn Token", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [-340, 260]}, {"parameters": {"values": {"string": [{"name": "sentiment", "value": "professional"}]}, "options": {}}, "id": "V54_SENTIMENT_LINKEDIN_POST", "name": "[AI_TOOL] Analyze Sentiment", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [-200, 380]}, {"parameters": {"conditions": {"boolean": [{"value1": "={{ $json.cache_hit }}", "operation": "equal", "value2": true}]}}, "id": "V58_CACHE_CHECK_LINKEDIN_POST", "name": "[CACHE] Check Post Cache", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [0, 380]}, {"parameters": {"agent": "conversationalAgent", "promptType": "define", "text": "Detalhes: {{ JSON.stringify($('Start').first().json.task_details) }}\nSentimento: {{ $('V54_SENTIMENT_LINKEDIN_POST').first().json.sentiment }}", "options": {"systemMessage": "Você é um especialista em LinkedIn. Crie um post profissional sobre o `topic`. Use um tom formal e inclua 3-5 hashtags relevantes. Ajuste ao sentimento."}}, "id": "V51_AGENT_LINKEDIN_POST", "name": "Agent: Create LinkedIn Post", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "position": [240, 380]}, {"parameters": {"values": {"string": []}, "options": {}}, "id": "V58_CACHE_SAVE_LINKEDIN_POST", "name": "[CACHE] Save Post to Cache", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [480, 380]}, {"parameters": {"values": {"string": []}, "options": {}}, "id": "V51_TOOL_LINKEDIN_POST", "name": "[TOOL] Post to LinkedIn API", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [700, 380]}, {"parameters": {"values": {"string": [{"name": "log_entry", "value": "={{ { \"user_id\": $json.user_id, \"network\": \"LinkedIn\", \"timestamp\": $now, \"action\": \"post_created\", \"details\": $json.task_details, \"success\": true, \"cached_result\": $json.cache_hit === true } }}"}]}, "options": {}}, "id": "V51_LOG_LINKEDIN_POST", "name": "[DB] Save Action Log", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [920, 380]}, {"parameters": {"conditions": {"boolean": [{"value1": "={{ $json.cache_hit }}", "operation": "equal", "value2": true}]}}, "id": "V54_CACHE_CHECK_LINKEDIN_REPORT", "name": "[CACHE] Check Report Cache", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [-200, 260]}, {"parameters": {"values": {"string": []}, "options": {}}, "id": "V54_TOOL_GET_LINKEDIN_ANALYTICS", "name": "[TOOL] Get LinkedIn Analytics", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [0, 260]}, {"parameters": {"agent": "conversationalAgent", "promptType": "define", "text": "Dados: {{ JSON.stringify($('V54_TOOL_GET_LINKEDIN_ANALYTICS').first().json) }}\nPeríodo: {{ $json.task_details.time_period_days }} dias.", "options": {"systemMessage": "Você é um analista de dados do LinkedIn. Analise os dados fornecidos e gere um relatório de performance executivo para o período especificado. Destaque KPIs importantes e forneça insights."}}, "id": "V54_AGENT_LINKEDIN_REPORT", "name": "Agent: Write LinkedIn Report", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "position": [240, 260]}, {"parameters": {"values": {"string": []}, "options": {}}, "id": "V54_CACHE_SAVE_LINKEDIN_REPORT", "name": "[CACHE] Save Report to Cache", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [480, 260]}, {"parameters": {"values": {"string": [{"name": "log_entry", "value": "={{ { \"user_id\": $json.user_id, \"network\": \"LinkedIn\", \"timestamp\": $now, \"action\": \"report_generated\", \"details\": $json.task_details, \"success\": true, \"cached_result\": $json.cache_hit === true } }}"}]}, "options": {}}, "id": "V54_LOG_LINKEDIN_REPORT", "name": "[DB] Save Action Log", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [700, 260]}, {"parameters": {"conditions": {"boolean": [{"value1": "={{ $json.cache_hit }}", "operation": "equal", "value2": true}]}}, "id": "V58_CACHE_CHECK_LINKEDIN_COMMENT", "name": "[CACHE] Check Comment Cache", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [-200, 500]}, {"parameters": {"agent": "conversationalAgent", "promptType": "define", "text": "Comentário: {{ $json.task_details.comment_text }}", "options": {"systemMessage": "Você é um especialista em LinkedIn. Responda a este comentário de forma profissional e útil."}}, "id": "V57_AGENT_LINKEDIN_COMMENT", "name": "Agent: <PERSON><PERSON><PERSON> to Comment", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "position": [0, 500]}, {"parameters": {"values": {"string": []}, "options": {}}, "id": "V58_CACHE_SAVE_LINKEDIN_COMMENT", "name": "[CACHE] Save Comment to Cache", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [240, 500]}, {"parameters": {"values": {"string": []}, "options": {}}, "id": "V57_TOOL_POST_LINKEDIN_COMMENT", "name": "[TOOL] Post Comment to LinkedIn", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [480, 500]}, {"parameters": {"values": {"string": [{"name": "log_entry", "value": "={{ { \"user_id\": $json.user_id, \"network\": \"LinkedIn\", \"timestamp\": $now, \"action\": \"comment_replied\", \"details\": $json.task_details, \"success\": true, \"cached_result\": $json.cache_hit === true } }}"}]}, "options": {}}, "id": "V57_LOG_LINKEDIN_COMMENT", "name": "[DB] Save Action Log", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [700, 500]}, {"parameters": {"routing": {"rules": {"values": [{"output": 0, "operation": "equal", "value1": "={{ $json.task }}", "value2": "create_post"}, {"output": 1, "operation": "equal", "value1": "={{ $json.task }}", "value2": "generate_performance_report"}, {"output": 2, "operation": "equal", "value1": "={{ $json.task }}", "value2": "respond_to_comment"}]}, "fieldToMatch": "={{ $json.task }}"}}, "id": "V54_ROUTE_INSTAGRAM_TASK", "name": "Route Instagram Task", "type": "n8n-nodes-base.switch", "typeVersion": 2, "position": [-1280, 140]}, {"parameters": {"conditions": {"boolean": [{"value1": "={{ $json.task_details.topic }}", "operation": "isset"}]}}, "id": "V54_VALIDATE_INSTAGRAM_POST", "name": "Validate: Instagram Post", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [-1040, 140]}, {"parameters": {"conditions": {"boolean": [{"value1": "={{ $json.task_details.time_period_days }}", "operation": "isset"}]}}, "id": "V55_VALIDATE_INSTAGRAM_REPORT", "name": "Validate: Instagram Report", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [-1040, 0]}, {"parameters": {"conditions": {"boolean": [{"value1": "={{ $json.task_details.comment_id }}", "operation": "isset"}]}}, "id": "V55_VALIDATE_INSTAGRAM_COMMENT", "name": "Validate: Instagram Comment", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [-1040, -120]}, {"parameters": {"values": {"string": []}, "options": {}}, "id": "V54_AUTH_INSTAGRAM", "name": "[CACHE] Get Instagram Token", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [-800, 80]}, {"parameters": {"conditions": {"boolean": [{"value1": "={{ $json.token_expired }}", "operation": "equal", "value2": true}]}}, "id": "V54_CHECK_TOKEN_INSTAGRAM", "name": "Check if Instagram Token Expired?", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [-580, 80]}, {"parameters": {"values": {"string": []}, "options": {}}, "id": "V54_RENEW_TOKEN_INSTAGRAM", "name": "[AUTH] Renew Instagram Token", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [-340, 80]}, {"parameters": {"values": {"string": [{"name": "sentiment", "value": "positive"}]}, "options": {}}, "id": "V54_SENTIMENT_INSTAGRAM_POST", "name": "[AI_TOOL] Analyze Sentiment", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [-200, 140]}, {"parameters": {"conditions": {"boolean": [{"value1": "={{ $json.cache_hit }}", "operation": "equal", "value2": true}]}}, "id": "V58_CACHE_CHECK_INSTAGRAM_POST", "name": "[CACHE] Check Post Cache", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [0, 140]}, {"parameters": {"agent": "conversationalAgent", "promptType": "define", "text": "Detalhes: {{ JSON.stringify($('Start').first().json.task_details) }}\nSentimento: {{ $('V54_SENTIMENT_INSTAGRAM_POST').first().json.sentiment }}", "options": {"systemMessage": "Você é um criador de conteúdo para Instagram. Sua tarefa é gerar uma ideia para um post sobre o `topic`. Descreva a imagem, escreva uma legenda envolvente com CTA, e sugira 7-10 hashtags. Ajuste o tom da legenda ao sentimento detectado."}}, "id": "V5_AGENT_INSTAGRAM_POST", "name": "Agent: Create Instagram Post", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "position": [240, 140]}, {"parameters": {"values": {"string": []}, "options": {}}, "id": "V58_CACHE_SAVE_INSTAGRAM_POST", "name": "[CACHE] Save Post to Cache", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [480, 140]}, {"parameters": {"values": {"string": []}, "options": {}}, "id": "V5_TOOL_POST_INSTAGRAM", "name": "[TOOL] Post to Instagram API", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [700, 140]}, {"parameters": {"values": {"string": [{"name": "log_entry", "value": "={{ { \"user_id\": $json.user_id, \"network\": \"Instagram\", \"timestamp\": $now, \"action\": \"post_created\", \"details\": $json.task_details, \"success\": true, \"cached_result\": $json.cache_hit === true } }}"}]}, "options": {}}, "id": "V5_LOG_INSTAGRAM_POST", "name": "[DB] Save Action Log", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [920, 140]}, {"parameters": {"conditions": {"boolean": [{"value1": "={{ $json.cache_hit }}", "operation": "equal", "value2": true}]}}, "id": "V58_CACHE_CHECK_INSTAGRAM_REPORT", "name": "[CACHE] Check Report Cache", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [-200, 0]}, {"parameters": {"values": {"string": []}, "options": {}}, "id": "V55_TOOL_GET_INSTAGRAM_ANALYTICS", "name": "[TOOL] Get Instagram Analytics", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [0, 0]}, {"parameters": {"agent": "conversationalAgent", "promptType": "define", "text": "Dados: {{ JSON.stringify($('V55_TOOL_GET_INSTAGRAM_ANALYTICS').first().json) }}", "options": {"systemMessage": "Você é um analista de dados do Instagram. Gere um relatório de performance, focando em engajamento."}}, "id": "V57_AGENT_INSTAGRAM_REPORT", "name": "Agent: Write Instagram Report", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "position": [240, 0]}, {"parameters": {"values": {"string": []}, "options": {}}, "id": "V58_CACHE_SAVE_INSTAGRAM_REPORT", "name": "[CACHE] Save Report to Cache", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [480, 0]}, {"parameters": {"values": {"string": [{"name": "log_entry", "value": "={{ { \"user_id\": $json.user_id, \"network\": \"Instagram\", \"timestamp\": $now, \"action\": \"report_generated\", \"details\": $json.task_details, \"success\": true, \"cached_result\": $json.cache_hit === true } }}"}]}, "options": {}}, "id": "V57_LOG_INSTAGRAM_REPORT", "name": "[DB] Save Action Log", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [700, 0]}, {"parameters": {"conditions": {"boolean": [{"value1": "={{ $json.cache_hit }}", "operation": "equal", "value2": true}]}}, "id": "V58_CACHE_CHECK_INSTAGRAM_COMMENT", "name": "[CACHE] Check Comment Cache", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [-200, -120]}, {"parameters": {"values": {"string": [{"name": "sentiment", "value": "positive"}]}, "options": {}}, "id": "V58_SENTIMENT_INSTAGRAM_COMMENT", "name": "[AI_TOOL] Analyze Sentiment Comment", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [0, -120]}, {"parameters": {"agent": "conversationalAgent", "promptType": "define", "text": "Comentário: {{ $json.task_details.comment_text }}\nSentimento: {{ $('V58_SENTIMENT_INSTAGRAM_COMMENT').first().json.sentiment }}", "options": {"systemMessage": "Você é um especialista em Instagram. Responda ao comentário de forma envolvente e alinhada ao tom do sentimento."}}, "id": "V55_AGENT_INSTAGRAM_COMMENT", "name": "Agent: <PERSON><PERSON><PERSON> to Comment", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "position": [240, -120]}, {"parameters": {"values": {"string": []}, "options": {}}, "id": "V58_CACHE_SAVE_INSTAGRAM_COMMENT", "name": "[CACHE] Save Comment to Cache", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [480, -120]}, {"parameters": {"values": {"string": []}, "options": {}}, "id": "V57_TOOL_POST_INSTAGRAM_COMMENT", "name": "[TOOL] Post Comment to Instagram", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [700, -120]}, {"parameters": {"values": {"string": [{"name": "log_entry", "value": "={{ { \"user_id\": $json.user_id, \"network\": \"Instagram\", \"timestamp\": $now, \"action\": \"comment_replied\", \"details\": $json.task_details, \"success\": true, \"cached_result\": $json.cache_hit === true } }}"}]}, "options": {}}, "id": "V57_LOG_INSTAGRAM_COMMENT", "name": "[DB] Save Action Log", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [920, -120]}, {"parameters": {"routing": {"rules": {"values": [{"output": 0, "operation": "equal", "value1": "={{ $json.task }}", "value2": "create_post"}, {"output": 1, "operation": "equal", "value1": "={{ $json.task }}", "value2": "generate_performance_report"}, {"output": 2, "operation": "equal", "value1": "={{ $json.task }}", "value2": "respond_to_comment"}]}, "fieldToMatch": "={{ $json.task }}"}}, "id": "V54_ROUTE_X_TASK", "name": "Route X Task", "type": "n8n-nodes-base.switch", "typeVersion": 2, "position": [-1280, 620]}, {"parameters": {"conditions": {"boolean": [{"value1": "={{ $json.task_details.topic.length <= 280 }}", "operation": "equal", "value2": true}]}}, "id": "V54_VALIDATE_X_POST", "name": "Validate: X Post", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [-1040, 620]}, {"parameters": {"conditions": {"boolean": [{"value1": "={{ $json.task_details.time_period_days }}", "operation": "isset"}]}}, "id": "V56_VALIDATE_X_REPORT", "name": "Validate: X Report", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [-1040, 740]}, {"parameters": {"conditions": {"boolean": [{"value1": "={{ $json.task_details.comment_id }}", "operation": "isset"}]}}, "id": "V54_VALIDATE_X_COMMENT", "name": "Validate: X Comment", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [-1040, 860]}, {"parameters": {"values": {"string": []}, "options": {}}, "id": "V54_AUTH_X", "name": "[CACHE] Get X Token", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [-800, 700]}, {"parameters": {"conditions": {"boolean": [{"value1": "={{ $json.token_expired }}", "operation": "equal", "value2": true}]}}, "id": "V54_CHECK_TOKEN_X", "name": "Check if X Token Expired?", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [-580, 700]}, {"parameters": {"values": {"string": []}, "options": {}}, "id": "V54_RENEW_TOKEN_X", "name": "[AUTH] Renew X Token", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [-340, 700]}, {"parameters": {"values": {"string": [{"name": "sentiment", "value": "neutral"}]}, "options": {}}, "id": "V58_SENTIMENT_X_POST", "name": "[AI_TOOL] Analyze Sentiment", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [-200, 620]}, {"parameters": {"conditions": {"boolean": [{"value1": "={{ $json.cache_hit }}", "operation": "equal", "value2": true}]}}, "id": "V58_CACHE_CHECK_X_POST", "name": "[CACHE] Check Post Cache", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [0, 620]}, {"parameters": {"agent": "conversationalAgent", "promptType": "define", "text": "Detalhes: {{ JSON.stringify($('Start').first().json.task_details) }}\nSentimento: {{ $('V58_SENTIMENT_X_POST').first().json.sentiment }}", "options": {"systemMessage": "Você é um especialista em X. Crie um tweet sobre o `topic` com até 280 caracteres, 1-2 hashtags e um CTA. Ajuste ao sentimento."}}, "id": "V52_AGENT_X", "name": "Agent: Create X Post", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "position": [240, 620]}, {"parameters": {"values": {"string": []}, "options": {}}, "id": "V58_CACHE_SAVE_X_POST", "name": "[CACHE] Save Post to Cache", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [480, 620]}, {"parameters": {"values": {"string": []}, "options": {}}, "id": "V52_TOOL_X", "name": "[TOOL] Post to X API", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [700, 620]}, {"parameters": {"values": {"string": [{"name": "log_entry", "value": "={{ { \"user_id\": $json.user_id, \"network\": \"X\", \"timestamp\": $now, \"action\": \"post_created\", \"details\": $json.task_details, \"success\": true, \"cached_result\": $json.cache_hit === true } }}"}]}, "options": {}}, "id": "V52_LOG_X", "name": "[DB] Save Action Log", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [920, 620]}, {"parameters": {"conditions": {"boolean": [{"value1": "={{ $json.cache_hit }}", "operation": "equal", "value2": true}]}}, "id": "V58_CACHE_CHECK_X_REPORT", "name": "[CACHE] Check Report Cache", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [-200, 740]}, {"parameters": {"values": {"string": []}, "options": {}}, "id": "V56_TOOL_GET_X_ANALYTICS", "name": "[TOOL] Get X Analytics", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [0, 740]}, {"parameters": {"agent": "conversationalAgent", "promptType": "define", "text": "Dados: {{ JSON.stringify($('V56_TOOL_GET_X_ANALYTICS').first().json) }}", "options": {"systemMessage": "Você é um analista de dados do X. Gere um relatório de performance conciso."}}, "id": "V56_AGENT_X_REPORT", "name": "Agent: Write X Report", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "position": [240, 740]}, {"parameters": {"values": {"string": []}, "options": {}}, "id": "V58_CACHE_SAVE_X_REPORT", "name": "[CACHE] Save Report to Cache", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [480, 740]}, {"parameters": {"values": {"string": [{"name": "log_entry", "value": "={{ { \"user_id\": $json.user_id, \"network\": \"X\", \"timestamp\": $now, \"action\": \"report_generated\", \"details\": $json.task_details, \"success\": true, \"cached_result\": $json.cache_hit === true } }}"}]}, "options": {}}, "id": "V56_LOG_X_REPORT", "name": "[DB] Save Action Log", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [700, 740]}, {"parameters": {"conditions": {"boolean": [{"value1": "={{ $json.cache_hit }}", "operation": "equal", "value2": true}]}}, "id": "V58_CACHE_CHECK_X_COMMENT", "name": "[CACHE] Check Comment Cache", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [-200, 860]}, {"parameters": {"values": {"string": [{"name": "sentiment", "value": "neutral"}]}, "options": {}}, "id": "V54_SENTIMENT_X_COMMENT", "name": "[AI_TOOL] Analyze Sentiment", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [0, 860]}, {"parameters": {"agent": "conversationalAgent", "promptType": "define", "text": "Comentário: {{ $json.task_details.comment_text }}\nSentimento: {{ $('V54_SENTIMENT_X_COMMENT').first().json.sentiment }}", "options": {"systemMessage": "Você é um especialista em X. Responda ao comentário de forma rápida e divertida, ajustando ao sentimento."}}, "id": "V54_AGENT_X_COMMENT", "name": "Agent: <PERSON><PERSON><PERSON> to Comment", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "position": [240, 860]}, {"parameters": {"values": {"string": []}, "options": {}}, "id": "V58_CACHE_SAVE_X_COMMENT", "name": "[CACHE] Save Comment to Cache", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [480, 860]}, {"parameters": {"values": {"string": []}, "options": {}}, "id": "V54_TOOL_X_COMMENT", "name": "[TOOL] Post Comment to X", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [700, 860]}, {"parameters": {"values": {"string": [{"name": "log_entry", "value": "={{ { \"user_id\": $json.user_id, \"network\": \"X\", \"timestamp\": $now, \"action\": \"comment_replied\", \"details\": $json.task_details, \"success\": true, \"cached_result\": $json.cache_hit === true } }}"}]}, "options": {}}, "id": "V54_LOG_X_COMMENT", "name": "[DB] Save Action Log", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [920, 860]}, {"parameters": {"routing": {"rules": {"values": [{"output": 0, "operation": "equal", "value1": "={{ $json.task }}", "value2": "create_post"}, {"output": 1, "operation": "equal", "value1": "={{ $json.task }}", "value2": "generate_performance_report"}, {"output": 2, "operation": "equal", "value1": "={{ $json.task }}", "value2": "respond_to_comment"}]}, "fieldToMatch": "={{ $json.task }}"}}, "id": "V54_ROUTE_TIKTOK_TASK", "name": "Route TikTok Task", "type": "n8n-nodes-base.switch", "typeVersion": 2, "position": [-1280, 1160]}, {"parameters": {"conditions": {"boolean": [{"value1": "={{ $json.task_details.topic }}", "operation": "isset"}]}}, "id": "V58_VALIDATE_TIKTOK_POST", "name": "Validate: TikTok Post", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [-1040, 1100]}, {"parameters": {"conditions": {"boolean": [{"value1": "={{ $json.task_details.time_period_days }}", "operation": "isset"}]}}, "id": "V58_VALIDATE_TIKTOK_REPORT", "name": "Validate: TikTok Report", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [-1040, 1220]}, {"parameters": {"conditions": {"boolean": [{"value1": "={{ $json.task_details.comment_id }}", "operation": "isset"}]}}, "id": "V58_VALIDATE_TIKTOK_COMMENT", "name": "Validate: TikTok Comment", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [-1040, 1340]}, {"parameters": {"values": {"string": []}, "options": {}}, "id": "V58_AUTH_TIKTOK", "name": "[CACHE] Get TikTok Token", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [-800, 1160]}, {"parameters": {"conditions": {"boolean": [{"value1": "={{ $json.token_expired }}", "operation": "equal", "value2": true}]}}, "id": "V58_CHECK_TOKEN_TIKTOK", "name": "Check if TikTok Token Expired?", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [-580, 1160]}, {"parameters": {"values": {"string": []}, "options": {}}, "id": "V58_RENEW_TOKEN_TIKTOK", "name": "[AUTH] Renew Tik<PERSON>ok Token", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [-340, 1160]}, {"parameters": {"values": {"string": [{"name": "sentiment", "value": "fun"}]}, "options": {}}, "id": "V58_SENTIMENT_TIKTOK_POST", "name": "[AI_TOOL] Analyze Sentiment", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [-200, 1100]}, {"parameters": {"conditions": {"boolean": [{"value1": "={{ $json.cache_hit }}", "operation": "equal", "value2": true}]}}, "id": "V58_CACHE_CHECK_TIKTOK_POST", "name": "[CACHE] Check Post Cache", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [0, 1100]}, {"parameters": {"agent": "conversationalAgent", "promptType": "define", "text": "Detalhes: {{ JSON.stringify($('Start').first().json.task_details) }}\nSentimento: {{ $('V58_SENTIMENT_TIKTOK_POST').first().json.sentiment }}", "options": {"systemMessage": "Você é um criador de conteúdo para TikTok. Crie um vídeo ou post sobre o `topic`. Descreva o conceito, sugira hashtags e adapte ao público e ao sentimento detectado."}}, "id": "V58_AGENT_TIKTOK_POST", "name": "Agent: <PERSON><PERSON> <PERSON><PERSON><PERSON>", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "position": [240, 1100]}, {"parameters": {"values": {"string": []}, "options": {}}, "id": "V58_CACHE_SAVE_TIKTOK_POST", "name": "[CACHE] Save Post to Cache", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [480, 1100]}, {"parameters": {"values": {"string": []}, "options": {}}, "id": "V52_TOOL_TIKTOK", "name": "[TOOL] Post to TikTok API", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [700, 1100]}, {"parameters": {"values": {"string": [{"name": "log_entry", "value": "={{ { \"user_id\": $json.user_id, \"network\": \"TikTok\", \"timestamp\": $now, \"action\": \"post_created\", \"details\": $json.task_details, \"success\": true, \"cached_result\": $json.cache_hit === true } }}"}]}, "options": {}}, "id": "V52_LOG_TIKTOK", "name": "[DB] Save Action Log", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [920, 1100]}, {"parameters": {"conditions": {"boolean": [{"value1": "={{ $json.cache_hit }}", "operation": "equal", "value2": true}]}}, "id": "V58_CACHE_CHECK_TIKTOK_REPORT", "name": "[CACHE] Check Report Cache", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [-200, 1220]}, {"parameters": {"values": {"string": []}, "options": {}}, "id": "V58_TOOL_GET_TIKTOK_ANALYTICS", "name": "[TOOL] Get TikTok Analytics", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [0, 1220]}, {"parameters": {"agent": "conversationalAgent", "promptType": "define", "text": "Dados: {{ JSON.stringify($('V58_TOOL_GET_TIKTOK_ANALYTICS').first().json) }}", "options": {"systemMessage": "Você é um analista de dados do TikTok. Gere um relatório de performance com foco em visualizações e engajamento."}}, "id": "V58_AGENT_TIKTOK_REPORT", "name": "Agent: Write TikTok Report", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "position": [240, 1220]}, {"parameters": {"values": {"string": []}, "options": {}}, "id": "V58_CACHE_SAVE_TIKTOK_REPORT", "name": "[CACHE] Save Report to Cache", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [480, 1220]}, {"parameters": {"values": {"string": [{"name": "log_entry", "value": "={{ { \"user_id\": $json.user_id, \"network\": \"TikTok\", \"timestamp\": $now, \"action\": \"report_generated\", \"details\": $json.task_details, \"success\": true, \"cached_result\": $json.cache_hit === true } }}"}]}, "options": {}}, "id": "V58_LOG_TIKTOK_REPORT", "name": "[DB] Save Action Log", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [700, 1220]}, {"parameters": {"conditions": {"boolean": [{"value1": "={{ $json.cache_hit }}", "operation": "equal", "value2": true}]}}, "id": "V58_CACHE_CHECK_TIKTOK_COMMENT", "name": "[CACHE] Check Comment Cache", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [-200, 1340]}, {"parameters": {"agent": "conversationalAgent", "promptType": "define", "text": "Comentário: {{ $json.task_details.comment_text }}", "options": {"systemMessage": "Você é um especialista em TikTok. Responda ao comentário de forma criativa e divertida."}}, "id": "V58_AGENT_TIKTOK_COMMENT", "name": "Agent: <PERSON><PERSON><PERSON> to Comment", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "position": [0, 1340]}, {"parameters": {"values": {"string": []}, "options": {}}, "id": "V58_CACHE_SAVE_TIKTOK_COMMENT", "name": "[CACHE] Save Comment to Cache", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [240, 1340]}, {"parameters": {"values": {"string": []}, "options": {}}, "id": "V58_TOOL_POST_TIKTOK_COMMENT", "name": "[TOOL] Post Comment to TikTok", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [480, 1340]}, {"parameters": {"values": {"string": [{"name": "log_entry", "value": "={{ { \"user_id\": $json.user_id, \"network\": \"TikTok\", \"timestamp\": $now, \"action\": \"comment_replied\", \"details\": $json.task_details, \"success\": true, \"cached_result\": $json.cache_hit === true } }}"}]}, "options": {}}, "id": "V58_LOG_TIKTOK_COMMENT", "name": "[DB] Save Action Log", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [700, 1340]}, {"parameters": {"routing": {"rules": {"values": [{"output": 0, "operation": "equal", "value1": "={{ $json.task }}", "value2": "create_post"}, {"output": 1, "operation": "equal", "value1": "={{ $json.task }}", "value2": "generate_performance_report"}, {"output": 2, "operation": "equal", "value1": "={{ $json.task }}", "value2": "respond_to_comment"}]}, "fieldToMatch": "={{ $json.task }}"}}, "id": "V54_ROUTE_FACEBOOK_TASK", "name": "Route Facebook Task", "type": "n8n-nodes-base.switch", "typeVersion": 2, "position": [-1280, 1580]}, {"parameters": {"conditions": {"boolean": [{"value1": "={{ $json.task_details.audience }}", "operation": "isset"}]}}, "id": "V54_VALIDATE_FACEBOOK_POST", "name": "Validate: Facebook Post", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [-1040, 1520]}, {"parameters": {"conditions": {"boolean": [{"value1": "={{ $json.task_details.time_period_days }}", "operation": "isset"}]}}, "id": "V56_VALIDATE_FACEBOOK_REPORT", "name": "Validate: Facebook Report", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [-1040, 1640]}, {"parameters": {"conditions": {"boolean": [{"value1": "={{ $json.task_details.comment_id }}", "operation": "isset"}]}}, "id": "V56_VALIDATE_FACEBOOK_COMMENT", "name": "Validate: Facebook Comment", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [-1040, 1760]}, {"parameters": {"values": {"string": []}, "options": {}}, "id": "V56_AUTH_FACEBOOK", "name": "[CACHE] Get Facebook Token", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [-800, 1580]}, {"parameters": {"conditions": {"boolean": [{"value1": "={{ $json.token_expired }}", "operation": "equal", "value2": true}]}}, "id": "V56_CHECK_TOKEN_FACEBOOK", "name": "Check if Facebook Token Expired?", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [-580, 1580]}, {"parameters": {"values": {"string": []}, "options": {}}, "id": "V56_RENEW_TOKEN_FACEBOOK", "name": "[AUTH] Renew Facebook Token", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [-340, 1580]}, {"parameters": {"values": {"string": [{"name": "sentiment", "value": "friendly"}]}, "options": {}}, "id": "V58_SENTIMENT_FACEBOOK_POST", "name": "[AI_TOOL] Analyze Sentiment", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [-200, 1520]}, {"parameters": {"conditions": {"boolean": [{"value1": "={{ $json.cache_hit }}", "operation": "equal", "value2": true}]}}, "id": "V58_CACHE_CHECK_FACEBOOK_POST", "name": "[CACHE] Check Post Cache", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [0, 1520]}, {"parameters": {"agent": "conversationalAgent", "promptType": "define", "text": "Detalhes: {{ JSON.stringify($('Start').first().json.task_details) }}\nSentimento: {{ $('V58_SENTIMENT_FACEBOOK_POST').first().json.sentiment }}", "options": {"systemMessage": "Você é um gerente de mídias sociais para o Facebook. Crie um post para uma página sobre o `topic`. Adapte o tom para ser informativo e alinhado ao sentimento detectado. Incentive comentários e compartilhamentos."}}, "id": "V51_AGENT_FACEBOOK", "name": "Agent: Create Facebook Post", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "position": [240, 1520]}, {"parameters": {"values": {"string": []}, "options": {}}, "id": "V58_CACHE_SAVE_FACEBOOK_POST", "name": "[CACHE] Save Post to Cache", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [480, 1520]}, {"parameters": {"values": {"string": []}, "options": {}}, "id": "V51_TOOL_FACEBOOK", "name": "[TOOL] Post to Facebook API", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [700, 1520]}, {"parameters": {"values": {"string": [{"name": "log_entry", "value": "={{ { \"user_id\": $json.user_id, \"network\": \"Facebook\", \"timestamp\": $now, \"action\": \"post_created\", \"details\": $json.task_details, \"success\": true, \"cached_result\": $json.cache_hit === true } }}"}]}, "options": {}}, "id": "V51_LOG_FACEBOOK", "name": "[DB] Save Action Log", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [920, 1520]}, {"parameters": {"conditions": {"boolean": [{"value1": "={{ $json.cache_hit }}", "operation": "equal", "value2": true}]}}, "id": "V58_CACHE_CHECK_FACEBOOK_REPORT", "name": "[CACHE] Check Report Cache", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [-200, 1640]}, {"parameters": {"values": {"string": []}, "options": {}}, "id": "V56_TOOL_GET_FACEBOOK_ANALYTICS", "name": "[TOOL] Get Facebook Analytics", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [0, 1640]}, {"parameters": {"agent": "conversationalAgent", "promptType": "define", "text": "Dados: {{ JSON.stringify($('V56_TOOL_GET_FACEBOOK_ANALYTICS').first().json) }}", "options": {"systemMessage": "Você é um analista de dados do Facebook. Gere um relatório de performance."}}, "id": "V56_AGENT_FACEBOOK_REPORT", "name": "Agent: Write Facebook Report", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "position": [240, 1640]}, {"parameters": {"values": {"string": []}, "options": {}}, "id": "V58_CACHE_SAVE_FACEBOOK_REPORT", "name": "[CACHE] Save Report to Cache", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [480, 1640]}, {"parameters": {"values": {"string": [{"name": "log_entry", "value": "={{ { \"user_id\": $json.user_id, \"network\": \"Facebook\", \"timestamp\": $now, \"action\": \"report_generated\", \"details\": $json.task_details, \"success\": true, \"cached_result\": $json.cache_hit === true } }}"}]}, "options": {}}, "id": "V56_LOG_FACEBOOK_REPORT", "name": "[DB] Save Action Log", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [700, 1640]}, {"parameters": {"conditions": {"boolean": [{"value1": "={{ $json.cache_hit }}", "operation": "equal", "value2": true}]}}, "id": "V58_CACHE_CHECK_FACEBOOK_COMMENT", "name": "[CACHE] Check Comment Cache", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [-200, 1760]}, {"parameters": {"agent": "conversationalAgent", "promptType": "define", "text": "Comentário: {{ $json.task_details.comment_text }}", "options": {"systemMessage": "Você é um especialista em Facebook. Responda ao comentário de forma amigável e útil."}}, "id": "V56_AGENT_FACEBOOK_COMMENT", "name": "Agent: <PERSON><PERSON><PERSON> to Comment", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "position": [0, 1760]}, {"parameters": {"values": {"string": []}, "options": {}}, "id": "V58_CACHE_SAVE_FACEBOOK_COMMENT", "name": "[CACHE] Save Comment to Cache", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [240, 1760]}, {"parameters": {"values": {"string": []}, "options": {}}, "id": "V56_TOOL_FACEBOOK_COMMENT", "name": "[TOOL] Post Comment to Facebook", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [480, 1760]}, {"parameters": {"values": {"string": [{"name": "log_entry", "value": "={{ { \"user_id\": $json.user_id, \"network\": \"Facebook\", \"timestamp\": $now, \"action\": \"comment_replied\", \"details\": $json.task_details, \"success\": true, \"cached_result\": $json.cache_hit === true } }}"}]}, "options": {}}, "id": "V56_LOG_FACEBOOK_COMMENT", "name": "[DB] Save Action Log", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [700, 1760]}], "pinData": {}, "connections": {"Start": {"main": [[{"node": "V55_VALIDATE_INFO", "type": "main", "index": 0}]]}, "Route by Network": {"main": [[{"node": "V54_ROUTE_LINKEDIN_TASK", "type": "main", "index": 0}], [{"node": "V54_ROUTE_INSTAGRAM_TASK", "type": "main", "index": 0}], [{"node": "V54_ROUTE_X_TASK", "type": "main", "index": 0}], [{"node": "V54_ROUTE_TIKTOK_TASK", "type": "main", "index": 0}], [{"node": "V54_ROUTE_FACEBOOK_TASK", "type": "main", "index": 0}]]}, "Error Trigger": {"main": [[{"node": "V5_ERROR_FORMAT", "type": "main", "index": 0}]]}, "Final Merge Point": {"main": [[{"node": "V55_EVOLUTION_API_NOTIFY", "type": "main", "index": 0}]]}, "V5_ERROR_FORMAT": {"main": [[{"node": "V5_ERROR_NOTIFY", "type": "main", "index": 0}]]}, "V5_OUTPUT_USER": {"main": [[{"node": "V5_FEEDBACK_CHATWOOT", "type": "main", "index": 0}]]}, "V55_VALIDATE_INFO": {"main": [[{"node": "V55_ANALYZE_MEDIA", "type": "main", "index": 0}]]}, "V53_PREPARE_SCHEDULE": {"main": [[{"node": "V5_ROUTE_NETWORK", "type": "main", "index": 0}]]}, "V51_SCHEDULE_TRIGGER": {"main": [[{"node": "V53_PREPARE_SCHEDULE", "type": "main", "index": 0}]]}, "V55_ANALYZE_MEDIA": {"main": [[{"node": "V5_ROUTE_NETWORK", "type": "main", "index": 0}]]}, "V55_EVOLUTION_API_NOTIFY": {"main": [[{"node": "V5_OUTPUT_USER", "type": "main", "index": 0}]]}, "V54_ROUTE_LINKEDIN_TASK": {"main": [[{"node": "V54_VALIDATE_LINKEDIN_POST", "type": "main", "index": 0}], [{"node": "V54_VALIDATE_LINKEDIN_REPORT", "type": "main", "index": 0}], [{"node": "V56_VALIDATE_LINKEDIN_COMMENT", "type": "main", "index": 0}]]}, "V54_VALIDATE_LINKEDIN_POST": {"main": [[{"node": "V54_AUTH_LINKEDIN", "type": "main", "index": 0}]]}, "V54_VALIDATE_LINKEDIN_REPORT": {"main": [[{"node": "V54_AUTH_LINKEDIN", "type": "main", "index": 0}]]}, "V56_VALIDATE_LINKEDIN_COMMENT": {"main": [[{"node": "V54_AUTH_LINKEDIN", "type": "main", "index": 0}]]}, "V54_AUTH_LINKEDIN": {"main": [[{"node": "V54_CHECK_TOKEN_LINKEDIN", "type": "main", "index": 0}]]}, "V54_CHECK_TOKEN_LINKEDIN": {"main": [[{"node": "V54_RENEW_TOKEN_LINKEDIN", "type": "main", "index": 0}], []]}, "V54_RENEW_TOKEN_LINKEDIN": {"main": [[]]}, "V54_SENTIMENT_LINKEDIN_POST": {"main": [[{"node": "V58_CACHE_CHECK_LINKEDIN_POST", "type": "main", "index": 0}]]}, "V58_CACHE_CHECK_LINKEDIN_POST": {"main": [[{"node": "V51_LOG_LINKEDIN_POST", "type": "main", "index": 0}], [{"node": "V51_AGENT_LINKEDIN_POST", "type": "main", "index": 0}]]}, "V51_AGENT_LINKEDIN_POST": {"main": [[{"node": "V58_CACHE_SAVE_LINKEDIN_POST", "type": "main", "index": 0}]]}, "V58_CACHE_SAVE_LINKEDIN_POST": {"main": [[{"node": "V51_TOOL_LINKEDIN_POST", "type": "main", "index": 0}]]}, "V51_TOOL_LINKEDIN_POST": {"main": [[{"node": "V51_LOG_LINKEDIN_POST", "type": "main", "index": 0}]]}, "V51_LOG_LINKEDIN_POST": {"main": [[{"node": "Final Merge Point", "type": "main", "index": 0}]]}, "V54_CACHE_CHECK_LINKEDIN_REPORT": {"main": [[{"node": "V54_LOG_LINKEDIN_REPORT", "type": "main", "index": 0}], [{"node": "V54_TOOL_GET_LINKEDIN_ANALYTICS", "type": "main", "index": 0}]]}, "V54_TOOL_GET_LINKEDIN_ANALYTICS": {"main": [[{"node": "V54_AGENT_LINKEDIN_REPORT", "type": "main", "index": 0}]]}, "V54_AGENT_LINKEDIN_REPORT": {"main": [[{"node": "V54_CACHE_SAVE_LINKEDIN_REPORT", "type": "main", "index": 0}]]}, "V54_CACHE_SAVE_LINKEDIN_REPORT": {"main": [[{"node": "V54_LOG_LINKEDIN_REPORT", "type": "main", "index": 0}]]}, "V54_LOG_LINKEDIN_REPORT": {"main": [[{"node": "Final Merge Point", "type": "main", "index": 0}]]}, "V58_CACHE_CHECK_LINKEDIN_COMMENT": {"main": [[{"node": "V57_LOG_LINKEDIN_COMMENT", "type": "main", "index": 0}], [{"node": "V57_AGENT_LINKEDIN_COMMENT", "type": "main", "index": 0}]]}, "V57_AGENT_LINKEDIN_COMMENT": {"main": [[{"node": "V58_CACHE_SAVE_LINKEDIN_COMMENT", "type": "main", "index": 0}]]}, "V58_CACHE_SAVE_LINKEDIN_COMMENT": {"main": [[{"node": "V57_TOOL_POST_LINKEDIN_COMMENT", "type": "main", "index": 0}]]}, "V57_TOOL_POST_LINKEDIN_COMMENT": {"main": [[{"node": "V57_LOG_LINKEDIN_COMMENT", "type": "main", "index": 0}]]}, "V57_LOG_LINKEDIN_COMMENT": {"main": [[{"node": "Final Merge Point", "type": "main", "index": 0}]]}, "V54_ROUTE_INSTAGRAM_TASK": {"main": [[{"node": "V54_VALIDATE_INSTAGRAM_POST", "type": "main", "index": 0}], [{"node": "V55_VALIDATE_INSTAGRAM_REPORT", "type": "main", "index": 0}], [{"node": "V55_VALIDATE_INSTAGRAM_COMMENT", "type": "main", "index": 0}]]}, "V54_VALIDATE_INSTAGRAM_POST": {"main": [[{"node": "V54_AUTH_INSTAGRAM", "type": "main", "index": 0}]]}, "V55_VALIDATE_INSTAGRAM_REPORT": {"main": [[{"node": "V54_AUTH_INSTAGRAM", "type": "main", "index": 0}]]}, "V55_VALIDATE_INSTAGRAM_COMMENT": {"main": [[{"node": "V54_AUTH_INSTAGRAM", "type": "main", "index": 0}]]}, "V54_AUTH_INSTAGRAM": {"main": [[{"node": "V54_CHECK_TOKEN_INSTAGRAM", "type": "main", "index": 0}]]}, "V54_CHECK_TOKEN_INSTAGRAM": {"main": [[{"node": "V54_RENEW_TOKEN_INSTAGRAM", "type": "main", "index": 0}], []]}, "V54_RENEW_TOKEN_INSTAGRAM": {"main": [[]]}, "V54_SENTIMENT_INSTAGRAM_POST": {"main": [[{"node": "V58_CACHE_CHECK_INSTAGRAM_POST", "type": "main", "index": 0}]]}, "V58_CACHE_CHECK_INSTAGRAM_POST": {"main": [[{"node": "V5_LOG_INSTAGRAM_POST", "type": "main", "index": 0}], [{"node": "V5_AGENT_INSTAGRAM_POST", "type": "main", "index": 0}]]}, "V5_AGENT_INSTAGRAM_POST": {"main": [[{"node": "V58_CACHE_SAVE_INSTAGRAM_POST", "type": "main", "index": 0}]]}, "V58_CACHE_SAVE_INSTAGRAM_POST": {"main": [[{"node": "V5_TOOL_POST_INSTAGRAM", "type": "main", "index": 0}]]}, "V5_TOOL_POST_INSTAGRAM": {"main": [[{"node": "V5_LOG_INSTAGRAM_POST", "type": "main", "index": 0}]]}, "V5_LOG_INSTAGRAM_POST": {"main": [[{"node": "Final Merge Point", "type": "main", "index": 0}]]}, "V58_CACHE_CHECK_INSTAGRAM_REPORT": {"main": [[{"node": "V57_LOG_INSTAGRAM_REPORT", "type": "main", "index": 0}], [{"node": "V55_TOOL_GET_INSTAGRAM_ANALYTICS", "type": "main", "index": 0}]]}, "V55_TOOL_GET_INSTAGRAM_ANALYTICS": {"main": [[{"node": "V57_AGENT_INSTAGRAM_REPORT", "type": "main", "index": 0}]]}, "V57_AGENT_INSTAGRAM_REPORT": {"main": [[{"node": "V58_CACHE_SAVE_INSTAGRAM_REPORT", "type": "main", "index": 0}]]}, "V58_CACHE_SAVE_INSTAGRAM_REPORT": {"main": [[{"node": "V57_LOG_INSTAGRAM_REPORT", "type": "main", "index": 0}]]}, "V57_LOG_INSTAGRAM_REPORT": {"main": [[{"node": "Final Merge Point", "type": "main", "index": 0}]]}, "V58_CACHE_CHECK_INSTAGRAM_COMMENT": {"main": [[{"node": "V57_LOG_INSTAGRAM_COMMENT", "type": "main", "index": 0}], [{"node": "V58_SENTIMENT_INSTAGRAM_COMMENT", "type": "main", "index": 0}]]}, "V58_SENTIMENT_INSTAGRAM_COMMENT": {"main": [[{"node": "V55_AGENT_INSTAGRAM_COMMENT", "type": "main", "index": 0}]]}, "V55_AGENT_INSTAGRAM_COMMENT": {"main": [[{"node": "V58_CACHE_SAVE_INSTAGRAM_COMMENT", "type": "main", "index": 0}]]}, "V58_CACHE_SAVE_INSTAGRAM_COMMENT": {"main": [[{"node": "V57_TOOL_POST_INSTAGRAM_COMMENT", "type": "main", "index": 0}]]}, "V57_TOOL_POST_INSTAGRAM_COMMENT": {"main": [[{"node": "V57_LOG_INSTAGRAM_COMMENT", "type": "main", "index": 0}]]}, "V57_LOG_INSTAGRAM_COMMENT": {"main": [[{"node": "Final Merge Point", "type": "main", "index": 0}]]}, "V54_ROUTE_X_TASK": {"main": [[{"node": "V54_VALIDATE_X_POST", "type": "main", "index": 0}], [{"node": "V56_VALIDATE_X_REPORT", "type": "main", "index": 0}], [{"node": "V54_VALIDATE_X_COMMENT", "type": "main", "index": 0}]]}, "V54_VALIDATE_X_POST": {"main": [[{"node": "V54_AUTH_X", "type": "main", "index": 0}]]}, "V56_VALIDATE_X_REPORT": {"main": [[{"node": "V54_AUTH_X", "type": "main", "index": 0}]]}, "V54_VALIDATE_X_COMMENT": {"main": [[{"node": "V54_AUTH_X", "type": "main", "index": 0}]]}, "V54_AUTH_X": {"main": [[{"node": "V54_CHECK_TOKEN_X", "type": "main", "index": 0}]]}, "V54_CHECK_TOKEN_X": {"main": [[{"node": "V54_RENEW_TOKEN_X", "type": "main", "index": 0}], []]}, "V54_RENEW_TOKEN_X": {"main": [[]]}, "V58_SENTIMENT_X_POST": {"main": [[{"node": "V58_CACHE_CHECK_X_POST", "type": "main", "index": 0}]]}, "V58_CACHE_CHECK_X_POST": {"main": [[{"node": "V52_LOG_X", "type": "main", "index": 0}], [{"node": "V52_AGENT_X", "type": "main", "index": 0}]]}, "V52_AGENT_X": {"main": [[{"node": "V58_CACHE_SAVE_X_POST", "type": "main", "index": 0}]]}, "V58_CACHE_SAVE_X_POST": {"main": [[{"node": "V52_TOOL_X", "type": "main", "index": 0}]]}, "V52_TOOL_X": {"main": [[{"node": "V52_LOG_X", "type": "main", "index": 0}]]}, "V52_LOG_X": {"main": [[{"node": "Final Merge Point", "type": "main", "index": 0}]]}, "V58_CACHE_CHECK_X_REPORT": {"main": [[{"node": "V56_LOG_X_REPORT", "type": "main", "index": 0}], [{"node": "V56_TOOL_GET_X_ANALYTICS", "type": "main", "index": 0}]]}, "V56_TOOL_GET_X_ANALYTICS": {"main": [[{"node": "V56_AGENT_X_REPORT", "type": "main", "index": 0}]]}, "V56_AGENT_X_REPORT": {"main": [[{"node": "V58_CACHE_SAVE_X_REPORT", "type": "main", "index": 0}]]}, "V58_CACHE_SAVE_X_REPORT": {"main": [[{"node": "V56_LOG_X_REPORT", "type": "main", "index": 0}]]}, "V56_LOG_X_REPORT": {"main": [[{"node": "Final Merge Point", "type": "main", "index": 0}]]}, "V58_CACHE_CHECK_X_COMMENT": {"main": [[{"node": "V54_LOG_X_COMMENT", "type": "main", "index": 0}], [{"node": "V54_SENTIMENT_X_COMMENT", "type": "main", "index": 0}]]}, "V54_SENTIMENT_X_COMMENT": {"main": [[{"node": "V54_AGENT_X_COMMENT", "type": "main", "index": 0}]]}, "V54_AGENT_X_COMMENT": {"main": [[{"node": "V58_CACHE_SAVE_X_COMMENT", "type": "main", "index": 0}]]}, "V58_CACHE_SAVE_X_COMMENT": {"main": [[{"node": "V54_TOOL_X_COMMENT", "type": "main", "index": 0}]]}, "V54_TOOL_X_COMMENT": {"main": [[{"node": "V54_LOG_X_COMMENT", "type": "main", "index": 0}]]}, "V54_LOG_X_COMMENT": {"main": [[{"node": "Final Merge Point", "type": "main", "index": 0}]]}, "V54_ROUTE_TIKTOK_TASK": {"main": [[{"node": "V58_VALIDATE_TIKTOK_POST", "type": "main", "index": 0}], [{"node": "V58_VALIDATE_TIKTOK_REPORT", "type": "main", "index": 0}], [{"node": "V58_VALIDATE_TIKTOK_COMMENT", "type": "main", "index": 0}]]}, "V58_VALIDATE_TIKTOK_POST": {"main": [[{"node": "V58_AUTH_TIKTOK", "type": "main", "index": 0}]]}, "V58_VALIDATE_TIKTOK_REPORT": {"main": [[{"node": "V58_AUTH_TIKTOK", "type": "main", "index": 0}]]}, "V58_VALIDATE_TIKTOK_COMMENT": {"main": [[{"node": "V58_AUTH_TIKTOK", "type": "main", "index": 0}]]}, "V58_AUTH_TIKTOK": {"main": [[{"node": "V58_CHECK_TOKEN_TIKTOK", "type": "main", "index": 0}]]}, "V58_CHECK_TOKEN_TIKTOK": {"main": [[{"node": "V58_RENEW_TOKEN_TIKTOK", "type": "main", "index": 0}], []]}, "V58_RENEW_TOKEN_TIKTOK": {"main": [[]]}, "V58_SENTIMENT_TIKTOK_POST": {"main": [[{"node": "V58_CACHE_CHECK_TIKTOK_POST", "type": "main", "index": 0}]]}, "V58_CACHE_CHECK_TIKTOK_POST": {"main": [[{"node": "V52_LOG_TIKTOK", "type": "main", "index": 0}], [{"node": "V58_AGENT_TIKTOK_POST", "type": "main", "index": 0}]]}, "V58_AGENT_TIKTOK_POST": {"main": [[{"node": "V58_CACHE_SAVE_TIKTOK_POST", "type": "main", "index": 0}]]}, "V58_CACHE_SAVE_TIKTOK_POST": {"main": [[{"node": "V52_TOOL_TIKTOK", "type": "main", "index": 0}]]}, "V52_TOOL_TIKTOK": {"main": [[{"node": "V52_LOG_TIKTOK", "type": "main", "index": 0}]]}, "V52_LOG_TIKTOK": {"main": [[{"node": "Final Merge Point", "type": "main", "index": 0}]]}, "V58_CACHE_CHECK_TIKTOK_REPORT": {"main": [[{"node": "V58_LOG_TIKTOK_REPORT", "type": "main", "index": 0}], [{"node": "V58_TOOL_GET_TIKTOK_ANALYTICS", "type": "main", "index": 0}]]}, "V58_TOOL_GET_TIKTOK_ANALYTICS": {"main": [[{"node": "V58_AGENT_TIKTOK_REPORT", "type": "main", "index": 0}]]}, "V58_AGENT_TIKTOK_REPORT": {"main": [[{"node": "V58_CACHE_SAVE_TIKTOK_REPORT", "type": "main", "index": 0}]]}, "V58_CACHE_SAVE_TIKTOK_REPORT": {"main": [[{"node": "V58_LOG_TIKTOK_REPORT", "type": "main", "index": 0}]]}, "V58_LOG_TIKTOK_REPORT": {"main": [[{"node": "Final Merge Point", "type": "main", "index": 0}]]}, "V58_CACHE_CHECK_TIKTOK_COMMENT": {"main": [[{"node": "V58_LOG_TIKTOK_COMMENT", "type": "main", "index": 0}], [{"node": "V58_AGENT_TIKTOK_COMMENT", "type": "main", "index": 0}]]}, "V58_AGENT_TIKTOK_COMMENT": {"main": [[{"node": "V58_CACHE_SAVE_TIKTOK_COMMENT", "type": "main", "index": 0}]]}, "V58_CACHE_SAVE_TIKTOK_COMMENT": {"main": [[{"node": "V58_TOOL_POST_TIKTOK_COMMENT", "type": "main", "index": 0}]]}, "V58_TOOL_POST_TIKTOK_COMMENT": {"main": [[{"node": "V58_LOG_TIKTOK_COMMENT", "type": "main", "index": 0}]]}, "V58_LOG_TIKTOK_COMMENT": {"main": [[{"node": "Final Merge Point", "type": "main", "index": 0}]]}, "V54_ROUTE_FACEBOOK_TASK": {"main": [[{"node": "V54_VALIDATE_FACEBOOK_POST", "type": "main", "index": 0}], [{"node": "V56_VALIDATE_FACEBOOK_REPORT", "type": "main", "index": 0}], [{"node": "V56_VALIDATE_FACEBOOK_COMMENT", "type": "main", "index": 0}]]}, "V54_VALIDATE_FACEBOOK_POST": {"main": [[{"node": "V56_AUTH_FACEBOOK", "type": "main", "index": 0}]]}, "V56_VALIDATE_FACEBOOK_REPORT": {"main": [[{"node": "V56_AUTH_FACEBOOK", "type": "main", "index": 0}]]}, "V56_VALIDATE_FACEBOOK_COMMENT": {"main": [[{"node": "V56_AUTH_FACEBOOK", "type": "main", "index": 0}]]}, "V56_AUTH_FACEBOOK": {"main": [[{"node": "V56_CHECK_TOKEN_FACEBOOK", "type": "main", "index": 0}]]}, "V56_CHECK_TOKEN_FACEBOOK": {"main": [[{"node": "V56_RENEW_TOKEN_FACEBOOK", "type": "main", "index": 0}], []]}, "V56_RENEW_TOKEN_FACEBOOK": {"main": [[]]}, "V58_SENTIMENT_FACEBOOK_POST": {"main": [[{"node": "V58_CACHE_CHECK_FACEBOOK_POST", "type": "main", "index": 0}]]}, "V58_CACHE_CHECK_FACEBOOK_POST": {"main": [[{"node": "V51_LOG_FACEBOOK", "type": "main", "index": 0}], [{"node": "V51_AGENT_FACEBOOK", "type": "main", "index": 0}]]}, "V51_AGENT_FACEBOOK": {"main": [[{"node": "V58_CACHE_SAVE_FACEBOOK_POST", "type": "main", "index": 0}]]}, "V58_CACHE_SAVE_FACEBOOK_POST": {"main": [[{"node": "V51_TOOL_FACEBOOK", "type": "main", "index": 0}]]}, "V51_TOOL_FACEBOOK": {"main": [[{"node": "V51_LOG_FACEBOOK", "type": "main", "index": 0}]]}, "V51_LOG_FACEBOOK": {"main": [[{"node": "Final Merge Point", "type": "main", "index": 0}]]}, "V58_CACHE_CHECK_FACEBOOK_REPORT": {"main": [[{"node": "V56_LOG_FACEBOOK_REPORT", "type": "main", "index": 0}], [{"node": "V56_TOOL_GET_FACEBOOK_ANALYTICS", "type": "main", "index": 0}]]}, "V56_TOOL_GET_FACEBOOK_ANALYTICS": {"main": [[{"node": "V56_AGENT_FACEBOOK_REPORT", "type": "main", "index": 0}]]}, "V56_AGENT_FACEBOOK_REPORT": {"main": [[{"node": "V58_CACHE_SAVE_FACEBOOK_REPORT", "type": "main", "index": 0}]]}, "V58_CACHE_SAVE_FACEBOOK_REPORT": {"main": [[{"node": "V56_LOG_FACEBOOK_REPORT", "type": "main", "index": 0}]]}, "V56_LOG_FACEBOOK_REPORT": {"main": [[{"node": "Final Merge Point", "type": "main", "index": 0}]]}, "V58_CACHE_CHECK_FACEBOOK_COMMENT": {"main": [[{"node": "V56_LOG_FACEBOOK_COMMENT", "type": "main", "index": 0}], [{"node": "V56_AGENT_FACEBOOK_COMMENT", "type": "main", "index": 0}]]}, "V56_AGENT_FACEBOOK_COMMENT": {"main": [[{"node": "V58_CACHE_SAVE_FACEBOOK_COMMENT", "type": "main", "index": 0}]]}, "V58_CACHE_SAVE_FACEBOOK_COMMENT": {"main": [[{"node": "V56_TOOL_FACEBOOK_COMMENT", "type": "main", "index": 0}]]}, "V56_TOOL_FACEBOOK_COMMENT": {"main": [[{"node": "V56_LOG_FACEBOOK_COMMENT", "type": "main", "index": 0}]]}, "V56_LOG_FACEBOOK_COMMENT": {"main": [[{"node": "Final Merge Point", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1", "errorWorkflow": "V5_ERROR_TRIGGER"}, "versionId": "EDITADO_PARA_V5_8_1_FINAL", "meta": {}, "id": "m1a2b3c4-d5e6-f7a8-b9c0-d1e2f3a4b5c9_v5_8_1_final", "tags": []}