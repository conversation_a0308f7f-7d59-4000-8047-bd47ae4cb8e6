{"meta": {"instanceId": "MULTI_IDENTITY_AUTONOMOUS_PROSPECTING"}, "name": "[ENGINE] Multi-Identity Autonomous Prospecting", "nodes": [{"parameters": {"rule": {"interval": [{"field": "hours", "hoursInterval": 6}]}}, "id": "trigger_prospecting_cycle", "name": "TRIGGER: Prospecting Cycle (6h)", "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1.1, "position": [140, 300]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT ai.id as identity_id, ai.identity_name, ai.display_name, ai.category, ai.expertise_areas, ai.target_audience, c.id as campaign_id, c.name as campaign_name, c.base_prompt, c.target_tags, c.goal_metric FROM agent.agent_identities ai JOIN agent.campaigns c ON ai.id = c.target_identity_id WHERE ai.status = 'active' AND c.status = 'active' AND c.campaign_type = 'expertise_driven' ORDER BY ai.id;", "options": {}}, "id": "get_active_identity_campaigns", "name": "Get Active Identity Campaigns", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [360, 300], "credentials": {"postgres": {"id": "postgres-main", "name": "PostgreSQL Main"}}}, {"parameters": {"batchSize": 1}, "id": "loop_identity_campaigns", "name": "Loop Identity Campaigns", "type": "n8n-nodes-base.splitInBatches", "typeVersion": 2, "position": [580, 300]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT c.id, c.nome, c.empresa_atual, c.endereco_bairro, c.tags, c.enriched_data, c.engagement_score FROM agent.contatos c WHERE c.jornada_status IN ('convertido', 'engajado') AND c.tags @> $1::jsonb ORDER BY c.engagement_score DESC LIMIT 3;", "options": {"parameters": {"values": ["={{ $('loop_identity_campaigns').item.json.target_tags }}"]}}}, "id": "find_identity_seed_contacts", "name": "Find Identity Seed Contacts", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [800, 300], "credentials": {"postgres": {"id": "postgres-main", "name": "PostgreSQL Main"}}}, {"parameters": {"batchSize": 1}, "id": "loop_seed_contacts", "name": "Loop Seed Contacts", "type": "n8n-nodes-base.splitInBatches", "typeVersion": 2, "position": [1020, 300]}, {"parameters": {"workflowId": "={{ $env.DISPATCHER_WORKFLOW_ID }}", "data": "={{ { body: { prompt: `Você é ${$('loop_identity_campaigns').item.json.display_name}. Analise o perfil semente e crie uma estratégia de prospecção: Nome: ${$('loop_seed_contacts').item.json.nome}, Empresa: ${$('loop_seed_contacts').item.json.empresa_atual}, Tags: ${JSON.stringify($('loop_seed_contacts').item.json.tags)}. Retorne JSON com prospecting_strategy, lookalike_tags e messaging_approach.`, task_type: 'complex_analysis', calling_workflow_id: $workflow.id, calling_node_id: 'create_identity_prospecting_strategy' } } }}", "options": {}}, "id": "create_identity_prospecting_strategy", "name": "Create Identity Prospecting Strategy", "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 2, "position": [1240, 300]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT c.id, c.nome, c.empresa_atual, c.tags, c.telefone FROM agent.contatos c WHERE c.jornada_status NOT IN ('convertido', 'descartado') AND c.telefone IS NOT NULL AND c.tags ?| ARRAY(SELECT jsonb_array_elements_text($1::jsonb)) AND NOT EXISTS (SELECT 1 FROM agent.campaign_interactions ci WHERE ci.contact_id = c.id AND ci.campaign_id = $2 AND ci.created_at > NOW() - INTERVAL '30 days') LIMIT 5;", "options": {"parameters": {"values": ["={{ JSON.stringify($('create_identity_prospecting_strategy').item.json.lookalike_tags || []) }}", "={{ $('loop_identity_campaigns').item.json.campaign_id }}"]}}}, "id": "find_matching_prospects", "name": "Find Matching Prospects", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [1460, 300], "credentials": {"postgres": {"id": "postgres-main", "name": "PostgreSQL Main"}}}, {"parameters": {"batchSize": 1}, "id": "loop_prospects", "name": "Loop Prospects", "type": "n8n-nodes-base.splitInBatches", "typeVersion": 2, "position": [1680, 300]}, {"parameters": {"workflowId": "={{ $env.DISPATCHER_WORKFLOW_ID }}", "data": "={{ { body: { prompt: `Você é ${$('loop_identity_campaigns').item.json.display_name}. Crie uma mensagem de prospecção personalizada para: Nome: ${$('loop_prospects').item.json.nome}, Empresa: ${$('loop_prospects').item.json.empresa_atual}. Use sua expertise em ${JSON.stringify($('loop_identity_campaigns').item.json.expertise_areas)} e o base prompt: ${$('loop_identity_campaigns').item.json.base_prompt}`, task_type: 'complex_analysis', calling_workflow_id: $workflow.id, calling_node_id: 'create_personalized_outreach_message' } } }}", "options": {}}, "id": "create_personalized_outreach_message", "name": "Create Personalized Outreach", "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 2, "position": [1900, 300]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "INSERT INTO agent.campaign_interactions (campaign_id, contact_id, message_sent, interaction_type, identity_specialist_id) VALUES ($1, $2, $3, 'auto_generated', $4) RETURNING *;", "options": {"parameters": {"values": ["={{ $('loop_identity_campaigns').item.json.campaign_id }}", "={{ $('loop_prospects').item.json.id }}", "={{ $('create_personalized_outreach_message').item.json }}", "={{ $('loop_identity_campaigns').item.json.identity_id }}"]}}}, "id": "log_campaign_interaction", "name": "Log Campaign Interaction", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [2120, 300], "credentials": {"postgres": {"id": "postgres-main", "name": "PostgreSQL Main"}}}, {"parameters": {"mode": "combine", "combinationMode": "mergeByPosition", "options": {}}, "id": "merge_prospect_results", "name": "Merge Prospect Results", "type": "n8n-nodes-base.merge", "typeVersion": 2.1, "position": [2340, 300]}, {"parameters": {"mode": "combine", "combinationMode": "mergeByPosition", "options": {}}, "id": "merge_seed_results", "name": "<PERSON><PERSON>d Results", "type": "n8n-nodes-base.merge", "typeVersion": 2.1, "position": [2560, 300]}, {"parameters": {"mode": "combine", "combinationMode": "mergeByPosition", "options": {}}, "id": "merge_campaign_results", "name": "Merge Campaign Results", "type": "n8n-nodes-base.merge", "typeVersion": 2.1, "position": [2780, 300]}, {"parameters": {"method": "POST", "url": "={{ $vars.SLACK_WEBHOOK_URL }}", "sendBody": true, "specifyBody": "json", "jsonBody": "{\n  \"text\": \"🤖 Multi-Identity Prospecting - Ciclo Completo\",\n  \"blocks\": [\n    {\n      \"type\": \"header\",\n      \"text\": {\n        \"type\": \"plain_text\",\n        \"text\": \"🤖 MULTI-IDENTITY PROSPECTING CYCLE\"\n      }\n    },\n    {\n      \"type\": \"section\",\n      \"fields\": [\n        {\n          \"type\": \"mrkdwn\",\n          \"text\": \"*🎭 Identidades Ativas:*\\n{{ $('get_active_identity_campaigns').all().length }}\"\n        },\n        {\n          \"type\": \"mrkdwn\",\n          \"text\": \"*🌱 Seeds Analisados:*\\n{{ $('find_identity_seed_contacts').all().length }}\"\n        },\n        {\n          \"type\": \"mrkdwn\",\n          \"text\": \"*🎯 Prospects Encontrados:*\\n{{ $('find_matching_prospects').all().length }}\"\n        },\n        {\n          \"type\": \"mrkdwn\",\n          \"text\": \"*✍️ Mensagens Criadas:*\\n{{ $('create_personalized_outreach_message').all().length }}\"\n        },\n        {\n          \"type\": \"mrkdwn\",\n          \"text\": \"*📱 Mensagens Enviadas:*\\n{{ $('send_whatsapp_message').all().length }}\"\n        },\n        {\n          \"type\": \"mrkdwn\",\n          \"text\": \"*📝 Interações Registradas:*\\n{{ $('log_campaign_interaction').all().length }}\"\n        }\n      ]\n    },\n    {\n      \"type\": \"section\",\n      \"text\": {\n        \"type\": \"mrkdwn\",\n        \"text\": \"*🏆 Top Performing Identities:*\\n{{ $('get_active_identity_campaigns').all().slice(0, 3).map(camp => `• ${camp.json.display_name}: ${camp.json.conversion_rate}% conversão`).join('\\n') }}\"\n      }\n    },\n    {\n      \"type\": \"context\",\n      \"elements\": [\n        {\n          \"type\": \"mrkdwn\",\n          \"text\": \"Próximo ciclo em 6 horas | Sistema adaptativo baseado em performance\"\n        }\n      ]\n    }\n  ],\n  \"channel\": \"#multi-identity-prospecting\"\n}", "options": {}}, "id": "notify_cycle_completion", "name": "📱 Notify Cycle Completion", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [2900, 300], "continueOnFail": true}], "connections": {"trigger_prospecting_cycle": {"main": [[{"node": "get_active_identity_campaigns", "type": "main", "index": 0}]]}, "get_active_identity_campaigns": {"main": [[{"node": "loop_identity_campaigns", "type": "main", "index": 0}]]}, "loop_identity_campaigns": {"main": [[{"node": "find_identity_seed_contacts", "type": "main", "index": 0}]]}, "find_identity_seed_contacts": {"main": [[{"node": "loop_seed_contacts", "type": "main", "index": 0}]]}, "loop_seed_contacts": {"main": [[{"node": "create_identity_prospecting_strategy", "type": "main", "index": 0}]]}, "create_identity_prospecting_strategy": {"main": [[{"node": "find_matching_prospects", "type": "main", "index": 0}]]}, "find_matching_prospects": {"main": [[{"node": "loop_prospects", "type": "main", "index": 0}]]}, "loop_prospects": {"main": [[{"node": "create_personalized_outreach_message", "type": "main", "index": 0}]]}, "create_personalized_outreach_message": {"main": [[{"node": "log_campaign_interaction", "type": "main", "index": 0}]]}, "log_campaign_interaction": {"main": [[{"node": "merge_prospect_results", "type": "main", "index": 1}]]}, "merge_prospect_results": {"main": [[{"node": "merge_seed_results", "type": "main", "index": 1}]]}, "merge_seed_results": {"main": [[{"node": "merge_campaign_results", "type": "main", "index": 1}]]}, "merge_campaign_results": {"main": [[{"node": "notify_cycle_completion", "type": "main", "index": 0}]]}}, "pinData": {}, "settings": {"executionOrder": "v1"}, "staticData": null, "tags": [], "triggerCount": 0, "updatedAt": "2024-01-15T10:00:00.000Z", "versionId": "multi-identity-prospecting-v1.0"}