{"name": "[PRI] Agente Inteligente v13.0 - Cost-Aware", "nodes": [{"parameters": {"path": "whatsapp", "responseMode": "onReceived"}, "id": "trigger_whatsapp", "name": "TRIGGER: WhatsApp", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [-2000, 840]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "INSERT INTO agent.contatos (numero_whatsapp, nome, language) VALUES ($1, $2, 'pt-BR') ON CONFLICT (numero_whatsapp) DO UPDATE SET nome = EXCLUDED.nome, updated_at = NOW() RETURNING *;", "options": {"parameters": {"values": ["={{$json.body.sender.id}}", "={{$json.body.sender.pushName}}"]}}}, "id": "db_get_or_create_contact", "name": "DB: Get/Create Contact", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [-1800, 840], "credentials": {"postgres": {"id": "YOUR_POSTGRES_CREDENTIAL_ID"}}}, {"parameters": {"conditions": {"options": {}, "conditions": [{"value1": "={{$json.enriched_data}}", "operation": "isEmpty"}]}}, "id": "if_needs_enrichment", "name": "IF: Needs Enrichment?", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [-1600, 640]}, {"parameters": {"workflowId": "={{ $env['ENRICHER_WORKFLOW_ID'] }}", "mode": "webhook", "data": "={{ { body: { contact_id: $json.id } } }}", "waitFor": "nothing"}, "id": "run_enricher_workflow", "name": "RUN: [ENRICHER] Workflow", "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 1, "position": [-1400, 540], "notes": "Triggers the lead enrichment process asynchronously."}, {"parameters": {}, "id": "merge_enrichment_flow", "name": "MERGE: Enrichment", "type": "n8n-nodes-base.merge", "typeVersion": 2, "position": [-1200, 640]}, {"parameters": {"keepOnlySet": true, "values": {"string": [{"name": "textContent", "value": "={{$json.body.message.text}}"}], "number": [{"name": "contactDbId", "value": "={{$json.id}}"}], "json": [{"name": "contactLanguage", "value": "={{$json.language}}"}]}, "options": {}}, "id": "set_initial_data", "name": "SET: Initial Data", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [-1640, 840]}, {"parameters": {"functionCode": "const crypto = require('crypto');\nconst contactId = $json.contactDbId;\nif (!contactId) { throw new Error('contactDbId is missing.'); }\n$json.contact_id_hashed = crypto.createHash('sha256').update(String(contactId)).digest('hex');\nconst creds = $credentials.DefaultAESKey;\nif (!creds || !creds.key || creds.key.length !== 32) { throw new Error('Chave de criptografia AES (32 bytes) não encontrada ou inválida.'); }\n$json.encryption_key = creds.key;\nreturn $items;", "options": {}}, "id": "sec_prepare_crypto", "name": "SEC: Prepare Crypto & Hash", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [-1440, 840], "credentials": {"genericCredential": {"id": "DefaultAESKey"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT encrypted_content FROM agent.memory_vectors WHERE contact_id = $1 ORDER BY timestamp DESC LIMIT 10;", "options": {"parameters": {"values": ["={{$json.contactDbId}}"]}}}, "id": "db_get_encrypted_memories", "name": "DB: Get Encrypted Memories", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [-1240, 840], "credentials": {"postgres": {"id": "YOUR_POSTGRES_CREDENTIAL_ID"}}}, {"parameters": {"functionCode": "const crypto = require('crypto');\nconst key = Buffer.from($('sec_prepare_crypto').first().json.encryption_key);\nconst decryptedMemories = [];\nfor (const item of $items) {\n  const encryptedContent = item.json.encrypted_content;\n  if (!encryptedContent || !encryptedContent.includes(':')) continue;\n  const parts = encryptedContent.split(':');\n  const iv = Buffer.from(parts.shift(), 'hex');\n  const encryptedText = Buffer.from(parts.join(':'), 'hex');\n  const decipher = crypto.createDecipheriv('aes-256-cbc', key, iv);\n  let decrypted = decipher.update(encryptedText);\n  decrypted = Buffer.concat([decrypted, decipher.final()]);\n  decryptedMemories.push(decrypted.toString());\n}\nconst previousData = $('set_initial_data').first().json;\nreturn [{ json: { ...previousData, decrypted_context: decryptedMemories.reverse().join('\\n') } }];"}, "id": "sec_decrypt_memories", "name": "SEC: Decrypt Memories", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [-1040, 840]}, {"parameters": {"conditions": {"string": [{"value1": "={{$json.contactLanguage}}", "operation": "isEmpty"}]}}, "id": "if_language_unknown", "name": "IF: Language Unknown?", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [-840, 840]}, {"parameters": {"workflowId": "={{ $env.DISPATCHER_WORKFLOW_ID }}", "data": "={{ { body: { prompt: `Qual o idioma principal deste texto? Responda com o código de 2 letras (ex: pt, en, es).\\n\\nTexto: {{$json.textContent}}`, task_type: 'simple_task', calling_workflow_id: $workflow.id, calling_node_id: 'detect_language' } } }}"}, "id": "detect_language", "name": "AI: Detect Language", "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 2, "position": [-640, 940]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "UPDATE agent.contatos SET language = $1 WHERE id = $2;", "options": {"parameters": {"values": ["={{$json.choices[0].message.content}}", "={{$json.contactDbId}}"]}}}, "id": "db_update_language", "name": "DB: Save Language", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [-440, 940], "credentials": {"postgres": {"id": "YOUR_POSTGRES_CREDENTIAL_ID"}}}, {"parameters": {}, "id": "merge_language_flow", "name": "MERGE: Language", "type": "n8n-nodes-base.merge", "typeVersion": 2, "position": [-640, 740]}, {"parameters": {"workflowId": "={{ $env.DISPATCHER_WORKFLOW_ID }}", "data": "={{ { body: { prompt: `Você é um roteador de intenção. Sua tarefa é analisar a mensagem do usuário e o histórico da conversa para decidir qual ferramenta usar, se houver.\\n\\n**FERRAMENTAS DISPONÍVEIS:**\\n- \\`send_email\\`: Para enviar um e-mail.\\n- \\`create_calendar_event\\`: Para agendar um evento na agenda.\\n- \\`general_conversation\\`: Se a mensagem for uma pergunta geral, continuação de um bate-papo, ou se nenhuma outra ferramenta for adequada.\\n\\n**HISTÓRICO DA CONVERSA:**\\n{{$json.decrypted_context}}\\n\\n**MENSAGEM ATUAL DO USUÁRIO:**\\n{{$json.textContent}}\\n\\nCom base na mensagem atual, qual ferramenta você deve usar? Responda APENAS com o objeto JSON contendo o nome da ferramenta e os parâmetros extraídos da mensagem.\\n\\n**Exemplo de Resposta:**\\n{\\\"tool_name\\\": \\\"create_calendar_event\\\", \\\"tool_parameters\\\": {\\\"summary\\\": \\\"Reunião sobre o projeto X\\\", \\\"startDateTime\\\": \\\"2024-09-25T10:00:00-03:00\\\", \\\"endDateTime\\\": \\\"2024-09-25T11:00:00-03:00\\\"}}`, task_type: 'complex_analysis', calling_workflow_id: $workflow.id, calling_node_id: 'call_dispatcher_for_routing' } } }}"}, "id": "call_dispatcher_for_routing", "name": "Call Dispatcher for Routing", "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 2, "position": [-420, 740]}, {"parameters": {"conditions": {"string": [{"value1": "={{$json.tool_name}}", "value2": "general_conversation"}]}}, "id": "if_tool_selected", "name": "IF: <PERSON><PERSON> Selected?", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [-200, 740]}, {"parameters": {"workflowId": "={{ $env.DISPATCHER_WORKFLOW_ID }}", "data": "={{ { body: { prompt: `Você é um assistente de IA conversacional. Continue o diálogo de forma útil e amigável.\\n\\n**HISTÓRICO DA CONVERSA:**\\n{{$json.decrypted_context}}\\n\\n**MENSAGEM DO USUÁRIO:**\\n{{$json.textContent}}\\n\\nResponda à mensagem do usuário, levando em consideração o histórico. Formate a resposta como um objeto JSON: \\`{\\\"reply\\\": \\\"Sua resposta aqui...\\\"}\\``, task_type: 'simple_task', calling_workflow_id: $workflow.id, calling_node_id: 'call_dispatcher_for_chat' } } }}"}, "id": "call_dispatcher_for_chat", "name": "Call Dispatcher for Chat", "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 2, "position": [20, 840]}, {"parameters": {"conditions": {"string": [{"value1": "={{$json.tool_name}}", "value2": "create_calendar_event"}]}}, "id": "switch_tool", "name": "Switch Tool", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [20, 640]}, {"parameters": {"workflowId": "={{$env.CALENDAR_ENGINE_WORKFLOW_ID}}", "options": {"parameters": {"values": {"json": [{"name": "params", "value": "={{ { ...$json.tool_parameters, contactId: $json.contactDbId } }}"}]}}}}, "id": "execute_calendar_tool", "name": "EXECUTE: Calendar Tool", "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 2, "position": [260, 540]}, {"parameters": {"workflowId": "={{$env.GMAIL_ENGINE_WORKFLOW_ID}}", "options": {"parameters": {"values": {"json": [{"name": "params", "value": "={{ { ...$json.tool_parameters, contactId: $json.contactDbId } }}"}]}}}}, "id": "execute_gmail_tool", "name": "EXECUTE: <PERSON><PERSON>l", "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 2, "position": [260, 740]}, {"parameters": {"keepOnlySet": true, "values": {"string": [{"name": "finalReply", "value": "={{$json.data.summary || 'Ação completada com sucesso.'}}"}]}, "options": {}}, "id": "set_tool_response", "name": "SET: Tool Response", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [480, 640]}, {"parameters": {"message": "={{$json.finalReply || $json.reply}}", "options": {}}, "id": "send_final_message", "name": "Send Final Message", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [700, 740]}, {"parameters": {"model": "text-embedding-3-small", "input": "={{$node[\"set_initial_data\"].json.textContent}}"}, "id": "ai_generate_embedding", "name": "AI: Generate Embedding", "type": "n8n-nodes-base.openAi", "typeVersion": 4, "position": [480, 1120], "credentials": {"openAiApi": {"id": "YOUR_OPENAI_CREDENTIAL_ID"}}}, {"parameters": {"functionCode": "const crypto = require('crypto');\nconst userInput = $('set_initial_data').first().json.textContent;\nconst aiReply = $json.finalReply || $json.reply;\nif (!userInput || !aiReply) { return []; }\nconst memoryToSave = `User: ${userInput}\\nAI: ${aiReply}`;\nconst encryptionKey = $('sec_prepare_crypto').first().json.encryption_key;\nconst iv = crypto.randomBytes(16);\nconst cipher = crypto.createCipheriv('aes-256-cbc', Buffer.from(encryptionKey), iv);\nlet encrypted = cipher.update(memoryToSave, 'utf8', 'hex');\nencrypted += cipher.final('hex');\n$json.encrypted_content = iv.toString('hex') + ':' + encrypted;\nreturn $items;", "options": {}}, "id": "sec_encrypt_memory", "name": "SEC: Encrypt Memory", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [480, 840]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "INSERT INTO agent.memory_vectors (contact_id, content, encrypted_content, embedding) VALUES ($1, 'Message encrypted.', $2, $3::vector);", "options": {"parameters": {"values": ["={{$node[\"set_initial_data\"].json.contactDbId}}", "={{$json.encrypted_content}}", "={{'[' + $node[\"ai_generate_embedding\"].json.embedding.join(',') + ']'}}"]}}}, "id": "db_insert_memory", "name": "DB: Insert Encrypted Memory", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [480, 980], "credentials": {"postgres": {"id": "YOUR_POSTGRES_CREDENTIAL_ID"}}}], "connections": {"trigger_whatsapp": {"main": [[{"node": "db_get_or_create_contact", "type": "main", "index": 0}]]}, "db_get_or_create_contact": {"main": [[{"node": "if_needs_enrichment", "type": "main", "index": 0}]]}, "if_needs_enrichment": {"main": [[{"node": "run_enricher_workflow", "type": "main", "index": 0}], [{"node": "merge_enrichment_flow", "type": "main", "index": 0}]]}, "run_enricher_workflow": {"main": [[{"node": "merge_enrichment_flow", "type": "main", "index": 0}]]}, "merge_enrichment_flow": {"main": [[{"node": "set_initial_data", "type": "main", "index": 0}]]}, "set_initial_data": {"main": [[{"node": "sec_prepare_crypto", "type": "main", "index": 0}]]}, "sec_prepare_crypto": {"main": [[{"node": "db_get_encrypted_memories", "type": "main", "index": 0}]]}, "db_get_encrypted_memories": {"main": [[{"node": "sec_decrypt_memories", "type": "main", "index": 0}]]}, "sec_decrypt_memories": {"main": [[{"node": "if_language_unknown", "type": "main", "index": 0}]]}, "if_language_unknown": {"main": [[{"node": "merge_language_flow", "type": "main", "index": 0}], [{"node": "detect_language", "type": "main", "index": 0}]]}, "detect_language": {"main": [[{"node": "db_update_language", "type": "main", "index": 0}]]}, "db_update_language": {"main": [[{"node": "merge_language_flow", "type": "main", "index": 1}]]}, "merge_language_flow": {"main": [[{"node": "call_dispatcher_for_routing", "type": "main", "index": 0}]]}, "call_dispatcher_for_routing": {"main": [[{"node": "if_tool_selected", "type": "main", "index": 0}]]}, "if_tool_selected": {"main": [[{"node": "switch_tool", "type": "main", "index": 0}], [{"node": "call_dispatcher_for_chat", "type": "main", "index": 0}]]}, "call_dispatcher_for_chat": {"main": [[{"node": "sec_encrypt_memory", "type": "main", "index": 0}]]}, "switch_tool": {"main": [[{"node": "execute_calendar_tool", "type": "main", "index": 0}], [{"node": "execute_gmail_tool", "type": "main", "index": 0}]]}, "execute_calendar_tool": {"main": [[{"node": "set_tool_response", "type": "main", "index": 0}]]}, "execute_gmail_tool": {"main": [[{"node": "set_tool_response", "type": "main", "index": 0}]]}, "set_tool_response": {"main": [[{"node": "sec_encrypt_memory", "type": "main", "index": 0}]]}, "sec_encrypt_memory": {"main": [[{"node": "send_final_message", "type": "main", "index": 0}], [{"node": "ai_generate_embedding", "type": "main", "index": 0}]]}, "ai_generate_embedding": {"main": [[{"node": "db_insert_memory", "type": "main", "index": 0}]]}}, "pinData": {}}