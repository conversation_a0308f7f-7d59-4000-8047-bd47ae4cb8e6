{"name": "[AGENT] Autonomous Seller v2.3 - Cost-Aware", "nodes": [{"parameters": {}, "id": "start_node", "name": "Start", "type": "n8n-nodes-base.start", "typeVersion": 1, "position": [200, 300], "notes": "Recebe de entrada: { contactId: 123, taskPrompt: '<PERSON>a<PERSON> um follow-up sobre a proposta X.' }"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT encrypted_content FROM agent.memory_vectors WHERE contact_id = $1 ORDER BY timestamp DESC LIMIT 15;", "options": {"parameters": {"values": ["={{$json.contactId}}"]}}}, "id": "db_get_encrypted_memories", "name": "DB: Get Encrypted Memories", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [440, 300], "credentials": {"postgres": {"id": "YOUR_POSTGRES_CREDENTIAL_ID"}}}, {"parameters": {"functionCode": "const crypto = require('crypto');\nconst creds = $credentials.DefaultAESKey;\nif (!creds || !creds.key || creds.key.length !== 32) {\n  throw new Error('Chave de criptografia AES (32 bytes) não encontrada ou inválida.');\n}\nconst key = Buffer.from(creds.key);\n\nconst decryptedMemories = [];\nfor (const item of $items) {\n  const encryptedContent = item.json.encrypted_content;\n  if (!encryptedContent || !encryptedContent.includes(':')) continue;\n\n  const parts = encryptedContent.split(':');\n  const iv = Buffer.from(parts.shift(), 'hex');\n  const encryptedText = Buffer.from(parts.join(':'), 'hex');\n  const decipher = crypto.createDecipheriv('aes-256-cbc', key, iv);\n  let decrypted = decipher.update(encryptedText);\n  decrypted = Buffer.concat([decrypted, decipher.final()]);\n  decryptedMemories.push(decrypted.toString());\n}\n\nreturn [{ json: { decrypted_context: decryptedMemories.join('\\n---\\n') } }];", "options": {}}, "id": "sec_decrypt_memories", "name": "SEC: Decrypt Memories", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [680, 300], "notes": "<PERSON><PERSON><PERSON> as mem<PERSON>rias usando a chave AES e o IV armazenado.", "credentials": {"genericCredential": {"id": "DefaultAESKey"}}}, {"parameters": {"workflowId": "={{ $env.DISPATCHER_WORKFLOW_ID }}", "data": "={{ { body: { \n    prompt: `Você é um vendedor especialista. Sua tarefa é analisar o histórico de uma conversa e executar uma tarefa específica.\\n\\n**HISTÓRICO DA CONVERSA:**\\n${$json.decrypted_context}\\n\\n**TAREFA ATUAL:**\\n${$('start_node').item.json.taskPrompt}\\n\\nCom base no histórico, formule a melhor resposta ou ação para cumprir a tarefa. A resposta deve ser em primeira pessoa, como se você estivesse falando diretamente com o cliente. Responda em JSON com a estrutura: {\\\"reply_text\\\": \\\"Sua resposta aqui...\\\", \\\"action_taken\\\": \\\"Um resumo da ação tomada, ex: Enviado follow-up.\\\"}`,\n    task_type: 'simple_task',\n    calling_workflow_id: $workflow.id,\n    calling_node_id: 'call_dispatcher_for_sales'\n} } }}", "options": {}}, "id": "call_dispatcher_for_sales", "name": "Call Dispatcher for Sales Task", "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 2, "position": [920, 300]}, {"parameters": {"workflowId": "={{$env.SEND_MESSAGE_WORKFLOW_ID}}", "options": {"parameters": {"values": {"string": [{"name": "channel", "value": "whatsapp"}, {"name": "recipientId", "value": "={{$('start_node').item.json.contactId}}"}, {"name": "messageContent", "value": "={{$json.reply_text}}"}]}}}}, "id": "send_reply_to_customer", "name": "SUB: Send Reply", "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 2, "position": [1160, 300], "notes": "Envia a resposta formulada pela IA para o cliente."}], "connections": {"start_node": {"main": [[{"node": "db_get_encrypted_memories"}]]}, "db_get_encrypted_memories": {"main": [[{"node": "sec_decrypt_memories"}]]}, "sec_decrypt_memories": {"main": [[{"node": "call_dispatcher_for_sales"}]]}, "call_dispatcher_for_sales": {"main": [[{"node": "send_reply_to_customer"}]]}}}