{
  "name": "AI Content Gen - Step 2: Handle Approval & Post",
  "nodes": [
    {
      "parameters": {
        "events": [
          "callback_query"
        ]
      },
      "name": "Telegram Trigger",
      "type": "n8n-nodes-base.telegramTrigger",
      "typeVersion": 1.1,
      "position": [
        440,
        300
      ],
      "credentials": {
        "telegramApi": {
          "id": "YOUR_TELEGRAM_CREDENTIAL_ID",
          "name": "Your Telegram Bot Credentials"
        }
      },
      "notes": "Disparado quando um botão (Aprovar/Recusar) é clicado no Telegram.\n**IMPORTANTE:** Requer que o n8n esteja acessível publicamente (Webhook URL)."
    },
    {
      "parameters": {
        "conditions": {
          "string": [
            {
              "value1": "={{ $json.data.split('::')[0] }}",
              "operation": "equal",
              "value2": "approve"
            }
          ]
        }
      },
      "name": "If Approved?",
      "type": "n8n-nodes-base.if",
      "typeVersion": 1,
      "position": [
        880,
        300
      ],
      "notes": "Verifica se o primeiro campo do callback data é 'approve'."
    },
    {
      "parameters": {
        "mode": "status",
        "text": "={{ decodeURIComponent($node['Parse Callback Data'].json.tweetText) }}",
        "options": {}
      },
      "name": "Post Tweet",
      "type": "n8n-nodes-base.twitter",
      "typeVersion": 1.1,
      "position": [
        1120,
        200
      ],
      "credentials": {
        "twitterApi": {
          "id": "YOUR_X_CREDENTIAL_ID",
          "name": "Your X (Twitter) Credentials"
        }
      },
      "notes": "Posta o tweet aprovado no X (Twitter).\nO texto vem dos dados decodificados do callback."
    },
    {
      "parameters": {
        "jsCode": "const callbackData = $json.data;\nconst parts = callbackData.split('::');\n\nconst result = {\n  action: parts[0],\n  executionId: parts[1] || null,\n  tweetText: parts[2] ? decodeURIComponent(parts[2]) : null,\n  originalUrl: parts[3] ? decodeURIComponent(parts[3]) : null\n};\n\nreturn result;"
      },
      "name": "Parse Callback Data",
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [
        660,
        300
      ],
      "notes": "Extrai as informações (ação, ID, texto, URL) do callback data recebido."
    }
  ],
  "connections": {
    "Telegram Trigger": {
      "main": [
        [
          {
            "node": "Parse Callback Data",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "If Approved?": {
      "main": [
        [
          {
            "node": "Post Tweet",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Parse Callback Data": {
      "main": [
        [
          {
            "node": "If Approved?",
            "type": "main",
            "index": 0
          }
        ]
      ]
    }
  },
  "settings": {},
  "staticData": null,
  "pinData": {},
  "versionId": "b2c3d4e5-f6a7-8901-bcde-f1234567890a", // Example Version ID
  "triggerCount": 1,
  "tags": [
    {
      "id": "1",
      "name": "AI"
    },
    {
      "id": "2",
      "name": "Social Media"
    },
    {
      "id": "3",
      "name": "Telegram"
    },
    {
      "id": "4",
      "name": "Twitter"
    }
  ]
}