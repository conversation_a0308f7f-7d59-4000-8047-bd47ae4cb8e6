{"name": "[ESCALATION] Performance Analytics v2.0", "nodes": [{"parameters": {"rule": {"interval": [{"field": "cronExpression", "expression": "0 */15 * * * *"}]}}, "id": "schedule-performance-analytics", "name": "⏰ Schedule Performance Analytics", "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1.2, "position": [240, 300]}, {"parameters": {"jsCode": "// 📊 Initialize performance analytics\nconst currentTime = new Date();\nconst analysisWindow = {\n  current_time: currentTime.toISOString(),\n  window_start: new Date(currentTime.getTime() - (15 * 60 * 1000)).toISOString(), // 15 minutes ago\n  window_end: currentTime.toISOString(),\n  analysis_type: 'performance_metrics',\n  analysis_id: `PERF_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`\n};\n\nconsole.log('📊 Starting performance analytics');\nconsole.log('🕐 Analysis window:', analysisWindow.window_start, 'to', analysisWindow.window_end);\nconsole.log('🆔 Analysis ID:', analysisWindow.analysis_id);\n\nreturn { analysisWindow };"}, "id": "initialize-analytics", "name": "📊 Initialize Analytics", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [460, 300]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "-- 📈 Escalation Volume Metrics\nSELECT \n  COUNT(*) as total_escalations,\n  COUNT(CASE WHEN priority = 'critical' THEN 1 END) as critical_escalations,\n  COUNT(CASE WHEN priority = 'high' THEN 1 END) as high_escalations,\n  COUNT(CASE WHEN priority = 'medium' THEN 1 END) as medium_escalations,\n  COUNT(CASE WHEN priority = 'low' THEN 1 END) as low_escalations,\n  COUNT(CASE WHEN status = 'resolved' THEN 1 END) as resolved_escalations,\n  COUNT(CASE WHEN status = 'active' THEN 1 END) as active_escalations,\n  COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_escalations,\n  AVG(EXTRACT(EPOCH FROM (resolved_at - created_at))/60) as avg_resolution_time_minutes,\n  AVG(EXTRACT(EPOCH FROM (assigned_at - created_at))/60) as avg_assignment_time_minutes,\n  COUNT(CASE WHEN assigned_at IS NULL THEN 1 END) as unassigned_escalations\nFROM agent.intelligent_escalations \nWHERE created_at >= $1 AND created_at <= $2", "additionalFields": {"mode": "independently"}, "options": {}}, "id": "fetch-escalation-metrics", "name": "📈 Fetch Escalation Metrics", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [680, 200], "credentials": {"postgres": {"id": "postgres-escalation-db", "name": "PostgreSQL - Escalation DB"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "-- 🤖 AI Analysis Performance\nSELECT \n  COUNT(*) as total_ai_analyses,\n  AVG(confidence_score) as avg_confidence_score,\n  COUNT(CASE WHEN confidence_score >= 0.8 THEN 1 END) as high_confidence_analyses,\n  COUNT(CASE WHEN confidence_score < 0.5 THEN 1 END) as low_confidence_analyses,\n  AVG(processing_time_ms) as avg_processing_time_ms,\n  COUNT(CASE WHEN human_review_required = true THEN 1 END) as human_reviews_required,\n  COUNT(CASE WHEN sentiment = 'positive' THEN 1 END) as positive_sentiment,\n  COUNT(CASE WHEN sentiment = 'negative' THEN 1 END) as negative_sentiment,\n  COUNT(CASE WHEN sentiment = 'neutral' THEN 1 END) as neutral_sentiment,\n  COUNT(CASE WHEN recommended_department IS NOT NULL THEN 1 END) as successful_routing_recommendations\nFROM agent.ai_analysis_results \nWHERE created_at >= $1 AND created_at <= $2", "additionalFields": {"mode": "independently"}, "options": {}}, "id": "fetch-ai-performance", "name": "🤖 Fetch AI Performance", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [680, 300], "credentials": {"postgres": {"id": "postgres-escalation-db", "name": "PostgreSQL - Escalation DB"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "-- 👥 Agent Performance Metrics\nSELECT \n  a.id as agent_id,\n  a.name as agent_name,\n  a.department,\n  a.status as agent_status,\n  COUNT(ie.escalation_id) as escalations_handled,\n  COUNT(CASE WHEN ie.status = 'resolved' THEN 1 END) as escalations_resolved,\n  AVG(EXTRACT(EPOCH FROM (ie.resolved_at - ie.assigned_at))/60) as avg_resolution_time_minutes,\n  COUNT(CASE WHEN ie.priority = 'critical' THEN 1 END) as critical_escalations_handled,\n  COUNT(CASE WHEN ie.priority = 'high' THEN 1 END) as high_escalations_handled,\n  AVG(CASE WHEN ie.customer_satisfaction_score IS NOT NULL THEN ie.customer_satisfaction_score END) as avg_satisfaction_score\nFROM agent.agents a\nLEFT JOIN agent.intelligent_escalations ie ON a.id = ie.assigned_agent_id \n  AND ie.assigned_at >= $1 AND ie.assigned_at <= $2\nWHERE a.status = 'active'\nGROUP BY a.id, a.name, a.department, a.status\nORDER BY escalations_handled DESC", "additionalFields": {"mode": "independently"}, "options": {}}, "id": "fetch-agent-performance", "name": "👥 Fetch Agent Performance", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [680, 400], "credentials": {"postgres": {"id": "postgres-escalation-db", "name": "PostgreSQL - Escalation DB"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "-- 🔗 Integration Performance\nSELECT \n  'chatwoot' as integration_type,\n  COUNT(*) as total_events,\n  COUNT(CASE WHEN escalation_created = true THEN 1 END) as escalations_created,\n  AVG(processing_time_ms) as avg_processing_time_ms,\n  COUNT(CASE WHEN error_message IS NOT NULL THEN 1 END) as error_count\nFROM agent.chatwoot_integrations \nWHER<PERSON> created_at >= $1 AND created_at <= $2\n\nUNION ALL\n\nSELECT \n  'slack' as integration_type,\n  COUNT(*) as total_events,\n  COUNT(CASE WHEN escalation_created = true THEN 1 END) as escalations_created,\n  AVG(processing_time_ms) as avg_processing_time_ms,\n  COUNT(CASE WHEN error_message IS NOT NULL THEN 1 END) as error_count\nFROM agent.slack_integrations \nWHERE created_at >= $1 AND created_at <= $2", "additionalFields": {"mode": "independently"}, "options": {}}, "id": "fetch-integration-performance", "name": "🔗 Fetch Integration Performance", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [680, 500], "credentials": {"postgres": {"id": "postgres-escalation-db", "name": "PostgreSQL - Escalation DB"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "-- ⏱️ SLA Performance Metrics\nWITH sla_targets AS (\n  SELECT \n    'critical' as priority, 15 as target_minutes\n  UNION ALL\n  SELECT \n    'high' as priority, 60 as target_minutes\n  UNION ALL\n  SELECT \n    'medium' as priority, 240 as target_minutes\n  UNION ALL\n  SELECT \n    'low' as priority, 480 as target_minutes\n),\nescalation_times AS (\n  SELECT \n    escalation_id,\n    priority,\n    EXTRACT(EPOCH FROM (COALESCE(resolved_at, NOW()) - created_at))/60 as actual_minutes,\n    status\n  FROM agent.intelligent_escalations \n  WHERE created_at >= $1 AND created_at <= $2\n)\nSELECT \n  et.priority,\n  st.target_minutes,\n  COUNT(*) as total_escalations,\n  COUNT(CASE WHEN et.actual_minutes <= st.target_minutes THEN 1 END) as within_sla,\n  COUNT(CASE WHEN et.actual_minutes > st.target_minutes THEN 1 END) as breached_sla,\n  ROUND((COUNT(CASE WHEN et.actual_minutes <= st.target_minutes THEN 1 END)::numeric / COUNT(*)::numeric) * 100, 2) as sla_compliance_percentage,\n  AVG(et.actual_minutes) as avg_actual_minutes,\n  MAX(et.actual_minutes) as max_actual_minutes\nFROM escalation_times et\nJOIN sla_targets st ON et.priority = st.priority\nGROUP BY et.priority, st.target_minutes\nORDER BY \n  CASE et.priority \n    WHEN 'critical' THEN 1\n    WHEN 'high' THEN 2\n    WHEN 'medium' THEN 3\n    WHEN 'low' THEN 4\n  END", "additionalFields": {"mode": "independently"}, "options": {}}, "id": "fetch-sla-performance", "name": "⏱️ Fetch SLA Performance", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [680, 600], "credentials": {"postgres": {"id": "postgres-escalation-db", "name": "PostgreSQL - Escalation DB"}}}, {"parameters": {"jsCode": "// 📊 Process and analyze performance data\nconst analysisWindow = $('initialize-analytics').first().json.analysisWindow;\nconst escalationMetrics = $('fetch-escalation-metrics').first().json;\nconst aiPerformance = $('fetch-ai-performance').first().json;\nconst agentPerformance = $('fetch-agent-performance').all().map(item => item.json);\nconst integrationPerformance = $('fetch-integration-performance').all().map(item => item.json);\nconst slaPerformance = $('fetch-sla-performance').all().map(item => item.json);\n\n// Calculate key performance indicators\nconst kpis = {\n  // Volume KPIs\n  total_escalations: escalationMetrics.total_escalations || 0,\n  escalation_growth_rate: 0, // Would need historical comparison\n  critical_escalation_rate: escalationMetrics.total_escalations > 0 ? \n    (escalationMetrics.critical_escalations / escalationMetrics.total_escalations * 100).toFixed(2) : 0,\n  \n  // Resolution KPIs\n  resolution_rate: escalationMetrics.total_escalations > 0 ? \n    (escalationMetrics.resolved_escalations / escalationMetrics.total_escalations * 100).toFixed(2) : 0,\n  avg_resolution_time_minutes: parseFloat(escalationMetrics.avg_resolution_time_minutes || 0).toFixed(2),\n  avg_assignment_time_minutes: parseFloat(escalationMetrics.avg_assignment_time_minutes || 0).toFixed(2),\n  \n  // AI Performance KPIs\n  ai_confidence_score: parseFloat(aiPerformance.avg_confidence_score || 0).toFixed(3),\n  ai_processing_time_ms: parseFloat(aiPerformance.avg_processing_time_ms || 0).toFixed(2),\n  human_review_rate: aiPerformance.total_ai_analyses > 0 ? \n    (aiPerformance.human_reviews_required / aiPerformance.total_ai_analyses * 100).toFixed(2) : 0,\n  \n  // Agent Performance KPIs\n  active_agents: agentPerformance.length,\n  avg_escalations_per_agent: agentPerformance.length > 0 ? \n    (agentPerformance.reduce((sum, agent) => sum + (agent.escalations_handled || 0), 0) / agentPerformance.length).toFixed(2) : 0,\n  top_performing_agent: agentPerformance.length > 0 ? \n    agentPerformance.reduce((top, agent) => \n      (agent.escalations_resolved || 0) > (top.escalations_resolved || 0) ? agent : top\n    ).agent_name : 'N/A',\n  \n  // Integration KPIs\n  chatwoot_success_rate: (() => {\n    const chatwoot = integrationPerformance.find(i => i.integration_type === 'chatwoot');\n    return chatwoot && chatwoot.total_events > 0 ? \n      ((chatwoot.total_events - chatwoot.error_count) / chatwoot.total_events * 100).toFixed(2) : 0;\n  })(),\n  slack_success_rate: (() => {\n    const slack = integrationPerformance.find(i => i.integration_type === 'slack');\n    return slack && slack.total_events > 0 ? \n      ((slack.total_events - slack.error_count) / slack.total_events * 100).toFixed(2) : 0;\n  })(),\n  \n  // SLA KPIs\n  overall_sla_compliance: slaPerformance.length > 0 ? \n    (slaPerformance.reduce((sum, sla) => sum + parseFloat(sla.sla_compliance_percentage || 0), 0) / slaPerformance.length).toFixed(2) : 0,\n  critical_sla_compliance: (() => {\n    const critical = slaPerformance.find(s => s.priority === 'critical');\n    return critical ? parseFloat(critical.sla_compliance_percentage || 0).toFixed(2) : 0;\n  })()\n};\n\n// Identify performance trends and alerts\nconst alerts = [];\nconst recommendations = [];\n\n// Critical escalation rate alert\nif (parseFloat(kpis.critical_escalation_rate) > 20) {\n  alerts.push({\n    type: 'critical_escalation_rate',\n    severity: 'high',\n    message: `Critical escalation rate is ${kpis.critical_escalation_rate}% (threshold: 20%)`,\n    value: kpis.critical_escalation_rate\n  });\n  recommendations.push('Review escalation criteria and consider adjusting thresholds');\n}\n\n// Resolution time alert\nif (parseFloat(kpis.avg_resolution_time_minutes) > 120) {\n  alerts.push({\n    type: 'high_resolution_time',\n    severity: 'medium',\n    message: `Average resolution time is ${kpis.avg_resolution_time_minutes} minutes (threshold: 120 minutes)`,\n    value: kpis.avg_resolution_time_minutes\n  });\n  recommendations.push('Consider increasing agent capacity or improving resolution processes');\n}\n\n// AI confidence alert\nif (parseFloat(kpis.ai_confidence_score) < 0.7) {\n  alerts.push({\n    type: 'low_ai_confidence',\n    severity: 'medium',\n    message: `AI confidence score is ${kpis.ai_confidence_score} (threshold: 0.7)`,\n    value: kpis.ai_confidence_score\n  });\n  recommendations.push('Review AI model training data and consider retraining');\n}\n\n// SLA compliance alert\nif (parseFloat(kpis.overall_sla_compliance) < 85) {\n  alerts.push({\n    type: 'low_sla_compliance',\n    severity: 'high',\n    message: `Overall SLA compliance is ${kpis.overall_sla_compliance}% (threshold: 85%)`,\n    value: kpis.overall_sla_compliance\n  });\n  recommendations.push('Urgent review of resource allocation and process optimization needed');\n}\n\n// Integration success rate alerts\nif (parseFloat(kpis.chatwoot_success_rate) < 95) {\n  alerts.push({\n    type: 'chatwoot_integration_issues',\n    severity: 'medium',\n    message: `Chatwoot integration success rate is ${kpis.chatwoot_success_rate}% (threshold: 95%)`,\n    value: kpis.chatwoot_success_rate\n  });\n}\n\nif (parseFloat(kpis.slack_success_rate) < 95) {\n  alerts.push({\n    type: 'slack_integration_issues',\n    severity: 'medium',\n    message: `Slack integration success rate is ${kpis.slack_success_rate}% (threshold: 95%)`,\n    value: kpis.slack_success_rate\n  });\n}\n\n// Calculate performance score (0-100)\nconst performanceScore = Math.round(\n  (parseFloat(kpis.resolution_rate) * 0.25) +\n  (parseFloat(kpis.overall_sla_compliance) * 0.25) +\n  (parseFloat(kpis.ai_confidence_score) * 100 * 0.2) +\n  (parseFloat(kpis.chatwoot_success_rate) * 0.15) +\n  (parseFloat(kpis.slack_success_rate) * 0.15)\n);\n\n// Generate performance analysis\nconst performanceAnalysis = {\n  analysis_id: analysisWindow.analysis_id,\n  analysis_window: analysisWindow,\n  kpis: kpis,\n  performance_score: performanceScore,\n  performance_grade: (() => {\n    if (performanceScore >= 90) return 'A';\n    if (performanceScore >= 80) return 'B';\n    if (performanceScore >= 70) return 'C';\n    if (performanceScore >= 60) return 'D';\n    return 'F';\n  })(),\n  alerts: alerts,\n  recommendations: recommendations,\n  detailed_metrics: {\n    escalation_metrics: escalationMetrics,\n    ai_performance: aiPerformance,\n    agent_performance: agentPerformance,\n    integration_performance: integrationPerformance,\n    sla_performance: slaPerformance\n  },\n  analysis_timestamp: new Date().toISOString()\n};\n\nconsole.log('📊 Performance analysis completed');\nconsole.log('🎯 Performance Score:', performanceScore, '(' + performanceAnalysis.performance_grade + ')');\nconsole.log('🚨 Alerts generated:', alerts.length);\nconsole.log('💡 Recommendations:', recommendations.length);\n\nreturn { performanceAnalysis };"}, "id": "analyze-performance-data", "name": "📊 Analyze Performance Data", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [900, 400]}, {"parameters": {"operation": "insert", "schema": "agent", "table": "performance_analytics", "columns": "analysis_id, analysis_window_start, analysis_window_end, performance_score, performance_grade, kpis_data, alerts_data, recommendations_data, detailed_metrics, created_at", "additionalFields": {"mode": "independently"}, "options": {}}, "id": "save-performance-analysis", "name": "💾 Save Performance Analysis", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [1120, 300], "credentials": {"postgres": {"id": "postgres-escalation-db", "name": "PostgreSQL - Escalation DB"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "has-critical-alerts", "leftValue": "={{ $json.performanceAnalysis.alerts.filter(alert => alert.severity === 'high').length }}", "rightValue": 0, "operator": {"type": "number", "operation": "gt"}}], "combinator": "and"}, "options": {}}, "id": "has-critical-alerts", "name": "🚨 Has Critical Alerts?", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [1120, 500]}, {"parameters": {"method": "POST", "url": "http://localhost:5678/webhook/smart-notifications", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"notification_type\": \"system_alert\",\n  \"priority\": \"critical\",\n  \"channels\": [\"slack\", \"email\"],\n  \"message_data\": {\n    \"alert_type\": \"Performance Alert\",\n    \"component\": \"Escalation System\",\n    \"description\": \"Critical performance issues detected in escalation system\",\n    \"performance_score\": $json.performanceAnalysis.performance_score,\n    \"performance_grade\": $json.performanceAnalysis.performance_grade,\n    \"critical_alerts\": $json.performanceAnalysis.alerts.filter(alert => alert.severity === 'high'),\n    \"analysis_id\": $json.performanceAnalysis.analysis_id\n  }\n}", "options": {"timeout": 10000}}, "id": "send-critical-alert", "name": "🚨 Send Critical Alert", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1340, 420]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "is-hourly-report", "leftValue": "={{ new Date().getMinutes() }}", "rightValue": 0, "operator": {"type": "number", "operation": "equals"}}], "combinator": "and"}, "options": {}}, "id": "is-hourly-report", "name": "📅 Is Hourly Report?", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [1120, 700]}, {"parameters": {"jsCode": "// 📈 Generate performance report\nconst performanceAnalysis = $input.first().json.performanceAnalysis;\n\n// Create comprehensive performance report\nconst report = {\n  report_id: `PERF_REPORT_${Date.now()}`,\n  report_type: 'hourly_performance',\n  generated_at: new Date().toISOString(),\n  analysis_period: {\n    start: performanceAnalysis.analysis_window.window_start,\n    end: performanceAnalysis.analysis_window.window_end\n  },\n  executive_summary: {\n    performance_score: performanceAnalysis.performance_score,\n    performance_grade: performanceAnalysis.performance_grade,\n    total_escalations: performanceAnalysis.kpis.total_escalations,\n    resolution_rate: performanceAnalysis.kpis.resolution_rate + '%',\n    sla_compliance: performanceAnalysis.kpis.overall_sla_compliance + '%',\n    critical_alerts: performanceAnalysis.alerts.filter(alert => alert.severity === 'high').length\n  },\n  key_metrics: {\n    volume: {\n      total_escalations: performanceAnalysis.kpis.total_escalations,\n      critical_rate: performanceAnalysis.kpis.critical_escalation_rate + '%',\n      resolution_rate: performanceAnalysis.kpis.resolution_rate + '%'\n    },\n    timing: {\n      avg_resolution_time: performanceAnalysis.kpis.avg_resolution_time_minutes + ' min',\n      avg_assignment_time: performanceAnalysis.kpis.avg_assignment_time_minutes + ' min'\n    },\n    ai_performance: {\n      confidence_score: performanceAnalysis.kpis.ai_confidence_score,\n      processing_time: performanceAnalysis.kpis.ai_processing_time_ms + ' ms',\n      human_review_rate: performanceAnalysis.kpis.human_review_rate + '%'\n    },\n    integrations: {\n      chatwoot_success: performanceAnalysis.kpis.chatwoot_success_rate + '%',\n      slack_success: performanceAnalysis.kpis.slack_success_rate + '%'\n    },\n    sla: {\n      overall_compliance: performanceAnalysis.kpis.overall_sla_compliance + '%',\n      critical_compliance: performanceAnalysis.kpis.critical_sla_compliance + '%'\n    }\n  },\n  alerts_summary: {\n    total_alerts: performanceAnalysis.alerts.length,\n    critical_alerts: performanceAnalysis.alerts.filter(alert => alert.severity === 'high').length,\n    medium_alerts: performanceAnalysis.alerts.filter(alert => alert.severity === 'medium').length,\n    low_alerts: performanceAnalysis.alerts.filter(alert => alert.severity === 'low').length,\n    alert_details: performanceAnalysis.alerts\n  },\n  recommendations: performanceAnalysis.recommendations,\n  trend_analysis: {\n    // This would include historical comparison if available\n    note: 'Historical trend analysis requires multiple data points over time'\n  },\n  next_actions: [\n    'Monitor critical alerts and implement recommended actions',\n    'Review agent performance and workload distribution',\n    'Analyze AI model performance and consider optimization',\n    'Validate integration health and error rates',\n    'Schedule follow-up analysis in next reporting cycle'\n  ]\n};\n\n// Generate formatted report content\nconst reportContent = `\n# 📊 Escalation System Performance Report\n\n**Report ID:** ${report.report_id}  \n**Generated:** ${report.generated_at}  \n**Period:** ${report.analysis_period.start} to ${report.analysis_period.end}  \n\n## 🎯 Executive Summary\n\n- **Performance Score:** ${report.executive_summary.performance_score}/100 (Grade: ${report.executive_summary.performance_grade})\n- **Total Escalations:** ${report.executive_summary.total_escalations}\n- **Resolution Rate:** ${report.executive_summary.resolution_rate}\n- **SLA Compliance:** ${report.executive_summary.sla_compliance}\n- **Critical Alerts:** ${report.executive_summary.critical_alerts}\n\n## 📈 Key Metrics\n\n### Volume Metrics\n- Total Escalations: ${report.key_metrics.volume.total_escalations}\n- Critical Rate: ${report.key_metrics.volume.critical_rate}\n- Resolution Rate: ${report.key_metrics.volume.resolution_rate}\n\n### Timing Metrics\n- Avg Resolution Time: ${report.key_metrics.timing.avg_resolution_time}\n- Avg Assignment Time: ${report.key_metrics.timing.avg_assignment_time}\n\n### AI Performance\n- Confidence Score: ${report.key_metrics.ai_performance.confidence_score}\n- Processing Time: ${report.key_metrics.ai_performance.processing_time}\n- Human Review Rate: ${report.key_metrics.ai_performance.human_review_rate}\n\n### Integration Health\n- Chatwoot Success: ${report.key_metrics.integrations.chatwoot_success}\n- Slack Success: ${report.key_metrics.integrations.slack_success}\n\n### SLA Performance\n- Overall Compliance: ${report.key_metrics.sla.overall_compliance}\n- Critical Compliance: ${report.key_metrics.sla.critical_compliance}\n\n## 🚨 Alerts Summary\n\n- **Total Alerts:** ${report.alerts_summary.total_alerts}\n- **Critical:** ${report.alerts_summary.critical_alerts}\n- **Medium:** ${report.alerts_summary.medium_alerts}\n- **Low:** ${report.alerts_summary.low_alerts}\n\n${report.alerts_summary.alert_details.map(alert => \n  `### ${alert.severity.toUpperCase()}: ${alert.type}\n${alert.message}\n`\n).join('\n')}\n\n## 💡 Recommendations\n\n${report.recommendations.map((rec, index) => `${index + 1}. ${rec}`).join('\n')}\n\n## 🎯 Next Actions\n\n${report.next_actions.map((action, index) => `${index + 1}. ${action}`).join('\n')}\n\n---\n*Generated by Escalation System Performance Analytics v2.0*\n`;\n\nconsole.log('📈 Performance report generated');\nconsole.log('📊 Report ID:', report.report_id);\nconsole.log('🎯 Performance Score:', report.executive_summary.performance_score);\n\nreturn { report, reportContent };"}, "id": "generate-performance-report", "name": "📈 Generate Performance Report", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1340, 700]}, {"parameters": {"method": "POST", "url": "http://localhost:5678/webhook/smart-notifications", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"notification_type\": \"system_alert\",\n  \"priority\": \"medium\",\n  \"channels\": [\"slack\", \"email\"],\n  \"message_data\": {\n    \"alert_type\": \"Performance Report\",\n    \"component\": \"Escalation System\",\n    \"description\": \"Hourly performance report available\",\n    \"report_id\": $json.report.report_id,\n    \"performance_score\": $json.report.executive_summary.performance_score,\n    \"performance_grade\": $json.report.executive_summary.performance_grade,\n    \"report_content\": $json.reportContent\n  }\n}", "options": {"timeout": 10000}}, "id": "send-performance-report", "name": "📤 Send Performance Report", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1560, 700]}, {"parameters": {"jsCode": "// 📊 Generate analytics summary\nconst performanceAnalysis = $('analyze-performance-data').first().json.performanceAnalysis;\nconst criticalAlertSent = $('send-critical-alert').first()?.json || null;\nconst reportSent = $('send-performance-report').first()?.json || null;\n\n// Create final analytics summary\nconst summary = {\n  success: true,\n  analysis_id: performanceAnalysis.analysis_id,\n  analysis_completed_at: new Date().toISOString(),\n  performance_score: performanceAnalysis.performance_score,\n  performance_grade: performanceAnalysis.performance_grade,\n  metrics_analyzed: {\n    escalation_metrics: true,\n    ai_performance: true,\n    agent_performance: true,\n    integration_performance: true,\n    sla_performance: true\n  },\n  alerts: {\n    total_generated: performanceAnalysis.alerts.length,\n    critical_alerts: performanceAnalysis.alerts.filter(alert => alert.severity === 'high').length,\n    critical_alert_sent: !!criticalAlertSent\n  },\n  recommendations: {\n    total_generated: performanceAnalysis.recommendations.length,\n    top_priority: performanceAnalysis.recommendations[0] || 'No recommendations at this time'\n  },\n  reporting: {\n    hourly_report_generated: !!reportSent,\n    report_sent: !!reportSent\n  },\n  next_analysis_scheduled: new Date(Date.now() + (15 * 60 * 1000)).toISOString(), // 15 minutes from now\n  system_health: {\n    overall_status: performanceAnalysis.performance_score >= 80 ? 'healthy' : \n                   performanceAnalysis.performance_score >= 60 ? 'warning' : 'critical',\n    key_concerns: performanceAnalysis.alerts.filter(alert => alert.severity === 'high').map(alert => alert.type)\n  }\n};\n\nconsole.log('📊 Performance analytics completed successfully');\nconsole.log('🎯 Overall Performance:', summary.performance_score, '(' + summary.performance_grade + ')');\nconsole.log('🏥 System Health:', summary.system_health.overall_status);\nconsole.log('🚨 Critical Alerts:', summary.alerts.critical_alerts);\nconsole.log('⏰ Next Analysis:', summary.next_analysis_scheduled);\n\nreturn summary;"}, "id": "generate-analytics-summary", "name": "📊 Generate Analytics Summary", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1780, 500]}], "pinData": {}, "connections": {"schedule-performance-analytics": {"main": [[{"node": "initialize-analytics", "type": "main", "index": 0}]]}, "initialize-analytics": {"main": [[{"node": "fetch-escalation-metrics", "type": "main", "index": 0}, {"node": "fetch-ai-performance", "type": "main", "index": 0}, {"node": "fetch-agent-performance", "type": "main", "index": 0}, {"node": "fetch-integration-performance", "type": "main", "index": 0}, {"node": "fetch-sla-performance", "type": "main", "index": 0}]]}, "fetch-escalation-metrics": {"main": [[{"node": "analyze-performance-data", "type": "main", "index": 0}]]}, "fetch-ai-performance": {"main": [[{"node": "analyze-performance-data", "type": "main", "index": 0}]]}, "fetch-agent-performance": {"main": [[{"node": "analyze-performance-data", "type": "main", "index": 0}]]}, "fetch-integration-performance": {"main": [[{"node": "analyze-performance-data", "type": "main", "index": 0}]]}, "fetch-sla-performance": {"main": [[{"node": "analyze-performance-data", "type": "main", "index": 0}]]}, "analyze-performance-data": {"main": [[{"node": "save-performance-analysis", "type": "main", "index": 0}, {"node": "has-critical-alerts", "type": "main", "index": 0}, {"node": "is-hourly-report", "type": "main", "index": 0}]]}, "save-performance-analysis": {"main": [[{"node": "generate-analytics-summary", "type": "main", "index": 0}]]}, "has-critical-alerts": {"main": [[{"node": "send-critical-alert", "type": "main", "index": 0}], []]}, "send-critical-alert": {"main": [[{"node": "generate-analytics-summary", "type": "main", "index": 0}]]}, "is-hourly-report": {"main": [[{"node": "generate-performance-report", "type": "main", "index": 0}], []]}, "generate-performance-report": {"main": [[{"node": "send-performance-report", "type": "main", "index": 0}]]}, "send-performance-report": {"main": [[{"node": "generate-analytics-summary", "type": "main", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1", "saveManualExecutions": true, "callerPolicy": "workflowsFromSameOwner", "errorWorkflow": "escalation-error-handler"}, "versionId": "v2.0", "meta": {"templateCredsSetupCompleted": true, "instanceId": "performance-analytics-v2"}, "id": "performance-analytics-v2", "tags": [{"createdAt": "2024-01-15T10:00:00.000Z", "updatedAt": "2024-01-15T10:00:00.000Z", "id": "escalation-v2", "name": "escalation-v2"}, {"createdAt": "2024-01-15T10:00:00.000Z", "updatedAt": "2024-01-15T10:00:00.000Z", "id": "analytics", "name": "analytics"}, {"createdAt": "2024-01-15T10:00:00.000Z", "updatedAt": "2024-01-15T10:00:00.000Z", "id": "performance", "name": "performance"}]}