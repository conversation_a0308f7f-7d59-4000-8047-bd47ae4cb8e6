{"system": {"name": "Intelligent Human Escalation Agent", "version": "1.0.0", "environment": "production", "timezone": "America/Sao_Paulo", "locale": "pt-BR", "debug_mode": false}, "database": {"host": "${DB_HOST}", "port": "${DB_PORT}", "database": "${DB_NAME}", "username": "${DB_USER}", "password": "${DB_PASSWORD}", "ssl": true, "pool": {"min": 5, "max": 20, "idle_timeout": 30000, "acquire_timeout": 60000}, "schema": "agent"}, "ai_services": {"openai": {"api_key": "${OPENAI_API_KEY}", "model": "gpt-4-turbo-preview", "max_tokens": 2000, "temperature": 0.3, "timeout": 30000}, "fallback": {"enabled": true, "heuristic_analysis": true, "confidence_threshold": 0.7}}, "integrations": {"chatwoot": {"base_url": "${CHATWOOT_BASE_URL}", "api_token": "${CHATWOOT_API_TOKEN}", "account_id": "${CHATWOOT_ACCOUNT_ID}", "timeout": 15000, "retry_attempts": 3}, "slack": {"bot_token": "${SLACK_BOT_TOKEN}", "app_token": "${SLACK_APP_TOKEN}", "signing_secret": "${SLACK_SIGNING_SECRET}", "channels": {"critical_escalations": "#critical-escalations", "general_notifications": "#escalation-notifications", "monitoring": "#system-monitoring"}, "timeout": 10000}}, "escalation": {"default_sla_hours": 4, "priority_levels": {"low": {"score_range": [1, 3], "sla_multiplier": 2.0, "auto_assign": true}, "medium": {"score_range": [4, 6], "sla_multiplier": 1.5, "auto_assign": true}, "high": {"score_range": [7, 8], "sla_multiplier": 1.0, "auto_assign": true}, "critical": {"score_range": [9, 10], "sla_multiplier": 0.5, "auto_assign": true, "immediate_notification": true}}, "urgency_levels": {"low": {"keywords": ["question", "doubt", "information", "help"], "weight": 1}, "medium": {"keywords": ["problem", "issue", "error", "bug"], "weight": 2}, "high": {"keywords": ["urgent", "important", "asap", "priority"], "weight": 3}, "critical": {"keywords": ["critical", "emergency", "down", "outage", "security"], "weight": 4}}, "specializations": {"technical_support": {"keywords": ["technical", "api", "integration", "code", "development"], "default_queue": "tech-support", "sla_hours": 2}, "billing_support": {"keywords": ["billing", "payment", "invoice", "refund", "subscription"], "default_queue": "billing-support", "sla_hours": 4}, "general_support": {"keywords": ["general", "question", "help", "how to"], "default_queue": "general-support", "sla_hours": 6}, "sales_support": {"keywords": ["sales", "pricing", "demo", "trial", "purchase"], "default_queue": "sales-support", "sla_hours": 1}}}, "queues": {"default_capacity": 10, "load_balancing": "round_robin", "auto_scaling": {"enabled": true, "scale_up_threshold": 0.8, "scale_down_threshold": 0.3, "cooldown_minutes": 15}, "configurations": {"tech-support": {"max_capacity": 15, "sla_target_hours": 2, "specialization": "technical_support", "priority_weight": 1.2}, "billing-support": {"max_capacity": 10, "sla_target_hours": 4, "specialization": "billing_support", "priority_weight": 1.0}, "general-support": {"max_capacity": 20, "sla_target_hours": 6, "specialization": "general_support", "priority_weight": 0.8}, "sales-support": {"max_capacity": 8, "sla_target_hours": 1, "specialization": "sales_support", "priority_weight": 1.5}}}, "monitoring": {"health_check_interval_minutes": 5, "metrics_retention_days": 90, "alert_thresholds": {"queue_utilization": 0.85, "sla_breach_percentage": 0.1, "avg_response_time_hours": 6, "critical_escalations_count": 5, "agent_availability_percentage": 0.7}, "dashboard": {"refresh_interval_seconds": 30, "real_time_updates": true, "export_formats": ["json", "csv", "pdf"]}}, "security": {"encryption": {"algorithm": "AES-256-GCM", "key_rotation_days": 90}, "authentication": {"jwt_secret": "${JWT_SECRET}", "token_expiry_hours": 24, "refresh_token_expiry_days": 30}, "rate_limiting": {"requests_per_minute": 100, "burst_limit": 200, "window_minutes": 15}, "audit": {"log_all_operations": true, "sensitive_data_masking": true, "retention_days": 365}}, "performance": {"cache": {"ai_analysis_ttl_hours": 24, "queue_status_ttl_minutes": 5, "agent_status_ttl_minutes": 2, "max_cache_size_mb": 512}, "timeouts": {"webhook_response_seconds": 30, "database_query_seconds": 15, "external_api_seconds": 20, "ai_analysis_seconds": 45}, "concurrency": {"max_concurrent_escalations": 50, "worker_threads": 4, "queue_processing_batch_size": 10}}, "notifications": {"email": {"enabled": false, "smtp_host": "${SMTP_HOST}", "smtp_port": "${SMTP_PORT}", "smtp_user": "${SMTP_USER}", "smtp_password": "${SMTP_PASSWORD}"}, "sms": {"enabled": false, "provider": "twi<PERSON>", "api_key": "${SMS_API_KEY}", "from_number": "${SMS_FROM_NUMBER}"}, "push": {"enabled": false, "firebase_key": "${FIREBASE_KEY}"}}, "logging": {"level": "info", "format": "json", "file_rotation": {"enabled": true, "max_size_mb": 100, "max_files": 10, "compress": true}, "destinations": {"console": true, "file": true, "external_service": false}}, "features": {"ai_context_analysis": {"enabled": true, "confidence_threshold": 0.7, "fallback_to_heuristic": true}, "intelligent_routing": {"enabled": true, "load_balancing": true, "skill_matching": true}, "real_time_monitoring": {"enabled": true, "dashboard_updates": true, "alert_notifications": true}, "auto_escalation": {"enabled": true, "sla_monitoring": true, "priority_adjustment": true}, "analytics": {"enabled": true, "performance_tracking": true, "trend_analysis": true, "predictive_insights": false}}, "maintenance": {"cleanup_jobs": {"old_cache_entries": {"enabled": true, "schedule": "0 2 * * *", "retention_hours": 168}, "completed_escalations": {"enabled": true, "schedule": "0 3 * * 0", "retention_days": 90}, "audit_logs": {"enabled": true, "schedule": "0 4 1 * *", "retention_days": 365}}, "health_checks": {"database_connectivity": true, "external_services": true, "queue_processing": true, "memory_usage": true}}, "development": {"mock_ai_responses": false, "simulate_delays": false, "test_mode": false, "debug_webhooks": false, "verbose_logging": false}}