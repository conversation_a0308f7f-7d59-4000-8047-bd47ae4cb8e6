{"name": "Agente Inteligente v10.0 (The Campaign Management Edition)", "nodes": [{"parameters": {}, "id": "e8d66df2-466d-4bb4-b258-3d1f0ddaf631", "name": "TRIGGER: WhatsApp (Webhook)", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [-3700, -140], "webhookId": "whatsapp-inbox-v9", "path": "whatsapp-inbox-v9"}, {"parameters": {"content": "### 🚀 Agente Inteligente v10.0 (The Campaign Management Edition)\n\n**Propósito**: Esta versão introduz um módulo completo de gerenciamento de campanhas, permitindo a execução de outreach proativo e baseado em eventos de forma personalizada, segmentada e mensurável.\n\n**Novos Recursos (v10.0 - Campanhas):**\n\n- **✅ Arquitetura de Campanhas:** O sistema agora possui duas novas tabelas (`agent.campaigns` e `agent.campaign_interactions`) para definir, executar e rastrear campanhas. As campanhas podem ter status (`draft`, `active`), público-alvo (`target_tags`), e metas (`goal_metric`).\n\n- **✅ Motor de Campanhas Proativas (Workflow A):** Um novo fluxo agendado (`TRIGGER: MOTOR: Campanhas Proativas`) substitui o antigo outreach. Ele:\n  1. Busca campanhas ativas baseadas em data (`proactive_timed`).\n  2. Encontra contatos elegíveis que ainda não foram abordados.\n  3. Usa a IA para criar uma mensagem única para cada contato, combinando o prompt base da campanha com os insights específicos daquele contato.\n  4. Envia a mensagem e registra a interação.\n\n- **✅ Ouvinte de Campanhas por Evento (Workflow B):** Um novo webhook (`TRIGGER: Ouvinte de Eventos de Campanha`) permite acionar campanhas por eventos externos, como aniversários ou conquistas profissionais vindas de um CRM.\n  1. Recebe um evento (ex: `event_type: 'aniversario'`).\n  2. Encontra a campanha correspondente.\n  3. Usa a IA para criar e enviar uma mensagem contextualizada.\n\n- **✅ Rastreamento de Metas de Campanha (LLMOps Aprimorado):** O fluxo de resposta principal foi modificado para detectar se uma mensagem é uma resposta a uma campanha.\n  1. Verifica se o usuário recebeu uma mensagem de campanha nas últimas 24h.\n  2. Se sim, uma IA (`AI: Analyze Reply for Goal Match`) avalia se a resposta atinge a meta da campanha (ex: 'positive_reply').\n  3. Se a meta for atingida, o progresso da campanha (`current_progress`) é incrementado no banco de dados e a interação é marcada como um sucesso.", "height": 780}, "id": "e5860dd3-30ad-467f-94a2-ef6c2e39908d", "name": "DOCUMENTAÇÃO DO WORKFLOW v10.0", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-3740, -1800]}, {"parameters": {"values": {"string": [{"name": "channel", "value": "whatsapp"}]}, "options": {}}, "id": "3167eb27-edca-4fd4-acdc-6b5860361a68", "name": "AGENT: Normalize WhatsApp Payload", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [-3460, -140]}, {"parameters": {"conditions": {"boolean": [{"value1": "={{ !$json.fromMe }}"}]}}, "id": "ab75d8d3-5569-45da-901d-531e2850fc25", "name": "IF: Is User Message?", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [-2820, 500]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT id, nome, grau_de_conexao, tags, consentimento_enriquecimento, permissions, preferences, insights FROM agent.contatos WHERE telefone = '{{$json.senderNumber}}' LIMIT 1;", "options": {}}, "id": "0693a1ee-a912-4fcf-aa18-7b95085e7ca7", "name": "DB: Verificar Contato", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [-2580, 500], "credentials": {"postgres": {"id": "YOUR_POSTGRES_CREDENTIAL_ID", "name": "PostgreSQL DB"}}}, {"parameters": {"conditions": {"number": [{"value1": "={{ $items.length }}"}]}}, "id": "df2dae10-64cd-4e8c-859a-5f6068d34195", "name": "IF: Contato Existe?", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [-2380, 500]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "INSERT INTO agent.contatos (telefone, nome, tags, permissions, preferences) VALUES ('{{$json.senderNumber}}', '{{$json.pushName || `Contato ${$json.senderNumber}`}}', '{{JSON.stringify($json.tags)}}'::jsonb, '[]'::jsonb, '{}'::jsonb) RETURNING id, 1 as grau_de_conexao, '{{JSON.stringify($json.tags)}}'::jsonb as tags, false as consentimento_enriquecimento, '[]'::jsonb as permissions, '{}'::jsonb as preferences, '[]'::jsonb as insights;", "options": {}}, "id": "b0cf31ea-be12-40f4-a034-7fc44a4113ae", "name": "DB: <PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [-2380, 840], "credentials": {"postgres": {"id": "YOUR_POSTGRES_CREDENTIAL_ID", "name": "PostgreSQL DB"}}}, {"parameters": {}, "id": "893d5dfd-b4ef-4de8-9e5c-bd08d3e2de81", "name": "MERGE: Contact Info", "type": "n8n-nodes-base.merge", "typeVersion": 2, "position": [-2180, 500]}, {"parameters": {"values": {"string": [{"name": "<PERSON><PERSON>ey", "value": "={{ $json.isGroup ? 'ctx:group:' + $json.senderId : 'ctx:contact:' + $json.contactDbId }}"}]}, "options": {}}, "id": "766ef8b3-bd60-4e3f-a3d1-933e4cc0068a", "name": "AGENT: Generate Context Key", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [-880, 840]}, {"parameters": {"operation": "get", "key": "={{ $json.contextKey }}"}, "id": "c1f7ca45-1c39-44ef-a6b3-6cf1b4cd3b54", "name": "CACHE: <PERSON><PERSON>er Contexto Curto Prazo", "type": "n8n-nodes-base.redis", "typeVersion": 1, "position": [-680, 840], "continueOnFail": true, "credentials": {"redis": {"id": "YOUR_REDIS_CREDENTIAL_ID", "name": "Redis DB"}}}, {"parameters": {"routing": {"rules": {"values": [{"operation": "regex", "value1": "={{$json.messageType}}", "value2": "conversation|extendedTextMessage"}, {"operation": "contains", "value1": "={{$json.messageType}}", "value2": "image"}, {"operation": "contains", "value1": "={{$json.messageType}}", "value2": "audio"}, {"operation": "contains", "value1": "={{$json.messageType}}", "value2": "video"}, {"operation": "equal", "value1": "={{$json.messageType}}", "value2": "document"}]}, "fieldToMatch": ""}, "options": {}}, "id": "3127dd4f-8cc7-400f-be9d-7cae128148e6", "name": "Rotear por Tipo de Mensagem", "type": "n8n-nodes-base.switch", "typeVersion": 2, "position": [-1080, 840]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "INSERT INTO agent.mensagens (id_mensagem_wpp, id_contato, id_grupo, timestamp, tipo, direcao, conteudo)\nVALUES ('{{$json.messageId}}', {{$json.contactDbId}}, {{$json.isGroup ? `'${$json.senderId}'` : 'null'}}, NOW(), '{{$json.messageType}}', 'entrada', '{{$json.textContent.replace(/'/g, \"''\")}}');", "options": {}}, "id": "ab65a9ba-23de-42cd-9f79-c5c2a1dd0ce2", "name": "DB: Log Mensagem de Texto", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [-880, 500], "credentials": {"postgres": {"id": "YOUR_POSTGRES_CREDENTIAL_ID", "name": "PostgreSQL DB"}}}, {"parameters": {"method": "POST", "url": "={{ $credentials.EvolutionApi.baseUrl }}/message/sendText/{{$json.instanceName}}", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "apikey", "value": "={{ $credentials.EvolutionApi.apiKey }}"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "number", "value": "={{ $json.senderId }}"}, {"name": "options", "value": "={{ { \"delay\": 1200, \"presence\": \"composing\" } }}", "type": "json"}, {"name": "textMessage", "value": "={{ { \"text\": $json.formatted_ai_output } }}"}]}, "options": {}}, "id": "97463fd3-d9d1-4e78-95ff-cae11ca5d848", "name": "TOOL: Send WhatsApp Text Reply", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [2880, 340], "credentials": {"httpHeaderAuth": {"id": "YOUR_EVOLUTION_API_CREDENTIAL_ID", "name": "EvolutionApi"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "INSERT INTO agent.mensagens (id_mensagem_wpp, id_contato, id_grupo, timestamp, tipo, direcao, conteudo)\nVALUES ('{{$json.messageId}}_reply', {{$json.contactDbId}}, {{$json.isGroup ? `'${$json.senderId}'` : 'null'}}, NOW(), 'text_reply', 'saida', '{{$json.formatted_ai_output.replace(/'/g, \"''\")}}');", "options": {}}, "id": "7275d27d-9473-45a8-b590-f213df65d836", "name": "DB: Log Resposta da IA", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [3080, 500], "credentials": {"postgres": {"id": "YOUR_POSTGRES_CREDENTIAL_ID", "name": "PostgreSQL DB"}}}, {"parameters": {"operation": "set", "key": "={{ $json.contextKey }}", "value": "={{ ($('CACHE: Obter Contexto Curto Prazo').item.json.data ? $('CACHE: Obter Contexto Curto Prazo').item.json.data + '\\n' : '') + 'User: ' + $json.textContent + '\\nAgent: ' + $json.formatted_ai_output }}", "options": {"expiration": "={{ $json.ttl_seconds }}"}}, "id": "d748f572-132d-4dd6-9e01-d7481bbefc6c", "name": "CACHE: <PERSON><PERSON> Curto Prazo", "type": "n8n-nodes-base.redis", "typeVersion": 1, "position": [3280, 500], "credentials": {"redis": {"id": "YOUR_REDIS_CREDENTIAL_ID", "name": "Redis DB"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "INSERT INTO agent.mensagens (id_mensagem_wpp, id_contato, id_grupo, timestamp, tipo, direcao, conteudo)\nVALUES ('{{$json.messageId}}', {{$json.contactDbId}}, {{$json.isGroup ? `'${$json.senderId}'` : 'null'}}, NOW(), '{{$json.messageType}}', 'entrada', '{{$json.mediaUrl}}')\nRETURNING id;", "options": {}}, "id": "ba72ed12-a8c6-4cc2-9d32-d8c9a33bb75f", "name": "DB: <PERSON>g Mensagem de Mídia", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [-840, 2200], "credentials": {"postgres": {"id": "YOUR_POSTGRES_CREDENTIAL_ID", "name": "PostgreSQL DB"}}}, {"parameters": {"queueName": "={{ $json.queue }}", "message": "={{ JSON.stringify({ message_db_id: $json.id, media_url: $json.mediaUrl, media_type: $json.messageType, sender_jid: $json.senderId, instance: $json.instanceName }) }}", "options": {"priority": "={{$json.priority || 0}}"}}, "id": "b8f0ca43-b9da-419b-b236-8da9035f8d08", "name": "TOOL: Enviar para Fila Específica", "type": "n8n-nodes-base.rabbitMqSender", "typeVersion": 1, "position": [-220, 2200], "credentials": {"rabbitMq": {"id": "YOUR_RABBITMQ_CREDENTIAL_ID", "name": "RabbitMQ Local"}}}, {"parameters": {"method": "POST", "url": "={{ $credentials.EvolutionApi.baseUrl }}/message/sendText/{{$json.instanceName}}", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "apikey", "value": "={{ $credentials.EvolutionApi.apiKey }}"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "number", "value": "={{ $json.senderId }}"}, {"name": "options", "value": "={{ { \"delay\": 1200, \"presence\": \"composing\" } }}", "type": "json"}, {"name": "textMessage", "value": "={{ { \"text\": `<PERSON><PERSON><PERSON> sua mídia (${$json.messageType.replace('Message','')}). <PERSON><PERSON> estou analisando e te respondo em um instante! 🧠` } }}", "type": "json"}]}, "options": {}}, "id": "bcf5e714-b4a1-432d-949c-f90c422896da", "name": "TOOL: <PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [-20, 2200], "credentials": {"httpHeaderAuth": {"id": "YOUR_EVOLUTION_API_CREDENTIAL_ID", "name": "EvolutionApi"}}}, {"parameters": {"queueName": "processing_results_queue"}, "id": "c62b467b-1d7b-4dfa-b183-cc94f71fcb4e", "name": "TRIGGER: <PERSON><PERSON><PERSON>tados", "type": "n8n-nodes-base.rabbitMqTrigger", "typeVersion": 1, "position": [-3700, 3420], "credentials": {"rabbitMq": {"id": "YOUR_RABBITMQ_CREDENTIAL_ID", "name": "RabbitMQ Local"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "UPDATE agent.mensagens \nSET analise_midia = '{{ JSON.stringify($json.body.result).replace(/'/g, \"''\") }}'\nWHERE id = {{$json.body.message_db_id}}\nRETURNING (SELECT telefone FROM agent.contatos c WHERE c.id = id_contato) as \"senderNumber\",\n(SELECT id FROM agent.contatos c WHERE c.id = id_contato) as \"contactDbId\",\n(SELECT id_grupo FROM agent.mensagens m WHERE m.id = {{$json.body.message_db_id}}) as \"groupId\";", "options": {}}, "id": "d040a3d4-b789-4e01-b6a6-976cc13d1be5", "name": "DB: Atualizar com Análise", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [-3460, 3420], "credentials": {"postgres": {"id": "YOUR_POSTGRES_CREDENTIAL_ID", "name": "PostgreSQL DB"}}}, {"parameters": {}, "id": "2d9b6ccf-8b9a-4c28-98e8-dd438699efcd", "name": "<PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.errorTrigger", "typeVersion": 1, "position": [2980, -120]}, {"parameters": {"values": {"string": [{"name": "errorMessage", "value": "={{ $execution.error.message }}"}, {"name": "errorNode", "value": "={{ $execution.error.node.name }}"}, {"name": "errorData", "value": "={{ JSON.stringify($input.item.json) }}"}]}, "options": {}}, "id": "ff3cdd6e-9b2f-488b-a437-0ed3ab2edbb5", "name": "ERROR: Format Message", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [3180, -120]}, {"parameters": {"message": "=🚨 Erro no Workflow 'Agente Inteligente v10.0'\n\n**Nó:** `{{$json.errorNode}}`\n**Mensagem:** `{{$json.errorMessage}}`\n**Usuário:** `{{$json.senderId}}`", "options": {}}, "id": "2b6ff1aa-50b3-4fde-bc51-14fcbb9ab0d7", "name": "ALERT: Notify Admin (via Slack/Email)", "type": "n8n-nodes-base.noOp", "typeVersion": 1, "position": [3380, -120], "notes": "NOTA: <PERSON><PERSON> é um nó placeholder. Substitua-o por um nó de 'Send Email', 'Slack', 'Discord', ou um 'HttpRequest' para o seu serviço de notificação preferido para tornar os alertas funcionais."}, {"parameters": {"functionCode": "const redis = $redis.get();\nconst key = `rate_limit:user:{{$json.senderId}}`;\n\n// Get user profile to set dynamic limit\nconst userTags = $json.tags || [];\nlet limit = 20; // Default limit\n\nif ($json.isGroup) {\n  limit = 40; // Higher limit for groups\n} else if (userTags.includes('vip')) {\n  limit = 100; // Very high limit for VIPs\n} else if ($json.grau_de_conexao >= 4) {\n  limit = 50; // Higher limit for connected users\n}\n\nconst count = await redis.incr(key);\nif (count === 1) {\n  await redis.expire(key, 60); // Expira em 60 segundos\n}\n\n$json.requestCount = count;\n$json.limit = limit;\n\nreturn items;", "options": {}}, "id": "d04a60f9-f458-45fe-8ac0-41ed499edba2", "name": "AGENT: Dynamic Rate Limiter Gate", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1280, 280]}, {"parameters": {"conditions": {"number": [{"value1": "={{ $json.requestCount }}", "operation": "larger", "value2": "={{ $json.limit }}"}]}}, "id": "0dae1774-a740-4d43-bdce-6ebc45d315b9", "name": "IF: Rate Limit Exceeded?", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [-1280, -20]}, {"parameters": {"method": "POST", "url": "={{ $credentials.EvolutionApi.baseUrl }}/message/sendText/{{$json.instanceName}}", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "apikey", "value": "={{ $credentials.EvolutionApi.apiKey }}"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "number", "value": "={{ $json.senderId }}"}, {"name": "textMessage", "value": "={{ { \"text\": \"<PERSON><PERSON>, respira! 🧘‍♂️ Você enviou muitas mensagens rapidamente. Por favor, aguarde um minuto antes de continuar.\" } }}"}]}, "options": {}}, "id": "252fcd2e-1e96-4191-8ac3-3cd711e5a5f1", "name": "TOOL: Enviar Msg Rate Limit", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [-1280, -220], "credentials": {"httpHeaderAuth": {"id": "YOUR_EVOLUTION_API_CREDENTIAL_ID", "name": "EvolutionApi"}}}, {"parameters": {"routing": {"rules": {"values": [{"operation": "contains", "value1": "={{$json.messageType}}", "value2": "audio"}, {"operation": "contains", "value1": "={{$json.messageType}}", "value2": "image"}, {"operation": "contains", "value1": "={{$json.messageType}}", "value2": "video"}, {"operation": "contains", "value1": "={{$json.messageType}}", "value2": "pdf"}]}, "fieldToMatch": ""}, "options": {}}, "id": "f51c7694-a15d-49fe-bd2b-c8de81cd6631", "name": "Rotear por TIPO de Mídia", "type": "n8n-nodes-base.switch", "typeVersion": 2, "position": [-640, 2200]}, {"parameters": {"values": {"string": [{"name": "queue", "value": "audio_queue"}]}, "options": {}}, "id": "18f03ff9-b7b8-44bd-9c09-eb5942de856b", "name": "Set Audio Queue", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [-640, 2020]}, {"parameters": {"values": {"string": [{"name": "queue", "value": "image_queue"}]}, "options": {}}, "id": "4b6b6ec4-1e0e-436f-b27e-394ff03d922a", "name": "Set Image Queue", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [-640, 2360]}, {"parameters": {"values": {"string": [{"name": "queue", "value": "video_processing_queue"}]}, "options": {}}, "id": "ddb81d8d-ab51-4f40-848e-d748f95c46e3", "name": "Set Video Queue", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [-640, 2520]}, {"parameters": {"agent": "simple", "model": "gpt-4o-mini", "text": "Detect the primary language of the following text. Respond with only the two-letter ISO 639-1 code (e.g., pt, en, es). If you are unsure, respond with 'pt'.\n\nText: \"{{ $json.textContent }}\"", "options": {}}, "id": "ad6cae45-f094-4d10-86c0-43572d422a57", "name": "AI: Detectar Idioma", "type": "@n8n/n8n-nodes-langchain.chain", "typeVersion": 1, "position": [560, 500], "credentials": {"openAiApi": {"id": "YOUR_OPENAI_CREDENTIAL_ID", "name": "OpenAI Account"}}}, {"parameters": {"conditions": {"boolean": [{"value1": "={{ $items[0].execution.error !== undefined }}", "operation": "equal", "value2": true}]}, "options": {}}, "id": "c60a2830-4663-4de7-920f-0740a6b6f005", "name": "IF: Primary AI Failed?", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [2480, 800]}, {"parameters": {"values": {"number": [{"name": "processingTimeMs", "value": "={{ Date.now() - Date.parse($startTime) }}"}], "string": [{"name": "channel", "value": "={{ $json.channel }}"}]}, "options": {}}, "id": "f6858e80-82a1-432d-9473-b7b5949d0347", "name": "METRICS: Log Execution Time", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [3960, 500]}, {"parameters": {"method": "POST", "url": "https://api.twilio.com/2010-04-01/Accounts/YOUR_TWILIO_ACCOUNT_SID/Calls.json", "authentication": "predefinedCredential", "credentialType": "t<PERSON><PERSON><PERSON><PERSON>", "sendBody": true, "bodyParameters": {"parameters": [{"name": "To", "value": "={{ $json.senderNumber }}"}, {"name": "From", "value": "YOUR_TWILIO_PHONE_NUMBER"}, {"name": "Twiml", "value": "<Response><Say language=\"pt-BR\"><PERSON><PERSON><PERSON>, você solicitou uma chamada. Um de nossos atendentes irá falar com você em breve.</Say></Response>"}]}, "options": {}}, "id": "ab83a48e-ffef-4be7-ae86-5381a1792df0", "name": "TOOL: <PERSON><PERSON><PERSON> (Twilio)", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [1520, -60], "credentials": {"twilioApi": {"id": "YOUR_TWILIO_CREDENTIAL_ID", "name": "Twilio API"}}}, {"parameters": {"agent": "simple", "model": "gpt-4o-mini", "text": "Analyze the user's message for its emotion and tone. Respond with a JSON object with two keys: `emotion` and `agent_tone`. \nFor 'emotion', choose one from: [joyful, neutral, sad, angry, surprised, analytical]. \nFor 'agent_tone', choose the best corresponding response tone from: [energetic_and_friendly, calm_and_neutral, empathetic_and_supportive, calm_and_conciliatory, curious_and_engaging, formal_and_informative].\n\nExample response: {\"emotion\": \"angry\", \"agent_tone\": \"calm_and_conciliatory\"}\n\nUser Message: \"{{ $json.textContent }}\"", "options": {}}, "id": "07e3cf44-482a-43d7-8656-3decc1e9389f", "name": "AI: <PERSON><PERSON><PERSON> & Tom", "type": "@n8n/n8n-nodes-langchain.chain", "typeVersion": 1, "position": [160, 500], "credentials": {"openAiApi": {"id": "YOUR_OPENAI_CREDENTIAL_ID", "name": "OpenAI Account"}}}, {"parameters": {"functionCode": "try {\n  const analysis = JSON.parse(items[0].json.data);\n  items[0].json.user_emotion = analysis.emotion;\n  items[0].json.agent_tone = analysis.agent_tone;\n} catch (e) {\n  items[0].json.user_emotion = 'neutral';\n  items[0].json.agent_tone = 'calm_and_neutral';\n}\n\nreturn items;", "options": {}}, "id": "c6204565-5c1a-4643-af0c-f38b25da11ce", "name": "AGENT: Parse Emotion Analysis", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [160, 700]}, {"parameters": {"routing": {"rules": {"values": [{"operation": "equal", "value1": "={{$json.channel}}", "value2": "whatsapp"}, {"operation": "equal", "value1": "={{$json.channel}}", "value2": "sms"}]}, "fieldToMatch": "={{$json.channel}}"}, "options": {}}, "id": "edffc3b0-ebc3-4d40-bdde-be76d49ca71a", "name": "ROUTE: Reply Format by Channel", "type": "n8n-nodes-base.switch", "typeVersion": 2, "position": [2880, 500]}, {"parameters": {"method": "POST", "url": "https://api.elevenlabs.io/v1/text-to-speech/YOUR_VOICE_ID_HERE", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "xi-api-key", "value": "={{ $credentials.elevenLabs.apiKey }}"}, {"name": "Accept", "value": "audio/mpeg"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "text", "value": "={{$json.ai_output}}"}, {"name": "model_id", "value": "eleven_multilingual_v2"}, {"name": "voice_settings", "value": "={{ { \"stability\": 0.5, \"similarity_boost\": 0.75 } }}", "type": "json"}]}, "options": {"response": {"response": {"returnFull": false, "returnBody": true, "returnHeaders": false, "responseFormat": "file"}}}}, "id": "d041c2c3-41ec-43c3-b3c9-f1ab755869a8", "name": "TOOL: Text-to-Speech (ElevenLabs)", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [2680, 220], "credentials": {"httpHeaderAuth": {"id": "YOUR_ELEVENLABS_CREDENTIAL_ID", "name": "ElevenLabs"}}}, {"parameters": {"method": "POST", "url": "={{ $credentials.EvolutionApi.baseUrl }}/message/sendAudio/{{$json.instanceName}}", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "apikey", "value": "={{ $credentials.EvolutionApi.apiKey }}"}]}, "sendBody": true, "contentType": "multipart-form-data", "bodyParameters": {"parameters": [{"name": "number", "value": "={{ $json.senderId }}"}, {"name": "audioMessage[mimetype]", "value": "audio/mpeg"}, {"name": "audioMessage[audio]", "value": "={{ $binary.data }}"}]}, "options": {}}, "id": "4b7b285b-ccf6-4767-af91-c124ec6fc931", "name": "TOOL: Send WhatsApp Audio Reply", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [2880, 220], "credentials": {"httpHeaderAuth": {"id": "YOUR_EVOLUTION_API_CREDENTIAL_ID", "name": "EvolutionApi"}}}, {"parameters": {"agent": "simple", "model": "gpt-4o-mini", "text": "Based on this conversation exchange, extract key insights about the user. Focus on their stated interests, problems, goals, or products mentioned. Return the insights as a JSON object with a single key `new_insights`, which is an array of strings. If no new insights are found, return an empty array. \n\nUser: {{$json.textContent}}\nAgent: {{$json.ai_output}}\n\nExample Response: {\"new_insights\": [\"interested_in_ia\", \"has_problem_with_billing\"]}", "options": {}}, "id": "d1e70418-4a69-4e78-be7c-c67d71626fce", "name": "AI: Extrair Insights do Contato", "type": "@n8n/n8n-nodes-langchain.chain", "typeVersion": 1, "position": [3480, 500], "credentials": {"openAiApi": {"id": "YOUR_OPENAI_CREDENTIAL_ID", "name": "OpenAI Account"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "UPDATE agent.contatos \nSET insights = COALESCE(insights, '[]'::jsonb) || '{{ $json.data.new_insights ? JSON.stringify($json.data.new_insights) : \"[]\" }}'::jsonb, \nlast_update = NOW()\nWHERE id = {{$json.contactDbId}};", "options": {}}, "id": "7bf3b934-297c-4fc9-b684-2a62372fde1c", "name": "DB: Atualizar Perfil do Contato", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [3680, 500], "credentials": {"postgres": {"id": "YOUR_POSTGRES_CREDENTIAL_ID", "name": "PostgreSQL DB"}}}, {"parameters": {"routing": {"rules": {"values": [{"operation": "stringContains", "value1": "={{$json.body.media_type}}", "value2": "audio"}]}, "fieldToMatch": ""}, "options": {}}, "id": "06b02660-f975-4700-bc56-3c5ef82672cc", "name": "SWITCH: Resultado é Transcrição?", "type": "n8n-nodes-base.switch", "typeVersion": 2, "position": [-3260, 3420]}, {"parameters": {"functionCode": "// Prepara os dados da transcrição para serem processados como uma mensagem de texto normal.\nconst original_payload = items[0].json.body;\n\n// Remapeia os campos para o formato esperado pelo `AGENT: Parse Common Payload`\nconst newBody = {};\nnewBody.instance = original_payload.instance;\nnewBody.data = {\n    messageType: 'conversation',\n    message: {\n        conversation: original_payload.result.transcription\n    },\n    key: {\n        remoteJid: original_payload.sender_jid,\n        id: `transcription_${original_payload.message_db_id}`,\n        fromMe: false\n    }\n};\n\nitems[0].json.body = newBody;\n\nreturn items;", "options": {}}, "id": "b7edcfcf-9d41-48cd-b145-c4a4506b3e89", "name": "AGENT: Preparar Loop de Transcrição", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-3260, 3620]}, {"parameters": {"rule": {"interval": [{"triggerAtHour": 2}]}}, "id": "ab81b5ff-813c-43f1-bd56-788b13c77d4c", "name": "TRIGGER: Daily Job (2 AM)", "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1.1, "position": [-3700, 3840]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT \n  (SELECT COUNT(*) FROM agent.contatos) as total_contacts,\n  (SELECT COUNT(*) FROM agent.mensagens WHERE timestamp >= NOW() - INTERVAL '1 day') as messages_last_24h,\n  (SELECT AVG((analise_sentimento->>'score')::numeric) FROM agent.mensagens WHERE timestamp >= NOW() - INTERVAL '1 day' AND analise_sentimento->>'score' IS NOT NULL) as avg_sentiment_score,\n  (SELECT jsonb_object_agg(g.group_type, g.count) FROM (SELECT group_type, count(*) as count FROM agent.group_metadata GROUP BY group_type) g) as contacts_by_group_type,\n  (SELECT COUNT(*) FROM agent.contatos WHERE consentimento_enriquecimento = TRUE) as consented_users,\n  (SELECT jsonb_object_agg(c.name, c.current_progress)\n   FROM agent.campaigns c WHERE c.status = 'active') as campaign_progress\n;", "options": {}}, "id": "bd16999a-f4ef-4b45-8ce0-d5a22df67431", "name": "DB: <PERSON><PERSON><PERSON>ti<PERSON>", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [-3460, 3840], "credentials": {"postgres": {"id": "YOUR_POSTGRES_CREDENTIAL_ID", "name": "PostgreSQL DB"}}}, {"parameters": {}, "id": "3bbdfa7a-6246-46c5-8f6b-acb023e1f744", "name": "MERGE: All Channels", "type": "n8n-nodes-base.merge", "typeVersion": 2, "position": [-3260, 500]}, {"parameters": {"agent": "anthropic.claude-3-haiku-********-v1:0", "text": "{{$json.textContent}}", "options": {"systemMessage": "You are a backup AI assistant. The primary AI failed. Please provide a concise and helpful response to the user's message."}}, "id": "da14cd0a-2007-422f-8cf5-bbca0e57f00d", "name": "AI: <PERSON>up Agent (Anthropic)", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1, "position": [2280, 940], "credentials": {"anthropicApi": {"id": "YOUR_ANTHROPIC_CREDENTIAL_ID", "name": "Anthropic Account"}}}, {"parameters": {"values": {"string": [{"name": "ai_output", "value": "={{ $('AI: Backup Agent (Anthropic)').item.json.data.output }}"}]}, "options": {}}, "id": "90e44201-1e24-42b7-a3a8-a3f85822f7cd", "name": "AGENT: Set Backup AI Output", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [-2180, 940]}, {"parameters": {}, "id": "ad64c9ab-443b-489e-8c3e-d82084b66df2", "name": "MERGE: AI Responses", "type": "n8n-nodes-base.merge", "typeVersion": 2, "position": [2480, 500]}, {"parameters": {"method": "POST", "url": "https://language.googleapis.com/v1/documents:analyzeSentiment?key={{$credentials.googleApi.apiKey}}", "sendHeaders": true, "headerParameters": {"parameters": []}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "document", "value": "={{ { \"type\": \"PLAIN_TEXT\", \"content\": $json.textContent, \"language\": $json.language } }}", "type": "json"}]}, "options": {}}, "id": "d040ed55-e51c-4b61-bbce-14cf3ec37ec1", "name": "TOOL: Pro Sentiment Analysis (Google NLP)", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [360, 500], "continueOnFail": true, "credentials": {"googleApi": {"id": "YOUR_GOOGLE_API_CREDENTIAL_ID", "name": "Google API"}}}, {"parameters": {}, "id": "b3e36e78-0cf7-4f68-96ad-d0ca17d0c3dd", "name": "TRIGGER: Telegram (Webhook)", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [-3700, 220], "webhookId": "telegram-inbox-v9", "path": "telegram-inbox-v9"}, {"parameters": {"values": {"string": [{"name": "channel", "value": "telegram"}]}, "options": {}}, "id": "40b15b6d-a6fd-4be0-8041-5ac72f5d9434", "name": "AGENT: Normalize Telegram Payload", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [-3460, 220]}, {"parameters": {}, "id": "b88b0f7e-fdba-4d2c-91bb-bdccac656cd8", "name": "TRIGGER: SMS (Twilio Webhook)", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [-3700, 500], "webhookId": "sms-inbox-v9", "path": "sms-inbox-v9", "httpMethod": "POST"}, {"parameters": {"values": {"string": [{"name": "channel", "value": "sms"}]}, "options": {}}, "id": "ad6ffeb5-44d4-4a25-a8c9-04430e3abf16", "name": "AGENT: Normalize SMS Payload", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [-3460, 500]}, {"parameters": {"agent": "simple", "model": "gpt-4o-mini", "text": "The user sent an audio message. The transcribed text is below. Detect the user's primary intent from the text. Choose one from the expert list. If unsure, respond with 'chitchat_agent'.\n\nExpert List: [calendar_agent, email_agent, data_analyst_agent, chitchat_agent, set_preference_agent, tool_agent]\n\nTranscribed Text: \"{{ $json.body.result.transcription }}\"", "options": {}}, "id": "c1fcf4e8-8bfe-4c54-9721-fffc2df70068", "name": "AI: <PERSON><PERSON><PERSON> In<PERSON>t from Audio", "type": "@n8n/n8n-nodes-langchain.chain", "typeVersion": 1, "position": [-3020, 3620], "credentials": {"openAiApi": {"id": "YOUR_OPENAI_CREDENTIAL_ID", "name": "OpenAI Account"}}}, {"parameters": {"rule": {"mode": "everyX", "unit": "minutes", "value": 1}}, "id": "067dd523-a1df-4b60-8f92-7de1c0c3be7c", "name": "TRIGGER: Check Reminders (Every Minute)", "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1.1, "position": [-3700, 4260]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT id, contact_id, channel, message, sender_id, instance_name\nFROM agent.reminders\nWHERE reminder_time <= NOW() AND status = 'scheduled';", "options": {}}, "id": "5f993358-1549-43c9-aa92-7f2824bb8d43", "name": "DB: <PERSON><PERSON> Due Reminders", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [-3460, 4260], "credentials": {"postgres": {"id": "YOUR_POSTGRES_CREDENTIAL_ID", "name": "PostgreSQL DB"}}}, {"parameters": {"batchSize": 1, "options": {}}, "id": "ab75d045-3da2-43bb-bbd5-a30932cd560b", "name": "Loop Over Reminders", "type": "n8n-nodes-base.splitInBatches", "typeVersion": 2, "position": [-3260, 4260]}, {"parameters": {"method": "POST", "url": "={{ $credentials.EvolutionApi.baseUrl }}/message/sendText/{{$json.instance_name}}", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "apikey", "value": "={{ $credentials.EvolutionApi.apiKey }}"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "number", "value": "={{$json.sender_id}}"}, {"name": "textMessage", "value": "={{ { \"text\": \"🔔 Lembrete: \" + $json.message } }}"}]}, "options": {}}, "id": "2da1ef4b-01ee-4899-ae1e-450f6bf3dd7e", "name": "TOOL: <PERSON> Reminder (WhatsApp)", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [-3020, 4260], "credentials": {"httpHeaderAuth": {"id": "YOUR_EVOLUTION_API_CREDENTIAL_ID", "name": "EvolutionApi"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "UPDATE agent.reminders SET status = 'sent' WHERE id = {{$json.id}};", "options": {}}, "id": "7665ac2f-7a43-47be-b1b7-fc7ac85514ee", "name": "DB: <PERSON> as <PERSON><PERSON>", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [-2820, 4260], "credentials": {"postgres": {"id": "YOUR_POSTGRES_CREDENTIAL_ID", "name": "PostgreSQL DB"}}}, {"parameters": {"functionCode": "const userTags = items[0].json.tags || [];\nlet ttl = 3600; // 1 hour default\n\nif (userTags.includes('vip')) {\n  ttl = 86400; // 24 hours for VIPs\n} else if (userTags.includes('active_support')) {\n  ttl = 14400; // 4 hours for active tickets\n} else if (userTags.includes('new_contact')) {\n  ttl = 28800; // 8 hours for new contacts to allow more interaction\n}\n\nitems[0].json.ttl_seconds = ttl;\n\nreturn items;", "options": {}}, "id": "be5ca223-9fbd-40d0-bb3e-6ce523fd03d0", "name": "AGENT: Set Dynamic TTL", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [3080, 660]}, {"parameters": {"functionCode": "const redis = $redis.get();\nconst key = `manual_override:user:{{$json.senderId}}`;\n\nconst override = await redis.get(key);\n\n// A flag será 'true' se o override estiver ativo.\nitems[0].json.manualOverrideActive = (override === 'true');\n\nreturn items;", "options": {}}, "id": "23d7da01-4475-47e0-9430-85fcc13bb7f5", "name": "DB: Check for Manual Override Flag", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-3020, 280], "credentials": {"redis": {"id": "YOUR_REDIS_CREDENTIAL_ID", "name": "Redis DB"}}}, {"parameters": {"conditions": {"boolean": [{"value1": "={{$json.manualOverrideActive}}", "operation": "equal", "value2": true}]}}, "id": "ba72ed12-a8c6-4cc2-9d32-d8c9a33bb75d", "name": "IF: Manual Override Active?", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [-3020, 60]}, {"parameters": {"message": "Fim do fluxo. Atendimento manual ativo.", "options": {}}, "id": "********-4122-4a0e-ae5c-9da5be08cdd4", "name": "Stop (Manual Override)", "type": "n8n-nodes-base.noOp", "typeVersion": 1, "position": [-3020, -140]}, {"parameters": {"method": "POST", "url": "=https://api.twilio.com/2010-04-01/Accounts/YOUR_TWILIO_ACCOUNT_SID/Messages.json", "authentication": "predefinedCredential", "credentialType": "t<PERSON><PERSON><PERSON><PERSON>", "sendBody": true, "bodyParameters": {"parameters": [{"name": "To", "value": "={{$json.senderId}}"}, {"name": "From", "value": "YOUR_TWILIO_PHONE_NUMBER"}, {"name": "Body", "value": "={{$json.formatted_ai_output}}"}]}, "options": {}}, "id": "27f7de11-fd31-482a-a9a7-9657edb56952", "name": "TOOL: Send SMS Reply", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [2880, 600], "credentials": {"twilioApi": {"id": "YOUR_TWILIO_CREDENTIAL_ID", "name": "Twilio API"}}}, {"parameters": {"functionCode": "// SECURITY NODE - Use a dedicated library in production!\n// This node provides basic sanitization against common vectors.\n// It's a first line of defense, not a complete solution.\n\nconst raw_text = items[0].json.textContent;\nif (!raw_text) {\n  items[0].json.sanitizedTextContent = '';\n  return items;\n}\n\n// 1. Agressively remove anything that looks like a script tag.\nlet sanitized = raw_text.replace(/<script\\b[^>]*>([\\s\\S]*?)<\\/script>/gmi, '');\n\n// 2. Agressively remove common SQL injection patterns. \n// FOR PRODUCTION: ALWAYS use parameterized queries or an ORM instead of string concatenation.\nsanitized = sanitized.replace(/(--|;|\\/\\*|' OR '1'='1|' or '1'='1)/gi, '');\n\n// 3. Remove other potentially harmful HTML tags, leaving only basic formatting.\n// A more robust solution would use a library like `sanitize-html`.\nsanitized = sanitized.replace(/<(?!b|i|strong|em|p|br|u)[^>]+>/gi, '');\n\n// 4. Escape special characters to prevent injection in other contexts.\nsanitized = sanitized.replace(/[&<>'\"/]/g, function (s) {\n    return {\n        '&': '&amp;',\n        '<': '&lt;',\n        '>': '&gt;',\n        '\"': '&quot;',\n        \"'\": '&#39;',\n        \"/\": '&#x2F;'\n    }[s];\n});\n\n\nitems[0].json.sanitizedTextContent = sanitized;\n\nif (raw_text !== sanitized) {\n  console.warn(`Potential malicious input sanitized from user ${items[0].json.senderId}. Original: ${raw_text}`)\n  // Here you could also send a security alert.\n  items[0].json.security_alert = true;\n}\n\nreturn items;", "options": {}}, "id": "c8117565-5c1a-4643-af0c-f38b25da11ce", "name": "AGENT: Sanitize Input", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1280, 80], "notes": "ALERTA DE SEGURANÇA: Esta sanitização é básica. Para ambientes de produção, use bibliotecas dedicadas como 'sanitize-html' e sempre use queries parametrizadas no banco de dados."}, {"parameters": {"functionCode": "const intent = items[0].json.data;\nlet priority = 0; // Normal priority\n\nif (intent === 'complaint') {\n  priority = 9; // High priority\n} else if (intent === 'request_info') {\n  priority = 5; // Medium priority\n}\n\nitems[0].json.priority = priority;\nreturn items;", "options": {}}, "id": "14eb3a1b-c75c-4861-ac09-acbeff331acb", "name": "AGENT: Set Task Priority", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-420, 2200]}, {"parameters": {"method": "POST", "url": "=https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK", "sendBody": true, "bodyParameters": {"parameters": [{"name": "text", "value": "=*Relatório Diário do Agente Inteligente v10.0*=\n\n*Resumo Quantitativo:*\n- Total de Contatos: `{{$('DB: Gerar Relatório Quantitativo').item.json.total_contacts}}`\n- Mensagens (24h): `{{$('DB: Gerar Relatório Quantitativo').item.json.messages_last_24h}}`\n- Sentimento Médio (24h): `{{$node['DB: Gerar Relatório Quantitativo'].json.avg_sentiment_score ? $node['DB: Gerar Relatório Quantitativo'].json.avg_sentiment_score.toFixed(2) : 'N/A'}}`\n- Progresso das Campanhas: `{{$node['DB: Gerar Relatório Quantitativo'].json.campaign_progress ? JSON.stringify($node['DB: Gerar Relatório Quantitativo'].json.campaign_progress, null, 2) : 'Nenhuma campanha ativa.'}}`"}]}, "options": {}}, "id": "1cf385da-09f6-4e15-9e25-8717e9c6d35a", "name": "TOOL: Send Daily Report (Slack)", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [-3020, 3840]}, {"parameters": {"agent": "simple", "model": "gpt-4o-mini", "text": "Format the following AI response based on the target channel: '{{$json.channel}}'. \nFor WhatsApp and Instagram, use emojis, short paragraphs and markdown like *bold* and _italic_.\nFor SMS, be very concise, without markdown and preferably under 160 characters. \nFor Telegram and Email, use full markdown for formatting (bold, italics, lists, etc).\n\nOriginal AI Response:\n\n{{$json.ai_output}}", "options": {}}, "id": "b1354020-854a-406d-92b7-66e29e1f3c8e", "name": "AI: Format Response by Channel", "type": "@n8n/n8n-nodes-langchain.chain", "typeVersion": 1, "position": [2680, 500], "credentials": {"openAiApi": {"id": "YOUR_OPENAI_CREDENTIAL_ID", "name": "OpenAI Account"}}}, {"parameters": {"values": {"string": [{"name": "formatted_ai_output", "value": "={{$json.data}}"}]}, "options": {}}, "id": "f31a6c0e-6422-40f0-bc49-098da34f6fc0", "name": "AGENT: Set Formatted AI Output", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [2680, 740]}, {"parameters": {"method": "POST", "url": "https://YOUR_PROMETHEUS_PUSHGATEWAY_URL/metrics/job/n8n_agent/instance/{{$json.instanceName}}", "sendBody": true, "contentType": "text", "body": "n8n_request_latency_ms{channel=\"{{$json.channel}}\",sender=\"{{$json.senderId}}\",group=\"{{$json.isGroup}}\"} {{$json.processingTimeMs}}\nn8n_message_count{channel=\"{{$json.channel}}\",is_group=\"{{$json.isGroup}}\"} 1\n{{ $items[0].execution.error ? 'n8n_workflow_failures_total{channel=\"' + $json.channel + '\"} 1\\n' : '' }}\n{{ $('IF: Primary AI Failed?').item.json.ai_backup_used ? 'n8n_ai_backup_uses_total{channel=\"' + $json.channel + '\"} 1\\n' : '' }}", "options": {}}, "id": "a57c7bd8-280d-40ba-83f0-4660d5b62b1c", "name": "TOOL: Publish Metrics to Prometheus", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [3960, 260]}, {"parameters": {}, "id": "ac502c8d-de58-47f2-b7e9-a31dfb15ff71", "name": "TRIGGER: Instagram Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [-3700, 740], "webhookId": "instagram-inbox-v9", "path": "instagram-inbox-v9"}, {"parameters": {"values": {"string": [{"name": "channel", "value": "instagram"}]}, "options": {}}, "id": "3316e5ff-f424-4213-830a-5b3d35f95559", "name": "AGENT: Normalize Instagram Payload", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [-3460, 740]}, {"parameters": {}, "id": "92555430-d5d4-4212-8427-5056333acbcf", "name": "TRIGGER: <PERSON><PERSON> (SendGrid)", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [-3700, 980], "webhookId": "email-inbox-v9", "path": "email-inbox-v9"}, {"parameters": {"values": {"string": [{"name": "channel", "value": "email"}]}, "options": {}}, "id": "b2e3b4f4-3c01-4512-963a-0d93703e0d25", "name": "AGENT: Normalize Email Payload", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [-3460, 980]}, {"parameters": {"functionCode": "// Este nó foi adicionado para centralizar a extração de dados dos diferentes webhooks (WhatsApp, Telegram, SMS etc.)\n// e criar um payload comum para o restante do workflow.\n\nconst item = items[0];\nconst channel = item.json.channel;\nconst body = item.json.body;\n\nswitch(channel) {\n  case 'facebook_lead_ad':\n    // Este caso é customizado para receber o lead do facebook e normalizar.\n    item.json.senderId = (body.phone_number) ? `${body.phone_number}@c.us` : (body.email || 'unknown'); // Formato para whatsapp ou email\n    item.json.senderNumber = body.phone_number || '';\n    item.json.textContent = `Novo lead: ${body.full_name}, Email: ${body.email}`;\n    item.json.messageType = 'conversation';\n    item.json.isGroup = false;\n    item.json.messageId = `fb_lead_${$now.toMillis()}`;\n    item.json.instanceName = 'facebook_leads';\n    item.json.pushName = body.full_name;\n    item.json.fromMe = false;\n    item.json.mediaUrl = '';\n    break;\n  case 'whatsapp':\n    item.json.senderId = body.data?.key?.remoteJid || '';\n    item.json.senderNumber = (body.data?.key?.remoteJid || '').split('@')[0];\n    item.json.textContent = body.data?.message?.conversation || body.data?.message?.extendedTextMessage?.text || body.data?.message?.imageMessage?.caption || body.data?.message?.videoMessage?.caption || '';\n    // Handle PDF documents from Evolution API\n    if (body.data?.message?.documentMessage?.mimetype === 'application/pdf') {\n        item.json.messageType = 'document';\n    } else {\n        item.json.messageType = Object.keys(body.data?.message || {})[0] || 'unknown';\n    }\n    item.json.isGroup = (body.data?.key?.remoteJid || '').includes('@g.us');\n    item.json.messageId = body.data?.key?.id;\n    item.json.instanceName = body.instance;\n    item.json.pushName = body.data?.pushName;\n    item.json.fromMe = body.data?.key?.fromMe || false;\n    item.json.mediaUrl = body.data?.message?.imageMessage?.url || body.data?.message?.audioMessage?.url || body.data?.message?.videoMessage?.url || body.data?.message?.documentMessage?.url || '';\n    item.json.groupName = body.data?.chat?.name || '';\n    break;\n  case 'telegram':\n    item.json.senderId = body.message?.from?.id?.toString() || '';\n    item.json.senderNumber = body.message?.from?.id?.toString() || '';\n    item.json.textContent = body.message?.text || '';\n    item.json.messageType = 'conversation';\n    item.json.isGroup = (body.message?.chat?.type === 'group' || body.message?.chat?.type === 'supergroup');\n    item.json.messageId = body.message?.message_id?.toString();\n    item.json.instanceName = 'telegram_bot';\n    item.json.pushName = body.message?.from?.first_name;\n    item.json.fromMe = false;\n    break;\n  case 'sms':\n    item.json.senderId = body.From || '';\n    item.json.senderNumber = body.From || '';\n    item.json.textContent = body.Body || '';\n    item.json.messageType = 'conversation';\n    item.json.isGroup = false;\n    item.json.messageId = body.MessageSid;\n    item.json.instanceName = 'twilio_sms';\n    item.json.pushName = body.From;\n    item.json.fromMe = false;\n    break;\n  case 'voice_webhook': // Novo canal para a interface de voz\n    item.json.senderId = body.senderId || 'unknown_voice_user';\n    item.json.senderNumber = body.senderNumber || 'unknown_voice_user';\n    item.json.textContent = body.transcribedText || '';\n    item.json.messageType = 'conversation';\n    item.json.isGroup = false;\n    item.json.messageId = `voice_${$now.toMillis()}`;\n    item.json.instanceName = 'voice_gateway';\n    item.json.pushName = body.pushName || 'Voice User';\n    item.json.fromMe = false;\n    break;\n  default:\n    console.error(`Canal desconhecido: ${channel}`);\n    return [];\n}\n\n// Garante que a ID de contato do DB esteja no item\nitem.json.contactDbId = item.json.id;\nitem.json.preferences = item.json.preferences || {};\n\nreturn item;", "options": {"keepOnlySet": false}}, "id": "e67e37de-c8cb-4e3a-9e9f-e3c35b67dabc", "name": "AGENT: Parse Common Payload", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-3020, 500]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT\n  '{{$json.senderNumber}}' = (SELECT value FROM agent.settings WHERE key = 'owner_jid' LIMIT 1)\n  OR\n  'tool_user' = ANY(SELECT jsonb_array_elements_text(permissions) FROM agent.contatos WHERE telefone = '{{$json.senderNumber}}')\n  AS \"is_authorized\";", "options": {}}, "id": "402e1b1d-c9db-4a1e-b8d9-e4a5d3f2e4b3", "name": "DB: Check Authorization", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [1920, 1420], "credentials": {"postgres": {"id": "YOUR_POSTGRES_CREDENTIAL_ID", "name": "PostgreSQL DB"}}, "notes": "Verifica se o usuário pode usar ferramentas sensíveis. Ele é autorizado se estiver na lista de permissões ou se for o dono do número (configurado em 'agent.settings')."}, {"parameters": {"conditions": {"boolean": [{"value1": "={{$json.is_authorized}}"}]}}, "id": "e4a5d3f2-e4b3-4f5e-9a9b-e8b9a1d3c5e3", "name": "IF: Is User Authorized?", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [2120, 1420]}, {"parameters": {"values": {"string": [{"name": "ai_output", "value": "🚫 Acesso Negado. Você não tem permissão para usar esta função."}]}, "options": {}}, "id": "d4e2a5b1-c3d5-4a4e-a1d2-b3c4e2a5b1c4", "name": "AGENT: Set Unauthorized Message", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [2120, 1620]}, {"parameters": {"authentication": "oAuth2", "resource": "email", "operation": "send", "to": "={{$('AI: Extract Email Details').item.json.data.recipient}}", "subject": "={{$('AI: Extract Email Details').item.json.data.subject}}", "text": "={{$('AI: Extract Email Details').item.json.data.body}}", "options": {}}, "id": "m3n4b5v6-c7x8-4z9a-b1c2-d3e4f5g6h7i8", "name": "TOOL: Send Email (Gmail)", "type": "n8n-nodes-base.googleMail", "typeVersion": 4.1, "position": [1720, 1060], "credentials": {"googleApi": {"id": "YOUR_GMAIL_CREDENTIAL_ID", "name": "Gmail API"}}}, {"parameters": {"agent": "simple", "model": "gpt-4o-mini", "text": "The user wants to send an email. From the following text, extract the recipient's email address, the subject line, and the body of the email. Return a single JSON object with the keys `recipient`, `subject`, and `body`.\n\nText: \"{{ $json.textContent }}\"", "options": {}}, "id": "p9o8i7u6-y5t4-4r3e-w2q1-a0s9d8f7g6h5", "name": "AI: Extract Email Details", "type": "@n8n/n8n-nodes-langchain.chain", "typeVersion": 1, "position": [1720, 860], "credentials": {"openAiApi": {"id": "YOUR_OPENAI_CREDENTIAL_ID", "name": "OpenAI Account"}}}, {"parameters": {"model": "text-embedding-3-small", "text": "={{$json.textContent}}", "options": {}}, "id": "f58f4a38-ff8f-4f27-897d-6c189e49a1d1", "name": "AI: Generate Query Embedding (RAG)", "type": "@n8n/n8n-nodes-langchain.openAiEmbeddings", "typeVersion": 1, "position": [-880, 280], "credentials": {"openAiApi": {"id": "YOUR_OPENAI_CREDENTIAL_ID", "name": "OpenAI Account"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT content FROM agent.memory_vectors WHERE contact_id = {{$json.contactDbId}} ORDER BY embedding <-> '{{JSON.stringify($json.data)}}' LIMIT 5;", "options": {}}, "id": "fc3f1e94-0d9c-48c9-8d69-eb7877c8e6df", "name": "VECTOR_DB: Search Relevant Memory (RAG)", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [-680, 280], "notes": "NOTA: Esta query requer a extensão `pgvector` instalada no PostgreSQL. A sintaxe é `ORDER BY embedding <-> '[...]'`. ", "credentials": {"postgres": {"id": "YOUR_POSTGRES_CREDENTIAL_ID", "name": "PostgreSQL DB"}}}, {"parameters": {"agent": "simple", "model": "gpt-4o-mini", "text": "Your only job is to analyze the user's request and decide which specialized agent should handle it. Your response must be ONLY ONE of the following keywords based on the request's primary goal: `calendar_agent`, `email_agent`, `tool_agent`, `data_analyst_agent`, `set_preference_agent`, `chitchat_agent`.\n\n- Use `calendar_agent` for creating, reading, or deleting calendar events.\n- Use `email_agent` for reading or sending emails.\n- Use `tool_agent` for finding documents or generating PDFs.\n- Use `data_analyst_agent` for running python code or analyzing files.\n- Use `set_preference_agent` for requests about changing how you reply (e.g., 'send me audio').\n- For anything else (greetings, general questions, conversation), use `chitchat_agent`.\n\nUSER'S REQUEST: \"{{$json.textContent}}\"", "options": {}}, "id": "a931a7ff-b8b8-472e-836e-d01a93883a48", "name": "AI: Router Agent (MoE)", "type": "@n8n/n8n-nodes-langchain.chain", "typeVersion": 1, "position": [-480, 500], "credentials": {"openAiApi": {"id": "YOUR_OPENAI_CREDENTIAL_ID", "name": "OpenAI Account"}}}, {"parameters": {"functionCode": "const memories = $items.map(item => item.json.content).join('\\n---\\n');\n\n// Add to the main execution flow to be used in prompts\nreturn [{ json: { retrieved_context: memories || 'Nenhuma memória de longo prazo encontrada.' } }];\n", "options": {}}, "id": "e6740c03-ebf8-4de3-93d4-b77eb87295aa", "name": "AGENT: Set Retrieved RAG Context", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-480, 280]}, {"parameters": {"routing": {"rules": {"values": [{"operation": "contains", "value1": "={{$json.data}}", "value2": "calendar_agent"}, {"operation": "contains", "value1": "={{$json.data}}", "value2": "email_agent"}, {"operation": "contains", "value1": "={{$json.data}}", "value2": "tool_agent"}, {"operation": "contains", "value1": "={{$json.data}}", "value2": "data_analyst_agent"}, {"operation": "contains", "value1": "={{$json.data}}", "value2": "set_preference_agent"}, {"operation": "contains", "value1": "={{$json.data}}", "value2": "create_deal"}, {"operation": "contains", "value1": "={{$json.data}}", "value2": "negative_feedback"}, {"operation": "contains", "value1": "={{$json.data}}", "value2": "/help"}]}, "fieldToMatch": "={{$json.data}}"}, "options": {"fallbackOutput": 7}}, "id": "bfd60f4c-0062-43f5-b20f-ac16fde211ed", "name": "SWITCH: Route to Expert", "type": "n8n-nodes-base.switch", "typeVersion": 2, "position": [1320, 500]}, {"parameters": {"agent": "conversationalAgent", "model": "gpt-4o-mini", "tools": ["google-calendar-tools"], "text": "{{$json.textContent}}", "options": {"memoryKey": "chat_history", "inputKey": "input", "systemMessage": "You are a Calendar Agent. You are an expert at scheduling, reading and deleting calendar events. Use your tools to fulfill the user's request. Current context: {{$json.context_summary}}. Long-term memory about user: {{$json.retrieved_context}}.", "promptVariables": {"values": [{"name": "context_summary", "value": "={{ $json.summarizedContext }}"}, {"name": "retrieved_context", "value": "={{ $json.retrieved_context }}"}]}}}, "id": "e8838ac1-98eb-4299-8bb4-ddc742617f69", "name": "AI: Calendar Agent", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1, "position": [1520, 280], "credentials": {"openAiApi": {"id": "YOUR_OPENAI_CREDENTIAL_ID", "name": "OpenAI Account"}, "google-calendar-tools": {"id": "YOUR_GOOGLE_CALENDAR_CREDENTIAL_ID", "name": "Google Calendar Tools"}}}, {"parameters": {"agent": "conversationalAgent", "model": "gpt-4o-mini", "tools": ["gmail-tools"], "text": "{{$json.textContent}}", "options": {"memoryKey": "chat_history", "inputKey": "input", "systemMessage": "You are an Email Agent. You are an expert at reading and sending emails. Use your tools to fulfill the user's request. Current context: {{$json.context_summary}}. Long-term memory about user: {{$json.retrieved_context}}.", "promptVariables": {"values": [{"name": "context_summary", "value": "={{ $json.summarizedContext }}"}, {"name": "retrieved_context", "value": "={{ $json.retrieved_context }}"}]}}}, "id": "b0f7f90f-3e1c-4389-9b93-b6d8b2d1264c", "name": "AI: Email Agent", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1, "position": [1520, 480], "credentials": {"openAiApi": {"id": "YOUR_OPENAI_CREDENTIAL_ID", "name": "OpenAI Account"}, "gmail-tools": {"id": "YOUR_GMAIL_CREDENTIAL_ID", "name": "<PERSON><PERSON>"}}}, {"parameters": {"agent": "conversationalAgent", "model": "gpt-4o", "text": "{{$json.textContent}}", "options": {"memoryKey": "chat_history", "inputKey": "input", "systemMessage": "You are <PERSON>, a general-purpose AI assistant. Your user is feeling '{{$json.user_emotion}}'. Adapt your tone to be '{{$json.agent_tone}}'. Engage in a helpful, conversational manner. Use the provided short-term and long-term context to inform your response.\n\nShort-term context: {{$json.context_summary}}\n\nRelevant long-term memory about this user: {{$json.retrieved_context}}\n\n{{ $json.isGroup ? `**Group Guidelines:** ${$json.group_guidelines}` : '' }}", "promptVariables": {"values": [{"name": "context_summary", "value": "={{ $json.summarizedContext }}"}, {"name": "retrieved_context", "value": "={{ $json.retrieved_context }}"}, {"name": "user_emotion", "value": "={{ $json.user_emotion }}"}, {"name": "agent_tone", "value": "={{ $json.agent_tone }}"}, {"name": "group_guidelines", "value": "={{ $json.group_guidelines || '' }}"}]}}}, "id": "ddcb0f74-d4dd-4fde-ac5d-0cdcf7d7edc6", "name": "AI: <PERSON><PERSON>at Agent", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1, "position": [2280, 500], "credentials": {"openAiApi": {"id": "YOUR_OPENAI_CREDENTIAL_ID", "name": "OpenAI Account"}}}, {"parameters": {}, "id": "e9b41a54-72cd-47d3-8a9d-b77edeb86fca", "name": "MERGE: Agent Outputs", "type": "n8n-nodes-base.merge", "typeVersion": 2, "position": [2280, 740]}, {"parameters": {"values": {"string": [{"name": "ai_output", "value": "={{ $json.data.output || $json.data }}"}]}, "options": {}}, "id": "e21b147d-8eb9-4674-8848-18e00d767ffc", "name": "AGENT: Set AI Output", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [-2180, 240]}, {"parameters": {"agent": "simple", "model": "gpt-4o-mini", "text": "The user wants to find or generate a document. From the text below, determine if they want to `find_document` or `generate_pdf`. Then, extract the document title or the text content to be put into the PDF.\n\nReturn ONLY a JSON object with two keys: `action` (either 'find_document' or 'generate_pdf') and `payload` (the document title or content).\n\nText: \"{{ $json.textContent }}\"", "options": {}}, "id": "2bf5271a-6379-4a94-b258-202f5a639b78", "name": "AI: Extract Too<PERSON> Details", "type": "@n8n/n8n-nodes-langchain.chain", "typeVersion": 1, "position": [1520, 880], "credentials": {"openAiApi": {"id": "YOUR_OPENAI_CREDENTIAL_ID", "name": "OpenAI Account"}}}, {"parameters": {"message": "**Requisição Externa Necessária**\nEste nó é um placeholder para uma chamada de API a um microsserviço que gera PDFs. \n\n**Exemplo de API:**\nURL: `https://your-pdf-service.com/generate`\nMethod: `POST`\nBody: `{ \"content\": \"{{$json.extracted_text}}\" }`\n\nO serviço deve retornar o PDF como um arquivo binário, que pode ser conectado ao nó `TOOL: Send WhatsApp Document`.", "options": {}}, "id": "b3d4f5e6-g7h8-4a1e-b2c3-d4e5f6g7h8j0", "name": "TOOL: Generate PDF (API Call)", "type": "n8n-nodes-base.noOp", "typeVersion": 1, "position": [1720, 1480]}, {"parameters": {"resource": "file", "operation": "search", "query": "={{$json.payload}}", "options": {}}, "id": "a9efedcb-49cc-433b-8267-beecab8364ed", "name": "TOOL: Search Google Drive", "type": "n8n-nodes-base.googleDrive", "typeVersion": 3.2, "position": [1720, 1820], "credentials": {"googleApi": {"id": "YOUR_GOOGLE_DRIVE_CREDENTIAL_ID", "name": "Google Drive API"}}}, {"parameters": {"conditions": {"string": [{"value1": "={{$json.action}}", "value2": "find_document"}]}}, "id": "f5e9a9b8-c7d6-4a5e-b2d3-e1d2b3c4d5e7", "name": "IF: Find or Generate?", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [1720, 1640]}, {"parameters": {"content": "**PLACEHOLDER - SECURITY WARNING**\nEste nó representa a execução de código Python em um ambiente seguro (sandboxed) via uma chamada de API.\n\n**Exemplo de API:**\nURL: `https://your-code-runner-service.com/execute`\nMethod: `POST`\nBody: `{ \"code\": \"{{$json.extracted_code}}\", \"files\": [ ... ] }`\n\nA implementação **DEVE** usar tecnologias como Docker para isolar a execução do código e prevenir acesso não autorizado ao sistema.", "height": 340}, "id": "e2402b8d-de58-47f2-b7e9-a31dfb15ff71", "name": "TOOL: Run Python Code (Sandboxed)", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [1520, 1040]}, {"parameters": {"conditions": {"boolean": [{"value1": "={{ $json.preferences?.reply_format === 'audio' }}"}]}}, "id": "a734e5a9-dc81-4235-9f50-f80e5b8d2ac2", "name": "IF: Audio Preference?", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [2680, -100]}, {"parameters": {"agent": "simple", "model": "gpt-4o-mini", "text": "The user wants to change a preference. Extract the preference `key` and `value` from their request. Keys should be snake_case, e.g., 'reply_format'. For replies, the value should be 'audio' or 'text'.\n\nReturn a single JSON object with `key` and `value`.\n\nRequest: \"{{ $json.textContent }}\"", "options": {}}, "id": "bfd5d7c3-8823-45bb-b30a-9d6a367ffef5", "name": "AI: Extract Preference", "type": "@n8n/n8n-nodes-langchain.chain", "typeVersion": 1, "position": [1520, 1380], "credentials": {"openAiApi": {"id": "YOUR_OPENAI_CREDENTIAL_ID", "name": "OpenAI Account"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "UPDATE agent.contatos SET preferences = preferences || '{{ JSON.stringify({[$json.key]: $json.value}) }}'::jsonb WHERE id = {{$json.contactDbId}};", "options": {}}, "id": "ed76d05f-fc8c-4fcc-97bd-fd3a8c1f930e", "name": "DB: Update User Preferences", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [1720, 1220], "credentials": {"postgres": {"id": "YOUR_POSTGRES_CREDENTIAL_ID", "name": "PostgreSQL DB"}}}, {"parameters": {"content": "### ARQUITETURA DE VOZ EM TEMPO REAL\n\nEste workflow representa a **parte lógica** do agente de voz. Ele não lida diretamente com o áudio. A arquitetura completa funciona assim:\n\n1.  **Interface de Voz (Ex: <PERSON><PERSON><PERSON> `<Stream>`, Bland.ai, Retell AI):** Gerencia a chamada telefônica.\n2.  **Servidor WebSocket (Externo):** A interface de voz se conecta a este servidor. Ele é responsável por:\n    - Receber o áudio do usuário em tempo real.\n    - Transcrever o áudio para texto (STT - Speech-to-Text).\n    - Enviar o texto para o gatilho `TRIGGER: Voice Agent Request` deste workflow n8n.\n    - Aguardar a resposta em JSON do n8n.\n    - Converter o texto da resposta da IA em áudio (TTS - Text-to-Speech), usando por exemplo o nó ElevenLabs.\n    - Enviar o áudio de volta para a chamada.\n\nEste fluxo n8n atua como o **cérebro**, enquanto o servidor WebSocket atua como as **\"orelhas e boca\"**.", "height": 540}, "id": "a1811559-0a6e-440d-b286-a36c86a632ea", "name": "DOCUMENTAÇÃO DA ARQUITETURA DE VOZ", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-1600, -780]}, {"parameters": {}, "id": "b0a68d01-ffb2-4d19-ac04-cfcdab5acb15", "name": "TRIGGER: Voice Agent Request (Webhook)", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [-1600, -300], "webhookId": "voice-agent-v9", "path": "voice-agent-v9", "httpMethod": "POST", "responseMode": "lastNode"}, {"parameters": {"values": {"string": [{"name": "channel", "value": "voice_webhook"}]}, "options": {}}, "id": "e30e7cc8-c0b7-418a-bb3f-3a26a575a6c4", "name": "AGENT: Normalize Voice Payload", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [-1400, -300]}, {"parameters": {}, "id": "f5127cf7-6b45-429f-ab00-58c06e8b4e72", "name": "MERGE: Voice and Text", "type": "n8n-nodes-base.merge", "typeVersion": 2, "position": [-1200, 500]}, {"parameters": {}, "id": "b0d4f134-ab6a-4632-a5e1-5e58bd7c79e6", "name": "Respond to Webhook", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [3080, -300], "options": {}}, {"parameters": {"conditions": {"boolean": [{"value1": "={{$json.failure_type !== undefined || $items[0].execution.error !== undefined}}"}]}}, "id": "ff9ed7a5-fd0d-40c2-9e9d-f19ff7f2b189", "name": "IF: Is Failure?", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [-2180, 1080]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "INSERT INTO agent.interaction_reviews (contact_id, timestamp, conversation_context, failure_type, status)\nVALUES ({{$json.contactDbId}}, NOW(), '{{ JSON.stringify({history: $json.summarizedContext, last_message: $json.textContent}) }}', '{{$json.failure_type || 'primary_ai_failed'}}', 'pending_review');", "options": {}}, "id": "a901844e-a1ac-4648-97e3-ff882e3b2c65", "name": "DB: Log Interaction for Review (LLMOps)", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [-2180, 1300], "credentials": {"postgres": {"id": "YOUR_POSTGRES_CREDENTIAL_ID", "name": "PostgreSQL DB"}}}, {"parameters": {"rule": {"interval": [{"weekday": 1}]}}, "id": "a3fc59cf-7d88-444f-b673-f112e400ed43", "name": "TRIGGER: Weekly Failure Review (Schedule)", "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1.1, "position": [-1600, -920]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT * FROM agent.interaction_reviews WHERE status = 'pending_review';", "options": {}}, "id": "a9ef387c-0359-42b7-bd26-b969c3a3ef89", "name": "DB: Get Pending Reviews", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [-1400, -920], "credentials": {"postgres": {"id": "YOUR_POSTGRES_CREDENTIAL_ID", "name": "PostgreSQL DB"}}}, {"parameters": {"agent": "simple", "model": "gpt-4o", "text": "The following is a JSON of failed interactions from the past week. Please analyze them to find common patterns or themes. Summarize the key problems in a markdown report for the development team, suggesting potential prompt refinements.\n\nFailed Interactions:\n{{ JSON.stringify($input.all()) }}", "options": {}}, "id": "c76ec7c6-f7a1-40be-ac75-fc2dd1de091e", "name": "AI: <PERSON><PERSON><PERSON><PERSON> (LLMOps)", "type": "@n8n/n8n-nodes-langchain.chain", "typeVersion": 1, "position": [-1200, -920], "credentials": {"openAiApi": {"id": "YOUR_OPENAI_CREDENTIAL_ID", "name": "OpenAI Account"}}}, {"parameters": {"method": "POST", "url": "=https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK", "sendBody": true, "bodyParameters": {"parameters": [{"name": "text", "value": "=*Weekly Agent Failure Report*=\n\nHere are the summarized failures and trends from the past week:\n\n{{$json.data}}\n\nPlease review these interactions to refine the agent's prompts and logic."}]}, "options": {}}, "id": "ed7d15a5-dd0b-4bd2-97b7-54acbb3ab4b6", "name": "TOOL: Send Report (Slack/Email)", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [-1000, -920]}, {"parameters": {"method": "POST", "url": "=https://api.hubapi.com/crm/v3/objects/deals", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer {{$credentials.hubspot.apiKey}}"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "properties", "value": "={{ { \"dealname\": $json.data.deal_name, \"amount\": $json.data.value, \"pipeline\": \"default\" } }}", "type": "json"}]}, "options": {}}, "id": "eb38ec16-a32e-4b72-ac0a-fd91d1ec9c1a", "name": "TOOL: Create HubSpot Deal", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [1520, 1780], "credentials": {"httpHeaderAuth": {"id": "YOUR_HUBSPOT_CREDENTIAL_ID", "name": "Hubspot API"}}}, {"parameters": {"values": {"string": [{"name": "failure_type", "value": "user_negative_feedback"}]}, "options": {}}, "id": "b1828c4e-4861-4560-af84-e4c1de35f796", "name": "Set Failure: <PERSON><PERSON>", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [1520, 2100]}, {"parameters": {"agent": "simple", "model": "gpt-4o", "text": "The conversation history is below, terminated by `[END]`. Convert this entire conversation into a single block of text and generate vector embeddings for it. This will be stored in a long-term memory database.\n\nCONVERSATION:\n{{$('CACHE: Obter Contexto Curto Prazo').item.json.data}}\n[END]", "options": {}}, "id": "ff7966f2-dd60-449e-b9b0-9c5ed1be8ba1", "name": "AI: Generate Embeddings (RAG Indexing)", "type": "@n8n/n8n-nodes-langchain.openAiEmbeddings", "typeVersion": 1, "position": [3700, -300], "credentials": {"openAiApi": {"id": "YOUR_OPENAI_CREDENTIAL_ID", "name": "OpenAI Account"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "INSERT INTO agent.memory_vectors (contact_id, timestamp, content, embedding) VALUES ({{$json.contactDbId}}, NOW(), '{{ $json.text_to_embed.replace(/'/g, \"''\") }}', '{{ JSON.stringify($json.embedding) }}');", "options": {}}, "id": "f516a7dd-abf1-41d3-bb24-dc7e4fd0d8d7", "name": "VECTOR_DB: Store Embeddings (RAG Indexing)", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [3900, -300], "credentials": {"postgres": {"id": "YOUR_POSTGRES_CREDENTIAL_ID", "name": "PostgreSQL DB"}}}, {"parameters": {}, "id": "cc07b5b7-78ab-433b-a2cc-35cdbfcc196c", "name": "TRIGGER: Index Conversation (Webhook)", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [3500, -300], "notes": "Este webhook seria chamado pelo fluxo principal no final de uma conversa para acionar a indexação RAG."}, {"parameters": {"method": "POST", "url": "={{$env.N8N_WEBHOOK_URL}}/webhook/{{$('cc07b5b7-78ab-433b-a2cc-35cdbfcc196c').item.webhookId}}", "sendBody": true, "bodyParameters": {"parameters": [{"name": "contactDbId", "value": "={{$json.contactDbId}}"}, {"name": "<PERSON><PERSON>ey", "value": "={{$json.contextKey}}"}]}, "options": {"continueOnFail": true}}, "id": "fb5d9047-fc55-4cc2-9ddc-d6edc9edb151", "name": "TOOL: Trigger RAG Indexing", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [3960, -500]}, {"parameters": {}, "id": "e9326e5e-399c-49aa-9bd2-be00a7479ed3", "name": "TRIGGER: Admin Panel", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [-1600, -1420], "httpMethod": "GET", "responseMode": "lastNode", "path": "agent-admin"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT id, nome, permissions FROM agent.contatos;", "options": {}}, "id": "dd167fde-5827-4402-bb0c-dcdd6ec8b375", "name": "DB: Get Users for Admin", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [-1400, -1420], "credentials": {"postgres": {"id": "YOUR_POSTGRES_CREDENTIAL_ID", "name": "PostgreSQL DB"}}}, {"parameters": {"functionCode": "const users = $items.map(item => `<li>${item.json.nome} (ID: ${item.json.id}) - Permissões: ${JSON.stringify(item.json.permissions)}</li>`).join('');\n\nconst html = `\n<!DOCTYPE html>\n<html>\n<head><title>Admin Panel</title></head>\n<body>\n  <h1>Painel de Administração do Agente</h1>\n  <h2>Gerenciar Usuários</h2>\n  <ul>${users}</ul>\n  \n  <form action=\"/webhook/admin-update-user\" method=\"post\">\n    <label>User ID:</label><input type=\"text\" name=\"userId\"><br>\n    <label>Per<PERSON><PERSON><PERSON> (ex: tool_user):</label><input type=\"text\" name=\"permission\"><br>\n    <button type=\"submit\" name=\"action\" value=\"add\">Adici<PERSON><PERSON><PERSON></button>\n    <button type=\"submit\" name=\"action\" value=\"remove\">Remover <PERSON></button>\n  </form>\n</body>\n</html>\n`;\n\nreturn { html: html };", "options": {}}, "id": "e0cd0d32-dfc0-410a-ad20-e29ed8407fcb", "name": "CODE: <PERSON><PERSON>", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1200, -1420]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT tool_name, description FROM agent.tools_registry t\nJOIN agent.contatos c ON c.telefone = '{{$json.senderNumber}}'\nWHERE t.required_permission = 'public' OR t.required_permission = ANY(SELECT jsonb_array_elements_text(c.permissions));", "options": {}}, "id": "ab65a9ba-23de-42cd-9f79-c5c2a1dd0cf4", "name": "DB: <PERSON> Permitted Tools", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [1520, 2420], "credentials": {"postgres": {"id": "YOUR_POSTGRES_CREDENTIAL_ID", "name": "PostgreSQL DB"}}}, {"parameters": {"functionCode": "const tools = items.map(item => `*/${item.json.tool_name}* - ${item.json.description}`).join('\\n');\n\nconst helpText = `Olá! Eu sou o Jarvis. Aqui está uma lista dos comandos que você pode usar:\\n\\n${tools}`;\n\nreturn { json: { ai_output: helpText } };", "options": {}}, "id": "dd167fde-5827-4402-bb0c-dcdd6ec8b374", "name": "AGENT: Format Help Message", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1720, 2420]}, {"parameters": {"rule": {"triggerAtHour": 9}}, "id": "31ebc12c-5645-4235-86ff-9080e7d58a8a", "name": "TRIGGER: MOTOR: <PERSON><PERSON><PERSON> (Diário)", "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1.1, "position": [-1600, -2800], "notes": "Roda diariamente para iniciar campanhas ativas baseadas em tempo."}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT * FROM agent.campaigns WHERE status = 'active' AND campaign_type = 'proactive_timed' AND NOW() BETWEEN start_date AND end_date;", "options": {}}, "id": "52ddca3e-d6e3-4d43-9cc2-26cf743b17c1", "name": "DB: Get Active Campaigns", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [-1400, -2800], "credentials": {"postgres": {"id": "YOUR_POSTGRES_CREDENTIAL_ID", "name": "PostgreSQL DB"}}}, {"parameters": {"batchSize": 1, "options": {}}, "id": "766b26be-b88f-4ed3-b6ed-10ccf7734ef5", "name": "Loop de Campanhas", "type": "n8n-nodes-base.splitInBatches", "typeVersion": 2, "position": [-1200, -2800]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT c.*, c.id as contact_id\nFROM agent.contatos c\nWHERE c.tags @> '{{$json.target_tags}}' -- Verifica se o contato tem TODAS as tags\nAND NOT EXISTS ( -- <PERSON><PERSON><PERSON> que o contato ainda não foi contatado para esta campanha\n    SELECT 1 FROM agent.campaign_interactions ci\n    WHERE ci.contact_id = c.id AND ci.campaign_id = {{$json.id}}\n);", "options": {}}, "id": "6eb3df2a-59c4-42b4-8255-0ebfa5735163", "name": "DB: Get Target Contacts", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [-1000, -2800], "credentials": {"postgres": {"id": "YOUR_POSTGRES_CREDENTIAL_ID", "name": "PostgreSQL DB"}}}, {"parameters": {"batchSize": 1, "options": {}}, "id": "2b6b55ce-39e2-4bd5-a4fc-ca760e1d89fa", "name": "Loop de Contatos", "type": "n8n-nodes-base.splitInBatches", "typeVersion": 2, "position": [-800, -2800]}, {"parameters": {"agent": "simple", "model": "gpt-4o-mini", "text": "Você é um especialista em comunicação para a campanha \"{{$('Loop de Campanhas').item.json.name}}\".\nSeu objetivo é {{$('Loop de Campanhas').item.json.description}}.\nA instrução base da campanha é: \"{{$('Loop de Campanhas').item.json.base_prompt}}\".\n\nAgora, crie uma mensagem pessoal e natural para \"{{$json.nome}}\".\nLeve em conta os seguintes insights que temos sobre ele(a): {{$json.insights.join(', ')}}.\n<PERSON><PERSON> amig<PERSON>, direto e finalize com uma pergunta ou chamada de ação clara.", "options": {}}, "id": "4dd6ddce-99f5-4752-bdf4-f9038ec14dfd", "name": "AI: Craft Personalized Campaign Message", "type": "@n8n/n8n-nodes-langchain.chain", "typeVersion": 1, "position": [-600, -2800], "credentials": {"openAiApi": {"id": "YOUR_OPENAI_CREDENTIAL_ID", "name": "OpenAI Account"}}}, {"parameters": {"method": "POST", "url": "={{ $credentials.EvolutionApi.baseUrl }}/message/sendText/{{$json.instanceName_placeholder}}", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "apikey", "value": "={{ $credentials.EvolutionApi.apiKey }}"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "number", "value": "={{$json.telefone}}"}, {"name": "textMessage", "value": "={{ { \"text\": $json.data } }}"}]}, "options": {}}, "id": "06059d9a-ff6e-4ab6-ae4f-561b6bb40ac2", "name": "TOOL: Send Campaign WhatsApp", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [-400, -2800], "credentials": {"httpHeaderAuth": {"id": "YOUR_EVOLUTION_API_CREDENTIAL_ID", "name": "EvolutionApi"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "INSERT INTO agent.campaign_interactions (campaign_id, contact_id, message_sent) VALUES ({{$('Loop de Campanhas').item.json.id}}, {{$json.contact_id}}, '{{$json.data.replace(/'/g, \"''\")}}');", "options": {}}, "id": "a978ffc1-58dc-4a00-afcf-b9c1d09e51fd", "name": "DB: Log Campaign Interaction", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [-200, -2800], "credentials": {"postgres": {"id": "YOUR_POSTGRES_CREDENTIAL_ID", "name": "PostgreSQL DB"}}}, {"parameters": {}, "id": "d049acac-6de7-4dd3-bbfa-b5a03d7cd9f1", "name": "TRIGGER: Ouvinte de Eventos de Campanha", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [-1600, -2400], "webhookId": "campaign-event", "path": "webhook/campaign-event", "httpMethod": "POST", "notes": "Webhook para receber eventos (ex: aniversário) de sistemas externos.\nPayload esperado: { \"contact_id\": 123, \"event_type\": \"aniversario\", \"event_details\": \"Faz 30 anos!\" }"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT * FROM agent.campaigns WHERE status = 'active' AND campaign_type = 'event_driven' AND event_trigger_keyword = '{{$json.body.event_type}}' LIMIT 1;", "options": {}}, "id": "ed7d15a5-dd0b-4bd2-97b7-54acbb3ab4b7", "name": "DB: Get Event Campaign", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [-1400, -2400], "credentials": {"postgres": {"id": "YOUR_POSTGRES_CREDENTIAL_ID", "name": "PostgreSQL DB"}}}, {"parameters": {"conditions": {"number": [{"value1": "={{ $items.length }}"}]}}, "id": "58c3866d-da67-4f6c-bf5f-00decfb80abf", "name": "IF: Campaign Found?", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [-1200, -2400]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT *, id as contact_id FROM agent.contatos WHERE id = {{$json.body.contact_id}}", "options": {}}, "id": "f516a7dd-abf1-41d3-bb24-dc7e4fd0d8d8", "name": "DB: Get Contact Info", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [-1000, -2400], "credentials": {"postgres": {"id": "YOUR_POSTGRES_CREDENTIAL_ID", "name": "PostgreSQL DB"}}}, {"parameters": {"agent": "simple", "model": "gpt-4o-mini", "text": "Você é um especialista em comunicação para a campanha \"{{$('DB: Get Event Campaign').item.json.name}}\".\nO evento que aconteceu foi: \"{{$json.body.event_type}}\".\nDetalhes do evento: \"{{$json.body.event_details}}\".\nSua instrução base da campanha é: \"{{$('DB: Get Event Campaign').item.json.base_prompt}}\".\n\nCrie uma mensagem pessoal e calorosa de parabéns para \"{{$json.nome}}\", mencionando o evento de forma natural.", "options": {}}, "id": "6a9082ef-df08-41fd-84b2-a4e90890ed15", "name": "AI: Craft Event-Driven Message", "type": "@n8n/n8n-nodes-langchain.chain", "typeVersion": 1, "position": [-800, -2400], "credentials": {"openAiApi": {"id": "YOUR_OPENAI_CREDENTIAL_ID", "name": "OpenAI Account"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "INSERT INTO agent.campaign_interactions (campaign_id, contact_id, message_sent) VALUES ({{$('DB: Get Event Campaign').item.json.id}}, {{$json.body.contact_id}}, '{{$json.data.replace(/'/g, \"''\")}}');", "options": {}}, "id": "ff9ed7a5-fd0d-40c2-9e9d-f19ff7f2b180", "name": "DB: Log Event Interaction", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [-400, -2400], "credentials": {"postgres": {"id": "YOUR_POSTGRES_CREDENTIAL_ID", "name": "PostgreSQL DB"}}}, {"parameters": {"method": "POST", "url": "={{ $credentials.EvolutionApi.baseUrl }}/message/sendText/{{$json.instanceName_placeholder}}", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "apikey", "value": "={{ $credentials.EvolutionApi.apiKey }}"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "number", "value": "={{$json.telefone}}"}, {"name": "textMessage", "value": "={{ { \"text\": $json.data } }}"}]}, "options": {}}, "id": "7b7a66cd-bd6d-476c-acb5-5d9dc00ec9a4", "name": "TOOL: Send Event WhatsApp", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [-600, -2400], "credentials": {"httpHeaderAuth": {"id": "YOUR_EVOLUTION_API_CREDENTIAL_ID", "name": "EvolutionApi"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT ci.id as interaction_id, c.goal_metric, ci.message_sent, c.id as campaign_id\nFROM agent.campaign_interactions ci\nJOIN agent.campaigns c ON ci.campaign_id = c.id\nWHERE ci.contact_id = {{$json.contactDbId}} AND ci.timestamp > NOW() - INTERVAL '24 hours' \nAND ci.response_received IS NULL\nORDER BY ci.timestamp DESC LIMIT 1;", "options": {}}, "id": "2d8efdfb-93ff-4e78-831d-b8d234a413d9", "name": "DB: Check for Recent Campaign Interaction", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [-1980, 500], "credentials": {"postgres": {"id": "YOUR_POSTGRES_CREDENTIAL_ID", "name": "PostgreSQL DB"}}, "notes": "Verifica se a mensagem atual é uma resposta a uma campanha enviada nas últimas 24h."}, {"parameters": {"conditions": {"number": [{"value1": "={{$items.length}}", "value2": 0}]}}, "id": "19b3d2b2-b3e7-40ac-90f7-87bb16089bd0", "name": "IF: Is Reply to a Campaign?", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [-1780, 500]}, {"parameters": {"agent": "simple", "model": "gpt-4o-mini", "text": "A meta desta campanha é: \"{{$json.goal_metric}}\".\nA mensagem enviada foi: \"{{$json.message_sent}}\"\nA resposta do usuário foi: \"{{$('AGENT: Sanitize Input').item.json.textContent}}\"\n\nA resposta do usuário atinge a meta da campanha? Responda APENAS com \"true\" ou \"false\".\nExemplo: se a meta for \"positive_reply\" e o usuário responder \"Que legal, quero sim!\", a resposta é \"true\". Se ele responder \"<PERSON><PERSON><PERSON>, mas não tenho interesse\", a resposta é \"false\".", "options": {}}, "id": "b0f7f90f-3e1c-4389-9b93-b6d8b2d1264d", "name": "AI: <PERSON><PERSON><PERSON> Rep<PERSON> for Goal Match", "type": "@n8n/n8n-nodes-langchain.chain", "typeVersion": 1, "position": [-1780, 700], "credentials": {"openAiApi": {"id": "YOUR_OPENAI_CREDENTIAL_ID", "name": "OpenAI Account"}}}, {"parameters": {"conditions": {"string": [{"value1": "={{$json.data}}", "value2": "true"}]}}, "id": "c168cfb9-1d48-43d5-94c0-0ed4eb4c4ab3", "name": "IF: Goal Achieved?", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [-1580, 700]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "UPDATE agent.campaigns SET current_progress = current_progress + 1 WHERE id = {{$json.campaign_id}};\n\nUPDATE agent.campaign_interactions SET achieved_goal = TRUE, response_received = '{{$('AGENT: Sanitize Input').item.json.textContent.replace(/'/g, \"''\")}}' WHERE id = {{$json.interaction_id}};", "options": {}}, "id": "31d6e6fe-1941-4c12-b91c-7204f32a7dd7", "name": "DB: Update Campaign Progress", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [-1380, 700], "credentials": {"postgres": {"id": "YOUR_POSTGRES_CREDENTIAL_ID", "name": "PostgreSQL DB"}}}, {"parameters": {}, "id": "dc9c5b6b-313d-4c3d-b40b-0361376d2143", "name": "MERGE: Campaign and Normal Flow", "type": "n8n-nodes-base.merge", "typeVersion": 2, "position": [-1580, 500]}, {"parameters": {}, "id": "063546a3-2c1a-4d2d-8e41-65dbca1867c2", "name": "TRIGGER: Facebook Lead Ads", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [-4860, 500], "webhookId": "facebook-lead-ads", "path": "webhook/facebook-lead-ads", "httpMethod": "POST", "notes": "Substitua por um nó \"Facebook Lead Ads Trigger\" real. O payload simulado deve conter campos como `full_name`, `email`, `phone_number`, e `bairro`."}, {"parameters": {"conditions": {"string": [{"value1": "={{ $json.body.bairro }}", "value2": "Bairro X"}]}, "options": {}}, "id": "a9003c2e-4b72-4d20-b0be-cc2e3b2e5a6a", "name": "IF: Lead in Target Area?", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [-4620, 500]}, {"parameters": {"method": "POST", "url": "={{ $credentials.EvolutionApi.baseUrl }}/message/sendText/{{$json.instanceName_placeholder}}", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "apikey", "value": "={{ $credentials.EvolutionApi.apiKey }}"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "number", "value": "={{$json.body.phone_number}}"}, {"name": "textMessage", "value": "={{ { \"text\": \"Ol<PERSON>! Agradecemos seu interesse. No momento, nossos serviços são exclusivos para moradores do Bairro X. Manteremos seu contato para futuras expansões!\" } }}"}]}, "options": {}}, "id": "a9a08433-85e7-47b1-af86-63d12d31e9fe", "name": "TOOL: Send Rejection WhatsApp", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [-4620, 720], "credentials": {"httpHeaderAuth": {"id": "YOUR_EVOLUTION_API_CREDENTIAL_ID", "name": "EvolutionApi"}}}, {"parameters": {"values": {"string": [{"name": "tags", "value": "={{ ['bairro_x', 'potencial_academia'] }}"}]}, "options": {"keepOnlySet": true}}, "id": "e6745f44-8d48-4384-96dd-a131bb454e99", "name": "AGENT: Add Geo & Interest Tags", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [-4420, 500]}, {"parameters": {"routing": {"rules": {"values": [{"operation": "regex", "value1": "={{ $runIndex % 2 }}", "value2": "0"}]}, "fieldToMatch": ""}, "options": {"fallbackOutput": "1"}}, "id": "8c454728-c116-43b3-82c8-7c858b29df99", "name": "SWITCH: A/B Test Distribution", "type": "n8n-nodes-base.switch", "typeVersion": 2, "position": [-4220, 500], "notes": "Distribui leads 50/50 entre a campanha A e a B."}, {"parameters": {"values": {"string": [{"name": "tags", "value": "={{ $json.tags.concat('obteve_desconto_20') }}"}]}, "options": {"keepOnlySet": true}}, "id": "4b5d2780-77a8-4720-af77-0136615b3c37", "name": "Add Tag: Campanha A", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [-4020, 400]}, {"parameters": {"values": {"string": [{"name": "tags", "value": "={{ $json.tags.concat('agendou_consulta_gratis') }}"}]}, "options": {"keepOnlySet": true}}, "id": "efde4a7c-5ab5-4757-b203-d9d3000df837", "name": "Add Tag: Campanha B", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [-4020, 600]}, {"parameters": {}, "id": "dc9c5b6b-313d-4c3d-b40b-0361376d2144", "name": "MERGE: A/B Tests", "type": "n8n-nodes-base.merge", "typeVersion": 2, "position": [-3820, 500]}, {"parameters": {"values": {"string": [{"name": "channel", "value": "facebook_lead_ad"}, {"name": "body", "value": "={{ $('063546a3-2c1a-4d2d-8e41-65dbca1867c2').item.json.body }}"}]}, "options": {}}, "id": "b0a531e2-6ed9-4bcf-a5dd-d477b620ac79", "name": "AGENT: Normalize FB Payload", "type": "n8n-nodes-base.set", "typeVersion": 4.1, "position": [-3620, 500]}, {"parameters": {"method": "POST", "url": "=https://graph.facebook.com/v19.0/CUSTOM_AUDIENCE_ID/users", "authentication": "oAuth2", "credentialType": "facebookMarketingApi", "sendBody": true, "bodyParameters": {"parameters": [{"name": "payload", "value": "={{ { \"schema\": [\"PHONE\"], \"data\": [ [crypto.createHash('sha256').update($json.telefone).digest('hex')] ] } }}", "type": "json"}]}, "options": {}}, "id": "e82939b4-370c-4d89-9b48-3bc9bbcae073", "name": "TOOL: Add to Meta Custom Audience", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [-2380, 1040], "notes": "Adici<PERSON> o lead a uma audiência de remarketing no Facebook/Instagram. Requer `CUSTOM_AUDIENCE_ID`.", "credentials": {"facebookMarketingApi": {"id": "YOUR_META_MARKETING_API_CREDENTIALS_ID", "name": "Meta Marketing API"}}}, {"parameters": {"conditions": {"string": [{"value1": "={{ JSON.stringify($json.tags) }}", "operation": "contains", "value2": "bairro_x"}]}}, "id": "a6452295-d222-4ed9-a64b-a010d18d0cc4", "name": "IF: Is <PERSON><PERSON> Lead?", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [-2380, 1240]}, {"parameters": {"method": "POST", "url": "=https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK_URL", "sendBody": true, "bodyParameters": {"parameters": [{"name": "text", "value": "🚀 Novo lead qualificado no <PERSON>rro X! Nome: {{$json.nome}}, Telefone: {{$json.telefone}}. Entrar em contato!"}]}, "options": {}}, "id": "766ef8b3-bd60-4e3f-a3d1-933e4cc0068b", "name": "TOOL: Notify Sales Team (Slack)", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [-2180, 1240]}, {"parameters": {"authentication": "oAuth2", "operation": "append", "spreadsheetId": "={{ $json.spreadsheetId_placeholder }}", "sheetName": "={{ $json.sheetName_placeholder }}", "options": {"columns": {"values": [{"column": "Timestamp", "expression": "={{ $now.toFormat('yyyy-MM-dd HH:mm:ss') }}"}, {"column": "Total Contacts", "expression": "={{ $json.total_contacts }}"}, {"column": "Messages 24h", "expression": "={{ $json.messages_last_24h }}"}, {"column": "Avg <PERSON>nt", "expression": "={{ $json.avg_sentiment_score }}"}, {"column": "Campaign Progress", "expression": "={{ JSON.stringify($json.campaign_progress) }}"}]}}}, "id": "1ab6b251-de12-4099-a35c-7d9a8c1f3d64", "name": "TOOL: Export Report to Sheets", "type": "n8n-nodes-base.googleSheets", "typeVersion": 4, "position": [-3220, 3840], "credentials": {"googleSheetsOAuth2Api": {"id": "YOUR_GOOGLE_SHEETS_CREDENTIAL_ID", "name": "Google Sheets API"}}}], "connections": {"TRIGGER: WhatsApp (Webhook)": {"main": [[{"node": "AGENT: Normalize WhatsApp Payload", "type": "main", "index": 0}]]}, "AGENT: Normalize WhatsApp Payload": {"main": [[{"node": "MERGE: All Channels", "type": "main", "index": 0}]]}, "IF: Is User Message?": {"main": [[{"node": "DB: Verificar Contato", "type": "main", "index": 0}]]}, "DB: Verificar Contato": {"main": [[{"node": "IF: Contato Existe?", "type": "main", "index": 0}]]}, "IF: Contato Existe?": {"main": [[{"node": "MERGE: Contact Info", "type": "main", "index": 0}], [{"node": "DB: <PERSON><PERSON><PERSON>", "type": "main", "index": 0}]]}, "DB: Criar Contato": {"main": [[{"node": "e82939b4-370c-4d89-9b48-3bc9bbcae073", "type": "main", "index": 0}]]}, "MERGE: Contact Info": {"main": [[{"node": "2d8efdfb-93ff-4e78-831d-b8d234a413d9", "type": "main", "index": 0}]]}, "AGENT: Generate Context Key": {"main": [[{"node": "CACHE: <PERSON><PERSON>er Contexto Curto Prazo", "type": "main", "index": 0}]]}, "CACHE: Obter Contexto Curto Prazo": {"main": [[{"node": "a931a7ff-b8b8-472e-836e-d01a93883a48", "type": "main", "index": 0}]]}, "Rotear por Tipo de Mensagem": {"main": [[{"node": "DB: Log Mensagem de Texto", "type": "main", "index": 0}], [{"node": "DB: <PERSON>g Mensagem de Mídia", "type": "main", "index": 0}], [{"node": "DB: <PERSON>g Mensagem de Mídia", "type": "main", "index": 0}], [{"node": "DB: <PERSON>g Mensagem de Mídia", "type": "main", "index": 0}], [{"node": "DB: <PERSON>g Mensagem de Mídia", "type": "main", "index": 0}]]}, "DB: Log Mensagem de Texto": {"main": [[{"node": "AGENT: Generate Context Key", "type": "main", "index": 0}]]}, "DB: Log Resposta da IA": {"main": [[{"node": "AGENT: Set Dynamic TTL", "type": "main", "index": 0}]]}, "CACHE: Salvar Contexto Curto Prazo": {"main": [[{"node": "AI: Extrair Insights do Contato", "type": "main", "index": 0}]]}, "DB: Log Mensagem de Mídia": {"main": [[{"node": "Rotear por TIPO de Mídia", "type": "main", "index": 0}]]}, "TOOL: Enviar para Fila Específica": {"main": [[{"node": "TOOL: <PERSON><PERSON><PERSON>", "type": "main", "index": 0}]]}, "TRIGGER: Ouvir Fila de Resultados": {"main": [[{"node": "DB: Atualizar com Análise", "type": "main", "index": 0}]]}, "DB: Atualizar com Análise": {"main": [[{"node": "SWITCH: Resultado é Transcrição?", "type": "main", "index": 0}]]}, "Error Trigger": {"main": [[{"node": "ERROR: Format Message", "type": "main", "index": 0}]]}, "ERROR: Format Message": {"main": [[{"node": "ALERT: Notify Admin (via Slack/Email)", "type": "main", "index": 0}]]}, "AGENT: Dynamic Rate Limiter Gate": {"main": [[{"node": "IF: Rate Limit Exceeded?", "type": "main", "index": 0}]]}, "IF: Rate Limit Exceeded?": {"main": [[{"node": "TOOL: Enviar Msg Rate Limit", "type": "main", "index": 0}], [{"node": "Rotear por Tipo de Mensagem", "type": "main", "index": 0}]]}, "Rotear por TIPO de Mídia": {"main": [[{"node": "Set Audio Queue", "type": "main", "index": 0}], [{"node": "Set Image Queue", "type": "main", "index": 0}], [{"node": "Set Video Queue", "type": "main", "index": 0}], [{"node": "Set PDF Queue", "type": "main", "index": 0}]]}, "Set Audio Queue": {"main": [[{"node": "AGENT: Set Task Priority", "type": "main", "index": 0}]]}, "Set Image Queue": {"main": [[{"node": "AGENT: Set Task Priority", "type": "main", "index": 0}]]}, "Set Video Queue": {"main": [[{"node": "AGENT: Set Task Priority", "type": "main", "index": 0}]]}, "IF: Primary AI Failed?": {"main": [[{"node": "e21b147d-8eb9-4674-8848-18e00d767ffc", "type": "main", "index": 0}], [{"node": "AI: <PERSON>up Agent (Anthropic)", "type": "main", "index": 0}]]}, "AI: Analisar Emoção & Tom": {"main": [[{"node": "TOOL: Pro Sentiment Analysis (Google NLP)", "type": "main", "index": 0}]]}, "AGENT: Parse Emotion Analysis": {"main": [[{"node": "AI: Detectar Idioma", "type": "main", "index": 0}]]}, "ROUTE: Reply Format by Channel": {"main": [[{"node": "TOOL: Send WhatsApp Text Reply", "type": "main", "index": 0}], [{"node": "27f7de11-fd31-482a-a9a7-9657edb56952", "type": "main", "index": 0}]]}, "TOOL: Text-to-Speech (ElevenLabs)": {"main": [[{"node": "TOOL: Send WhatsApp Audio Reply", "type": "main", "index": 0}]]}, "TOOL: Send WhatsApp Audio Reply": {"main": [[{"node": "DB: Log Resposta da IA", "type": "main", "index": 0}]]}, "AI: Extrair Insights do Contato": {"main": [[{"node": "DB: Atualizar Perfil do Contato", "type": "main", "index": 0}]]}, "DB: Atualizar Perfil do Contato": {"main": [[{"node": "TOOL: Trigger RAG Indexing", "type": "main", "index": 0}]]}, "SWITCH: Resultado é Transcrição?": {"main": [[{"node": "AGENT: Preparar Loop de Transcrição", "type": "main", "index": 0}]]}, "AGENT: Preparar Loop de Transcrição": {"main": [[{"node": "c1fcf4e8-8bfe-4c54-9721-fffc2df70068", "type": "main", "index": 0}]]}, "TRIGGER: Daily Job (2 AM)": {"main": [[{"node": "DB: <PERSON><PERSON><PERSON>ti<PERSON>", "type": "main", "index": 0}]]}, "DB: Gerar Relatório Quantitativo": {"main": [[{"node": "1ab6b251-de12-4099-a35c-7d9a8c1f3d64", "type": "main", "index": 0}]]}, "MERGE: All Channels": {"main": [[{"node": "e67e37de-c8cb-4e3a-9e9f-e3c35b67dabc", "type": "main", "index": 0}]]}, "AI: Backup Agent (Anthropic)": {"main": [[{"node": "AGENT: Set Backup AI Output", "type": "main", "index": 0}]]}, "AGENT: Set Backup AI Output": {"main": [[{"node": "ff9ed7a5-fd0d-40c2-9e9d-f19ff7f2b189", "type": "main", "index": 0}, {"node": "e9b41a54-72cd-47d3-8a9d-b77edeb86fca", "type": "main", "index": 1}]]}, "MERGE: AI Responses": {"main": [[{"node": "b1354020-854a-406d-92b7-66e29e1f3c8e", "type": "main", "index": 0}]]}, "TOOL: Pro Sentiment Analysis (Google NLP)": {"main": [[{"node": "AGENT: Parse Emotion Analysis", "type": "main", "index": 0}]]}, "TRIGGER: Telegram (Webhook)": {"main": [[{"node": "AGENT: Normalize Telegram Payload", "type": "main", "index": 0}]]}, "AGENT: Normalize Telegram Payload": {"main": [[{"node": "MERGE: All Channels", "type": "main", "index": 2}]]}, "TRIGGER: SMS (Twilio Webhook)": {"main": [[{"node": "AGENT: Normalize SMS Payload", "type": "main", "index": 0}]]}, "AGENT: Normalize SMS Payload": {"main": [[{"node": "MERGE: All Channels", "type": "main", "index": 3}]]}, "c1fcf4e8-8bfe-4c54-9721-fffc2df70068": {"main": [[{"node": "bfd60f4c-0062-43f5-b20f-ac16fde211ed", "type": "main", "index": 0}]]}, "TRIGGER: Check Reminders (Every Minute)": {"main": [[{"node": "DB: <PERSON><PERSON> Due Reminders", "type": "main", "index": 0}]]}, "DB: Fetch Due Reminders": {"main": [[{"node": "Loop Over Reminders", "type": "main", "index": 0}]]}, "Loop Over Reminders": {"main": [[{"node": "TOOL: <PERSON> Reminder (WhatsApp)", "type": "main", "index": 0}]]}, "TOOL: Send Reminder (WhatsApp)": {"main": [[{"node": "DB: <PERSON> as <PERSON><PERSON>", "type": "main", "index": 0}]]}, "AGENT: Set Dynamic TTL": {"main": [[{"node": "CACHE: <PERSON><PERSON> Curto Prazo", "type": "main", "index": 0}]]}, "DB: Check for Manual Override Flag": {"main": [[{"node": "IF: Manual Override Active?", "type": "main", "index": 0}]]}, "IF: Manual Override Active?": {"main": [[{"node": "Stop (Manual Override)", "type": "main", "index": 0}], [{"node": "IF: Is User Message?", "type": "main", "index": 0}]]}, "AGENT: Sanitize Input": {"main": [[{"node": "AGENT: Dynamic Rate Limiter Gate", "type": "main", "index": 0}]]}, "AGENT: Set Task Priority": {"main": [[{"node": "TOOL: Enviar para Fila Específica", "type": "main", "index": 0}]]}, "AI: Format Response by Channel": {"main": [[{"node": "AGENT: Set Formatted AI Output", "type": "main", "index": 0}]]}, "AGENT: Set Formatted AI Output": {"main": [[{"node": "IF: Audio Preference?", "type": "main", "index": 0}]]}, "TRIGGER: Instagram Webhook": {"main": [[{"node": "AGENT: Normalize Instagram Payload", "type": "main", "index": 0}]]}, "AGENT: Normalize Instagram Payload": {"main": [[{"node": "MERGE: All Channels", "type": "main", "index": 4}]]}, "TRIGGER: Email Webhook (SendGrid)": {"main": [[{"node": "AGENT: Normalize Email Payload", "type": "main", "index": 0}]]}, "AGENT: Normalize Email Payload": {"main": [[{"node": "MERGE: All Channels", "type": "main", "index": 5}]]}, "AGENT: Parse Common Payload": {"main": [[{"node": "DB: Check for Manual Override Flag", "type": "main", "index": 0}]]}, "DB: Check Authorization": {"main": [[{"node": "IF: Is User Authorized?", "type": "main", "index": 0}]]}, "IF: Is User Authorized?": {"main": [[{"node": "f5e9a9b8-c7d6-4a5e-b2d3-e1d2b3c4d5e7", "type": "main", "index": 0}], [{"node": "d4e2a5b1-c3d5-4a4e-a1d2-b3c4e2a5b1c4", "type": "main", "index": 0}]]}, "AGENT: Set Unauthorized Message": {"main": [[{"node": "e9b41a54-72cd-47d3-8a9d-b77edeb86fca", "type": "main", "index": 2}]]}, "AI: Generate Query Embedding (RAG)": {"main": [[{"node": "VECTOR_DB: Search Relevant Memory (RAG)", "type": "main", "index": 0}]]}, "VECTOR_DB: Search Relevant Memory (RAG)": {"main": [[{"node": "AGENT: Set Retrieved RAG Context", "type": "main", "index": 0}]]}, "AI: Router Agent (MoE)": {"main": [[{"node": "AI: <PERSON><PERSON><PERSON> & Tom", "type": "main", "index": 0}]]}, "AGENT: Set Retrieved RAG Context": {"main": [[{"node": "AI: Router Agent (MoE)", "type": "main", "index": 0}]]}, "SWITCH: Route to Expert": {"main": [[{"node": "AI: Calendar Agent", "type": "main", "index": 0}], [{"node": "AI: Email Agent", "type": "main", "index": 0}], [{"node": "2bf5271a-6379-4a94-b258-202f5a639b78", "type": "main", "index": 0}], [{"node": "e2402b8d-de58-47f2-b7e9-a31dfb15ff71", "type": "main", "index": 0}], [{"node": "bfd5d7c3-8823-45bb-b30a-9d6a367ffef5", "type": "main", "index": 0}], [{"node": "eb38ec16-a32e-4b72-ac0a-fd91d1ec9c1a", "type": "main", "index": 0}], [{"node": "b1828c4e-4861-4560-af84-e4c1de35f796", "type": "main", "index": 0}], [{"node": "ab65a9ba-23de-42cd-9f79-c5c2a1dd0cf4", "type": "main", "index": 0}, {"node": "ddcb0f74-d4dd-4fde-ac5d-0cdcf7d7edc6", "type": "main", "index": 0}]]}, "AI: Calendar Agent": {"main": [[{"node": "IF: Primary AI Failed?", "type": "main", "index": 0}]]}, "AI: Email Agent": {"main": [[{"node": "IF: Primary AI Failed?", "type": "main", "index": 0}]]}, "AI: Chitchat Agent": {"main": [[{"node": "IF: Primary AI Failed?", "type": "main", "index": 0}]]}, "MERGE: Agent Outputs": {"main": [[{"node": "IF: Primary AI Failed?", "type": "main", "index": 0}]]}, "AGENT: Set AI Output": {"main": [[{"node": "ff9ed7a5-fd0d-40c2-9e9d-f19ff7f2b189", "type": "main", "index": 0}, {"node": "e9b41a54-72cd-47d3-8a9d-b77edeb86fca", "type": "main", "index": 0}]]}, "2bf5271a-6379-4a94-b258-202f5a639b78": {"main": [[{"node": "p9o8i7u6-y5t4-4r3e-w2q1-a0s9d8f7g6h5", "type": "main", "index": 0}]]}, "IF: Find or Generate?": {"main": [[{"node": "a9efedcb-49cc-433b-8267-beecab8364ed", "type": "main", "index": 0}], [{"node": "b3d4f5e6-g7h8-4a1e-b2c3-d4e5f6g7h8j0", "type": "main", "index": 0}]]}, "IF: Audio Preference?": {"main": [[{"node": "ROUTE: Reply Format by Channel", "type": "main", "index": 0}], [{"node": "TOOL: Text-to-Speech (ElevenLabs)", "type": "main", "index": 0}]]}, "AI: Extract Preference": {"main": [[{"node": "ed76d05f-fc8c-4fcc-97bd-fd3a8c1f930e", "type": "main", "index": 0}]]}, "TRIGGER: Voice Agent Request (Webhook)": {"main": [[{"node": "AGENT: Normalize Voice Payload", "type": "main", "index": 0}]]}, "AGENT: Normalize Voice Payload": {"main": [[{"node": "MERGE: Voice and Text", "type": "main", "index": 0}]]}, "MERGE: Voice and Text": {"main": [[{"node": "b0d4f134-ab6a-4632-a5e1-5e58bd7c79e6", "type": "main", "index": 0}]]}, "IF: Is Failure?": {"main": [[{"node": "MERGE: AI Responses", "type": "main", "index": 0}], [{"node": "a901844e-a1ac-4648-97e3-ff882e3b2c65", "type": "main", "index": 0}]]}, "a901844e-a1ac-4648-97e3-ff882e3b2c65": {"main": [[{"node": "MERGE: AI Responses", "type": "main", "index": 1}]]}, "TRIGGER: Weekly Failure Review (Schedule)": {"main": [[{"node": "a9ef387c-0359-42b7-bd26-b969c3a3ef89", "type": "main", "index": 0}]]}, "a9ef387c-0359-42b7-bd26-b969c3a3ef89": {"main": [[{"node": "c76ec7c6-f7a1-40be-ac75-fc2dd1de091e", "type": "main", "index": 0}]]}, "c76ec7c6-f7a1-40be-ac75-fc2dd1de091e": {"main": [[{"node": "ed7d15a5-dd0b-4bd2-97b7-54acbb3ab4b6", "type": "main", "index": 0}]]}, "Set Failure: User Feedback": {"main": [[{"node": "ff9ed7a5-fd0d-40c2-9e9d-f19ff7f2b189", "type": "main", "index": 0}]]}, "TRIGGER: Index Conversation (Webhook)": {"main": [[{"node": "ff7966f2-dd60-449e-b9b0-9c5ed1be8ba1", "type": "main", "index": 0}]]}, "ff7966f2-dd60-449e-b9b0-9c5ed1be8ba1": {"main": [[{"node": "f516a7dd-abf1-41d3-bb24-dc7e4fd0d8d7", "type": "main", "index": 0}]]}, "TOOL: Trigger RAG Indexing": {"main": [[{"node": "METRICS: Log Execution Time", "type": "main", "index": 0}]]}, "TRIGGER: Admin Panel": {"main": [[{"node": "dd167fde-5827-4402-bb0c-dcdd6ec8b375", "type": "main", "index": 0}]]}, "dd167fde-5827-4402-bb0c-dcdd6ec8b375": {"main": [[{"node": "e0cd0d32-dfc0-410a-ad20-e29ed8407fcb", "type": "main", "index": 0}]]}, "ab65a9ba-23de-42cd-9f79-c5c2a1dd0cf4": {"main": [[{"node": "dd167fde-5827-4402-bb0c-dcdd6ec8b374", "type": "main", "index": 0}]]}, "dd167fde-5827-4402-bb0c-dcdd6ec8b374": {"main": [[{"node": "e9b41a54-72cd-47d3-8a9d-b77edeb86fca", "type": "main", "index": 3}]]}, "TRIGGER: MOTOR: Campanhas Proativas (Diário)": {"main": [[{"node": "DB: Get Active Campaigns", "type": "main", "index": 0}]]}, "DB: Get Active Campaigns": {"main": [[{"node": "Loop de Campanhas", "type": "main", "index": 0}]]}, "Loop de Campanhas": {"main": [[{"node": "DB: Get Target Contacts", "type": "main", "index": 0}]]}, "DB: Get Target Contacts": {"main": [[{"node": "Loop de Contatos", "type": "main", "index": 0}]]}, "Loop de Contatos": {"main": [[{"node": "AI: Craft Personalized Campaign Message", "type": "main", "index": 0}]]}, "AI: Craft Personalized Campaign Message": {"main": [[{"node": "TOOL: Send Campaign WhatsApp", "type": "main", "index": 0}]]}, "TOOL: Send Campaign WhatsApp": {"main": [[{"node": "DB: Log Campaign Interaction", "type": "main", "index": 0}]]}, "TRIGGER: Ouvinte de Eventos de Campanha": {"main": [[{"node": "DB: Get Event Campaign", "type": "main", "index": 0}]]}, "DB: Get Event Campaign": {"main": [[{"node": "IF: Campaign Found?", "type": "main", "index": 0}]]}, "IF: Campaign Found?": {"main": [[{"node": "DB: Get Contact Info", "type": "main", "index": 0}]]}, "DB: Get Contact Info": {"main": [[{"node": "AI: Craft Event-Driven Message", "type": "main", "index": 0}]]}, "AI: Craft Event-Driven Message": {"main": [[{"node": "TOOL: Send Event WhatsApp", "type": "main", "index": 0}]]}, "TOOL: Send Event WhatsApp": {"main": [[{"node": "ff9ed7a5-fd0d-40c2-9e9d-f19ff7f2b180", "type": "main", "index": 0}]]}, "DB: Check for Recent Campaign Interaction": {"main": [[{"node": "IF: Is Reply to a Campaign?", "type": "main", "index": 0}]]}, "IF: Is Reply to a Campaign?": {"main": [[{"node": "MERGE: Campaign and Normal Flow", "type": "main", "index": 0}], [{"node": "AI: <PERSON><PERSON><PERSON> Rep<PERSON> for Goal Match", "type": "main", "index": 0}]]}, "AI: Analyze Reply for Goal Match": {"main": [[{"node": "IF: Goal Achieved?", "type": "main", "index": 0}]]}, "IF: Goal Achieved?": {"main": [[{"node": "DB: Update Campaign Progress", "type": "main", "index": 0}], [{"node": "MERGE: Campaign and Normal Flow", "type": "main", "index": 1}]]}, "DB: Update Campaign Progress": {"main": [[{"node": "MERGE: Campaign and Normal Flow", "type": "main", "index": 2}]]}, "MERGE: Campaign and Normal Flow": {"main": [[{"node": "AGENT: Sanitize Input", "type": "main", "index": 0}]]}, "TRIGGER: Facebook Lead Ads": {"main": [[{"node": "a9003c2e-4b72-4d20-b0be-cc2e3b2e5a6a", "type": "main", "index": 0}]]}, "IF: Lead in Target Area?": {"main": [[{"node": "e6745f44-8d48-4384-96dd-a131bb454e99", "type": "main", "index": 0}], [{"node": "a9a08433-85e7-47b1-af86-63d12d31e9fe", "type": "main", "index": 0}]]}, "AGENT: Add Geo & Interest Tags": {"main": [[{"node": "8c454728-c116-43b3-82c8-7c858b29df99", "type": "main", "index": 0}]]}, "SWITCH: A/B Test Distribution": {"main": [[{"node": "4b5d2780-77a8-4720-af77-0136615b3c37", "type": "main", "index": 0}], [{"node": "efde4a7c-5ab5-4757-b203-d9d3000df837", "type": "main", "index": 0}]]}, "Add Tag: Campanha A": {"main": [[{"node": "dc9c5b6b-313d-4c3d-b40b-0361376d2144", "type": "main", "index": 0}]]}, "Add Tag: Campanha B": {"main": [[{"node": "dc9c5b6b-313d-4c3d-b40b-0361376d2144", "type": "main", "index": 1}]]}, "MERGE: A/B Tests": {"main": [[{"node": "b0a531e2-6ed9-4bcf-a5dd-d477b620ac79", "type": "main", "index": 0}]]}, "AGENT: Normalize FB Payload": {"main": [[{"node": "MERGE: All Channels", "type": "main", "index": 1}]]}, "TOOL: Add to Meta Custom Audience": {"main": [[{"node": "a6452295-d222-4ed9-a64b-a010d18d0cc4", "type": "main", "index": 0}]]}, "IF: Is Bairro X Lead?": {"main": [[{"node": "766ef8b3-bd60-4e3f-a3d1-933e4cc0068b", "type": "main", "index": 0}], [{"node": "MERGE: Contact Info", "type": "main", "index": 2}]]}, "TOOL: Notify Sales Team (Slack)": {"main": [[{"node": "MERGE: Contact Info", "type": "main", "index": 1}]]}, "TOOL: Export Report to Sheets": {"main": [[{"node": "1cf385da-09f6-4e15-9e25-8717e9c6d35a", "type": "main", "index": 0}]]}}, "settings": {"errorWorkflow": "<PERSON><PERSON><PERSON>", "timezone": "America/Sao_Paulo", "saveDataSuccess": "all", "saveDataError": "all"}, "staticData": null, "triggerCount": 18, "active": true}