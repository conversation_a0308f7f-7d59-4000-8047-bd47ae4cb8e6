[SYSTEM] Sistema de log inicializado: install.log
[2025-07-05 21:07:16][SUCCESS][<PERSON><PERSON>leLoader] M<PERSON><PERSON>lo AutomationUtils v3.0 carregado com sucesso.
[33;1mWARNING: The names of some imported commands from the module 'AutomationUtils' include unapproved verbs that might make them less discoverable. To find the commands with unapproved verbs, run the Import-Module command again with the Verbose parameter. For a list of approved verbs, type Get-Verb.[0m
[33;1mWARNING: Some imported command names contain one or more of the following restricted characters: # , ( ) {{ }} [ ] & - / \ $ ^ ; : " ' < > | ? @ ` * % + = ~[0m
[2025-07-05 21:07:16][SUCCESS][WorkflowOnboarding] Módulo 'AutomationUtils' carregado com sucesso.

╔══════════════════════════════════════════════════════════╗
║          WORKFLOW ONBOARDING SYSTEM v2.1                 ║
║    Configuração Robusta de Workflows Específicos         ║
╚══════════════════════════════════════════════════════════╝
[2025-07-05 21:07:17][INFO][WorkflowOnboarding] Iniciando onboarding do workflow: Enterprise Sales Machine
[2025-07-05 21:07:17][INFO][WorkflowOnboarding] Banco de dados alvo: n8n_fila
[2025-07-05 21:07:17][INFO][WorkflowOnboarding] Contêiner PostgreSQL: postgres_aula
[2025-07-05 21:07:18][INFO][WorkflowDiscovery] Iniciando descoberta rigorosa de workflows em: C:\Users\<USER>\Desktop\N8N-evolution V5\workflows
[2025-07-05 21:07:18][SUCCESS][WorkflowDiscovery] Workflow único encontrado com sucesso: C:\Users\<USER>\Desktop\N8N-evolution V5\workflows\1_Atendimento_Cliente\Enterprise Sales Machine

✅ Workflow encontrado: Enterprise Sales Machine
   Localização: C:\Users\<USER>\Desktop\N8N-evolution V5\workflows\1_Atendimento_Cliente\Enterprise Sales Machine

📋 Verificando pré-requisitos...
[2025-07-05 21:07:21][SUCCESS][Prerequisites] ✓ Contêiner PostgreSQL está rodando
[2025-07-05 21:07:21][INFO][Prerequisites] Testando conectividade com banco de dados 'n8n_fila'...
[2025-07-05 21:07:26][SUCCESS][Prerequisites] ✓ Conectividade com banco de dados verificada
✅ Todos os pré-requisitos atendidos!

🗄️  Aplicando esquema SQL do workflow...
[2025-07-05 21:07:26][INFO][SQLSchema] Diretório SQL encontrado: C:\Users\<USER>\Desktop\N8N-evolution V5\workflows\1_Atendimento_Cliente\Enterprise Sales Machine\sql
[2025-07-05 21:07:27][INFO][SQLSchema] Encontrados 34 arquivos SQL para aplicar
📑 Encontrados 34 arquivos SQL:

[1/34] Processando: 002_add_ai_enhancement_tables.sql
[2025-07-05 21:07:28][INFO][SQLProcessor] Iniciando processamento robusto melhorado de: 002_add_ai_enhancement_tables.sql
[2025-07-05 21:07:28][DEBUG][SQLProcessor] Iniciando leitura robusta melhorada do arquivo: C:\Users\<USER>\Desktop\N8N-evolution V5\workflows\1_Atendimento_Cliente\Enterprise Sales Machine\sql\002_add_ai_enhancement_tables.sql
[2025-07-05 21:07:28][DEBUG][SQLProcessor] Arquivo encontrado. Tamanho: 19858 bytes
[2025-07-05 21:07:29][DEBUG][SQLProcessor] Tentando encoding: UTF8
[2025-07-05 21:07:29][SUCCESS][SQLProcessor] Arquivo lido com sucesso usando encoding: UTF8
[2025-07-05 21:07:29][DEBUG][SQLProcessor] Total de linhas lidas: 480
[2025-07-05 21:07:29][INFO][SQLProcessor] Arquivo lido com sucesso usando encoding: UTF8. Processando 480 linhas...
[2025-07-05 21:07:29][DEBUG][SQLProcessor] Analisando linha: '-- ====================================================='
[2025-07-05 21:07:29][DEBUG][SQLProcessor] Analisando linha: '-- ESCALATION AGENT - AI ENHANCEMENT TABLES'
[2025-07-05 21:07:30][DEBUG][SQLProcessor] Analisando linha: '-- ====================================================='
[2025-07-05 21:07:30][DEBUG][SQLProcessor] Analisando linha: '-- Descrição: Adiciona tabelas para suporte a melhorias de IA,'
[2025-07-05 21:07:31][DEBUG][SQLProcessor] Analisando linha: '--           análise avançada e monitoramento do sistema'
[2025-07-05 21:07:31][DEBUG][SQLProcessor] Analisando linha: '-- Versão: 2.0'
[2025-07-05 21:07:31][DEBUG][SQLProcessor] Analisando linha: '-- Data: 2024-01-15'
[2025-07-05 21:07:31][DEBUG][SQLProcessor] Analisando linha: '-- Autor: Sistema de Escalação Inteligente'
[2025-07-05 21:07:31][DEBUG][SQLProcessor] Analisando linha: '-- ====================================================='
[2025-07-05 21:07:32][DEBUG][SQLProcessor] Analisando linha: '-- Verificar se o schema existe'
[2025-07-05 21:07:32][DEBUG][SQLProcessor] Analisando linha: 'CREATE SCHEMA IF NOT EXISTS agent;'
[2025-07-05 21:07:33][DEBUG][SQLProcessor] Analisando linha: '-- ====================================================='
[2025-07-05 21:07:33][DEBUG][SQLProcessor] Analisando linha: '-- TABELA: ai_analysis_results'
[2025-07-05 21:07:33][DEBUG][SQLProcessor] Analisando linha: '-- Descrição: Armazena resultados de análises de IA'
[2025-07-05 21:07:33][DEBUG][SQLProcessor] Analisando linha: '-- ====================================================='
[2025-07-05 21:07:33][DEBUG][SQLProcessor] Analisando linha: 'CREATE TABLE IF NOT EXISTS agent.ai_analysis_results ('
[2025-07-05 21:07:34][DEBUG][SQLProcessor] Analisando linha: 'id BIGSERIAL PRIMARY KEY,'
[2025-07-05 21:07:34][DEBUG][SQLProcessor] Analisando linha: 'escalation_id BIGINT REFERENCES agent.intelligent_escalations(id) ON DELETE CASCADE,'
[2025-07-05 21:07:34][DEBUG][SQLProcessor] Analisando linha: 'analysis_type VARCHAR(50) NOT NULL CHECK (analysis_type IN ('
[2025-07-05 21:07:35][DEBUG][SQLProcessor] Analisando linha: ''conversation_analysis','
[2025-07-05 21:07:35][DEBUG][SQLProcessor] Analisando linha: ''sentiment_analysis','
[2025-07-05 21:07:35][DEBUG][SQLProcessor] Analisando linha: ''keyword_extraction','
[2025-07-05 21:07:36][DEBUG][SQLProcessor] Analisando linha: ''pattern_detection','
[2025-07-05 21:07:36][DEBUG][SQLProcessor] Analisando linha: ''specialization_prediction','
[2025-07-05 21:07:36][DEBUG][SQLProcessor] Analisando linha: ''urgency_assessment','
[2025-07-05 21:07:37][DEBUG][SQLProcessor] Analisando linha: ''feedback_learning','
[2025-07-05 21:07:37][DEBUG][SQLProcessor] Analisando linha: ''batch_analysis''
[2025-07-05 21:07:37][DEBUG][SQLProcessor] Analisando linha: ')),'
[2025-07-05 21:07:37][DEBUG][SQLProcessor] Analisando linha: 'analysis_data JSONB NOT NULL,'
[2025-07-05 21:07:37][DEBUG][SQLProcessor] Analisando linha: 'confidence_score DECIMAL(5,2) CHECK (confidence_score >= 0 AND confidence_score <= 100),'
[2025-07-05 21:07:37][DEBUG][SQLProcessor] Analisando linha: 'processing_time_ms INTEGER,'
[2025-07-05 21:07:38][DEBUG][SQLProcessor] Analisando linha: 'model_version VARCHAR(20),'
[2025-07-05 21:07:38][DEBUG][SQLProcessor] Analisando linha: 'correlation_id UUID,'
[2025-07-05 21:07:39][DEBUG][SQLProcessor] Analisando linha: ''
[2025-07-05 21:07:39][DEBUG][SQLProcessor] Analisando linha: '-- Campos de auditoria'
[2025-07-05 21:07:39][DEBUG][SQLProcessor] Analisando linha: 'created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),'
[2025-07-05 21:07:39][DEBUG][SQLProcessor] Analisando linha: 'updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),'
[2025-07-05 21:07:40][DEBUG][SQLProcessor] Analisando linha: 'created_by VARCHAR(100) DEFAULT 'system','
[2025-07-05 21:07:40][DEBUG][SQLProcessor] Analisando linha: 'updated_by VARCHAR(100) DEFAULT 'system''
[2025-07-05 21:07:40][DEBUG][SQLProcessor] Analisando linha: ');'
[2025-07-05 21:07:40][DEBUG][SQLProcessor] Analisando linha: '-- Índices para ai_analysis_results'
[2025-07-05 21:07:40][DEBUG][SQLProcessor] Analisando linha: 'CREATE INDEX IF NOT EXISTS idx_ai_analysis_escalation_id ON agent.ai_analysis_results(escalation_id);'
[2025-07-05 21:07:40][DEBUG][SQLProcessor] Analisando linha: 'CREATE INDEX IF NOT EXISTS idx_ai_analysis_type ON agent.ai_analysis_results(analysis_type);'
[2025-07-05 21:07:41][DEBUG][SQLProcessor] Analisando linha: 'CREATE INDEX IF NOT EXISTS idx_ai_analysis_created_at ON agent.ai_analysis_results(created_at);'
[2025-07-05 21:07:41][DEBUG][SQLProcessor] Analisando linha: 'CREATE INDEX IF NOT EXISTS idx_ai_analysis_confidence ON agent.ai_analysis_results(confidence_score);'
[2025-07-05 21:07:41][DEBUG][SQLProcessor] Analisando linha: 'CREATE INDEX IF NOT EXISTS idx_ai_analysis_correlation ON agent.ai_analysis_results(correlation_id);'
[2025-07-05 21:07:41][DEBUG][SQLProcessor] Analisando linha: '-- Índice GIN para busca em JSONB'
[2025-07-05 21:07:42][DEBUG][SQLProcessor] Analisando linha: 'CREATE INDEX IF NOT EXISTS idx_ai_analysis_data_gin ON agent.ai_analysis_results USING GIN(analysis_data);'
[2025-07-05 21:07:42][DEBUG][SQLProcessor] Analisando linha: '-- ====================================================='
[2025-07-05 21:07:42][DEBUG][SQLProcessor] Analisando linha: '-- TABELA: ai_learning_feedback'
[2025-07-05 21:07:42][DEBUG][SQLProcessor] Analisando linha: '-- Descrição: Armazena feedback para aprendizado da IA'
[2025-07-05 21:07:43][DEBUG][SQLProcessor] Analisando linha: '-- ====================================================='
[2025-07-05 21:07:43][DEBUG][SQLProcessor] Analisando linha: 'CREATE TABLE IF NOT EXISTS agent.ai_learning_feedback ('
[2025-07-05 21:07:43][DEBUG][SQLProcessor] Analisando linha: 'id BIGSERIAL PRIMARY KEY,'
[2025-07-05 21:07:44][DEBUG][SQLProcessor] Analisando linha: 'escalation_id BIGINT NOT NULL REFERENCES agent.intelligent_escalations(id) ON DELETE CASCADE,'
[2025-07-05 21:07:44][DEBUG][SQLProcessor] Analisando linha: 'feedback_type VARCHAR(30) NOT NULL CHECK (feedback_type IN ('
[2025-07-05 21:07:44][DEBUG][SQLProcessor] Analisando linha: ''resolution_success','
[2025-07-05 21:07:44][DEBUG][SQLProcessor] Analisando linha: ''prediction_accuracy','
[2025-07-05 21:07:44][DEBUG][SQLProcessor] Analisando linha: ''specialization_match','
[2025-07-05 21:07:44][DEBUG][SQLProcessor] Analisando linha: ''urgency_assessment','
[2025-07-05 21:07:44][DEBUG][SQLProcessor] Analisando linha: ''customer_satisfaction','
[2025-07-05 21:07:45][DEBUG][SQLProcessor] Analisando linha: ''agent_performance''
[2025-07-05 21:07:45][DEBUG][SQLProcessor] Analisando linha: ')),'
[2025-07-05 21:07:45][DEBUG][SQLProcessor] Analisando linha: 'feedback_data JSONB NOT NULL,'
[2025-07-05 21:07:46][DEBUG][SQLProcessor] Analisando linha: 'feedback_score DECIMAL(5,2) CHECK (feedback_score >= 0 AND feedback_score <= 100),'
[2025-07-05 21:07:46][DEBUG][SQLProcessor] Analisando linha: 'agent_feedback TEXT,'
[2025-07-05 21:07:46][DEBUG][SQLProcessor] Analisando linha: 'customer_satisfaction_score INTEGER CHECK (customer_satisfaction_score >= 1 AND customer_satisfaction_score <= 5),'
[2025-07-05 21:07:46][DEBUG][SQLProcessor] Analisando linha: 'resolution_time_minutes INTEGER,'
[2025-07-05 21:07:46][DEBUG][SQLProcessor] Analisando linha: 'actual_specialization VARCHAR(50),'
[2025-07-05 21:07:47][DEBUG][SQLProcessor] Analisando linha: 'predicted_specialization VARCHAR(50),'
[2025-07-05 21:07:47][DEBUG][SQLProcessor] Analisando linha: 'prediction_accuracy DECIMAL(5,2),'
[2025-07-05 21:07:48][DEBUG][SQLProcessor] Analisando linha: ''
[2025-07-05 21:07:48][DEBUG][SQLProcessor] Analisando linha: '-- Campos de auditoria'
[2025-07-05 21:07:48][DEBUG][SQLProcessor] Analisando linha: 'created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),'
[2025-07-05 21:07:48][DEBUG][SQLProcessor] Analisando linha: 'updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),'
[2025-07-05 21:07:48][DEBUG][SQLProcessor] Analisando linha: 'created_by VARCHAR(100) DEFAULT 'system','
[2025-07-05 21:07:48][DEBUG][SQLProcessor] Analisando linha: 'updated_by VARCHAR(100) DEFAULT 'system''
[2025-07-05 21:07:49][DEBUG][SQLProcessor] Analisando linha: ');'
[2025-07-05 21:07:49][DEBUG][SQLProcessor] Analisando linha: '-- Índices para ai_learning_feedback'
[2025-07-05 21:07:49][DEBUG][SQLProcessor] Analisando linha: 'CREATE INDEX IF NOT EXISTS idx_ai_feedback_escalation_id ON agent.ai_learning_feedback(escalation_id);'
[2025-07-05 21:07:49][DEBUG][SQLProcessor] Analisando linha: 'CREATE INDEX IF NOT EXISTS idx_ai_feedback_type ON agent.ai_learning_feedback(feedback_type);'
[2025-07-05 21:07:50][DEBUG][SQLProcessor] Analisando linha: 'CREATE INDEX IF NOT EXISTS idx_ai_feedback_created_at ON agent.ai_learning_feedback(created_at);'
[2025-07-05 21:07:50][DEBUG][SQLProcessor] Analisando linha: 'CREATE INDEX IF NOT EXISTS idx_ai_feedback_score ON agent.ai_learning_feedback(feedback_score);'
[2025-07-05 21:07:50][DEBUG][SQLProcessor] Analisando linha: 'CREATE INDEX IF NOT EXISTS idx_ai_feedback_satisfaction ON agent.ai_learning_feedback(customer_satisfaction_score);'
[2025-07-05 21:07:50][DEBUG][SQLProcessor] Analisando linha: '-- ====================================================='
[2025-07-05 21:07:50][DEBUG][SQLProcessor] Analisando linha: '-- TABELA: system_alerts'
[2025-07-05 21:07:50][DEBUG][SQLProcessor] Analisando linha: '-- Descrição: Armazena alertas do sistema de monitoramento'
[2025-07-05 21:07:50][DEBUG][SQLProcessor] Analisando linha: '-- ====================================================='
[2025-07-05 21:07:51][DEBUG][SQLProcessor] Analisando linha: 'CREATE TABLE IF NOT EXISTS agent.system_alerts ('
[2025-07-05 21:07:51][DEBUG][SQLProcessor] Analisando linha: 'id BIGSERIAL PRIMARY KEY,'
[2025-07-05 21:07:51][DEBUG][SQLProcessor] Analisando linha: 'alert_type VARCHAR(50) NOT NULL CHECK (alert_type IN ('
[2025-07-05 21:07:52][DEBUG][SQLProcessor] Analisando linha: ''high_queue_volume','
[2025-07-05 21:07:52][DEBUG][SQLProcessor] Analisando linha: ''agent_unavailable','
[2025-07-05 21:07:52][DEBUG][SQLProcessor] Analisando linha: ''resolution_time_exceeded','
[2025-07-05 21:07:53][DEBUG][SQLProcessor] Analisando linha: ''low_satisfaction_score','
[2025-07-05 21:07:53][DEBUG][SQLProcessor] Analisando linha: ''ai_confidence_low','
[2025-07-05 21:07:53][DEBUG][SQLProcessor] Analisando linha: ''system_error','
[2025-07-05 21:07:53][DEBUG][SQLProcessor] Analisando linha: ''integration_failure','
[2025-07-05 21:07:54][DEBUG][SQLProcessor] Analisando linha: ''performance_degradation','
[2025-07-05 21:07:54][DEBUG][SQLProcessor] Analisando linha: ''capacity_threshold','
[2025-07-05 21:07:54][DEBUG][SQLProcessor] Analisando linha: ''escalation_timeout''
[2025-07-05 21:07:55][DEBUG][SQLProcessor] Analisando linha: ')),'
[2025-07-05 21:07:55][DEBUG][SQLProcessor] Analisando linha: 'severity VARCHAR(20) NOT NULL CHECK (severity IN ('low', 'medium', 'high', 'critical')),'
[2025-07-05 21:07:55][DEBUG][SQLProcessor] Analisando linha: 'status VARCHAR(20) NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'acknowledged', 'resolved', 'suppressed')),'
[2025-07-05 21:07:56][DEBUG][SQLProcessor] Analisando linha: 'title VARCHAR(200) NOT NULL,'
[2025-07-05 21:07:56][DEBUG][SQLProcessor] Analisando linha: 'message TEXT NOT NULL,'
[2025-07-05 21:07:56][DEBUG][SQLProcessor] Analisando linha: 'alert_data JSONB,'
[2025-07-05 21:07:57][DEBUG][SQLProcessor] Analisando linha: 'escalation_id BIGINT REFERENCES agent.intelligent_escalations(id) ON DELETE SET NULL,'
[2025-07-05 21:07:57][DEBUG][SQLProcessor] Analisando linha: 'agent_id BIGINT REFERENCES agent.agents(agent_id) ON DELETE SET NULL,'
[2025-07-05 21:07:58][DEBUG][SQLProcessor] Analisando linha: 'threshold_value DECIMAL(10,2),'
[2025-07-05 21:07:58][DEBUG][SQLProcessor] Analisando linha: 'current_value DECIMAL(10,2),'
[2025-07-05 21:07:58][DEBUG][SQLProcessor] Analisando linha: 'notification_sent BOOLEAN DEFAULT FALSE,'
[2025-07-05 21:07:59][DEBUG][SQLProcessor] Analisando linha: 'acknowledged_at TIMESTAMP WITH TIME ZONE,'
[2025-07-05 21:07:59][DEBUG][SQLProcessor] Analisando linha: 'acknowledged_by VARCHAR(100),'
[2025-07-05 21:07:59][DEBUG][SQLProcessor] Analisando linha: 'resolved_at TIMESTAMP WITH TIME ZONE,'
[2025-07-05 21:07:59][DEBUG][SQLProcessor] Analisando linha: 'resolved_by VARCHAR(100),'
[2025-07-05 21:08:00][DEBUG][SQLProcessor] Analisando linha: ''
[2025-07-05 21:08:00][DEBUG][SQLProcessor] Analisando linha: '-- Campos de auditoria'
[2025-07-05 21:08:00][DEBUG][SQLProcessor] Analisando linha: 'created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),'
[2025-07-05 21:08:01][DEBUG][SQLProcessor] Analisando linha: 'updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),'
[2025-07-05 21:08:01][DEBUG][SQLProcessor] Analisando linha: 'created_by VARCHAR(100) DEFAULT 'system','
[2025-07-05 21:08:01][DEBUG][SQLProcessor] Analisando linha: 'updated_by VARCHAR(100) DEFAULT 'system''
[2025-07-05 21:08:02][DEBUG][SQLProcessor] Analisando linha: ');'
[2025-07-05 21:08:02][DEBUG][SQLProcessor] Analisando linha: '-- Índices para system_alerts'
[2025-07-05 21:08:02][DEBUG][SQLProcessor] Analisando linha: 'CREATE INDEX IF NOT EXISTS idx_system_alerts_type ON agent.system_alerts(alert_type);'
[2025-07-05 21:08:03][DEBUG][SQLProcessor] Analisando linha: 'CREATE INDEX IF NOT EXISTS idx_system_alerts_severity ON agent.system_alerts(severity);'
[2025-07-05 21:08:03][DEBUG][SQLProcessor] Analisando linha: 'CREATE INDEX IF NOT EXISTS idx_system_alerts_status ON agent.system_alerts(status);'
[2025-07-05 21:08:03][DEBUG][SQLProcessor] Analisando linha: 'CREATE INDEX IF NOT EXISTS idx_system_alerts_created_at ON agent.system_alerts(created_at);'
[2025-07-05 21:08:03][DEBUG][SQLProcessor] Analisando linha: 'CREATE INDEX IF NOT EXISTS idx_system_alerts_escalation_id ON agent.system_alerts(escalation_id);'
[2025-07-05 21:08:03][DEBUG][SQLProcessor] Analisando linha: 'CREATE INDEX IF NOT EXISTS idx_system_alerts_agent_id ON agent.system_alerts(agent_id);'
[2025-07-05 21:08:03][DEBUG][SQLProcessor] Analisando linha: '-- Índice composto para consultas de monitoramento'
[2025-07-05 21:08:04][DEBUG][SQLProcessor] Analisando linha: 'CREATE INDEX IF NOT EXISTS idx_system_alerts_status_severity ON agent.system_alerts(status, severity, created_at);'
[2025-07-05 21:08:04][DEBUG][SQLProcessor] Analisando linha: '-- ====================================================='
[2025-07-05 21:08:04][DEBUG][SQLProcessor] Analisando linha: '-- TABELA: integration_logs'
[2025-07-05 21:08:04][DEBUG][SQLProcessor] Analisando linha: '-- Descrição: Logs de integrações externas (Chatwoot, Slack)'
[2025-07-05 21:08:04][DEBUG][SQLProcessor] Analisando linha: '-- ====================================================='
[2025-07-05 21:08:05][DEBUG][SQLProcessor] Analisando linha: 'CREATE TABLE IF NOT EXISTS agent.integration_logs ('
[2025-07-05 21:08:05][DEBUG][SQLProcessor] Analisando linha: 'id BIGSERIAL PRIMARY KEY,'
[2025-07-05 21:08:06][DEBUG][SQLProcessor] Analisando linha: 'integration_type VARCHAR(30) NOT NULL CHECK (integration_type IN ('
[2025-07-05 21:08:06][DEBUG][SQLProcessor] Analisando linha: ''chatwoot','
[2025-07-05 21:08:07][DEBUG][SQLProcessor] Analisando linha: ''slack','
[2025-07-05 21:08:07][DEBUG][SQLProcessor] Analisando linha: ''webhook','
[2025-07-05 21:08:07][DEBUG][SQLProcessor] Analisando linha: ''api','
[2025-07-05 21:08:07][DEBUG][SQLProcessor] Analisando linha: ''email','
[2025-07-05 21:08:08][DEBUG][SQLProcessor] Analisando linha: ''sms''
[2025-07-05 21:08:08][DEBUG][SQLProcessor] Analisando linha: ')),'
[2025-07-05 21:08:08][DEBUG][SQLProcessor] Analisando linha: 'operation VARCHAR(50) NOT NULL,'
[2025-07-05 21:08:08][DEBUG][SQLProcessor] Analisando linha: 'escalation_id BIGINT REFERENCES agent.intelligent_escalations(id) ON DELETE SET NULL,'
[2025-07-05 21:08:09][DEBUG][SQLProcessor] Analisando linha: 'external_id VARCHAR(100),'
[2025-07-05 21:08:09][DEBUG][SQLProcessor] Analisando linha: 'request_data JSONB,'
[2025-07-05 21:08:09][DEBUG][SQLProcessor] Analisando linha: 'response_data JSONB,'
[2025-07-05 21:08:10][DEBUG][SQLProcessor] Analisando linha: 'status_code INTEGER,'
[2025-07-05 21:08:10][DEBUG][SQLProcessor] Analisando linha: 'success BOOLEAN NOT NULL DEFAULT FALSE,'
[2025-07-05 21:08:10][DEBUG][SQLProcessor] Analisando linha: 'error_message TEXT,'
[2025-07-05 21:08:11][DEBUG][SQLProcessor] Analisando linha: 'processing_time_ms INTEGER,'
[2025-07-05 21:08:11][DEBUG][SQLProcessor] Analisando linha: 'retry_count INTEGER DEFAULT 0,'
[2025-07-05 21:08:11][DEBUG][SQLProcessor] Analisando linha: 'correlation_id UUID,'
[2025-07-05 21:08:11][DEBUG][SQLProcessor] Analisando linha: ''
[2025-07-05 21:08:11][DEBUG][SQLProcessor] Analisando linha: '-- Campos de auditoria'
[2025-07-05 21:08:11][DEBUG][SQLProcessor] Analisando linha: 'created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),'
[2025-07-05 21:08:12][DEBUG][SQLProcessor] Analisando linha: 'updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),'
[2025-07-05 21:08:12][DEBUG][SQLProcessor] Analisando linha: 'created_by VARCHAR(100) DEFAULT 'system','
[2025-07-05 21:08:13][DEBUG][SQLProcessor] Analisando linha: 'updated_by VARCHAR(100) DEFAULT 'system''
[2025-07-05 21:08:13][DEBUG][SQLProcessor] Analisando linha: ');'
[2025-07-05 21:08:13][DEBUG][SQLProcessor] Analisando linha: '-- Índices para integration_logs'
[2025-07-05 21:08:14][DEBUG][SQLProcessor] Analisando linha: 'CREATE INDEX IF NOT EXISTS idx_integration_logs_type ON agent.integration_logs(integration_type);'
[2025-07-05 21:08:14][DEBUG][SQLProcessor] Analisando linha: 'CREATE INDEX IF NOT EXISTS idx_integration_logs_operation ON agent.integration_logs(operation);'
[2025-07-05 21:08:14][DEBUG][SQLProcessor] Analisando linha: 'CREATE INDEX IF NOT EXISTS idx_integration_logs_escalation_id ON agent.integration_logs(escalation_id);'
[2025-07-05 21:08:14][DEBUG][SQLProcessor] Analisando linha: 'CREATE INDEX IF NOT EXISTS idx_integration_logs_success ON agent.integration_logs(success);'
[2025-07-05 21:08:15][DEBUG][SQLProcessor] Analisando linha: 'CREATE INDEX IF NOT EXISTS idx_integration_logs_created_at ON agent.integration_logs(created_at);'
[2025-07-05 21:08:15][DEBUG][SQLProcessor] Analisando linha: 'CREATE INDEX IF NOT EXISTS idx_integration_logs_correlation ON agent.integration_logs(correlation_id);'
[2025-07-05 21:08:15][DEBUG][SQLProcessor] Analisando linha: '-- Índice composto para análise de performance'
[2025-07-05 21:08:15][DEBUG][SQLProcessor] Analisando linha: 'CREATE INDEX IF NOT EXISTS idx_integration_logs_perf ON agent.integration_logs(integration_type, success, created_at);'
[2025-07-05 21:08:15][DEBUG][SQLProcessor] Analisando linha: '-- ====================================================='
[2025-07-05 21:08:16][DEBUG][SQLProcessor] Analisando linha: '-- TABELA: performance_metrics'
[2025-07-05 21:08:16][DEBUG][SQLProcessor] Analisando linha: '-- Descrição: Métricas de performance do sistema'
[2025-07-05 21:08:16][DEBUG][SQLProcessor] Analisando linha: '-- ====================================================='
[2025-07-05 21:08:16][DEBUG][SQLProcessor] Analisando linha: 'CREATE TABLE IF NOT EXISTS agent.performance_metrics ('
[2025-07-05 21:08:16][DEBUG][SQLProcessor] Analisando linha: 'id BIGSERIAL PRIMARY KEY,'
[2025-07-05 21:08:17][DEBUG][SQLProcessor] Analisando linha: 'metric_type VARCHAR(50) NOT NULL CHECK (metric_type IN ('
[2025-07-05 21:08:17][DEBUG][SQLProcessor] Analisando linha: ''response_time','
[2025-07-05 21:08:17][DEBUG][SQLProcessor] Analisando linha: ''resolution_time','
[2025-07-05 21:08:17][DEBUG][SQLProcessor] Analisando linha: ''queue_wait_time','
[2025-07-05 21:08:17][DEBUG][SQLProcessor] Analisando linha: ''ai_processing_time','
[2025-07-05 21:08:17][DEBUG][SQLProcessor] Analisando linha: ''agent_utilization','
[2025-07-05 21:08:17][DEBUG][SQLProcessor] Analisando linha: ''customer_satisfaction','
[2025-07-05 21:08:18][DEBUG][SQLProcessor] Analisando linha: ''escalation_volume','
[2025-07-05 21:08:18][DEBUG][SQLProcessor] Analisando linha: ''success_rate','
[2025-07-05 21:08:18][DEBUG][SQLProcessor] Analisando linha: ''error_rate','
[2025-07-05 21:08:18][DEBUG][SQLProcessor] Analisando linha: ''throughput''
[2025-07-05 21:08:18][DEBUG][SQLProcessor] Analisando linha: ')),'
[2025-07-05 21:08:19][DEBUG][SQLProcessor] Analisando linha: 'metric_name VARCHAR(100) NOT NULL,'
[2025-07-05 21:08:19][DEBUG][SQLProcessor] Analisando linha: 'metric_value DECIMAL(15,4) NOT NULL,'
[2025-07-05 21:08:19][DEBUG][SQLProcessor] Analisando linha: 'metric_unit VARCHAR(20),'
[2025-07-05 21:08:19][DEBUG][SQLProcessor] Analisando linha: 'aggregation_period VARCHAR(20) CHECK (aggregation_period IN ('minute', 'hour', 'day', 'week', 'month')),'
[2025-07-05 21:08:19][DEBUG][SQLProcessor] Analisando linha: 'period_start TIMESTAMP WITH TIME ZONE NOT NULL,'
[2025-07-05 21:08:19][DEBUG][SQLProcessor] Analisando linha: 'period_end TIMESTAMP WITH TIME ZONE NOT NULL,'
[2025-07-05 21:08:19][DEBUG][SQLProcessor] Analisando linha: 'metadata JSONB,'
[2025-07-05 21:08:19][DEBUG][SQLProcessor] Analisando linha: ''
[2025-07-05 21:08:19][DEBUG][SQLProcessor] Analisando linha: '-- Campos de auditoria'
[2025-07-05 21:08:20][DEBUG][SQLProcessor] Analisando linha: 'created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),'
[2025-07-05 21:08:20][DEBUG][SQLProcessor] Analisando linha: 'updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),'
[2025-07-05 21:08:20][DEBUG][SQLProcessor] Analisando linha: 'created_by VARCHAR(100) DEFAULT 'system','
[2025-07-05 21:08:20][DEBUG][SQLProcessor] Analisando linha: 'updated_by VARCHAR(100) DEFAULT 'system''
[2025-07-05 21:08:20][DEBUG][SQLProcessor] Analisando linha: ');'
[2025-07-05 21:08:20][DEBUG][SQLProcessor] Analisando linha: '-- Índices para performance_metrics'
[2025-07-05 21:08:20][DEBUG][SQLProcessor] Analisando linha: 'CREATE INDEX IF NOT EXISTS idx_performance_metrics_type ON agent.performance_metrics(metric_type);'
[2025-07-05 21:08:21][DEBUG][SQLProcessor] Analisando linha: 'CREATE INDEX IF NOT EXISTS idx_performance_metrics_name ON agent.performance_metrics(metric_name);'
[2025-07-05 21:08:21][DEBUG][SQLProcessor] Analisando linha: 'CREATE INDEX IF NOT EXISTS idx_performance_metrics_period ON agent.performance_metrics(aggregation_period, period_start);'
[2025-07-05 21:08:21][DEBUG][SQLProcessor] Analisando linha: 'CREATE INDEX IF NOT EXISTS idx_performance_metrics_created_at ON agent.performance_metrics(created_at);'
[2025-07-05 21:08:21][DEBUG][SQLProcessor] Analisando linha: '-- Índice composto para consultas de séries temporais'
[2025-07-05 21:08:21][DEBUG][SQLProcessor] Analisando linha: 'CREATE INDEX IF NOT EXISTS idx_performance_metrics_timeseries ON agent.performance_metrics(metric_type, metric_name, period_start);'
[2025-07-05 21:08:22][DEBUG][SQLProcessor] Analisando linha: '-- ====================================================='
[2025-07-05 21:08:22][DEBUG][SQLProcessor] Analisando linha: '-- VIEWS PARA MONITORAMENTO'
[2025-07-05 21:08:23][DEBUG][SQLProcessor] Analisando linha: '-- ====================================================='
[2025-07-05 21:08:23][DEBUG][SQLProcessor] Analisando linha: '-- View para estatísticas em tempo real'
[2025-07-05 21:08:23][DEBUG][SQLProcessor] Analisando linha: 'CREATE OR REPLACE VIEW agent.v_real_time_stats AS'
[2025-07-05 21:08:23][DEBUG][SQLProcessor] Analisando linha: 'SELECT'
[2025-07-05 21:08:24][DEBUG][SQLProcessor] Analisando linha: 'COUNT(*) as total_escalations,'
[2025-07-05 21:08:24][DEBUG][SQLProcessor] Analisando linha: 'COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_escalations,'
[2025-07-05 21:08:24][DEBUG][SQLProcessor] Analisando linha: 'COUNT(CASE WHEN status = 'in_progress' THEN 1 END) as active_escalations,'
[2025-07-05 21:08:25][DEBUG][SQLProcessor] Analisando linha: 'COUNT(CASE WHEN status = 'resolved' THEN 1 END) as resolved_escalations,'
[2025-07-05 21:08:25][DEBUG][SQLProcessor] Analisando linha: 'COUNT(CASE WHEN urgency = 'high' THEN 1 END) as high_priority,'
[2025-07-05 21:08:25][DEBUG][SQLProcessor] Analisando linha: 'COUNT(CASE WHEN urgency = 'critical' THEN 1 END) as critical_priority,'
[2025-07-05 21:08:26][DEBUG][SQLProcessor] Analisando linha: 'AVG(EXTRACT(EPOCH FROM (resolved_at - created_at))/60) as avg_resolution_time_minutes,'
[2025-07-05 21:08:26][DEBUG][SQLProcessor] Analisando linha: 'AVG(ai_confidence_score) as avg_ai_confidence,'
[2025-07-05 21:08:26][DEBUG][SQLProcessor] Analisando linha: 'MAX(created_at) as last_escalation_time'
[2025-07-05 21:08:27][DEBUG][SQLProcessor] Analisando linha: 'FROM agent.intelligent_escalations'
[2025-07-05 21:08:27][DEBUG][SQLProcessor] Analisando linha: 'WHERE created_at >= NOW() - INTERVAL '24 hours';'
[2025-07-05 21:08:28][DEBUG][SQLProcessor] Analisando linha: '-- View para performance de agentes'
[2025-07-05 21:08:28][DEBUG][SQLProcessor] Analisando linha: 'CREATE OR REPLACE VIEW agent.v_agent_performance AS'
[2025-07-05 21:08:28][DEBUG][SQLProcessor] Analisando linha: 'SELECT'
[2025-07-05 21:08:29][DEBUG][SQLProcessor] Analisando linha: 'a.agent_id,'
[2025-07-05 21:08:29][DEBUG][SQLProcessor] Analisando linha: 'a.name,'
[2025-07-05 21:08:29][DEBUG][SQLProcessor] Analisando linha: 'a.specialization,'
[2025-07-05 21:08:29][DEBUG][SQLProcessor] Analisando linha: 'a.status,'
[2025-07-05 21:08:29][DEBUG][SQLProcessor] Analisando linha: 'a.current_workload,'
[2025-07-05 21:08:30][DEBUG][SQLProcessor] Analisando linha: 'a.max_concurrent_escalations,'
[2025-07-05 21:08:30][DEBUG][SQLProcessor] Analisando linha: 'COUNT(e.id) as handled_escalations_24h,'
[2025-07-05 21:08:31][DEBUG][SQLProcessor] Analisando linha: 'AVG(EXTRACT(EPOCH FROM (e.resolved_at - e.assigned_at))/60) as avg_handling_time_minutes,'
[2025-07-05 21:08:31][DEBUG][SQLProcessor] Analisando linha: 'AVG(e.customer_satisfaction_score) as avg_satisfaction_score,'
[2025-07-05 21:08:31][DEBUG][SQLProcessor] Analisando linha: 'COUNT(CASE WHEN e.status = 'resolved' THEN 1 END) as resolved_count,'
[2025-07-05 21:08:32][DEBUG][SQLProcessor] Analisando linha: 'ROUND('
[2025-07-05 21:08:32][DEBUG][SQLProcessor] Analisando linha: 'COUNT(CASE WHEN e.status = 'resolved' THEN 1 END)::DECIMAL /'
[2025-07-05 21:08:33][DEBUG][SQLProcessor] Analisando linha: 'NULLIF(COUNT(e.id), 0) * 100, 2'
[2025-07-05 21:08:33][DEBUG][SQLProcessor] Analisando linha: ') as resolution_rate_percentage'
[2025-07-05 21:08:33][DEBUG][SQLProcessor] Analisando linha: 'FROM agent.agents a'
[2025-07-05 21:08:34][DEBUG][SQLProcessor] Analisando linha: 'LEFT JOIN agent.intelligent_escalations e ON a.agent_id = e.assigned_agent_id'
[2025-07-05 21:08:34][DEBUG][SQLProcessor] Analisando linha: 'AND e.assigned_at >= NOW() - INTERVAL '24 hours''
[2025-07-05 21:08:35][DEBUG][SQLProcessor] Analisando linha: 'GROUP BY a.agent_id, a.name, a.specialization, a.status, a.current_workload, a.max_concurrent_escalations;'
[2025-07-05 21:08:35][DEBUG][SQLProcessor] Analisando linha: '-- View para alertas ativos'
[2025-07-05 21:08:35][DEBUG][SQLProcessor] Analisando linha: 'CREATE OR REPLACE VIEW agent.v_active_alerts AS'
[2025-07-05 21:08:35][DEBUG][SQLProcessor] Analisando linha: 'SELECT'
[2025-07-05 21:08:35][DEBUG][SQLProcessor] Analisando linha: 'alert_type,'
[2025-07-05 21:08:35][DEBUG][SQLProcessor] Analisando linha: 'severity,'
[2025-07-05 21:08:36][DEBUG][SQLProcessor] Analisando linha: 'COUNT(*) as alert_count,'
[2025-07-05 21:08:36][DEBUG][SQLProcessor] Analisando linha: 'MAX(created_at) as latest_alert,'
[2025-07-05 21:08:36][DEBUG][SQLProcessor] Analisando linha: 'MIN(created_at) as earliest_alert,'
[2025-07-05 21:08:36][DEBUG][SQLProcessor] Analisando linha: 'AVG(current_value) as avg_current_value,'
[2025-07-05 21:08:37][DEBUG][SQLProcessor] Analisando linha: 'MAX(current_value) as max_current_value'
[2025-07-05 21:08:37][DEBUG][SQLProcessor] Analisando linha: 'FROM agent.system_alerts'
[2025-07-05 21:08:37][DEBUG][SQLProcessor] Analisando linha: 'WHERE status = 'active''
[2025-07-05 21:08:37][DEBUG][SQLProcessor] Analisando linha: 'GROUP BY alert_type, severity'
[2025-07-05 21:08:37][DEBUG][SQLProcessor] Analisando linha: 'ORDER BY'
[2025-07-05 21:08:38][DEBUG][SQLProcessor] Analisando linha: 'CASE severity'
[2025-07-05 21:08:38][DEBUG][SQLProcessor] Analisando linha: 'WHEN 'critical' THEN 1'
[2025-07-05 21:08:38][DEBUG][SQLProcessor] Analisando linha: 'WHEN 'high' THEN 2'
[2025-07-05 21:08:38][DEBUG][SQLProcessor] Analisando linha: 'WHEN 'medium' THEN 3'
[2025-07-05 21:08:38][DEBUG][SQLProcessor] Analisando linha: 'WHEN 'low' THEN 4'
[2025-07-05 21:08:38][DEBUG][SQLProcessor] Analisando linha: 'END,'
[2025-07-05 21:08:38][DEBUG][SQLProcessor] Analisando linha: 'alert_count DESC;'
[2025-07-05 21:08:39][DEBUG][SQLProcessor] Analisando linha: '-- ====================================================='
[2025-07-05 21:08:39][DEBUG][SQLProcessor] Analisando linha: '-- FUNÇÕES PARA AUTOMAÇÃO'
[2025-07-05 21:08:40][DEBUG][SQLProcessor] Analisando linha: '-- ====================================================='
[2025-07-05 21:08:40][DEBUG][SQLProcessor] Analisando linha: '-- Função para calcular score de saúde do sistema'
[2025-07-05 21:08:40][DEBUG][SQLProcessor] Analisando linha: 'CREATE OR REPLACE FUNCTION agent.calculate_system_health_score()'
[2025-07-05 21:08:40][DEBUG][SQLProcessor] Analisando linha: 'RETURNS INTEGER AS $$'
[2025-07-05 21:08:40][DEBUG][SQLProcessor] Analisando linha: 'DECLARE'
[2025-07-05 21:08:40][DEBUG][SQLProcessor] Analisando linha: 'health_score INTEGER := 100;'
[2025-07-05 21:08:41][DEBUG][SQLProcessor] Analisando linha: 'critical_alerts INTEGER;'
[2025-07-05 21:08:41][DEBUG][SQLProcessor] Analisando linha: 'high_alerts INTEGER;'
[2025-07-05 21:08:41][DEBUG][SQLProcessor] Analisando linha: 'avg_resolution_time DECIMAL;'
[2025-07-05 21:08:41][DEBUG][SQLProcessor] Analisando linha: 'pending_escalations INTEGER;'
[2025-07-05 21:08:42][DEBUG][SQLProcessor] Analisando linha: 'BEGIN'
[2025-07-05 21:08:42][DEBUG][SQLProcessor] Analisando linha: '-- Contar alertas críticos'
[2025-07-05 21:08:42][DEBUG][SQLProcessor] Analisando linha: 'SELECT COUNT(*) INTO critical_alerts'
[2025-07-05 21:08:42][DEBUG][SQLProcessor] Analisando linha: 'FROM agent.system_alerts'
[2025-07-05 21:08:42][DEBUG][SQLProcessor] Analisando linha: 'WHERE status = 'active' AND severity = 'critical';'
[2025-07-05 21:08:43][DEBUG][SQLProcessor] Analisando linha: ''
[2025-07-05 21:08:43][DEBUG][SQLProcessor] Analisando linha: '-- Contar alertas altos'
[2025-07-05 21:08:43][DEBUG][SQLProcessor] Analisando linha: 'SELECT COUNT(*) INTO high_alerts'
[2025-07-05 21:08:43][DEBUG][SQLProcessor] Analisando linha: 'FROM agent.system_alerts'
[2025-07-05 21:08:43][DEBUG][SQLProcessor] Analisando linha: 'WHERE status = 'active' AND severity = 'high';'
[2025-07-05 21:08:43][DEBUG][SQLProcessor] Analisando linha: ''
[2025-07-05 21:08:44][DEBUG][SQLProcessor] Analisando linha: '-- Calcular tempo médio de resolução (últimas 24h)'
[2025-07-05 21:08:44][DEBUG][SQLProcessor] Analisando linha: 'SELECT AVG(EXTRACT(EPOCH FROM (resolved_at - created_at))/60) INTO avg_resolution_time'
[2025-07-05 21:08:44][DEBUG][SQLProcessor] Analisando linha: 'FROM agent.intelligent_escalations'
[2025-07-05 21:08:44][DEBUG][SQLProcessor] Analisando linha: 'WHERE resolved_at >= NOW() - INTERVAL '24 hours';'
[2025-07-05 21:08:45][DEBUG][SQLProcessor] Analisando linha: ''
[2025-07-05 21:08:45][DEBUG][SQLProcessor] Analisando linha: '-- Contar escalações pendentes'
[2025-07-05 21:08:45][DEBUG][SQLProcessor] Analisando linha: 'SELECT COUNT(*) INTO pending_escalations'
[2025-07-05 21:08:45][DEBUG][SQLProcessor] Analisando linha: 'FROM agent.intelligent_escalations'
[2025-07-05 21:08:45][DEBUG][SQLProcessor] Analisando linha: 'WHERE status = 'pending';'
[2025-07-05 21:08:45][DEBUG][SQLProcessor] Analisando linha: ''
[2025-07-05 21:08:46][DEBUG][SQLProcessor] Analisando linha: '-- Aplicar penalidades'
[2025-07-05 21:08:46][DEBUG][SQLProcessor] Analisando linha: 'health_score := health_score - (critical_alerts * 20);'
[2025-07-05 21:08:46][DEBUG][SQLProcessor] Analisando linha: 'health_score := health_score - (high_alerts * 10);'
[2025-07-05 21:08:46][DEBUG][SQLProcessor] Analisando linha: ''
[2025-07-05 21:08:46][DEBUG][SQLProcessor] Analisando linha: 'IF avg_resolution_time > 180 THEN'
[2025-07-05 21:08:46][DEBUG][SQLProcessor] Analisando linha: 'health_score := health_score - 15;'
[2025-07-05 21:08:47][DEBUG][SQLProcessor] Analisando linha: 'ELSIF avg_resolution_time > 120 THEN'
[2025-07-05 21:08:47][DEBUG][SQLProcessor] Analisando linha: 'health_score := health_score - 10;'
[2025-07-05 21:08:47][DEBUG][SQLProcessor] Analisando linha: 'END IF;'
[2025-07-05 21:08:47][DEBUG][SQLProcessor] Analisando linha: ''
[2025-07-05 21:08:48][DEBUG][SQLProcessor] Analisando linha: 'IF pending_escalations > 50 THEN'
[2025-07-05 21:08:48][DEBUG][SQLProcessor] Analisando linha: 'health_score := health_score - 20;'
[2025-07-05 21:08:48][DEBUG][SQLProcessor] Analisando linha: 'ELSIF pending_escalations > 20 THEN'
[2025-07-05 21:08:48][DEBUG][SQLProcessor] Analisando linha: 'health_score := health_score - 10;'
[2025-07-05 21:08:48][DEBUG][SQLProcessor] Analisando linha: 'END IF;'
[2025-07-05 21:08:49][DEBUG][SQLProcessor] Analisando linha: ''
[2025-07-05 21:08:49][DEBUG][SQLProcessor] Analisando linha: '-- Garantir que o score não seja negativo'
[2025-07-05 21:08:49][DEBUG][SQLProcessor] Analisando linha: 'health_score := GREATEST(health_score, 0);'
[2025-07-05 21:08:50][DEBUG][SQLProcessor] Analisando linha: ''
[2025-07-05 21:08:50][DEBUG][SQLProcessor] Analisando linha: 'RETURN health_score;'
[2025-07-05 21:08:50][DEBUG][SQLProcessor] Analisando linha: 'END;'
[2025-07-05 21:08:51][DEBUG][SQLProcessor] Analisando linha: '$$ LANGUAGE plpgsql;'
[2025-07-05 21:08:51][DEBUG][SQLProcessor] Analisando linha: '-- Função para criar alerta automático'
[2025-07-05 21:08:51][DEBUG][SQLProcessor] Analisando linha: 'CREATE OR REPLACE FUNCTION agent.create_system_alert('
[2025-07-05 21:08:51][DEBUG][SQLProcessor] Analisando linha: 'p_alert_type VARCHAR(50),'
[2025-07-05 21:08:51][DEBUG][SQLProcessor] Analisando linha: 'p_severity VARCHAR(20),'
[2025-07-05 21:08:52][DEBUG][SQLProcessor] Analisando linha: 'p_title VARCHAR(200),'
[2025-07-05 21:08:52][DEBUG][SQLProcessor] Analisando linha: 'p_message TEXT,'
[2025-07-05 21:08:52][DEBUG][SQLProcessor] Analisando linha: 'p_escalation_id BIGINT DEFAULT NULL,'
[2025-07-05 21:08:53][DEBUG][SQLProcessor] Analisando linha: 'p_agent_id BIGINT DEFAULT NULL,'
[2025-07-05 21:08:53][DEBUG][SQLProcessor] Analisando linha: 'p_threshold_value DECIMAL DEFAULT NULL,'
[2025-07-05 21:08:53][DEBUG][SQLProcessor] Analisando linha: 'p_current_value DECIMAL DEFAULT NULL,'
[2025-07-05 21:08:53][DEBUG][SQLProcessor] Analisando linha: 'p_alert_data JSONB DEFAULT NULL'
[2025-07-05 21:08:54][DEBUG][SQLProcessor] Analisando linha: ')'
[2025-07-05 21:08:54][DEBUG][SQLProcessor] Analisando linha: 'RETURNS BIGINT AS $$'
[2025-07-05 21:08:54][DEBUG][SQLProcessor] Analisando linha: 'DECLARE'
[2025-07-05 21:08:54][DEBUG][SQLProcessor] Analisando linha: 'alert_id BIGINT;'
[2025-07-05 21:08:55][DEBUG][SQLProcessor] Analisando linha: 'BEGIN'
[2025-07-05 21:08:55][DEBUG][SQLProcessor] Analisando linha: 'INSERT INTO agent.system_alerts ('
[2025-07-05 21:08:55][DEBUG][SQLProcessor] Analisando linha: 'alert_type, severity, title, message, escalation_id, agent_id,'
[2025-07-05 21:08:55][DEBUG][SQLProcessor] Analisando linha: 'threshold_value, current_value, alert_data'
[2025-07-05 21:08:56][DEBUG][SQLProcessor] Analisando linha: ') VALUES ('
[2025-07-05 21:08:56][DEBUG][SQLProcessor] Analisando linha: 'p_alert_type, p_severity, p_title, p_message, p_escalation_id, p_agent_id,'
[2025-07-05 21:08:56][DEBUG][SQLProcessor] Analisando linha: 'p_threshold_value, p_current_value, p_alert_data'
[2025-07-05 21:08:56][DEBUG][SQLProcessor] Analisando linha: ') RETURNING id INTO alert_id;'
[2025-07-05 21:08:56][DEBUG][SQLProcessor] Analisando linha: ''
[2025-07-05 21:08:57][DEBUG][SQLProcessor] Analisando linha: 'RETURN alert_id;'
[2025-07-05 21:08:57][DEBUG][SQLProcessor] Analisando linha: 'END;'
[2025-07-05 21:08:57][DEBUG][SQLProcessor] Analisando linha: '$$ LANGUAGE plpgsql;'
[2025-07-05 21:08:57][DEBUG][SQLProcessor] Analisando linha: '-- ====================================================='
[2025-07-05 21:08:57][DEBUG][SQLProcessor] Analisando linha: '-- TRIGGERS PARA AUDITORIA'
[2025-07-05 21:08:57][DEBUG][SQLProcessor] Analisando linha: '-- ====================================================='
[2025-07-05 21:08:58][DEBUG][SQLProcessor] Analisando linha: '-- Trigger para atualizar updated_at automaticamente'
[2025-07-05 21:08:58][DEBUG][SQLProcessor] Analisando linha: 'CREATE OR REPLACE FUNCTION agent.update_updated_at_column()'
[2025-07-05 21:08:58][DEBUG][SQLProcessor] Analisando linha: 'RETURNS TRIGGER AS $$'
[2025-07-05 21:08:59][DEBUG][SQLProcessor] Analisando linha: 'BEGIN'
[2025-07-05 21:08:59][DEBUG][SQLProcessor] Analisando linha: 'NEW.updated_at = NOW();'
[2025-07-05 21:08:59][DEBUG][SQLProcessor] Analisando linha: 'RETURN NEW;'
[2025-07-05 21:08:59][DEBUG][SQLProcessor] Analisando linha: 'END;'
[2025-07-05 21:08:59][DEBUG][SQLProcessor] Analisando linha: '$$ LANGUAGE plpgsql;'
[2025-07-05 21:08:59][DEBUG][SQLProcessor] Analisando linha: '-- Aplicar trigger em todas as novas tabelas'
[2025-07-05 21:09:00][DEBUG][SQLProcessor] Analisando linha: 'CREATE TRIGGER trigger_ai_analysis_results_updated_at'
[2025-07-05 21:09:00][DEBUG][SQLProcessor] Analisando linha: 'BEFORE UPDATE ON agent.ai_analysis_results'
[2025-07-05 21:09:00][DEBUG][SQLProcessor] Analisando linha: 'FOR EACH ROW EXECUTE FUNCTION agent.update_updated_at_column();'
[2025-07-05 21:09:00][DEBUG][SQLProcessor] Analisando linha: 'CREATE TRIGGER trigger_ai_learning_feedback_updated_at'
[2025-07-05 21:09:00][DEBUG][SQLProcessor] Analisando linha: 'BEFORE UPDATE ON agent.ai_learning_feedback'
[2025-07-05 21:09:00][DEBUG][SQLProcessor] Analisando linha: 'FOR EACH ROW EXECUTE FUNCTION agent.update_updated_at_column();'
[2025-07-05 21:09:00][DEBUG][SQLProcessor] Analisando linha: 'CREATE TRIGGER trigger_system_alerts_updated_at'
[2025-07-05 21:09:00][DEBUG][SQLProcessor] Analisando linha: 'BEFORE UPDATE ON agent.system_alerts'
[2025-07-05 21:09:00][DEBUG][SQLProcessor] Analisando linha: 'FOR EACH ROW EXECUTE FUNCTION agent.update_updated_at_column();'
[2025-07-05 21:09:01][DEBUG][SQLProcessor] Analisando linha: 'CREATE TRIGGER trigger_integration_logs_updated_at'
[2025-07-05 21:09:01][DEBUG][SQLProcessor] Analisando linha: 'BEFORE UPDATE ON agent.integration_logs'
[2025-07-05 21:09:01][DEBUG][SQLProcessor] Analisando linha: 'FOR EACH ROW EXECUTE FUNCTION agent.update_updated_at_column();'
[2025-07-05 21:09:02][DEBUG][SQLProcessor] Analisando linha: 'CREATE TRIGGER trigger_performance_metrics_updated_at'
[2025-07-05 21:09:02][DEBUG][SQLProcessor] Analisando linha: 'BEFORE UPDATE ON agent.performance_metrics'
[2025-07-05 21:09:02][DEBUG][SQLProcessor] Analisando linha: 'FOR EACH ROW EXECUTE FUNCTION agent.update_updated_at_column();'
[2025-07-05 21:09:03][DEBUG][SQLProcessor] Analisando linha: '-- ====================================================='
[2025-07-05 21:09:03][DEBUG][SQLProcessor] Analisando linha: '-- COMENTÁRIOS NAS TABELAS'
[2025-07-05 21:09:03][DEBUG][SQLProcessor] Analisando linha: '-- ====================================================='
[2025-07-05 21:09:04][DEBUG][SQLProcessor] Analisando linha: 'COMMENT ON TABLE agent.ai_analysis_results IS 'Armazena resultados de análises de IA para escalações';'
[2025-07-05 21:09:04][DEBUG][SQLProcessor] Analisando linha: 'COMMENT ON TABLE agent.ai_learning_feedback IS 'Feedback para aprendizado e melhoria da IA';'
[2025-07-05 21:09:04][DEBUG][SQLProcessor] Analisando linha: 'COMMENT ON TABLE agent.system_alerts IS 'Alertas do sistema de monitoramento';'
[2025-07-05 21:09:04][DEBUG][SQLProcessor] Analisando linha: 'COMMENT ON TABLE agent.integration_logs IS 'Logs de integrações com sistemas externos';'
[2025-07-05 21:09:04][DEBUG][SQLProcessor] Analisando linha: 'COMMENT ON TABLE agent.performance_metrics IS 'Métricas de performance do sistema';'
[2025-07-05 21:09:05][DEBUG][SQLProcessor] Analisando linha: 'COMMENT ON VIEW agent.v_real_time_stats IS 'Estatísticas em tempo real do sistema de escalação';'
[2025-07-05 21:09:05][DEBUG][SQLProcessor] Analisando linha: 'COMMENT ON VIEW agent.v_agent_performance IS 'Performance dos agentes nas últimas 24 horas';'
[2025-07-05 21:09:06][DEBUG][SQLProcessor] Analisando linha: 'COMMENT ON VIEW agent.v_active_alerts IS 'Resumo de alertas ativos agrupados por tipo e severidade';'
[2025-07-05 21:09:06][DEBUG][SQLProcessor] Analisando linha: 'COMMENT ON FUNCTION agent.calculate_system_health_score() IS 'Calcula score de saúde do sistema baseado em métricas';'
[2025-07-05 21:09:07][DEBUG][SQLProcessor] Analisando linha: 'COMMENT ON FUNCTION agent.create_system_alert(VARCHAR, VARCHAR, VARCHAR, TEXT, BIGINT, BIGINT, DECIMAL, DECIMAL, JSONB) IS 'Cria um novo alerta no sistema';'
[2025-07-05 21:09:07][DEBUG][SQLProcessor] Analisando linha: '-- ====================================================='
[2025-07-05 21:09:07][DEBUG][SQLProcessor] Analisando linha: '-- DADOS INICIAIS PARA TESTES'
[2025-07-05 21:09:07][DEBUG][SQLProcessor] Analisando linha: '-- ====================================================='
[2025-07-05 21:09:08][DEBUG][SQLProcessor] Analisando linha: '-- Inserir algumas métricas de exemplo'
[2025-07-05 21:09:08][DEBUG][SQLProcessor] Analisando linha: 'INSERT INTO agent.performance_metrics (metric_type, metric_name, metric_value, metric_unit, aggregation_period, period_start, period_end) VALUES'
[2025-07-05 21:09:08][DEBUG][SQLProcessor] Analisando linha: '('response_time', 'avg_api_response_time', 150.5, 'ms', 'hour', NOW() - INTERVAL '1 hour', NOW()),'
[2025-07-05 21:09:08][DEBUG][SQLProcessor] Analisando linha: '('resolution_time', 'avg_escalation_resolution', 95.2, 'minutes', 'hour', NOW() - INTERVAL '1 hour', NOW()),'
[2025-07-05 21:09:09][DEBUG][SQLProcessor] Analisando linha: '('customer_satisfaction', 'avg_satisfaction_score', 4.2, 'rating', 'hour', NOW() - INTERVAL '1 hour', NOW()),'
[2025-07-05 21:09:09][DEBUG][SQLProcessor] Analisando linha: '('escalation_volume', 'total_escalations', 45, 'count', 'hour', NOW() - INTERVAL '1 hour', NOW()),'
[2025-07-05 21:09:09][DEBUG][SQLProcessor] Analisando linha: '('success_rate', 'escalation_success_rate', 92.5, 'percentage', 'hour', NOW() - INTERVAL '1 hour', NOW())'
[2025-07-05 21:09:09][DEBUG][SQLProcessor] Analisando linha: 'ON CONFLICT DO NOTHING;'
[2025-07-05 21:09:09][DEBUG][SQLProcessor] Analisando linha: '-- ====================================================='
[2025-07-05 21:09:10][DEBUG][SQLProcessor] Analisando linha: '-- VERIFICAÇÃO FINAL'
[2025-07-05 21:09:10][DEBUG][SQLProcessor] Analisando linha: '-- ====================================================='
[2025-07-05 21:09:10][DEBUG][SQLProcessor] Analisando linha: '-- Verificar se todas as tabelas foram criadas'
[2025-07-05 21:09:11][DEBUG][SQLProcessor] Analisando linha: 'DO $$'
[2025-07-05 21:09:11][DEBUG][SQLProcessor] Analisando linha: 'DECLARE'
[2025-07-05 21:09:11][DEBUG][SQLProcessor] Analisando linha: 'table_count INTEGER;'
[2025-07-05 21:09:11][DEBUG][SQLProcessor] Analisando linha: 'BEGIN'
[2025-07-05 21:09:11][DEBUG][SQLProcessor] Analisando linha: 'SELECT COUNT(*) INTO table_count'
[2025-07-05 21:09:11][DEBUG][SQLProcessor] Analisando linha: 'FROM information_schema.tables'
[2025-07-05 21:09:11][DEBUG][SQLProcessor] Analisando linha: 'WHERE table_schema = 'agent''
[2025-07-05 21:09:12][DEBUG][SQLProcessor] Analisando linha: 'AND table_name IN ('
[2025-07-05 21:09:12][DEBUG][SQLProcessor] Analisando linha: ''ai_analysis_results','
[2025-07-05 21:09:12][DEBUG][SQLProcessor] Analisando linha: ''ai_learning_feedback','
[2025-07-05 21:09:12][DEBUG][SQLProcessor] Analisando linha: ''system_alerts','
[2025-07-05 21:09:12][DEBUG][SQLProcessor] Analisando linha: ''integration_logs','
[2025-07-05 21:09:13][DEBUG][SQLProcessor] Analisando linha: ''performance_metrics''
[2025-07-05 21:09:13][DEBUG][SQLProcessor] Analisando linha: ');'
[2025-07-05 21:09:14][DEBUG][SQLProcessor] Analisando linha: ''
[2025-07-05 21:09:14][DEBUG][SQLProcessor] Analisando linha: 'IF table_count = 5 THEN'
[2025-07-05 21:09:14][DEBUG][SQLProcessor] Analisando linha: 'RAISE NOTICE 'SUCCESS: Todas as 5 tabelas de AI Enhancement foram criadas com sucesso!';'
[2025-07-05 21:09:14][DEBUG][SQLProcessor] Analisando linha: 'ELSE'
[2025-07-05 21:09:15][DEBUG][SQLProcessor] Analisando linha: 'RAISE WARNING 'ATENÇÃO: Apenas % de 5 tabelas foram criadas. Verifique os logs.', table_count;'
[2025-07-05 21:09:15][DEBUG][SQLProcessor] Analisando linha: 'END IF;'
[2025-07-05 21:09:16][DEBUG][SQLProcessor] Analisando linha: 'END $$;'
[2025-07-05 21:09:16][DEBUG][SQLProcessor] Analisando linha: '-- Log de conclusão'
[2025-07-05 21:09:16][DEBUG][SQLProcessor] Analisando linha: 'INSERT INTO agent.escalation_logs (escalation_id, log_level, message, metadata, created_by)'
[2025-07-05 21:09:16][DEBUG][SQLProcessor] Analisando linha: 'VALUES ('
[2025-07-05 21:09:17][DEBUG][SQLProcessor] Analisando linha: 'NULL,'
[2025-07-05 21:09:17][DEBUG][SQLProcessor] Analisando linha: ''INFO','
[2025-07-05 21:09:17][DEBUG][SQLProcessor] Analisando linha: ''AI Enhancement tables created successfully','
[2025-07-05 21:09:18][DEBUG][SQLProcessor] Analisando linha: ''{"script": "002_add_ai_enhancement_tables.sql", "version": "2.0", "tables_created": 5}','
[2025-07-05 21:09:18][DEBUG][SQLProcessor] Analisando linha: ''system''
[2025-07-05 21:09:18][DEBUG][SQLProcessor] Analisando linha: ') ON CONFLICT DO NOTHING;'
[2025-07-05 21:09:19][DEBUG][SQLProcessor] Analisando linha: '-- ====================================================='
[2025-07-05 21:09:19][DEBUG][SQLProcessor] Analisando linha: '-- FIM DO SCRIPT'
[2025-07-05 21:09:19][DEBUG][SQLProcessor] Analisando linha: '-- ====================================================='
[2025-07-05 21:09:19][INFO][SQLProcessor] Nenhum comando \connect encontrado. Usando banco padrão: 'n8n_fila'
[2025-07-05 21:09:24][DEBUG][SQLProcessor] Arquivo temporário único gerado (tentativa 1): /tmp/002_add_ai_enhancement_tables_20250705_210920_457_15996_a9185803db37_NaxGZJCR.sql
[2025-07-05 21:09:24][DEBUG][SQLProcessor] Arquivo temporário local criado: C:\Users\<USER>\AppData\Local\Temp\tmpwgyadt.tmp
[2025-07-05 21:09:25][DEBUG][SQLProcessor] Arquivo local validado. Tamanho: 20339 bytes
[2025-07-05 21:09:25][DEBUG][SQLProcessor] Copiando arquivo processado para contêiner: /tmp/002_add_ai_enhancement_tables_20250705_210920_457_15996_a9185803db37_NaxGZJCR.sql
[2025-07-05 21:09:33][SUCCESS][SQLProcessor] Arquivo copiado e verificado com sucesso no contêiner
[2025-07-05 21:09:33][INFO][SQLProcessor] Executando script SQL processado no banco 'n8n_fila' com ON_ERROR_STOP
[2025-07-05 21:09:40][ERROR][SQLProcessor] Execução SQL falhou. Código de saída: 3
[2025-07-05 21:09:41][ERROR][SQLProcessor] Arquivo SQL: 002_add_ai_enhancement_tables.sql, Banco: n8n_fila
[2025-07-05 21:09:41][ERROR][SQLProcessor] Comando executado: exec postgres_aula psql -U postgres -d n8n_fila -f /tmp/002_add_ai_enhancement_tables_20250705_210920_457_15996_a9185803db37_NaxGZJCR.sql --set ON_ERROR_STOP=1
[2025-07-05 21:09:41][ERROR][SQLSchema] Erros encontrados ao executar 002_add_ai_enhancement_tables.sql no banco 'n8n_fila'
   ❌ Execução falhou com erros:
   ❗ Erros Adicionais:
      CREATE SCHEMA
[2025-07-05 21:09:42][ERROR][SQLSchema] Other Error: CREATE SCHEMA
      psql:/tmp/002_add_ai_enhancement_tables_20250705_210920_457_15996_a9185803db37_NaxGZJCR.sql:12: NOTICE:  schema "agent" already exists, skipping
[2025-07-05 21:09:42][ERROR][SQLSchema] Other Error: psql:/tmp/002_add_ai_enhancement_tables_20250705_210920_457_15996_a9185803db37_NaxGZJCR.sql:12: NOTICE:  schema "agent" already exists, skipping
      ... e mais 1 erros

   💡 Sugestões de Solução:
