{"name": "[CONFIG] Human-AI Cycle Manager", "nodes": [{"parameters": {"httpMethod": "POST", "path": "human-ai-cycle-config", "options": {}}, "id": "f47ac10b-58cc-4372-a567-0e02b2c3d479", "name": "🔧 Webhook - Config Request", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300], "webhookId": "human-ai-cycle-config"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "c1", "leftValue": "={{ $json.action }}", "rightValue": "get_config", "operator": {"type": "string", "operation": "equals"}}, {"id": "c2", "leftValue": "={{ $json.action }}", "rightValue": "update_config", "operator": {"type": "string", "operation": "equals"}}, {"id": "c3", "leftValue": "={{ $json.action }}", "rightValue": "get_metrics", "operator": {"type": "string", "operation": "equals"}}], "combinator": "or"}, "options": {}}, "id": "8f14e45f-ceea-467a-9b87-7df96c6f135e", "name": "🔀 Route Action", "type": "n8n-nodes-base.switch", "typeVersion": 3, "position": [460, 300]}, {"parameters": {"operation": "select", "schema": {"value": "agent"}, "table": {"value": "human_ai_cycle_config"}, "where": {"values": [{"column": "is_active", "condition": "equal", "value": true}]}, "sort": {"values": [{"column": "config_key", "direction": "ASC"}]}, "options": {}}, "id": "6ba7b810-9dad-11d1-80b4-00c04fd430c8", "name": "📊 Get Current Config", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [680, 180], "credentials": {"postgres": {"id": "postgres-main", "name": "PostgreSQL - Main DB"}}}, {"parameters": {"operation": "upsert", "schema": {"value": "agent"}, "table": {"value": "human_ai_cycle_config"}, "columnToMatchOn": "config_key", "valuesToSend": {"values": [{"column": "config_key", "value": "={{ $json.config_key }}"}, {"column": "config_value", "value": "={{ $json.config_value }}"}, {"column": "description", "value": "={{ $json.description || '' }}"}, {"column": "updated_by", "value": "={{ $json.updated_by || 'system' }}"}, {"column": "updated_at", "value": "={{ new Date().toISOString() }}"}]}, "options": {}}, "id": "6ba7b811-9dad-11d1-80b4-00c04fd430c8", "name": "💾 Update Config", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [680, 300], "credentials": {"postgres": {"id": "postgres-main", "name": "PostgreSQL - Main DB"}}}, {"parameters": {"operation": "select", "schema": {"value": "agent"}, "table": {"value": "cycle_metrics"}, "where": {"values": [{"column": "metric_date", "condition": "greaterOrEqual", "value": "={{ $json.start_date || new Date(Date.now() - 7*24*60*60*1000).toISOString().split('T')[0] }}"}, {"column": "metric_date", "condition": "lessOrEqual", "value": "={{ $json.end_date || new Date().toISOString().split('T')[0] }}"}]}, "sort": {"values": [{"column": "metric_date", "direction": "DESC"}]}, "options": {}}, "id": "6ba7b812-9dad-11d1-80b4-00c04fd430c8", "name": "📈 Get Metrics", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [680, 420], "credentials": {"postgres": {"id": "postgres-main", "name": "PostgreSQL - Main DB"}}}, {"parameters": {"jsCode": "// 🔧 Formatar resposta de configuração\nconst configs = $input.all();\nconst formattedConfigs = {};\n\nconfigs.forEach(config => {\n  formattedConfigs[config.json.config_key] = {\n    value: config.json.config_value,\n    description: config.json.description,\n    updated_at: config.json.updated_at\n  };\n});\n\nreturn {\n  success: true,\n  action: 'get_config',\n  data: formattedConfigs,\n  timestamp: new Date().toISOString()\n};"}, "id": "8f14e45f-ceea-467a-9b87-7df96c6f135f", "name": "🔧 Format Config Response", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [900, 180]}, {"parameters": {"jsCode": "// 💾 Validar e processar atualização de configuração\nconst input = $input.first().json;\n\n// Validações básicas\nif (!input.config_key) {\n  throw new Error('config_key é obrigatório');\n}\n\nif (!input.config_value) {\n  throw new Error('config_value é obrigatório');\n}\n\n// Validar se config_value é um JSON válido\ntry {\n  if (typeof input.config_value === 'string') {\n    JSON.parse(input.config_value);\n  }\n} catch (e) {\n  throw new Error('config_value deve ser um JSON válido');\n}\n\n// Validações específicas por tipo de configuração\nconst validConfigs = [\n  'review_thresholds',\n  'review_sla',\n  'content_types_config',\n  'performance_weights',\n  'ai_learning_config'\n];\n\nif (!validConfigs.includes(input.config_key)) {\n  throw new Error(`config_key deve ser um dos seguintes: ${validConfigs.join(', ')}`);\n}\n\n// Validações específicas\nif (input.config_key === 'review_thresholds') {\n  const thresholds = typeof input.config_value === 'string' ? \n    JSON.parse(input.config_value) : input.config_value;\n  \n  const required = ['low_confidence', 'medium_confidence', 'high_confidence', 'auto_approve_threshold'];\n  for (const key of required) {\n    if (!(key in thresholds) || thresholds[key] < 0 || thresholds[key] > 1) {\n      throw new Error(`${key} deve estar entre 0 e 1`);\n    }\n  }\n}\n\nreturn {\n  config_key: input.config_key,\n  config_value: typeof input.config_value === 'string' ? \n    input.config_value : JSON.stringify(input.config_value),\n  description: input.description || '',\n  updated_by: input.updated_by || 'api_user'\n};"}, "id": "8f14e45f-ceea-467a-9b87-7df96c6f1360", "name": "✅ Validate Update", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [500, 300]}, {"parameters": {"jsCode": "// 📈 Processar e formatar métricas\nconst metrics = $input.all();\n\n// Agrupar métricas por influenciador\nconst groupedMetrics = {};\nlet totalMetrics = {\n  total_content_generated: 0,\n  content_approved: 0,\n  content_rejected: 0,\n  total_engagement: 0,\n  total_cost: 0\n};\n\nmetrics.forEach(metric => {\n  const data = metric.json;\n  \n  if (!groupedMetrics[data.influencer_id]) {\n    groupedMetrics[data.influencer_id] = {\n      influencer_id: data.influencer_id,\n      metrics: [],\n      totals: {\n        total_content_generated: 0,\n        content_approved: 0,\n        content_rejected: 0,\n        total_engagement: 0,\n        total_cost: 0\n      }\n    };\n  }\n  \n  groupedMetrics[data.influencer_id].metrics.push(data);\n  \n  // Somar totais por influenciador\n  groupedMetrics[data.influencer_id].totals.total_content_generated += data.total_content_generated || 0;\n  groupedMetrics[data.influencer_id].totals.content_approved += data.content_approved || 0;\n  groupedMetrics[data.influencer_id].totals.content_rejected += data.content_rejected || 0;\n  groupedMetrics[data.influencer_id].totals.total_engagement += data.total_engagement || 0;\n  groupedMetrics[data.influencer_id].totals.total_cost += (data.cost_per_content * data.total_content_generated) || 0;\n  \n  // Somar totais gerais\n  totalMetrics.total_content_generated += data.total_content_generated || 0;\n  totalMetrics.content_approved += data.content_approved || 0;\n  totalMetrics.content_rejected += data.content_rejected || 0;\n  totalMetrics.total_engagement += data.total_engagement || 0;\n  totalMetrics.total_cost += (data.cost_per_content * data.total_content_generated) || 0;\n});\n\n// Calcular taxas\ntotalMetrics.approval_rate = totalMetrics.total_content_generated > 0 ? \n  (totalMetrics.content_approved / totalMetrics.total_content_generated * 100).toFixed(2) : 0;\n\nObject.values(groupedMetrics).forEach(influencer => {\n  const totals = influencer.totals;\n  totals.approval_rate = totals.total_content_generated > 0 ? \n    (totals.content_approved / totals.total_content_generated * 100).toFixed(2) : 0;\n});\n\nreturn {\n  success: true,\n  action: 'get_metrics',\n  data: {\n    summary: totalMetrics,\n    by_influencer: Object.values(groupedMetrics),\n    period: {\n      start_date: $input.first().json.start_date || new Date(Date.now() - 7*24*60*60*1000).toISOString().split('T')[0],\n      end_date: $input.first().json.end_date || new Date().toISOString().split('T')[0]\n    }\n  },\n  timestamp: new Date().toISOString()\n};"}, "id": "8f14e45f-ceea-467a-9b87-7df96c6f1361", "name": "📊 Format Metrics Response", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [900, 420]}, {"parameters": {"jsCode": "// 💾 Formatar resposta de atualização\nconst result = $input.first().json;\n\nreturn {\n  success: true,\n  action: 'update_config',\n  message: `Configuração '${result.config_key}' atualizada com sucesso`,\n  data: {\n    config_key: result.config_key,\n    updated_at: new Date().toISOString()\n  },\n  timestamp: new Date().toISOString()\n};"}, "id": "8f14e45f-ceea-467a-9b87-7df96c6f1362", "name": "✅ Format Update Response", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [900, 300]}, {"parameters": {"respondWith": "json", "responseBody": "={{ $json }}", "options": {}}, "id": "f47ac10b-58cc-4372-a567-0e02b2c3d480", "name": "📤 Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1120, 300]}, {"parameters": {"jsCode": "// 🚨 Tratamento de erro\nconst error = $input.first().json;\n\nreturn {\n  success: false,\n  error: {\n    message: error.message || 'Erro interno do servidor',\n    code: error.code || 'INTERNAL_ERROR',\n    details: error.details || null\n  },\n  timestamp: new Date().toISOString()\n};"}, "id": "8f14e45f-ceea-467a-9b87-7df96c6f1363", "name": "🚨 <PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [900, 540]}, {"parameters": {"operation": "insert", "schema": {"value": "agent"}, "table": {"value": "system_logs"}, "columns": {"mappingMode": "defineBelow", "values": [{"column": "event_type", "value": "human_ai_config_error"}, {"column": "event_data", "value": "={{ JSON.stringify({error: $json.error, action: $('🔧 Webhook - Config Request').first().json.action}) }}"}, {"column": "severity", "value": "error"}]}, "options": {}}, "id": "6ba7b813-9dad-11d1-80b4-00c04fd430c8", "name": "📝 Log Error", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [1120, 540], "credentials": {"postgres": {"id": "postgres-main", "name": "PostgreSQL - Main DB"}}}], "connections": {"🔧 Webhook - Config Request": {"main": [[{"node": "🔀 Route Action", "type": "main", "index": 0}]]}, "🔀 Route Action": {"main": [[{"node": "📊 Get Current Config", "type": "main", "index": 0}], [{"node": "✅ Validate Update", "type": "main", "index": 0}], [{"node": "📈 Get Metrics", "type": "main", "index": 0}]]}, "📊 Get Current Config": {"main": [[{"node": "🔧 Format Config Response", "type": "main", "index": 0}]]}, "💾 Update Config": {"main": [[{"node": "✅ Format Update Response", "type": "main", "index": 0}]]}, "📈 Get Metrics": {"main": [[{"node": "📊 Format Metrics Response", "type": "main", "index": 0}]]}, "🔧 Format Config Response": {"main": [[{"node": "📤 Response", "type": "main", "index": 0}]]}, "✅ Validate Update": {"main": [[{"node": "💾 Update Config", "type": "main", "index": 0}]]}, "📊 Format Metrics Response": {"main": [[{"node": "📤 Response", "type": "main", "index": 0}]]}, "✅ Format Update Response": {"main": [[{"node": "📤 Response", "type": "main", "index": 0}]]}, "🚨 Error Handler": {"main": [[{"node": "📝 Log Error", "type": "main", "index": 0}]]}, "📝 Log Error": {"main": [[{"node": "📤 Response", "type": "main", "index": 0}]]}}, "pinData": {}, "settings": {"executionOrder": "v1"}, "staticData": null, "tags": [{"createdAt": "2024-12-19T10:00:00.000Z", "updatedAt": "2024-12-19T10:00:00.000Z", "id": "config", "name": "config"}, {"createdAt": "2024-12-19T10:00:00.000Z", "updatedAt": "2024-12-19T10:00:00.000Z", "id": "human-ai", "name": "human-ai"}], "triggerCount": 1, "updatedAt": "2024-12-19T10:00:00.000Z", "versionId": "1.0.0"}