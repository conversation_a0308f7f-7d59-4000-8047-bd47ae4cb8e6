{"name": "[ESCALATION] Smart Notifications v2.0", "nodes": [{"parameters": {"httpMethod": "POST", "path": "smart-notifications", "options": {"rawBody": true}}, "id": "webhook-smart-notifications", "name": "📢 Smart Notifications Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300], "webhookId": "smart-notifications-v2"}, {"parameters": {"jsCode": "// 📥 Process notification request\nconst inputData = $input.all()[0].json;\n\n// Extract notification data\nconst notificationData = {\n  escalation_id: inputData.escalation_id || null,\n  notification_type: inputData.notification_type || 'general',\n  priority: inputData.priority || 'medium',\n  channels: inputData.channels || ['email'],\n  recipients: inputData.recipients || [],\n  message_data: inputData.message_data || {},\n  escalation_data: inputData.escalation_data || {},\n  assignment_data: inputData.assignment_data || {},\n  template_override: inputData.template_override || null,\n  delivery_options: inputData.delivery_options || {},\n  created_at: new Date().toISOString(),\n  notification_id: `NOTIF_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`\n};\n\n// Validate required fields\nif (!notificationData.escalation_id && !notificationData.message_data.subject) {\n  throw new Error('Either escalation_id or message subject is required');\n}\n\n// Set notification urgency based on priority\nconst urgencyLevels = {\n  'critical': { urgency: 'immediate', max_delay: 0 },\n  'high': { urgency: 'urgent', max_delay: 300 }, // 5 minutes\n  'medium': { urgency: 'normal', max_delay: 900 }, // 15 minutes\n  'low': { urgency: 'low', max_delay: 3600 } // 1 hour\n};\n\nnotificationData.urgency_config = urgencyLevels[notificationData.priority] || urgencyLevels.medium;\n\n// Add processing timestamp\nnotificationData.processing_started_at = new Date().toISOString();\n\nconsole.log('📢 Processing notification:', notificationData.notification_id);\nconsole.log('🎯 Type:', notificationData.notification_type);\nconsole.log('📊 Priority:', notificationData.priority);\nconsole.log('📡 Channels:', notificationData.channels);\nconsole.log('⚡ Urgency:', notificationData.urgency_config.urgency);\n\nreturn { notificationData };"}, "id": "process-notification-data", "name": "📥 Process Notification Data", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [460, 300]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "has-escalation-id", "leftValue": "={{ $json.notificationData.escalation_id }}", "rightValue": "", "operator": {"type": "string", "operation": "notEmpty"}}], "combinator": "and"}, "options": {}}, "id": "needs-escalation-data", "name": "🔍 Needs Escalation Data?", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [680, 300]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT \n  ie.*,\n  a.name as agent_name,\n  a.email as agent_email,\n  a.department as agent_department,\n  a.slack_user_id as agent_slack_id\nFROM agent.intelligent_escalations ie\nLEFT JOIN agent.agents a ON ie.assigned_agent_id = a.id\nWHERE ie.escalation_id = $1", "additionalFields": {"mode": "independently"}, "options": {}}, "id": "fetch-escalation-details", "name": "📋 Fetch Escalation Details", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [900, 200], "credentials": {"postgres": {"id": "postgres-escalation-db", "name": "PostgreSQL - Escalation DB"}}}, {"parameters": {"jsCode": "// 🎨 Generate notification templates\nconst notificationData = $input.first().json.notificationData;\nconst escalationDetails = $('fetch-escalation-details').first()?.json || null;\n\n// Merge escalation data if available\nif (escalationDetails) {\n  notificationData.escalation_data = {\n    ...notificationData.escalation_data,\n    ...escalationDetails\n  };\n}\n\n// Define notification templates\nconst templates = {\n  critical_escalation: {\n    slack: {\n      title: '🚨 CRITICAL ESCALATION ALERT',\n      color: '#FF0000',\n      fields: [\n        { title: 'Escalation ID', value: notificationData.escalation_id, short: true },\n        { title: 'Priority', value: notificationData.priority.toUpperCase(), short: true },\n        { title: 'Category', value: notificationData.escalation_data.category || 'Unknown', short: true },\n        { title: 'Source', value: notificationData.escalation_data.source || 'Unknown', short: true },\n        { title: 'Subject', value: notificationData.escalation_data.subject || 'No subject', short: false },\n        { title: 'Assigned Agent', value: notificationData.escalation_data.agent_name || 'Unassigned', short: true },\n        { title: 'Department', value: notificationData.escalation_data.agent_department || 'N/A', short: true }\n      ],\n      actions: [\n        { name: 'view', text: 'View Details', type: 'button', value: notificationData.escalation_id },\n        { name: 'escalate', text: 'Escalate Further', type: 'button', value: notificationData.escalation_id, style: 'danger' }\n      ]\n    },\n    email: {\n      subject: `🚨 CRITICAL: Escalation ${notificationData.escalation_id} requires immediate attention`,\n      template: 'critical_escalation_email',\n      priority: 'high'\n    },\n    sms: {\n      message: `CRITICAL ESCALATION: ${notificationData.escalation_id} - ${notificationData.escalation_data.subject || 'Immediate attention required'}`\n    }\n  },\n  high_priority_escalation: {\n    slack: {\n      title: '⚠️ High Priority Escalation',\n      color: '#FF8C00',\n      fields: [\n        { title: 'Escalation ID', value: notificationData.escalation_id, short: true },\n        { title: 'Priority', value: notificationData.priority.toUpperCase(), short: true },\n        { title: 'Category', value: notificationData.escalation_data.category || 'Unknown', short: true },\n        { title: 'Assigned Agent', value: notificationData.escalation_data.agent_name || 'Unassigned', short: true },\n        { title: 'Subject', value: notificationData.escalation_data.subject || 'No subject', short: false }\n      ],\n      actions: [\n        { name: 'view', text: 'View Details', type: 'button', value: notificationData.escalation_id },\n        { name: 'assign', text: 'Assign to Me', type: 'button', value: notificationData.escalation_id }\n      ]\n    },\n    email: {\n      subject: `⚠️ High Priority: Escalation ${notificationData.escalation_id}`,\n      template: 'high_priority_escalation_email',\n      priority: 'high'\n    }\n  },\n  escalation_assigned: {\n    slack: {\n      title: '✅ Escalation Assigned',\n      color: '#00FF00',\n      fields: [\n        { title: 'Escalation ID', value: notificationData.escalation_id, short: true },\n        { title: 'Assigned to', value: notificationData.escalation_data.agent_name || 'Unknown', short: true },\n        { title: 'Department', value: notificationData.escalation_data.agent_department || 'N/A', short: true },\n        { title: 'Priority', value: notificationData.priority.toUpperCase(), short: true }\n      ]\n    },\n    email: {\n      subject: `✅ Escalation ${notificationData.escalation_id} has been assigned`,\n      template: 'escalation_assigned_email',\n      priority: 'normal'\n    }\n  },\n  escalation_resolved: {\n    slack: {\n      title: '🎉 Escalation Resolved',\n      color: '#00AA00',\n      fields: [\n        { title: 'Escalation ID', value: notificationData.escalation_id, short: true },\n        { title: 'Resolved by', value: notificationData.escalation_data.agent_name || 'Unknown', short: true },\n        { title: 'Resolution Time', value: notificationData.escalation_data.resolution_time || 'N/A', short: true },\n        { title: 'Category', value: notificationData.escalation_data.category || 'Unknown', short: true }\n      ]\n    },\n    email: {\n      subject: `🎉 Escalation ${notificationData.escalation_id} has been resolved`,\n      template: 'escalation_resolved_email',\n      priority: 'normal'\n    }\n  },\n  queue_alert: {\n    slack: {\n      title: '⏳ Queue Alert',\n      color: '#FFA500',\n      fields: [\n        { title: 'Queue Status', value: notificationData.message_data.queue_status || 'Unknown', short: true },\n        { title: 'Queue Size', value: notificationData.message_data.queue_size || 'N/A', short: true },\n        { title: 'Average Wait Time', value: notificationData.message_data.avg_wait_time || 'N/A', short: true },\n        { title: 'SLA Risk', value: notificationData.message_data.sla_risk || 'Low', short: true }\n      ],\n      actions: [\n        { name: 'rebalance', text: 'Rebalance Queue', type: 'button', value: 'queue_rebalance' }\n      ]\n    },\n    email: {\n      subject: `⏳ Queue Alert: ${notificationData.message_data.queue_status || 'Attention Required'}`,\n      template: 'queue_alert_email',\n      priority: 'normal'\n    }\n  },\n  system_alert: {\n    slack: {\n      title: '🔧 System Alert',\n      color: '#800080',\n      fields: [\n        { title: 'Alert Type', value: notificationData.message_data.alert_type || 'Unknown', short: true },\n        { title: 'Severity', value: notificationData.priority.toUpperCase(), short: true },\n        { title: 'Component', value: notificationData.message_data.component || 'System', short: true },\n        { title: 'Description', value: notificationData.message_data.description || 'No description', short: false }\n      ]\n    },\n    email: {\n      subject: `🔧 System Alert: ${notificationData.message_data.alert_type || 'System Issue'}`,\n      template: 'system_alert_email',\n      priority: notificationData.priority === 'critical' ? 'high' : 'normal'\n    }\n  }\n};\n\n// Get template for notification type\nconst selectedTemplate = templates[notificationData.notification_type] || templates.high_priority_escalation;\n\n// Apply template override if provided\nif (notificationData.template_override) {\n  Object.assign(selectedTemplate, notificationData.template_override);\n}\n\n// Generate final notification content\nconst notificationContent = {\n  notification_id: notificationData.notification_id,\n  escalation_id: notificationData.escalation_id,\n  type: notificationData.notification_type,\n  priority: notificationData.priority,\n  urgency: notificationData.urgency_config.urgency,\n  channels: notificationData.channels,\n  templates: selectedTemplate,\n  delivery_timestamp: new Date().toISOString(),\n  metadata: {\n    processing_started_at: notificationData.processing_started_at,\n    template_generated_at: new Date().toISOString(),\n    escalation_data_included: !!escalationDetails\n  }\n};\n\nconsole.log('🎨 Generated notification templates for:', notificationContent.notification_id);\nconsole.log('📋 Template type:', notificationData.notification_type);\nconsole.log('📡 Channels to notify:', notificationContent.channels);\n\nreturn { notificationContent };"}, "id": "generate-notification-templates", "name": "🎨 Generate Notification Templates", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1120, 300]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "has-slack-channel", "leftValue": "={{ $json.notificationContent.channels }}", "rightValue": "slack", "operator": {"type": "array", "operation": "contains"}}], "combinator": "and"}, "options": {}}, "id": "should-send-slack", "name": "💬 Should Send Slack?", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [1340, 200]}, {"parameters": {"jsCode": "// 💬 Prepare Slack notification\nconst notificationContent = $input.first().json.notificationContent;\nconst slackTemplate = notificationContent.templates.slack;\n\n// Determine target channels based on priority and type\nconst channelMapping = {\n  critical_escalation: ['#critical-escalations', '#tech-escalations', '#management'],\n  high_priority_escalation: ['#high-priority', '#tech-escalations'],\n  escalation_assigned: ['#escalation-updates'],\n  escalation_resolved: ['#escalation-updates'],\n  queue_alert: ['#queue-monitoring', '#tech-escalations'],\n  system_alert: ['#system-alerts', '#tech-escalations']\n};\n\nconst targetChannels = channelMapping[notificationContent.type] || ['#general-escalations'];\n\n// Build Slack message\nconst slackMessage = {\n  notification_id: notificationContent.notification_id,\n  channels: targetChannels,\n  message: {\n    text: slackTemplate.title,\n    attachments: [\n      {\n        color: slackTemplate.color,\n        title: slackTemplate.title,\n        fields: slackTemplate.fields,\n        actions: slackTemplate.actions || [],\n        footer: 'Escalation System v2.0',\n        footer_icon: 'https://platform.slack-edge.com/img/default_application_icon.png',\n        ts: Math.floor(Date.now() / 1000)\n      }\n    ]\n  },\n  delivery_options: {\n    urgency: notificationContent.urgency,\n    retry_count: notificationContent.priority === 'critical' ? 3 : 1,\n    timeout: 10000\n  }\n};\n\n// Add mention for critical escalations\nif (notificationContent.priority === 'critical') {\n  slackMessage.message.text = '<!channel> ' + slackMessage.message.text;\n}\n\nconsole.log('💬 Preparing Slack notification');\nconsole.log('📡 Target channels:', targetChannels);\nconsole.log('⚡ Urgency:', notificationContent.urgency);\n\nreturn { slackMessage };"}, "id": "prepare-slack-notification", "name": "💬 Prepare Slack Notification", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1560, 120]}, {"parameters": {"method": "POST", "url": "https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "text", "value": "={{ $json.slackMessage.message.text }}"}, {"name": "attachments", "value": "={{ JSON.stringify($json.slackMessage.message.attachments) }}"}, {"name": "channel", "value": "={{ $json.slackMessage.channels[0] }}"}]}, "options": {"timeout": 10000}}, "id": "send-slack-notification", "name": "📤 Send Slack Notification", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1780, 120], "credentials": {"httpHeaderAuth": {"id": "slack-webhook-auth", "name": "<PERSON><PERSON><PERSON> Webhook <PERSON>"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "has-email-channel", "leftValue": "={{ $('generate-notification-templates').first().json.notificationContent.channels }}", "rightValue": "email", "operator": {"type": "array", "operation": "contains"}}], "combinator": "and"}, "options": {}}, "id": "should-send-email", "name": "📧 Should Send Email?", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [1340, 380]}, {"parameters": {"jsCode": "// 📧 Prepare email notification\nconst notificationContent = $input.first().json.notificationContent;\nconst emailTemplate = notificationContent.templates.email;\nconst escalationData = notificationContent.escalation_data || {};\n\n// Determine recipients based on escalation data and notification type\nlet recipients = [];\n\n// Add assigned agent if available\nif (escalationData.agent_email) {\n  recipients.push({\n    email: escalationData.agent_email,\n    name: escalationData.agent_name || 'Agent',\n    type: 'assigned_agent'\n  });\n}\n\n// Add department managers for critical escalations\nif (notificationContent.priority === 'critical') {\n  // This would typically query the database for department managers\n  recipients.push({\n    email: '<EMAIL>',\n    name: 'Department Manager',\n    type: 'manager'\n  });\n}\n\n// Add system administrators for system alerts\nif (notificationContent.type === 'system_alert') {\n  recipients.push({\n    email: '<EMAIL>',\n    name: 'System Administrator',\n    type: 'sysadmin'\n  });\n}\n\n// Fallback to general escalation email\nif (recipients.length === 0) {\n  recipients.push({\n    email: '<EMAIL>',\n    name: 'Escalation Team',\n    type: 'team'\n  });\n}\n\n// Build email content\nconst emailContent = {\n  notification_id: notificationContent.notification_id,\n  recipients: recipients,\n  subject: emailTemplate.subject,\n  template: emailTemplate.template,\n  priority: emailTemplate.priority,\n  data: {\n    escalation_id: notificationContent.escalation_id,\n    escalation_data: escalationData,\n    notification_type: notificationContent.type,\n    priority: notificationContent.priority,\n    timestamp: new Date().toISOString(),\n    dashboard_url: `https://dashboard.company.com/escalations/${notificationContent.escalation_id}`,\n    support_url: 'https://support.company.com'\n  },\n  delivery_options: {\n    urgency: notificationContent.urgency,\n    retry_count: notificationContent.priority === 'critical' ? 2 : 1\n  }\n};\n\nconsole.log('📧 Preparing email notification');\nconsole.log('👥 Recipients:', recipients.length);\nconsole.log('📋 Template:', emailTemplate.template);\nconsole.log('📊 Priority:', emailTemplate.priority);\n\nreturn { emailContent };"}, "id": "prepare-email-notification", "name": "📧 Prepare Email Notification", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1560, 380]}, {"parameters": {"fromEmail": "<EMAIL>", "toEmail": "={{ $json.emailContent.recipients.map(r => r.email).join(',') }}", "subject": "={{ $json.emailContent.subject }}", "emailType": "html", "message": "<html>\n<head>\n  <style>\n    body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }\n    .container { max-width: 600px; margin: 0 auto; background-color: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }\n    .header { background-color: #007bff; color: white; padding: 15px; border-radius: 5px; margin-bottom: 20px; }\n    .priority-critical { background-color: #dc3545; }\n    .priority-high { background-color: #fd7e14; }\n    .priority-medium { background-color: #ffc107; color: #000; }\n    .priority-low { background-color: #28a745; }\n    .content { line-height: 1.6; }\n    .escalation-details { background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0; }\n    .button { display: inline-block; padding: 10px 20px; background-color: #007bff; color: white; text-decoration: none; border-radius: 5px; margin: 10px 5px; }\n    .footer { margin-top: 30px; padding-top: 20px; border-top: 1px solid #dee2e6; font-size: 12px; color: #6c757d; }\n  </style>\n</head>\n<body>\n  <div class=\"container\">\n    <div class=\"header priority-{{ $json.emailContent.data.priority }}\">\n      <h2>{{ $json.emailContent.subject }}</h2>\n    </div>\n    \n    <div class=\"content\">\n      <p>Dear Team,</p>\n      \n      <p>This is an automated notification regarding escalation <strong>{{ $json.emailContent.data.escalation_id }}</strong>.</p>\n      \n      <div class=\"escalation-details\">\n        <h3>Escalation Details</h3>\n        <ul>\n          <li><strong>Escalation ID:</strong> {{ $json.emailContent.data.escalation_id }}</li>\n          <li><strong>Priority:</strong> {{ $json.emailContent.data.priority.toUpperCase() }}</li>\n          <li><strong>Type:</strong> {{ $json.emailContent.data.notification_type }}</li>\n          <li><strong>Category:</strong> {{ $json.emailContent.data.escalation_data.category || 'N/A' }}</li>\n          <li><strong>Source:</strong> {{ $json.emailContent.data.escalation_data.source || 'N/A' }}</li>\n          <li><strong>Subject:</strong> {{ $json.emailContent.data.escalation_data.subject || 'N/A' }}</li>\n          <li><strong>Assigned Agent:</strong> {{ $json.emailContent.data.escalation_data.agent_name || 'Unassigned' }}</li>\n          <li><strong>Timestamp:</strong> {{ $json.emailContent.data.timestamp }}</li>\n        </ul>\n      </div>\n      \n      <p>\n        <a href=\"{{ $json.emailContent.data.dashboard_url }}\" class=\"button\">View in Dashboard</a>\n        <a href=\"{{ $json.emailContent.data.support_url }}\" class=\"button\">Support Center</a>\n      </p>\n      \n      <p>Please take appropriate action based on the priority level and escalation type.</p>\n    </div>\n    \n    <div class=\"footer\">\n      <p>This is an automated message from the Intelligent Escalation System v2.0</p>\n      <p>Notification ID: {{ $json.emailContent.notification_id }}</p>\n    </div>\n  </div>\n</body>\n</html>", "options": {"allowUnauthorizedCerts": false}}, "id": "send-email-notification", "name": "📤 Send Email Notification", "type": "n8n-nodes-base.emailSend", "typeVersion": 2, "position": [1780, 380], "credentials": {"smtp": {"id": "smtp-escalation-notifications", "name": "SMTP - Escalation Notifications"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "has-sms-channel", "leftValue": "={{ $('generate-notification-templates').first().json.notificationContent.channels }}", "rightValue": "sms", "operator": {"type": "array", "operation": "contains"}}, {"id": "is-critical-priority", "leftValue": "={{ $('generate-notification-templates').first().json.notificationContent.priority }}", "rightValue": "critical", "operator": {"type": "string", "operation": "equals"}}], "combinator": "and"}, "options": {}}, "id": "should-send-sms", "name": "📱 Should Send SMS?", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [1340, 560]}, {"parameters": {"jsCode": "// 📱 Prepare SMS notification (for critical escalations only)\nconst notificationContent = $input.first().json.notificationContent;\nconst smsTemplate = notificationContent.templates.sms;\n\n// Define emergency contacts for SMS notifications\nconst emergencyContacts = [\n  { phone: '+**********', name: 'Emergency Manager', role: 'manager' },\n  { phone: '+1234567891', name: 'On-Call Engineer', role: 'engineer' },\n  { phone: '+1234567892', name: 'System Administrator', role: 'sysadmin' }\n];\n\n// Build SMS content\nconst smsContent = {\n  notification_id: notificationContent.notification_id,\n  recipients: emergencyContacts,\n  message: smsTemplate.message,\n  delivery_options: {\n    urgency: 'immediate',\n    retry_count: 2,\n    timeout: 30000\n  }\n};\n\nconsole.log('📱 Preparing SMS notification for critical escalation');\nconsole.log('👥 Emergency contacts:', emergencyContacts.length);\nconsole.log('📝 Message:', smsTemplate.message);\n\nreturn { smsContent };"}, "id": "prepare-sms-notification", "name": "📱 Prepare SMS Notification", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1560, 560]}, {"parameters": {"method": "POST", "url": "https://api.twilio.com/2010-04-01/Accounts/YOUR_ACCOUNT_SID/Messages.json", "authentication": "genericCredentialType", "genericAuthType": "httpBasicAuth", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/x-www-form-urlencoded"}]}, "sendBody": true, "specifyBody": "form", "bodyParameters": {"parameters": [{"name": "From", "value": "+**********"}, {"name": "To", "value": "={{ $json.smsContent.recipients[0].phone }}"}, {"name": "Body", "value": "={{ $json.smsContent.message }}"}]}, "options": {"timeout": 30000}}, "id": "send-sms-notification", "name": "📤 Send SMS Notification", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1780, 560], "credentials": {"httpBasicAuth": {"id": "twilio-sms-auth", "name": "<PERSON><PERSON><PERSON>"}}}, {"parameters": {"operation": "insert", "schema": "agent", "table": "notification_logs", "columns": "notification_id, escalation_id, notification_type, priority, channels, recipients_data, delivery_status, created_at, metadata", "additionalFields": {"mode": "independently"}, "options": {}}, "id": "log-notification-delivery", "name": "📝 Log Notification Delivery", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [2000, 300], "credentials": {"postgres": {"id": "postgres-escalation-db", "name": "PostgreSQL - Escalation DB"}}}, {"parameters": {"jsCode": "// 📊 Generate notification response\nconst notificationContent = $('generate-notification-templates').first().json.notificationContent;\nconst slackResult = $('send-slack-notification').first()?.json || null;\nconst emailResult = $('send-email-notification').first()?.json || null;\nconst smsResult = $('send-sms-notification').first()?.json || null;\n\n// Collect delivery results\nconst deliveryResults = {\n  slack: slackResult ? { status: 'sent', timestamp: new Date().toISOString() } : null,\n  email: emailResult ? { status: 'sent', timestamp: new Date().toISOString() } : null,\n  sms: smsResult ? { status: 'sent', timestamp: new Date().toISOString() } : null\n};\n\n// Calculate delivery statistics\nconst totalChannels = notificationContent.channels.length;\nconst successfulDeliveries = Object.values(deliveryResults).filter(result => result !== null).length;\nconst deliveryRate = totalChannels > 0 ? (successfulDeliveries / totalChannels) * 100 : 0;\n\n// Generate final response\nconst response = {\n  success: true,\n  notification_id: notificationContent.notification_id,\n  escalation_id: notificationContent.escalation_id,\n  notification_type: notificationContent.type,\n  priority: notificationContent.priority,\n  urgency: notificationContent.urgency,\n  delivery: {\n    channels_requested: notificationContent.channels,\n    channels_delivered: Object.keys(deliveryResults).filter(channel => deliveryResults[channel] !== null),\n    delivery_rate: deliveryRate,\n    results: deliveryResults\n  },\n  timestamps: {\n    processing_started_at: notificationContent.metadata.processing_started_at,\n    template_generated_at: notificationContent.metadata.template_generated_at,\n    delivery_completed_at: new Date().toISOString()\n  },\n  performance: {\n    total_processing_time_ms: new Date() - new Date(notificationContent.metadata.processing_started_at),\n    successful_deliveries: successfulDeliveries,\n    failed_deliveries: totalChannels - successfulDeliveries\n  }\n};\n\nconsole.log('📊 Notification delivery completed');\nconsole.log('✅ Successful deliveries:', successfulDeliveries, '/', totalChannels);\nconsole.log('📈 Delivery rate:', deliveryRate.toFixed(1), '%');\nconsole.log('⏱️ Total processing time:', response.performance.total_processing_time_ms, 'ms');\n\nreturn response;"}, "id": "generate-notification-response", "name": "📊 Generate Notification Response", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [2220, 300]}], "pinData": {}, "connections": {"webhook-smart-notifications": {"main": [[{"node": "process-notification-data", "type": "main", "index": 0}]]}, "process-notification-data": {"main": [[{"node": "needs-escalation-data", "type": "main", "index": 0}]]}, "needs-escalation-data": {"main": [[{"node": "fetch-escalation-details", "type": "main", "index": 0}], [{"node": "generate-notification-templates", "type": "main", "index": 0}]]}, "fetch-escalation-details": {"main": [[{"node": "generate-notification-templates", "type": "main", "index": 0}]]}, "generate-notification-templates": {"main": [[{"node": "should-send-slack", "type": "main", "index": 0}, {"node": "should-send-email", "type": "main", "index": 0}, {"node": "should-send-sms", "type": "main", "index": 0}]]}, "should-send-slack": {"main": [[{"node": "prepare-slack-notification", "type": "main", "index": 0}], []]}, "prepare-slack-notification": {"main": [[{"node": "send-slack-notification", "type": "main", "index": 0}]]}, "send-slack-notification": {"main": [[{"node": "log-notification-delivery", "type": "main", "index": 0}]]}, "should-send-email": {"main": [[{"node": "prepare-email-notification", "type": "main", "index": 0}], []]}, "prepare-email-notification": {"main": [[{"node": "send-email-notification", "type": "main", "index": 0}]]}, "send-email-notification": {"main": [[{"node": "log-notification-delivery", "type": "main", "index": 0}]]}, "should-send-sms": {"main": [[{"node": "prepare-sms-notification", "type": "main", "index": 0}], []]}, "prepare-sms-notification": {"main": [[{"node": "send-sms-notification", "type": "main", "index": 0}]]}, "send-sms-notification": {"main": [[{"node": "log-notification-delivery", "type": "main", "index": 0}]]}, "log-notification-delivery": {"main": [[{"node": "generate-notification-response", "type": "main", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1", "saveManualExecutions": true, "callerPolicy": "workflowsFromSameOwner", "errorWorkflow": "escalation-error-handler"}, "versionId": "v2.0", "meta": {"templateCredsSetupCompleted": true, "instanceId": "smart-notifications-v2"}, "id": "smart-notifications-v2", "tags": [{"createdAt": "2024-01-15T10:00:00.000Z", "updatedAt": "2024-01-15T10:00:00.000Z", "id": "escalation-v2", "name": "escalation-v2"}, {"createdAt": "2024-01-15T10:00:00.000Z", "updatedAt": "2024-01-15T10:00:00.000Z", "id": "notifications", "name": "notifications"}, {"createdAt": "2024-01-15T10:00:00.000Z", "updatedAt": "2024-01-15T10:00:00.000Z", "id": "multi-channel", "name": "multi-channel"}]}