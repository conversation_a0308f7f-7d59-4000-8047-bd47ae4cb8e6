{"name": "[AGGREGATOR] Compile Infoproduct", "nodes": [{"parameters": {}, "id": "start_node", "name": "Start", "type": "n8n-nodes-base.start", "typeVersion": 1, "position": [200, 300], "notes": "Recebe: { infoproduct_id }"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT \n  (SELECT COUNT(*) FROM agent.infoproduct_chapters WHERE infoproduct_id = $1 AND status IN ('completed', 'failed')) as chapters_processed,\n  jsonb_array_length(content_structure) as chapters_total\nFROM agent.generated_infoproducts\nWHERE id = $1;", "options": {"parameters": {"values": ["={{$json.infoproduct_id}}"]}}}, "id": "db_check_progress", "name": "DB: Check Progress", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [440, 300], "credentials": {"postgres": {"id": "YOUR_POSTGRES_CREDENTIAL_ID"}}}, {"parameters": {"conditions": {"number": [{"value1": "={{$json.chapters_processed}}", "operation": "equal", "value2": "={{$json.chapters_total}}"}]}}, "id": "if_is_complete", "name": "IF: Is Complete?", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [660, 300], "notes": "Verifica se o número de capítulos processados é igual ao total esperado."}, {"parameters": {}, "id": "stop_not_complete", "name": "Stop: Not Complete", "type": "n8n-nodes-base.noOp", "typeVersion": 1, "position": [880, 500]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT chapter_content FROM agent.infoproduct_chapters WHERE infoproduct_id = $1 AND status = 'completed' ORDER BY chapter_order ASC;", "options": {"parameters": {"values": ["={{$json.infoproduct_id}}"]}}}, "id": "db_get_all_chapters", "name": "DB: Get All Chapters", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [880, 300], "credentials": {"postgres": {"id": "YOUR_POSTGRES_CREDENTIAL_ID"}}}, {"parameters": {"functionCode": "const chapters = $items.map(item => item.json.chapter_content);\nconst fullContent = chapters.join('\\n\\n---\\n\\n');\nreturn [{ json: { full_content: fullContent } }];", "options": {}}, "id": "code_compile_content", "name": "CODE: Compile Full Content", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1100, 300]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "UPDATE agent.generated_infoproducts SET full_content = $1, status = 'review' WHERE id = $2 RETURNING title;", "options": {"parameters": {"values": ["={{$json.full_content}}", "={{$('start_node').item.json.infoproduct_id}}"]}}}, "id": "db_save_full_content", "name": "DB: Save Full Content", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [1320, 300], "credentials": {"postgres": {"id": "YOUR_POSTGRES_CREDENTIAL_ID"}}}, {"parameters": {"method": "POST", "url": "={{$credentials.slackWebhook.url}}", "body": "={{ ({ 'text': `✅ Novo Infoproduto Compilado: ${$json.title}\\n\\nO rascunho completo foi gerado e está pronto para revisão (ID: ${$('start_node').item.json.infoproduct_id}).` }) }}", "options": {}}, "id": "slack_notify_manager", "name": "SLACK: Notify Manager", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [1540, 300], "credentials": {"httpHeaderAuth": {"id": "YOUR_SLACK_WEBHOOK_CREDENTIAL"}}}], "connections": {"start_node": {"main": [[{"node": "db_check_progress"}]]}, "db_check_progress": {"main": [[{"node": "if_is_complete"}]]}, "if_is_complete": {"main": [[{"node": "db_get_all_chapters"}], [{"node": "stop_not_complete"}]]}, "db_get_all_chapters": {"main": [[{"node": "code_compile_content"}]]}, "code_compile_content": {"main": [[{"node": "db_save_full_content"}]]}, "db_save_full_content": {"main": [[{"node": "slack_notify_manager"}]]}}}