{"name": "[SUPERVISOR] Sales Performance v1.1 - Cost-Aware", "nodes": [{"parameters": {"rule": "cron", "cronTime": "0 9 * * 1"}, "id": "trigger_weekly", "name": "TRIGGER: Weekly (Mon 9am)", "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1.1, "position": [400, 300]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT * FROM agent.sales_goals WHERE status = 'active' AND period_end_date >= CURRENT_DATE;", "options": {}}, "id": "db_get_goals", "name": "DB: Get Active Sales Goals", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [640, 300], "credentials": {"postgres": {"id": "YOUR_POSTGRES_CREDENTIAL_ID"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT g.id as goal_id, g.metric_key, g.target_value, (SELECT COALESCE(SUM(total_amount), 0) FROM agent.orders WHERE order_date >= g.period_start_date AND order_date <= g.period_end_date) as current_revenue, (SELECT COUNT(id) FROM agent.contatos WHERE jornada_status = 'convertido' AND updated_at >= g.period_start_date AND updated_at <= g.period_end_date) as current_conversions FROM agent.sales_goals g WHERE g.id = $1;", "options": {"parameters": {"values": ["={{$json.id}}"]}}}, "id": "db_get_performance", "name": "DB: Get Current Performance", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [860, 300], "credentials": {"postgres": {"id": "YOUR_POSTGRES_CREDENTIAL_ID"}}}, {"parameters": {"functionCode": "const performance = $json;\nlet currentValue = 0;\nif (performance.metric_key === 'revenue') {\n  currentValue = performance.current_revenue;\n} else if (performance.metric_key === 'new_conversions') {\n  currentValue = performance.current_conversions;\n}\n\n$json.current_value_for_metric = currentValue;\nreturn $items;", "options": {}}, "id": "code_get_current_value", "name": "CODE: Get Current Value for Metric", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1100, 300]}, {"parameters": {"workflowId": "={{ $env.DISPATCHER_WORKFLOW_ID }}", "data": "={{ { body: { \n    prompt: `Você é um Supervisor de Vendas. Analise a performance em relação à meta.\\n\\n**META:**\\n- Métrica: ${$json.metric_key}\\n- Alvo: ${$json.target_value}\\n\\n**PERFORMANCE ATUAL:**\\n- Valor Corrente: ${$json.current_value_for_metric}\\n\\n**TAREFA:**\\nGere um relatório em JSON com \\`status_summary\\`, \\`anomaly_detection\\`, \\`suggested_actions\\`.`,\n    task_type: 'complex_analysis',\n    calling_workflow_id: $workflow.id,\n    calling_node_id: 'call_dispatcher_for_analysis'\n} } }}", "options": {}}, "id": "call_dispatcher_for_analysis", "name": "Call Dispatcher for Analysis", "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 2, "position": [1320, 300]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "UPDATE agent.sales_goals SET current_value = $1, updated_at = NOW() WHERE id = $2;", "options": {"parameters": {"values": ["={{$node[\"code_get_current_value\"].json.current_value_for_metric}}", "{{$node[\"code_get_current_value\"].json.goal_id}}"]}}}, "id": "db_update_goal", "name": "DB: Update Goal's Current Value", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [1540, 200], "credentials": {"postgres": {"id": "YOUR_POSTGRES_CREDENTIAL_ID"}}}, {"parameters": {"method": "POST", "url": "={{$credentials.slackWebhook.url}}", "body": "={{ ({ 'text': `*📈 Relatório Semanal do Supervisor de Vendas*\\n\\n*Meta:* ${$('db_get_goals').item.json.name}\\n\\n*Status:* ${$json.status_summary}\\n*Anomalias:* ${$json.anomaly_detection}\\n\\n*Ações Sugeridas:*\\n- ${$json.suggested_actions.join('\\n- ')}` }) }}"}, "id": "slack_notify_manager", "name": "SLACK: Notify Manager", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [1540, 400], "credentials": {"httpHeaderAuth": {"id": "YOUR_SLACK_WEBHOOK_CREDENTIAL"}}}], "connections": {"trigger_weekly": {"main": [[{"node": "db_get_goals"}]]}, "db_get_goals": {"main": [[{"node": "db_get_performance"}]]}, "db_get_performance": {"main": [[{"node": "code_get_current_value"}]]}, "code_get_current_value": {"main": [[{"node": "call_dispatcher_for_analysis"}]]}, "call_dispatcher_for_analysis": {"main": [[{"node": "db_update_goal"}, {"node": "slack_notify_manager"}]]}}}