{"name": "[ESCALATION] AI Enhancement Engine v1.0", "nodes": [{"parameters": {"path": "escalation-ai-enhance", "httpMethod": "POST", "responseMode": "responseNode", "options": {"rawBody": true, "allowedOrigins": "*"}}, "id": "webhook-trigger", "name": "🚀 Trigger: AI Enhancement", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300], "webhookId": "escalation-ai-enhance"}, {"parameters": {"jsCode": "// =====================================================\n// AI ENHANCEMENT - REQUEST VALIDATOR\n// =====================================================\n\nconst Joi = require('joi');\nconst crypto = require('crypto');\n\n// Schema de validação para melhorias de IA\nconst aiEnhancementSchema = Joi.object({\n  escalation_id: Joi.number().integer().positive().optional(),\n  enhancement_type: Joi.string().valid('analyze_conversation', 'predict_resolution', 'suggest_specialization', 'evaluate_urgency', 'learn_from_feedback', 'batch_analysis').required(),\n  conversation_data: Joi.object({\n    messages: Joi.array().items(Joi.object()).max(500).optional(),\n    context: Joi.object().optional(),\n    metadata: Joi.object().optional()\n  }).optional(),\n  feedback_data: Joi.object({\n    escalation_id: Joi.number().integer().positive().required(),\n    resolution_success: Joi.boolean().required(),\n    agent_feedback: Joi.string().max(1000).optional(),\n    customer_satisfaction: Joi.number().min(1).max(5).optional(),\n    resolution_time_minutes: Joi.number().positive().optional(),\n    actual_specialization: Joi.string().optional(),\n    ai_prediction_accuracy: Joi.number().min(0).max(100).optional()\n  }).when('enhancement_type', { is: 'learn_from_feedback', then: Joi.required() }),\n  batch_criteria: Joi.object({\n    date_range: Joi.object({\n      start_date: Joi.string().isoDate().required(),\n      end_date: Joi.string().isoDate().required()\n    }).optional(),\n    status_filter: Joi.array().items(Joi.string()).optional(),\n    urgency_filter: Joi.array().items(Joi.string()).optional(),\n    limit: Joi.number().integer().min(1).max(1000).default(100)\n  }).when('enhancement_type', { is: 'batch_analysis', then: Joi.required() }),\n  analysis_options: Joi.object({\n    include_sentiment: Joi.boolean().default(true),\n    include_keywords: Joi.boolean().default(true),\n    include_patterns: Joi.boolean().default(true),\n    include_predictions: Joi.boolean().default(true),\n    confidence_threshold: Joi.number().min(0).max(100).default(70)\n  }).optional(),\n  metadata: Joi.object().optional()\n});\n\nfunction generateCorrelationId() {\n  return crypto.randomUUID();\n}\n\nconst startTime = Date.now();\nconst correlationId = generateCorrelationId();\n\ntry {\n  const requestBody = $input.first().json.body;\n  const headers = $input.first().json.headers;\n  \n  console.log(`[${correlationId}] AI enhancement request received`, {\n    timestamp: new Date().toISOString(),\n    enhancementType: requestBody.enhancement_type,\n    escalationId: requestBody.escalation_id\n  });\n  \n  const { error, value: validatedData } = aiEnhancementSchema.validate(requestBody, {\n    abortEarly: false,\n    stripUnknown: true,\n    convert: true\n  });\n  \n  if (error) {\n    const validationErrors = error.details.map(detail => ({\n      field: detail.path.join('.'),\n      message: detail.message,\n      value: detail.context?.value\n    }));\n    \n    return [{\n      json: {\n        success: false,\n        error: 'VALIDATION_ERROR',\n        message: 'AI enhancement validation failed',\n        details: validationErrors,\n        correlation_id: correlationId,\n        timestamp: new Date().toISOString()\n      }\n    }];\n  }\n  \n  const processingTime = Date.now() - startTime;\n  \n  const outputData = {\n    ...validatedData,\n    correlation_id: correlationId,\n    validation_time_ms: processingTime,\n    received_at: new Date().toISOString(),\n    user_agent: headers['user-agent'],\n    ip_address: headers['x-forwarded-for'] || headers['x-real-ip'] || 'unknown',\n    validation_status: 'success',\n    next_step: 'ai_processing'\n  };\n  \n  console.log(`[${correlationId}] AI enhancement validation successful`, {\n    processingTime,\n    enhancementType: validatedData.enhancement_type\n  });\n  \n  return [{ json: outputData }];\n  \n} catch (error) {\n  console.error(`[${correlationId}] AI enhancement validation error:`, error);\n  \n  return [{\n    json: {\n      success: false,\n      error: 'INTERNAL_ERROR',\n      message: 'Internal AI enhancement validation error',\n      correlation_id: correlationId,\n      timestamp: new Date().toISOString(),\n      details: {\n        error: error.message,\n        stack: error.stack\n      }\n    }\n  }];\n}"}, "id": "request-validator", "name": "✅ Validator: AI Request", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [460, 300]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "validation-success", "leftValue": "={{ $json.validation_status }}", "rightValue": "success", "operator": {"type": "string", "operation": "equals"}}], "combinator": "and"}, "options": {}}, "id": "validation-check", "name": "🔍 Check: Validation Status", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [680, 300]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "analyze-conversation", "leftValue": "={{ $json.enhancement_type }}", "rightValue": "analyze_conversation", "operator": {"type": "string", "operation": "equals"}}, {"id": "predict-resolution", "leftValue": "={{ $json.enhancement_type }}", "rightValue": "predict_resolution", "operator": {"type": "string", "operation": "equals"}}, {"id": "learn-feedback", "leftValue": "={{ $json.enhancement_type }}", "rightValue": "learn_from_feedback", "operator": {"type": "string", "operation": "equals"}}, {"id": "batch-analysis", "leftValue": "={{ $json.enhancement_type }}", "rightValue": "batch_analysis", "operator": {"type": "string", "operation": "equals"}}], "combinator": "or"}, "options": {}}, "id": "enhancement-router", "name": "🔀 Router: Enhancement Type", "type": "n8n-nodes-base.switch", "typeVersion": 3, "position": [900, 300]}, {"parameters": {"jsCode": "// =====================================================\n// AI - CONVERSATION ANALYSIS\n// =====================================================\n\nconst enhancementData = $input.first().json;\nconst conversationData = enhancementData.conversation_data;\nconst analysisOptions = enhancementData.analysis_options || {};\nconst correlationId = enhancementData.correlation_id;\n\ntry {\n  // Análise de sentimento\n  function analyzeSentiment(messages) {\n    if (!messages || messages.length === 0) return { score: 0, label: 'neutral' };\n    \n    // Palavras-chave para análise de sentimento\n    const positiveWords = ['obrigado', 'excelente', 'ótimo', 'perfeito', 'satisfeito', 'resolvido', 'ajudou'];\n    const negativeWords = ['problema', 'erro', 'ruim', 'péssimo', 'insatisfeito', 'demora', 'falha', 'bug'];\n    const urgentWords = ['urgente', 'crítico', 'emergência', 'imediato', 'agora', 'rápido'];\n    \n    let positiveScore = 0;\n    let negativeScore = 0;\n    let urgencyScore = 0;\n    \n    messages.forEach(message => {\n      const text = (message.content || message.text || '').toLowerCase();\n      \n      positiveWords.forEach(word => {\n        if (text.includes(word)) positiveScore++;\n      });\n      \n      negativeWords.forEach(word => {\n        if (text.includes(word)) negativeScore++;\n      });\n      \n      urgentWords.forEach(word => {\n        if (text.includes(word)) urgencyScore++;\n      });\n    });\n    \n    const totalScore = positiveScore - negativeScore;\n    let label = 'neutral';\n    \n    if (totalScore > 2) label = 'positive';\n    else if (totalScore < -2) label = 'negative';\n    \n    return {\n      score: Math.max(-100, Math.min(100, totalScore * 10)),\n      label,\n      urgency_indicators: urgencyScore,\n      positive_indicators: positiveScore,\n      negative_indicators: negativeScore\n    };\n  }\n  \n  // Extração de palavras-chave\n  function extractKeywords(messages) {\n    if (!messages || messages.length === 0) return [];\n    \n    const text = messages.map(m => m.content || m.text || '').join(' ').toLowerCase();\n    const words = text.split(/\\s+/);\n    \n    // Palavras-chave técnicas\n    const technicalKeywords = ['api', 'erro', 'bug', 'sistema', 'login', 'senha', 'pagamento', 'cobrança', 'fatura'];\n    const foundKeywords = [];\n    \n    technicalKeywords.forEach(keyword => {\n      const count = words.filter(word => word.includes(keyword)).length;\n      if (count > 0) {\n        foundKeywords.push({ keyword, frequency: count });\n      }\n    });\n    \n    return foundKeywords.sort((a, b) => b.frequency - a.frequency);\n  }\n  \n  // Detecção de padrões\n  function detectPatterns(messages) {\n    if (!messages || messages.length === 0) return {};\n    \n    const patterns = {\n      question_count: 0,\n      exclamation_count: 0,\n      avg_message_length: 0,\n      response_time_pattern: 'unknown',\n      escalation_triggers: []\n    };\n    \n    let totalLength = 0;\n    \n    messages.forEach(message => {\n      const text = message.content || message.text || '';\n      totalLength += text.length;\n      \n      if (text.includes('?')) patterns.question_count++;\n      if (text.includes('!')) patterns.exclamation_count++;\n      \n      // Detectar gatilhos de escalação\n      const escalationTriggers = ['falar com supervisor', 'gerente', 'reclamação', 'cancelar', 'processo'];\n      escalationTriggers.forEach(trigger => {\n        if (text.toLowerCase().includes(trigger)) {\n          patterns.escalation_triggers.push(trigger);\n        }\n      });\n    });\n    \n    patterns.avg_message_length = messages.length > 0 ? Math.round(totalLength / messages.length) : 0;\n    \n    return patterns;\n  }\n  \n  // Predição de especialização\n  function predictSpecialization(messages, keywords) {\n    const specializationKeywords = {\n      'technical': ['api', 'erro', 'bug', 'sistema', 'código', 'integração'],\n      'billing': ['pagamento', 'cobrança', 'fatura', 'valor', 'preço', 'desconto'],\n      'sales': ['comprar', 'produto', 'serviço', 'plano', 'upgrade', 'contrato'],\n      'support': ['ajuda', 'dúvida', 'como', 'tutorial', 'configurar'],\n      'legal': ['contrato', 'termo', 'política', 'privacidade', 'lgpd'],\n      'compliance': ['regulamentação', 'norma', 'auditoria', 'conformidade']\n    };\n    \n    const scores = {};\n    \n    Object.keys(specializationKeywords).forEach(spec => {\n      scores[spec] = 0;\n      \n      specializationKeywords[spec].forEach(keyword => {\n        const found = keywords.find(k => k.keyword.includes(keyword));\n        if (found) {\n          scores[spec] += found.frequency;\n        }\n      });\n    });\n    \n    const maxScore = Math.max(...Object.values(scores));\n    const predictedSpec = Object.keys(scores).find(spec => scores[spec] === maxScore);\n    \n    return {\n      predicted_specialization: maxScore > 0 ? predictedSpec : 'general',\n      confidence: maxScore > 0 ? Math.min(100, maxScore * 20) : 0,\n      scores\n    };\n  }\n  \n  // Executar análises\n  const messages = conversationData?.messages || [];\n  const sentiment = analysisOptions.include_sentiment ? analyzeSentiment(messages) : null;\n  const keywords = analysisOptions.include_keywords ? extractKeywords(messages) : [];\n  const patterns = analysisOptions.include_patterns ? detectPatterns(messages) : {};\n  const specialization = analysisOptions.include_predictions ? predictSpecialization(messages, keywords) : null;\n  \n  // Calcular score geral de análise\n  const analysisScore = Math.round(\n    (sentiment?.score || 0) * 0.3 +\n    (keywords.length * 5) * 0.2 +\n    (specialization?.confidence || 0) * 0.3 +\n    (patterns.escalation_triggers?.length * 10) * 0.2\n  );\n  \n  const analysisResult = {\n    analysis_id: crypto.randomUUID(),\n    escalation_id: enhancementData.escalation_id,\n    analysis_type: 'conversation_analysis',\n    sentiment_analysis: sentiment,\n    keywords_extracted: keywords,\n    patterns_detected: patterns,\n    specialization_prediction: specialization,\n    overall_score: Math.max(0, Math.min(100, analysisScore)),\n    confidence_level: analysisOptions.confidence_threshold || 70,\n    processed_at: new Date().toISOString(),\n    processing_time_ms: Date.now() - new Date(enhancementData.received_at).getTime()\n  };\n  \n  console.log(`[${correlationId}] Conversation analysis completed`, {\n    analysisScore,\n    sentimentLabel: sentiment?.label,\n    keywordCount: keywords.length,\n    predictedSpec: specialization?.predicted_specialization\n  });\n  \n  return [{\n    json: {\n      ...enhancementData,\n      analysis_result: analysisResult,\n      processing_status: 'analysis_completed'\n    }\n  }];\n  \n} catch (error) {\n  console.error(`[${correlationId}] Error in conversation analysis:`, error);\n  \n  return [{\n    json: {\n      ...enhancementData,\n      error: 'ANALYSIS_ERROR',\n      message: 'Failed to analyze conversation',\n      details: {\n        error: error.message,\n        stack: error.stack\n      },\n      processing_status: 'error'\n    }\n  }];\n}"}, "id": "conversation-analysis", "name": "🧠 AI: Conversation Analysis", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1120, 200]}, {"parameters": {"jsCode": "// =====================================================\n// AI - FEEDBACK LEARNING\n// =====================================================\n\nconst enhancementData = $input.first().json;\nconst feedbackData = enhancementData.feedback_data;\nconst correlationId = enhancementData.correlation_id;\n\ntry {\n  // Processar feedback para aprendizado\n  function processFeedbackLearning(feedback) {\n    const learningMetrics = {\n      prediction_accuracy: feedback.ai_prediction_accuracy || 0,\n      resolution_success: feedback.resolution_success,\n      satisfaction_score: feedback.customer_satisfaction || 0,\n      resolution_time: feedback.resolution_time_minutes || 0,\n      specialization_match: feedback.actual_specialization ? 1 : 0\n    };\n    \n    // Calcular score de aprendizado\n    const learningScore = (\n      learningMetrics.prediction_accuracy * 0.3 +\n      (learningMetrics.resolution_success ? 100 : 0) * 0.3 +\n      (learningMetrics.satisfaction_score * 20) * 0.2 +\n      (learningMetrics.resolution_time < 60 ? 100 : Math.max(0, 100 - learningMetrics.resolution_time)) * 0.1 +\n      learningMetrics.specialization_match * 100 * 0.1\n    );\n    \n    return {\n      learning_score: Math.round(learningScore),\n      metrics: learningMetrics,\n      improvement_areas: identifyImprovementAreas(learningMetrics),\n      recommendations: generateRecommendations(learningMetrics)\n    };\n  }\n  \n  // Identificar áreas de melhoria\n  function identifyImprovementAreas(metrics) {\n    const areas = [];\n    \n    if (metrics.prediction_accuracy < 70) {\n      areas.push('prediction_accuracy');\n    }\n    \n    if (!metrics.resolution_success) {\n      areas.push('resolution_effectiveness');\n    }\n    \n    if (metrics.satisfaction_score < 4) {\n      areas.push('customer_satisfaction');\n    }\n    \n    if (metrics.resolution_time > 120) {\n      areas.push('response_time');\n    }\n    \n    if (metrics.specialization_match === 0) {\n      areas.push('specialization_routing');\n    }\n    \n    return areas;\n  }\n  \n  // Gerar recomendações\n  function generateRecommendations(metrics) {\n    const recommendations = [];\n    \n    if (metrics.prediction_accuracy < 70) {\n      recommendations.push({\n        area: 'AI Model Training',\n        action: 'Retrain prediction models with recent escalation data',\n        priority: 'high'\n      });\n    }\n    \n    if (!metrics.resolution_success) {\n      recommendations.push({\n        area: 'Agent Training',\n        action: 'Provide additional training for handling similar cases',\n        priority: 'medium'\n      });\n    }\n    \n    if (metrics.satisfaction_score < 4) {\n      recommendations.push({\n        area: 'Customer Experience',\n        action: 'Review communication protocols and response templates',\n        priority: 'high'\n      });\n    }\n    \n    if (metrics.resolution_time > 120) {\n      recommendations.push({\n        area: 'Process Optimization',\n        action: 'Analyze workflow bottlenecks and optimize routing',\n        priority: 'medium'\n      });\n    }\n    \n    return recommendations;\n  }\n  \n  const learningResult = processFeedbackLearning(feedbackData);\n  \n  const feedbackAnalysis = {\n    feedback_id: crypto.randomUUID(),\n    escalation_id: feedbackData.escalation_id,\n    analysis_type: 'feedback_learning',\n    learning_result: learningResult,\n    feedback_summary: {\n      resolution_success: feedbackData.resolution_success,\n      satisfaction_rating: feedbackData.customer_satisfaction,\n      agent_feedback: feedbackData.agent_feedback,\n      resolution_time: feedbackData.resolution_time_minutes\n    },\n    processed_at: new Date().toISOString(),\n    processing_time_ms: Date.now() - new Date(enhancementData.received_at).getTime()\n  };\n  \n  console.log(`[${correlationId}] Feedback learning completed`, {\n    learningScore: learningResult.learning_score,\n    improvementAreas: learningResult.improvement_areas.length,\n    recommendations: learningResult.recommendations.length\n  });\n  \n  return [{\n    json: {\n      ...enhancementData,\n      analysis_result: feedbackAnalysis,\n      processing_status: 'learning_completed'\n    }\n  }];\n  \n} catch (error) {\n  console.error(`[${correlationId}] Error in feedback learning:`, error);\n  \n  return [{\n    json: {\n      ...enhancementData,\n      error: 'LEARNING_ERROR',\n      message: 'Failed to process feedback learning',\n      details: {\n        error: error.message,\n        stack: error.stack\n      },\n      processing_status: 'error'\n    }\n  }];\n}"}, "id": "feedback-learning", "name": "📚 AI: Feedback Learning", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1120, 400]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "INSERT INTO agent.ai_analysis_results (escalation_id, analysis_type, analysis_data, confidence_score, created_at, correlation_id) VALUES ($1, $2, $3, $4, NOW(), $5)", "options": {"queryParameters": "={{ [\n            $json.analysis_result.escalation_id || null,\n            $json.analysis_result.analysis_type,\n            JSON.stringify($json.analysis_result),\n            $json.analysis_result.overall_score || $json.analysis_result.learning_result?.learning_score || 0,\n            $json.correlation_id\n          ] }}"}}, "id": "store-analysis", "name": "💾 Store: Analysis Result", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [1340, 300], "credentials": {"postgres": {"id": "postgres-main", "name": "PostgreSQL - Main Database"}}}, {"parameters": {"respondWith": "json", "responseBody": "={{ {\n  success: true,\n  message: 'AI enhancement completed successfully',\n  data: {\n    enhancement_type: $json.enhancement_type,\n    analysis_result: $json.analysis_result,\n    correlation_id: $json.correlation_id,\n    processed_at: new Date().toISOString()\n  }\n} }}"}, "id": "success-response", "name": "✅ Response: Success", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1560, 300]}, {"parameters": {"respondWith": "json", "responseBody": "={{ $json }}", "options": {"responseCode": 400}}, "id": "error-response", "name": "❌ Response: Error", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [680, 500]}], "connections": {"webhook-trigger": {"main": [[{"node": "request-validator", "type": "main", "index": 0}]]}, "request-validator": {"main": [[{"node": "validation-check", "type": "main", "index": 0}]]}, "validation-check": {"main": [[{"node": "enhancement-router", "type": "main", "index": 0}], [{"node": "error-response", "type": "main", "index": 0}]]}, "enhancement-router": {"main": [[{"node": "conversation-analysis", "type": "main", "index": 0}], [], [{"node": "feedback-learning", "type": "main", "index": 0}]]}, "conversation-analysis": {"main": [[{"node": "store-analysis", "type": "main", "index": 0}]]}, "feedback-learning": {"main": [[{"node": "store-analysis", "type": "main", "index": 0}]]}, "store-analysis": {"main": [[{"node": "success-response", "type": "main", "index": 0}]]}}, "pinData": {}, "settings": {"executionOrder": "v1"}, "staticData": null, "tags": [{"createdAt": "2024-01-15T10:00:00.000Z", "updatedAt": "2024-01-15T10:00:00.000Z", "id": "escalation", "name": "escalation"}, {"createdAt": "2024-01-15T10:00:00.000Z", "updatedAt": "2024-01-15T10:00:00.000Z", "id": "ai", "name": "ai"}, {"createdAt": "2024-01-15T10:00:00.000Z", "updatedAt": "2024-01-15T10:00:00.000Z", "id": "enhancement", "name": "enhancement"}], "triggerCount": 0, "updatedAt": "2024-01-15T10:00:00.000Z", "versionId": "1"}