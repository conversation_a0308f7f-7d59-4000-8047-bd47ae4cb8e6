# Evolution API Configuration File
# This file contains application-specific settings for Evolution API
# Docker Compose configuration is in docker-compose.yml

# Database Configuration
database:
  provider: postgresql
  connection_uri: postgresql://postgres:${POSTGRES_PASSWORD}@postgres:5432/evolution
  url: postgresql://postgres:${POSTGRES_PASSWORD}@postgres:5432/evolution

# Redis Configuration
redis:
  enabled: true
  uri: redis://redis:6379/6
  prefix_key: evolution
  save_instances: false

# Cache Configuration
cache:
  redis:
    enabled: true
    uri: redis://redis:6379/6
    prefix_key: evolution
    save_instances: false
  local:
    enabled: false

# Authentication Configuration
authentication:
  api_key: ${AUTHENTICATION_API_KEY}

s3:
  enabled: true
  endpoint: http://minio:9000
  bucket: evolution
  accessKey: admin
  secretKey: uJm1t2!9FD-_ZQiBa%X@
  forcePathStyle: true
