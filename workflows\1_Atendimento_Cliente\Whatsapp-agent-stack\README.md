# Agente de WhatsApp Inteligente e Autônomo

Esta stack Docker Compose implementa uma solução completa para um agente de WhatsApp inteligente, utilizando um conjunto de ferramentas open-source para análise, orquestração e visualização de dados.

## Arquitetura de Serviços

- **Traefik**: Reverse Proxy e Load Balancer que gerencia todo o tráfego e SSL.
- **PostgreSQL**: Banco de dados relacional para todos os dados persistentes.
- **Redis**: Cache de alta velocidade para sessões, contexto e jobs.
- **RabbitMQ**: Fila de mensagens para comunicação assíncrona e resiliente entre serviços.
- **Evolution API**: Gateway para a API não-oficial do WhatsApp.
- **n8n**: Orquestrador de workflows (o "cérebro" do agente).
- **Chatwoot**: Plataforma de atendimento humano para escalar conversas.
- **Metabase**: Ferramenta de Business Intelligence para dashboards e relatórios.
- **ai_processor**: Microsserviço Python customizado para processamento pesado (áudio, imagem, vídeo).
- **Prometheus**: Sistema de monitoramento e alertas.
- **Grafana**: Plataforma para visualização de métricas do Prometheus.

## Pré-requisitos

1.  Docker e Docker Compose instalados no servidor.
2.  Um servidor Linux (Ubuntu 22.04 LTS recomendado) com acesso à internet.
3.  Um nome de domínio apontando para o IP público do seu servidor (necessário para SSL).

## Como Instalar

1.  **Clone o repositório:**
    ```bash
    git clone <url-do-seu-repositorio>
    cd whatsapp-agent-stack
    ```

2.  **Configure as variáveis de ambiente:**
    Copie o arquivo de exemplo e edite-o com suas próprias informações.
    ```bash
    cp .env.example .env
    nano .env
    ```
    **Atenção:** Preencha `DOMAIN`, `ACME_EMAIL` e gere as chaves secretas conforme indicado no arquivo.

3.  **Inicie a Stack:**
    ```bash
    docker-compose up -d
    ```

4.  **Verifique se todos os serviços estão rodando:**
    ```bash
    docker-compose ps
    ```
    Todos os serviços devem estar com o status `Up` ou `healthy`. Pode levar alguns minutos para tudo iniciar pela primeira vez.

## Acesso aos Serviços

Após a instalação, você poderá acessar as interfaces através dos subdomínios configurados:

- **Evolution API**: `https://evolution.seu-dominio.com` (escaneie o QR Code para conectar seu WhatsApp).
- **n8n**: `https://n8n.seu-dominio.com` (configure o usuário administrador no primeiro acesso).
- **Chatwoot**: `https://chatwoot.seu-dominio.com`.
- **Metabase**: `https://metabase.seu-dominio.com`.
- **RabbitMQ Admin**: `https://rabbitmq.seu-dominio.com` (use as credenciais do `.env`).
- **Prometheus**: `https://prometheus.seu-dominio.com`.
- **Grafana**: `https://grafana.seu-dominio.com`.
- **Traefik Dashboard**: `http://localhost:8080` (acessível apenas do servidor).

## Próximos Passos (Configuração Pós-Instalação)

1.  **Evolution API**: Acesse a UI, gere uma instância e escaneie o QR Code. Configure a URL do webhook para apontar para o seu n8n. Ex: `https://n8n.seu-dominio.com/webhook/whatsapp-inbox`
2.  **n8n**:
    - Crie suas credenciais para o PostgreSQL usando as variáveis do `.env`.
    - Crie um workflow que receba o webhook da Evolution API.
    - Para processar mídias, use o nó `RabbitMQ Sender` para enviar uma mensagem para a fila `media_processing_queue` com o payload `{ "message_id": 123, "media_url": "url", "media_type": "audio" }`.
3.  **Metabase**: Conecte ao seu banco de dados `agent_db` e comece a criar suas perguntas e dashboards sobre o schema `agent`.