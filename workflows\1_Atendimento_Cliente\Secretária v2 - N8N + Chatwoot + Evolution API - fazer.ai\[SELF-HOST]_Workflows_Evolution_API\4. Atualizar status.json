{"nodes": [{"parameters": {"httpMethod": "POST", "path": "e56e25c6-d00e-40aa-83b6-4985aea910f1", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-60, 0], "id": "7be3b5c6-c04f-4aee-a273-81c3edf27985", "name": " Webhook Digitando...", "webhookId": "e56e25c6-d00e-40aa-83b6-4985aea910f1"}, {"parameters": {"resource": "chat-api", "operation": "send-presence", "instanceName": "={{ $json.body.instancia }}", "remoteJid": "={{ $json.body.telefone }}", "presence": "={{ $json.body.status }}", "delay": 20000}, "type": "n8n-nodes-evolution-api.evolutionApi", "typeVersion": 1, "position": [180, 0], "id": "17440d7e-977c-4188-b865-7922eaf23f4e", "name": "Atualizar status", "credentials": {}}, {"parameters": {"content": "[![fazer.ai](https://framerusercontent.com/images/HqY9djLTzyutSKnuLLqBr92KbM.png?scale-down-to=256)](https://fazer.ai?utm_source=n8n&utm_campaign=sec-ep2&utm_medium=evo-4)\n\n## Esse é um template faça você mesmo do canal\n## <PERSON> Moreira\n\n### Inscreva-se no nosso canal no YouTube\n[![YouTube Lucas Moreira](https://img.shields.io/youtube/channel/subscribers/UCtmp6SxzLscu0GRTbgM8FTw?style=flat-square&logo=youtube&label=Inscreva-se&color=f00)](https://youtube.com/@eulucassmoreira?si=0lH7hwX9pukjhmPQ)\n\n### Siga nosso GitHub\n[![GitHub fazer.ai](https://img.shields.io/badge/github-%23121011.svg?style=for-the-badge&logo=github&logoColor=white&label)](https://github.com/fazer-ai)\n", "height": 440, "width": 550, "color": 7}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-380, -520], "id": "49077a75-7f43-4f9c-af1f-5916036f8f20", "name": "Sticky Note10"}, {"parameters": {"content": "## Quer entender como funciona?\n\n\n### Assista o vídeo, deixe um like, e se inscreva no canal para ter acesso a mais workflows como esse!\n\n[![IMAGE ALT TEXT HERE](https://i1.ytimg.com/vi_webp/cvTWGNJGAu4/maxresdefault.webp)](https://www.youtube.com/watch?v=cvTWGNJGAu4)", "height": 440, "width": 500, "color": 7}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [180, -520], "id": "62e9e4bc-6fd2-4b03-8636-f9983cc2b554", "name": "Sticky Note13"}, {"parameters": {"content": "![Evolution API](https://mintlify.s3.us-west-1.amazonaws.com/evolution/logo/dark.svg)", "height": 100, "width": 280, "color": 7}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-160, -220], "id": "f5062013-a868-494d-ab88-e236a52b388c", "name": "Sticky Note20"}], "connections": {" Webhook Digitando...": {"main": [[{"node": "Atualizar status", "type": "main", "index": 0}]]}}, "pinData": {}, "meta": {}}