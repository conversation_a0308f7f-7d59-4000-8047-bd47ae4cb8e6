{"name": "[SUB] Scout - Analyze Theme v1.1 - Cost-Aware", "nodes": [{"parameters": {}, "id": "start_node", "name": "Start", "type": "n8n-nodes-base.start", "typeVersion": 1, "position": [200, 300], "notes": "Re<PERSON>be de entrada: { theme: '...' }"}, {"parameters": {"search_term": "={{`'${$json.theme}' ideas for dropshipping products`}}", "explanation": "Busca por ideias de produtos de dropshipping baseados no tema."}, "id": "web_search_dropship", "name": "WEB: Search Dropship Products", "type": "n8n-nodes-base.webSearch", "typeVersion": 1, "position": [420, 200]}, {"parameters": {"search_term": "={{`'${$json.theme}' best selling online courses and ebooks`}}", "explanation": "Busca por info-produtos baseados no tema."}, "id": "web_search_infoproduct", "name": "WEB: Search Infoproducts", "type": "n8n-nodes-base.webSearch", "typeVersion": 1, "position": [420, 400]}, {"parameters": {}, "id": "merge_searches", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.merge", "typeVersion": 2, "position": [620, 300]}, {"parameters": {"batchSize": 1}, "id": "loop_results", "name": "Loop Over Search Results", "type": "n8n-nodes-base.splitInBatches", "typeVersion": 2, "position": [820, 300]}, {"parameters": {"workflowId": "={{ $env.DISPATCHER_WORKFLOW_ID }}", "data": "={{ { body: { \n    prompt: `Você é um analista de produtos. Analise o seguinte resultado de busca e extraia os detalhes do produto.\\n\\nRESULTADO DA BUSCA:\\n- Título: ${$json.title}\\n- Snippet: ${$json.snippet}\\n- Link: ${$json.link}\\n\\nSua tarefa é preencher o seguinte objeto JSON. Se uma informação não estiver clara, deixe o campo como 'não especificado'.\\n\\n- \\`product_name\\`: O nome claro do produto.\\n- \\`product_type\\`: 'dropshipping' ou 'infoproduct'.\\n- \\`description\\`: Um resumo em uma frase do que é o produto.\\n- \\`demand_reasoning\\`: Explique por que este produto resolve a necessidade '${$('start_node').item.json.theme}'.\\n- \\`ai_analysis\\`: {\\n    \\\"estimated_margin_score\\\": [0-10],\\n    \\\"competition_score\\\": [0-10],\\n    \\\"audience_fit_score\\\": [0-10]\\n  }\\n`,\n    task_type: 'complex_analysis',\n    calling_workflow_id: $workflow.id,\n    calling_node_id: 'call_dispatcher_to_analyze'\n} } }}", "options": {}}, "id": "call_dispatcher_to_analyze", "name": "Call Dispatcher to Ana<PERSON><PERSON>", "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 2, "position": [1040, 300]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "INSERT INTO agent.product_opportunities (product_name, product_type, source_url, description, demand_reasoning, ai_analysis) VALUES ($1, $2, $3, $4, $5, $6::jsonb);", "options": {"parameters": {"values": ["={{JSON.parse($json.choices[0].message.content).product_name}}", "={{JSON.parse($json.choices[0].message.content).product_type}}", "={{$('loop_results').item.json.link}}", "={{JSON.parse($json.choices[0].message.content).description}}", "={{JSON.parse($json.choices[0].message.content).demand_reasoning}}", "={{JSON.stringify(JSON.parse($json.choices[0].message.content).ai_analysis)}}"]}}}, "id": "db_save_opportunity", "name": "DB: Save Opportunity", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [1280, 300], "credentials": {"postgres": {"id": "YOUR_POSTGRES_CREDENTIAL_ID"}}}], "connections": {"start_node": {"main": [[{"node": "web_search_dropship"}, {"node": "web_search_infoproduct"}]]}, "web_search_dropship": {"main": [[{"node": "merge_searches"}]]}, "web_search_infoproduct": {"main": [[{"node": "merge_searches", "type": "main", "index": 1}]]}, "merge_searches": {"main": [[{"node": "loop_results"}]]}, "loop_results": {"main": [[{"node": "call_dispatcher_to_analyze"}]]}, "call_dispatcher_to_analyze": {"main": [[{"node": "db_save_opportunity"}]]}}}