# Teste das linhas específicas que estão causando problemas

# Teste 1: Regex da linha 231
$testString = "KEY=VALUE"
if ($testString -match '^([^=]+)=(.*)$') {
    Write-Host "Regex OK: $($matches[1]) = $($matches[2])" -ForegroundColor Green
} else {
    Write-Host "Regex FALHOU" -ForegroundColor Red
}

# Teste 2: String da linha 595
try {
    $dashboardPath = "test.html"
    Log-Event "Dashboard HTML gerado em modo fallback: $dashboardPath" "Dashboard" "SUCCESS"
    Write-Host "String linha 595 OK" -ForegroundColor Green
} catch {
    Write-Host "String linha 595 FALHOU: $($_.Exception.Message)" -ForegroundColor Red
}

# Teste 3: String da linha 857
try {
    Log-Event "Script finalizado com sucesso!" "Orchestrator" "SUCCESS"
    Write-Host "String linha 857 OK" -ForegroundColor Green
} catch {
    Write-Host "String linha 857 FALHOU: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "Teste concluido" -ForegroundColor Cyan
