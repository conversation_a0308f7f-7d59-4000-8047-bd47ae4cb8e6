# Script para validar sintaxe do PowerShell
param(
    [string]$FilePath = "c:\Users\<USER>\Desktop\N8N-evolution V5\Start-Environment.ps1"
)

Write-Host "Validando sintaxe do arquivo: $FilePath" -ForegroundColor Cyan

try {
    # Ler o conteúdo do arquivo
    $content = Get-Content -Path $FilePath -Raw
    
    # Verificar balanceamento de chaves
    Write-Host "`nVerificando balanceamento de chaves..." -ForegroundColor Cyan
    
    $openBraces = ($content.ToCharArray() | Where-Object { $_ -eq '{' }).Count
    $closeBraces = ($content.ToCharArray() | Where-Object { $_ -eq '}' }).Count
    
    Write-Host "Chaves abertas: $openBraces" -ForegroundColor White
    Write-Host "Chaves fechadas: $closeBraces" -ForegroundColor White
    
    if ($openBraces -eq $closeBraces) {
        Write-Host "✅ Chaves balanceadas!" -ForegroundColor Green
    } else {
        Write-Host "❌ Chaves desbalanceadas! Diferença: $($openBraces - $closeBraces)" -ForegroundColor Red
    }
    
    # Verificar parênteses
    $openParens = ($content.ToCharArray() | Where-Object { $_ -eq '(' }).Count
    $closeParens = ($content.ToCharArray() | Where-Object { $_ -eq ')' }).Count
    
    Write-Host "Parênteses abertos: $openParens" -ForegroundColor White
    Write-Host "Parênteses fechados: $closeParens" -ForegroundColor White
    
    if ($openParens -eq $closeParens) {
        Write-Host "✅ Parênteses balanceados!" -ForegroundColor Green
    } else {
        Write-Host "❌ Parênteses desbalanceados! Diferença: $($openParens - $closeParens)" -ForegroundColor Red
    }
    
    # Verificar colchetes
    $openBrackets = ($content.ToCharArray() | Where-Object { $_ -eq '[' }).Count
    $closeBrackets = ($content.ToCharArray() | Where-Object { $_ -eq ']' }).Count
    
    Write-Host "Colchetes abertos: $openBrackets" -ForegroundColor White
    Write-Host "Colchetes fechados: $closeBrackets" -ForegroundColor White
    
    if ($openBrackets -eq $closeBrackets) {
        Write-Host "✅ Colchetes balanceados!" -ForegroundColor Green
    } else {
        Write-Host "❌ Colchetes desbalanceados! Diferença: $($openBrackets - $closeBrackets)" -ForegroundColor Red
    }
    
    # Tentar executar uma validação básica
    Write-Host "`nTentando validação básica..." -ForegroundColor Cyan
    try {
        $null = [scriptblock]::Create($content)
        Write-Host "✅ Script pode ser compilado!" -ForegroundColor Green
    } catch {
        Write-Host "❌ Erro de compilação: $($_.Exception.Message)" -ForegroundColor Red
    }
    
} catch {
    Write-Host "❌ Erro ao validar sintaxe: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`nValidação concluída." -ForegroundColor Cyan