{"meta": {"instanceId": "REPURPOSE_CROSS_PLATFORM_ENGINE"}, "name": "[REPURPOSE] Cross-Platform Content Engine", "nodes": [{"parameters": {}, "id": "webhook_trigger", "name": "WEBHOOK: Content Published", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [200, 300], "webhookId": "content-repurpose", "path": "content-repurpose"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "-- Buscar regras de repurposing aplicáveis\nSELECT \n  rr.*,\n  rr.transformation_rules\nFROM agent.repurposing_rules rr\nWHERE rr.source_platform = $1\nAND rr.success_rate > 0.3\nORDER BY rr.success_rate DESC, rr.avg_performance_gain DESC\nLIMIT 5;", "options": {"parameters": {"values": ["={{ $json.source_platform || $json.platform }}"]}}}, "id": "get_repurposing_rules", "name": "Get Repurposing Rules", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [420, 300], "credentials": {"postgres": {"id": "postgres-main", "name": "PostgreSQL Main"}}}, {"parameters": {"batchSize": 1}, "id": "loop_repurpose_rules", "name": "Loop Repurpose Rules", "type": "n8n-nodes-base.splitInBatches", "typeVersion": 2, "position": [640, 300]}, {"parameters": {"workflowId": "={{ $env.DISPATCHER_WORKFLOW_ID }}", "data": "={{ {\n  body: {\n    prompt: `Você é um especialista em repurposing de conteúdo para redes sociais. Adapte o conteúdo abaixo seguindo as regras específicas da plataforma de destino.\\n\\n**CONTEÚDO ORIGINAL:**\\nPlataforma: ${$('webhook_trigger').item.json.source_platform || $('webhook_trigger').item.json.platform}\\nTexto: ${$('webhook_trigger').item.json.original_content || $('webhook_trigger').item.json.content_text}\\nInfluenciador: ${$('webhook_trigger').item.json.influencer_name || 'N/A'}\\n\\n**PLATAFORMA DE DESTINO:** ${$json.target_platform}\\n\\n**REGRAS DE TRANSFORMAÇÃO:**\\n${JSON.stringify($json.transformation_rules, null, 2)}\\n\\n**INSTRUÇÕES ESPECÍFICAS:**\\n${$json.target_platform === 'twitter' ? '- Dividir em thread se necessário (máx 8 tweets)\\n- Usar numeração (1/8, 2/8, etc.)\\n- Incluir hashtags trending\\n- Manter CTA no último tweet' : ''}\\n${$json.target_platform === 'instagram' ? '- Formato carousel se aplicável\\n- Linguagem mais visual e inspiracional\\n- Incluir emojis relevantes\\n- CTA para engajamento nos comentários' : ''}\\n${$json.target_platform === 'linkedin' ? '- Tom profissional e educativo\\n- Incluir insights de negócio\\n- Estrutura com subtítulos\\n- CTA para discussão' : ''}\\n\\nCrie o conteúdo adaptado mantendo a essência da mensagem original mas otimizado para a nova plataforma.\\n\\nResposta em JSON:\\n{\\n  \\\"adapted_content\\\": \\\"texto adaptado aqui\\\",\\n  \\\"hashtags\\\": [\\\"hashtag1\\\", \\\"hashtag2\\\"],\\n  \\\"best_posting_time\\\": \\\"HH:MM\\\",\\n  \\\"engagement_prediction\\\": \\\"low/medium/high\\\",\\n  \\\"adaptation_notes\\\": \\\"mudanças feitas\\\"\\n}`,\n    task_type: 'complex_analysis',\n    calling_workflow_id: $workflow.id,\n    calling_node_id: 'adapt_content_for_platform'\n  }\n} }}", "options": {}}, "id": "adapt_content_for_platform", "name": "AI: Adapt Content for Platform", "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 2, "position": [860, 300]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "-- Buscar timing ótimo para o influenciador na plataforma de destino\nSELECT \n  ota.hour_of_day,\n  ota.day_of_week,\n  ota.engagement_score,\n  ota.confidence_level\nFROM agent.optimal_timing_analysis ota\nWHERE ota.influencer_id = $1\nAND ota.platform = $2\nAND ota.confidence_level > 0.5\nORDER BY ota.engagement_score DESC\nLIMIT 1;", "options": {"parameters": {"values": ["={{ $('webhook_trigger').item.json.influencer_id }}", "={{ $('loop_repurpose_rules').item.json.target_platform }}"]}}}, "id": "get_optimal_timing", "name": "Get Optimal Timing", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [1080, 300], "credentials": {"postgres": {"id": "postgres-main", "name": "PostgreSQL Main"}}}, {"parameters": {"assignments": {"assignments": [{"id": "schedule_time", "name": "schedule_time", "value": "={{ \n  const optimalHour = $('get_optimal_timing').first()?.json?.hour_of_day || 9;\n  const now = new Date();\n  const scheduleDate = new Date();\n  \n  // Agendar para o próximo horário ó<PERSON>\n  if (now.getHours() >= optimalHour) {\n    scheduleDate.setDate(scheduleDate.getDate() + 1);\n  }\n  scheduleDate.setHours(optimalHour, 0, 0, 0);\n  \n  return scheduleDate.toISOString();\n}}", "type": "string"}, {"id": "performance_prediction", "name": "performance_prediction", "value": "={{ \n  const baseScore = $('loop_repurpose_rules').item.json.success_rate * 100;\n  const timingBonus = $('get_optimal_timing').first()?.json?.confidence_level * 20 || 0;\n  const platformBonus = {\n    'linkedin': 15,\n    'twitter': 10, \n    'instagram': 12,\n    'tiktok': 8\n  }[$('loop_repurpose_rules').item.json.target_platform] || 5;\n  \n  return Math.round(baseScore + timingBonus + platformBonus);\n}}", "type": "number"}]}, "options": {}}, "id": "calculate_scheduling", "name": "Calculate Smart Scheduling", "type": "n8n-nodes-base.set", "typeVersion": 3.3, "position": [1300, 300]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "quality_check", "leftValue": "={{ $json.performance_prediction }}", "rightValue": 60, "operator": {"type": "number", "operation": "gte"}}]}}, "id": "quality_gate", "name": "Quality Gate (60+ Score)", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [1520, 300]}, {"parameters": {"method": "POST", "url": "https://api.bufferapp.com/v1/updates/create.json", "sendBody": true, "specifyBody": "json", "jsonBody": "={{ {\n  text: $('adapt_content_for_platform').item.json.adapted_content,\n  profile_ids: [$('loop_repurpose_rules').item.json.target_platform + '_profile_id'],\n  scheduled_at: $json.schedule_time,\n  media: {\n    link: $('webhook_trigger').item.json.original_url || null\n  }\n} }}", "options": {}}, "id": "schedule_repurposed_content", "name": "📅 Schedule Repurposed Content", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [1740, 300], "continueOnFail": true, "credentials": {"httpHeaderAuth": {"id": "buffer-api-main"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "-- Registrar con<PERSON><PERSON><PERSON> repurposed\nINSERT INTO agent.published_content (\n  influencer_id,\n  platform,\n  platform_post_id,\n  content_text,\n  status,\n  published_at,\n  engagement_metrics\n)\nVALUES (\n  $1,\n  $2,\n  $3,\n  $4,\n  'scheduled',\n  $5::timestamptz,\n  jsonb_build_object(\n    'predicted_score', $6,\n    'repurposed_from', $7,\n    'adaptation_type', $8\n  )\n)\nRETURNING *;", "options": {"parameters": {"values": ["={{ $('webhook_trigger').item.json.influencer_id }}", "={{ $('loop_repurpose_rules').item.json.target_platform }}", "={{ $('schedule_repurposed_content').item.json.id || 'scheduled_' + Date.now() }}", "={{ $('adapt_content_for_platform').item.json.adapted_content }}", "={{ $('calculate_scheduling').item.json.schedule_time }}", "={{ $('calculate_scheduling').item.json.performance_prediction }}", "={{ $('loop_repurpose_rules').item.json.source_platform }}", "={{ $('loop_repurpose_rules').item.json.rule_name }}"]}}}, "id": "log_repurposed_content", "name": "📝 Log Repurposed Content", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [1960, 300], "credentials": {"postgres": {"id": "postgres-main", "name": "PostgreSQL Main"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "-- Atualizar estatísticas da regra de repurposing\nUPDATE agent.repurposing_rules \nSET \n  usage_count = usage_count + 1,\n  last_used = NOW(),\n  success_rate = CASE \n    WHEN usage_count = 0 THEN 0.7\n    ELSE (success_rate * usage_count + 0.8) / (usage_count + 1)\n  END\nWHERE id = $1\nRETURNING *;", "options": {"parameters": {"values": ["={{ $('loop_repurpose_rules').item.json.id }}"]}}}, "id": "update_rule_stats", "name": "📊 Update Rule Statistics", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [2180, 300], "credentials": {"postgres": {"id": "postgres-main", "name": "PostgreSQL Main"}}}, {"parameters": {"method": "POST", "url": "={{ $vars.SLACK_WEBHOOK_URL }}", "sendBody": true, "specifyBody": "json", "jsonBody": "{\n  \"text\": \"❌ Conteúdo repurposed rejeitado por baixa qualidade\",\n  \"blocks\": [\n    {\n      \"type\": \"section\",\n      \"text\": {\n        \"type\": \"mrkdwn\",\n        \"text\": \"*❌ Quality Gate Failed*\\n\\nConteúdo não passou no filtro de qualidade:\\n\\n*Score de Performance:* {{ $('calculate_scheduling').item.json.performance_prediction }}/100\\n*Plataforma de Destino:* {{ $('loop_repurpose_rules').item.json.target_platform }}\\n*Regra Aplicada:* {{ $('loop_repurpose_rules').item.json.rule_name }}\\n\\n*Conteúdo Original:*\\n{{ $('webhook_trigger').item.json.original_content.substring(0, 200) }}...\\n\\n_Conteúdo não foi agendado devido à baixa previsão de performance._\"\n      }\n    }\n  ],\n  \"channel\": \"#content-quality\"\n}", "options": {}}, "id": "notify_quality_rejection", "name": "🚨 Notify Quality Rejection", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [1740, 500], "continueOnFail": true}, {"parameters": {"mode": "combine", "combinationMode": "mergeByPosition", "options": {}}, "id": "merge_loop_results", "name": "Merge Loop Results", "type": "n8n-nodes-base.merge", "typeVersion": 2.1, "position": [2400, 300]}, {"parameters": {"method": "POST", "url": "={{ $vars.SLACK_WEBHOOK_URL }}", "sendBody": true, "specifyBody": "json", "jsonBody": "{\n  \"text\": \"🔄 Repurposing Engine - Execução Completa\",\n  \"blocks\": [\n    {\n      \"type\": \"header\",\n      \"text\": {\n        \"type\": \"plain_text\",\n        \"text\": \"🔄 CROSS-PLATFORM REPURPOSING REPORT\"\n      }\n    },\n    {\n      \"type\": \"section\",\n      \"fields\": [\n        {\n          \"type\": \"mrkdwn\",\n          \"text\": \"*📝 Conteúdo Original:*\\n{{ $('webhook_trigger').item.json.original_content ? $('webhook_trigger').item.json.original_content.substring(0, 100) + '...' : 'N/A' }}\"\n        },\n        {\n          \"type\": \"mrkdwn\",\n          \"text\": \"*🎯 Plataforma de Origem:*\\n{{ $('webhook_trigger').item.json.source_platform || $('webhook_trigger').item.json.platform || 'N/A' }}\"\n        },\n        {\n          \"type\": \"mrkdwn\",\n          \"text\": \"*📊 Regras Aplicadas:*\\n{{ $('get_repurposing_rules').all().length }}\"\n        },\n        {\n          \"type\": \"mrkdwn\",\n          \"text\": \"*✅ Conteúdos Agendados:*\\n{{ $('log_repurposed_content').all().length }}\"\n        }\n      ]\n    },\n    {\n      \"type\": \"section\",\n      \"text\": {\n        \"type\": \"mrkdwn\",\n        \"text\": \"*🎯 Plataformas de Destino:*\\n{{ $('get_repurposing_rules').all().map(rule => `• ${rule.json.target_platform} (Score: ${rule.json.success_rate * 100}%)`).join('\\n') }}\"\n      }\n    },\n    {\n      \"type\": \"context\",\n      \"elements\": [\n        {\n          \"type\": \"mrkdwn\",\n          \"text\": \"Engine ativo 24/7 | Próximo conteúdo será processado automaticamente\"\n        }\n      ]\n    }\n  ],\n  \"channel\": \"#content-repurposing\"\n}", "options": {}}, "id": "notify_completion", "name": "📱 Notify Completion", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [2620, 300], "continueOnFail": true}], "connections": {"webhook_trigger": {"main": [[{"node": "get_repurposing_rules", "type": "main", "index": 0}]]}, "get_repurposing_rules": {"main": [[{"node": "loop_repurpose_rules", "type": "main", "index": 0}]]}, "loop_repurpose_rules": {"main": [[{"node": "adapt_content_for_platform", "type": "main", "index": 0}]]}, "adapt_content_for_platform": {"main": [[{"node": "get_optimal_timing", "type": "main", "index": 0}]]}, "get_optimal_timing": {"main": [[{"node": "calculate_scheduling", "type": "main", "index": 0}]]}, "calculate_scheduling": {"main": [[{"node": "quality_gate", "type": "main", "index": 0}]]}, "quality_gate": {"main": [[{"node": "schedule_repurposed_content", "type": "main", "index": 0}], [{"node": "notify_quality_rejection", "type": "main", "index": 0}]]}, "schedule_repurposed_content": {"main": [[{"node": "log_repurposed_content", "type": "main", "index": 0}]]}, "log_repurposed_content": {"main": [[{"node": "update_rule_stats", "type": "main", "index": 0}]]}, "update_rule_stats": {"main": [[{"node": "merge_loop_results", "type": "main", "index": 0}]]}, "notify_quality_rejection": {"main": [[{"node": "merge_loop_results", "type": "main", "index": 1}]]}, "merge_loop_results": {"main": [[{"node": "notify_completion", "type": "main", "index": 0}]]}}, "settings": {"executionOrder": "v1"}, "staticData": null, "tags": [{"createdAt": "2024-01-15T12:00:00.000Z", "updatedAt": "2024-01-15T12:00:00.000Z", "id": "repurposing", "name": "repurposing"}], "triggerCount": 1, "updatedAt": "2024-01-15T12:00:00.000Z", "versionId": "1"}