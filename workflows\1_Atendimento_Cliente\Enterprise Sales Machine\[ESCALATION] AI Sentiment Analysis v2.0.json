{"name": "[ESCALATION] 🤖 AI Sentiment Analysis v2.0", "nodes": [{"parameters": {"path": "ai-sentiment-analysis", "options": {"noResponseBody": false}}, "id": "sentiment-webhook", "name": "🤖 AI Analysis Request", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [180, 300], "webhookId": "ai-sentiment-analysis-webhook"}, {"parameters": {"jsCode": "// Validar e processar dados de entrada\nconst inputData = $json;\n\n// Validações básicas\nif (!inputData.message_content) {\n  throw new Error('message_content é obrigatório');\n}\n\n// Extrair dados principais\nconst messageContent = inputData.message_content;\nconst conversationHistory = inputData.conversation_history || [];\nconst customerContext = inputData.customer_context || {};\nconst urgencyIndicators = inputData.urgency_indicators || {};\n\n// Preparar contexto completo para análise\nconst fullContext = {\n  current_message: messageContent,\n  conversation_history: conversationHistory,\n  customer_info: {\n    previous_conversations: customerContext.previous_conversations || 0,\n    customer_tier: customerContext.customer_tier || 'standard',\n    account_age_days: customerContext.account_age_days || 0\n  },\n  urgency_context: {\n    keywords_found: urgencyIndicators.keywords_found || false,\n    response_time_hours: urgencyIndicators.response_time_hours || 0,\n    message_count: urgencyIndicators.message_count || 1,\n    priority_level: urgencyIndicators.priority_level || 'medium'\n  }\n};\n\n// Preparar texto combinado para análise\nlet combinedText = messageContent;\nif (conversationHistory.length > 0) {\n  const recentHistory = conversationHistory.slice(-3); // Últimas 3 mensagens\n  combinedText = recentHistory.join(' ') + ' ' + messageContent;\n}\n\n// Limitar tamanho do texto (máximo 2000 caracteres)\nif (combinedText.length > 2000) {\n  combinedText = combinedText.substring(0, 2000);\n}\n\n// Detectar idioma (simples detecção baseada em palavras comuns)\nconst portugueseWords = ['não', 'sim', 'obrigado', 'por favor', 'problema', 'ajuda'];\nconst englishWords = ['not', 'yes', 'thank', 'please', 'problem', 'help'];\n\nconst textLower = combinedText.toLowerCase();\nconst ptCount = portugueseWords.filter(word => textLower.includes(word)).length;\nconst enCount = englishWords.filter(word => textLower.includes(word)).length;\n\nconst detectedLanguage = ptCount > enCount ? 'pt' : 'en';\n\n// Preparar resultado\nconst result = {\n  analysis_id: require('crypto').randomUUID(),\n  input_data: inputData,\n  processed_context: fullContext,\n  analysis_text: combinedText,\n  detected_language: detectedLanguage,\n  text_length: combinedText.length,\n  processing_timestamp: new Date().toISOString(),\n  ready_for_ai: true\n};\n\nreturn result;"}, "id": "validate-and-prepare", "name": "✅ Validate & Prepare", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [400, 300]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT * FROM agent.ai_cache \nWHERE content_hash = $1 \nAND analysis_type = 'sentiment' \nAND created_at > NOW() - INTERVAL '24 hours'\nLIMIT 1", "additionalFields": {"values": ["={{ require('crypto').createHash('md5').update($json.analysis_text).digest('hex') }}"]}}, "id": "check-cache", "name": "💾 Check Cache", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [620, 300]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "cache-found", "leftValue": "={{ $json.length }}", "rightValue": 0, "operator": {"type": "number", "operation": "larger"}}], "combineOperation": "all"}}, "id": "cache-decision", "name": "🔍 Cache Found?", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [840, 300]}, {"parameters": {"jsCode": "// Retornar resultado do cache\nconst cacheData = $input.first().json[0];\nconst preparedData = $('validate-and-prepare').first().json;\n\n// Parsear resultado do cache\nconst cachedResult = typeof cacheData.result === 'string' ? \n  JSON.parse(cacheData.result) : cacheData.result;\n\n// Preparar resposta com dados do cache\nconst result = {\n  ...cachedResult,\n  analysis_id: preparedData.analysis_id,\n  from_cache: true,\n  cache_age_minutes: Math.floor((new Date() - new Date(cacheData.created_at)) / (1000 * 60)),\n  processed_at: new Date().toISOString()\n};\n\nreturn result;"}, "id": "return-cached-result", "name": "⚡ Return C<PERSON>d Result", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1060, 200]}, {"parameters": {"jsCode": "// Preparar prompt para análise de sentimento\nconst preparedData = $('validate-and-prepare').first().json;\nconst text = preparedData.analysis_text;\nconst language = preparedData.detected_language;\nconst context = preparedData.processed_context;\n\n// Prompt base em português\nconst promptPt = `Analise o sentimento e urgência da seguinte mensagem de atendimento ao cliente:\n\nMensagem: \"${text}\"\n\nContexto adicional:\n- Conversas anteriores do cliente: ${context.customer_info.previous_conversations}\n- Nível do cliente: ${context.customer_info.customer_tier}\n- Tempo de resposta atual: ${context.urgency_context.response_time_hours} horas\n- Número de mensagens na conversa: ${context.urgency_context.message_count}\n- Prioridade atual: ${context.urgency_context.priority_level}\n\nPor favor, forneça uma análise detalhada incluindo:\n1. Score de sentimento (-1.0 a 1.0, onde -1.0 é muito negativo e 1.0 é muito positivo)\n2. Emoção principal detectada (angry, frustrated, neutral, satisfied, happy)\n3. Nível de urgência (low, medium, high, critical)\n4. Confiança na análise (0.0 a 1.0)\n5. Palavras-chave que influenciaram a análise\n6. Recomendações de ação\n\nResponda APENAS em formato JSON válido.`;\n\n// Prompt base em inglês\nconst promptEn = `Analyze the sentiment and urgency of the following customer service message:\n\nMessage: \"${text}\"\n\nAdditional context:\n- Customer's previous conversations: ${context.customer_info.previous_conversations}\n- Customer tier: ${context.customer_info.customer_tier}\n- Current response time: ${context.urgency_context.response_time_hours} hours\n- Number of messages in conversation: ${context.urgency_context.message_count}\n- Current priority: ${context.urgency_context.priority_level}\n\nPlease provide a detailed analysis including:\n1. Sentiment score (-1.0 to 1.0, where -1.0 is very negative and 1.0 is very positive)\n2. Main emotion detected (angry, frustrated, neutral, satisfied, happy)\n3. Urgency level (low, medium, high, critical)\n4. Analysis confidence (0.0 to 1.0)\n5. Keywords that influenced the analysis\n6. Action recommendations\n\nRespond ONLY in valid JSON format.`;\n\n// Selecionar prompt baseado no idioma\nconst selectedPrompt = language === 'pt' ? promptPt : promptEn;\n\n// Preparar dados para a API\nconst apiRequest = {\n  model: process.env.OPENAI_MODEL || 'gpt-3.5-turbo',\n  messages: [\n    {\n      role: 'system',\n      content: language === 'pt' ? \n        'Você é um especialista em análise de sentimento para atendimento ao cliente. Sempre responda em JSON válido.' :\n        'You are an expert in sentiment analysis for customer service. Always respond in valid JSON.'\n    },\n    {\n      role: 'user',\n      content: selectedPrompt\n    }\n  ],\n  temperature: 0.3,\n  max_tokens: 500,\n  response_format: { type: 'json_object' }\n};\n\nreturn {\n  api_request: apiRequest,\n  analysis_id: preparedData.analysis_id,\n  input_text: text,\n  detected_language: language,\n  context_used: context,\n  prompt_length: selectedPrompt.length,\n  request_timestamp: new Date().toISOString()\n};"}, "id": "prepare-ai-prompt", "name": "📝 Prepare AI Prompt", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1060, 400]}, {"parameters": {"url": "https://api.openai.com/v1/chat/completions", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendBody": true, "specifyBody": "json", "jsonBody": "={{ JSON.stringify($json.api_request) }}", "options": {"timeout": 30000}}, "id": "call-openai-api", "name": "🧠 Call OpenAI API", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [1280, 400], "continueOnFail": true}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "api-success", "leftValue": "={{ $json.error }}", "rightValue": "", "operator": {"type": "string", "operation": "isEmpty"}}], "combineOperation": "all"}}, "id": "check-api-success", "name": "✅ API Success?", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [1500, 400]}, {"parameters": {"jsCode": "// Processar resposta da OpenAI\nconst apiResponse = $input.first().json;\nconst promptData = $('prepare-ai-prompt').first().json;\nconst preparedData = $('validate-and-prepare').first().json;\n\ntry {\n  // Extrair conteúdo da resposta\n  const content = apiResponse.choices[0].message.content;\n  const aiResult = JSON.parse(content);\n  \n  // Validar campos obrigatórios\n  const requiredFields = ['sentiment_score', 'emotion', 'urgency_level', 'confidence'];\n  const missingFields = requiredFields.filter(field => !(field in aiResult));\n  \n  if (missingFields.length > 0) {\n    throw new Error(`Campos obrigatórios ausentes: ${missingFields.join(', ')}`);\n  }\n  \n  // Normalizar valores\n  const normalizedResult = {\n    sentiment_score: Math.max(-1, Math.min(1, parseFloat(aiResult.sentiment_score) || 0)),\n    emotion: (aiResult.emotion || 'neutral').toLowerCase(),\n    urgency_level: (aiResult.urgency_level || 'medium').toLowerCase(),\n    confidence: Math.max(0, Math.min(1, parseFloat(aiResult.confidence) || 0.5)),\n    keywords: Array.isArray(aiResult.keywords) ? aiResult.keywords : [],\n    recommendations: Array.isArray(aiResult.recommendations) ? aiResult.recommendations : [],\n    raw_analysis: aiResult\n  };\n  \n  // Calcular métricas adicionais\n  const processingTime = Date.now() - new Date(promptData.request_timestamp).getTime();\n  const tokensUsed = apiResponse.usage ? apiResponse.usage.total_tokens : 0;\n  \n  // Preparar resultado final\n  const finalResult = {\n    analysis_id: promptData.analysis_id,\n    ...normalizedResult,\n    \n    // Metadados da análise\n    analysis_metadata: {\n      model_used: promptData.api_request.model,\n      detected_language: promptData.detected_language,\n      processing_time_ms: processingTime,\n      tokens_used: tokensUsed,\n      api_response_id: apiResponse.id,\n      created_at: new Date().toISOString()\n    },\n    \n    // Contexto original\n    input_context: preparedData.processed_context,\n    \n    // Status\n    success: true,\n    from_cache: false,\n    error: null\n  };\n  \n  return finalResult;\n  \n} catch (error) {\n  // Em caso de erro, retornar análise básica\n  return {\n    analysis_id: promptData.analysis_id,\n    sentiment_score: 0,\n    emotion: 'neutral',\n    urgency_level: 'medium',\n    confidence: 0.1,\n    keywords: [],\n    recommendations: ['Análise manual recomendada'],\n    \n    analysis_metadata: {\n      model_used: 'fallback',\n      detected_language: promptData.detected_language,\n      processing_time_ms: 0,\n      tokens_used: 0,\n      created_at: new Date().toISOString()\n    },\n    \n    input_context: preparedData.processed_context,\n    \n    success: false,\n    from_cache: false,\n    error: error.message,\n    fallback_used: true\n  };\n}"}, "id": "process-ai-response", "name": "⚙️ Process AI Response", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1720, 300]}, {"parameters": {"jsCode": "// Análise de fallback quando a API falha\nconst promptData = $('prepare-ai-prompt').first().json;\nconst preparedData = $('validate-and-prepare').first().json;\nconst apiError = $input.first().json;\n\n// Análise básica baseada em palavras-chave\nconst text = promptData.input_text.toLowerCase();\n\n// Palavras-chave para sentimento\nconst negativeWords = ['ruim', 'péssimo', 'horrível', 'problema', 'erro', 'falha', 'cancelar', 'reembolso', 'insatisfeito', 'bad', 'terrible', 'awful', 'problem', 'error', 'cancel', 'refund'];\nconst positiveWords = ['bom', 'ótimo', 'excelente', 'obrigado', 'parabéns', 'satisfeito', 'good', 'great', 'excellent', 'thank', 'satisfied'];\nconst urgentWords = ['urgente', 'emergência', 'imediato', 'agora', 'rápido', 'urgent', 'emergency', 'immediate', 'now', 'quick'];\n\n// Contar ocorrências\nconst negativeCount = negativeWords.filter(word => text.includes(word)).length;\nconst positiveCount = positiveWords.filter(word => text.includes(word)).length;\nconst urgentCount = urgentWords.filter(word => text.includes(word)).length;\n\n// Calcular sentimento básico\nlet sentimentScore = 0;\nif (negativeCount > positiveCount) {\n  sentimentScore = -0.5 - (negativeCount * 0.1);\n} else if (positiveCount > negativeCount) {\n  sentimentScore = 0.5 + (positiveCount * 0.1);\n}\nsentimentScore = Math.max(-1, Math.min(1, sentimentScore));\n\n// Determinar emoção\nlet emotion = 'neutral';\nif (sentimentScore < -0.5) emotion = 'angry';\nelse if (sentimentScore < -0.2) emotion = 'frustrated';\nelse if (sentimentScore > 0.5) emotion = 'happy';\nelse if (sentimentScore > 0.2) emotion = 'satisfied';\n\n// Determinar urgência\nlet urgencyLevel = 'medium';\nif (urgentCount > 0 || negativeCount > 2) urgencyLevel = 'high';\nelse if (urgentCount > 1 || negativeCount > 3) urgencyLevel = 'critical';\nelse if (positiveCount > negativeCount) urgencyLevel = 'low';\n\n// Preparar resultado de fallback\nconst fallbackResult = {\n  analysis_id: promptData.analysis_id,\n  sentiment_score: sentimentScore,\n  emotion: emotion,\n  urgency_level: urgencyLevel,\n  confidence: 0.3, // Baixa confiança para análise de fallback\n  keywords: [...negativeWords.filter(word => text.includes(word)), ...positiveWords.filter(word => text.includes(word)), ...urgentWords.filter(word => text.includes(word))],\n  recommendations: ['Análise manual recomendada devido à falha da IA'],\n  \n  analysis_metadata: {\n    model_used: 'keyword_fallback',\n    detected_language: promptData.detected_language,\n    processing_time_ms: 0,\n    tokens_used: 0,\n    created_at: new Date().toISOString()\n  },\n  \n  input_context: preparedData.processed_context,\n  \n  success: false,\n  from_cache: false,\n  error: apiError.error || 'API call failed',\n  fallback_used: true,\n  \n  fallback_analysis: {\n    negative_words_found: negativeCount,\n    positive_words_found: positiveCount,\n    urgent_words_found: urgentCount,\n    analysis_method: 'keyword_based'\n  }\n};\n\nreturn fallbackResult;"}, "id": "fallback-analysis", "name": "🔄 Fallback Analysis", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1720, 500]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "INSERT INTO agent.ai_cache (\n  content_hash, analysis_type, input_data, result,\n  model_used, tokens_used, processing_time_ms,\n  confidence_score, created_at\n) VALUES (\n  $1, 'sentiment', $2, $3, $4, $5, $6, $7, NOW()\n) ON CONFLICT (content_hash, analysis_type) DO UPDATE SET\n  result = EXCLUDED.result,\n  updated_at = NOW()", "additionalFields": {"values": ["={{ require('crypto').createHash('md5').update($('validate-and-prepare').first().json.analysis_text).digest('hex') }}", "={{ JSON.stringify($('validate-and-prepare').first().json.input_data) }}", "={{ JSON.stringify($json) }}", "={{ $json.analysis_metadata.model_used }}", "={{ $json.analysis_metadata.tokens_used }}", "={{ $json.analysis_metadata.processing_time_ms }}", "={{ $json.confidence }}"]}}, "id": "cache-result", "name": "💾 <PERSON><PERSON>", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [1940, 400], "continueOnFail": true}, {"parameters": {"operation": "execute<PERSON>uery", "query": "INSERT INTO agent.ai_analysis_results (\n  analysis_id, analysis_type, input_text, sentiment_score,\n  emotion, urgency_level, confidence_score, keywords,\n  recommendations, model_used, processing_time_ms,\n  tokens_used, success, error_message, metadata, created_at\n) VALUES (\n  $1, 'sentiment', $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, NOW()\n)", "additionalFields": {"values": ["={{ $json.analysis_id }}", "={{ $('validate-and-prepare').first().json.analysis_text }}", "={{ $json.sentiment_score }}", "={{ $json.emotion }}", "={{ $json.urgency_level }}", "={{ $json.confidence }}", "={{ JSON.stringify($json.keywords) }}", "={{ JSON.stringify($json.recommendations) }}", "={{ $json.analysis_metadata.model_used }}", "={{ $json.analysis_metadata.processing_time_ms }}", "={{ $json.analysis_metadata.tokens_used }}", "={{ $json.success }}", "={{ $json.error }}", "={{ JSON.stringify($json) }}"]}}, "id": "log-analysis-result", "name": "📊 Log Analysis Result", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [2160, 400]}, {"parameters": {"respondWith": "json", "responseBody": "={{ JSON.stringify($json) }}"}, "id": "return-analysis-result", "name": "📤 Return Analysis Result", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [2380, 400]}, {"parameters": {"respondWith": "json", "responseBody": "={{ JSON.stringify($json) }}"}, "id": "return-cached-response", "name": "⚡ Return Cached Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1280, 200]}, {"parameters": {"path": "ai-batch-analysis", "options": {"noResponseBody": false}}, "id": "batch-analysis-webhook", "name": "📦 Batch Analysis", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [180, 700], "webhookId": "ai-batch-analysis-webhook"}, {"parameters": {"jsCode": "// Processar múltiplas análises em lote\nconst batchData = $json;\n\nif (!Array.isArray(batchData.messages)) {\n  throw new Error('messages deve ser um array');\n}\n\nif (batchData.messages.length > 10) {\n  throw new Error('Máximo de 10 mensagens por lote');\n}\n\n// Preparar cada mensagem para análise\nconst processedMessages = batchData.messages.map((message, index) => ({\n  batch_id: batchData.batch_id || require('crypto').randomUUID(),\n  message_index: index,\n  analysis_id: require('crypto').randomUUID(),\n  message_content: message.content,\n  conversation_history: message.conversation_history || [],\n  customer_context: message.customer_context || {},\n  urgency_indicators: message.urgency_indicators || {},\n  metadata: message.metadata || {}\n}));\n\nreturn {\n  batch_id: batchData.batch_id || require('crypto').randomUUID(),\n  total_messages: processedMessages.length,\n  messages: processedMessages,\n  batch_created_at: new Date().toISOString()\n};"}, "id": "prepare-batch-analysis", "name": "📋 Prepare Batch Analysis", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [400, 700]}, {"parameters": {"batchSize": 1, "options": {}}, "id": "split-batch-messages", "name": "🔄 Split Batch Messages", "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [620, 700]}, {"parameters": {"url": "http://localhost:5678/webhook/ai-sentiment-analysis", "sendBody": true, "specifyBody": "json", "jsonBody": "={{ JSON.stringify($json) }}", "options": {"timeout": 30000}}, "id": "process-single-message", "name": "🔍 Process Single Message", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [840, 700]}, {"parameters": {"jsCode": "// Consolidar resultados do lote\nconst allResults = $input.all();\nconst batchData = $('prepare-batch-analysis').first().json;\n\n// Processar todos os resultados\nconst processedResults = allResults.map((result, index) => {\n  const analysisResult = typeof result.json === 'string' ? JSON.parse(result.json) : result.json;\n  \n  return {\n    message_index: index,\n    analysis_id: analysisResult.analysis_id,\n    sentiment_score: analysisResult.sentiment_score,\n    emotion: analysisResult.emotion,\n    urgency_level: analysisResult.urgency_level,\n    confidence: analysisResult.confidence,\n    success: analysisResult.success,\n    from_cache: analysisResult.from_cache,\n    processing_time_ms: analysisResult.analysis_metadata?.processing_time_ms || 0,\n    tokens_used: analysisResult.analysis_metadata?.tokens_used || 0\n  };\n});\n\n// Calcular estatísticas do lote\nconst successCount = processedResults.filter(r => r.success).length;\nconst cacheHits = processedResults.filter(r => r.from_cache).length;\nconst totalTokens = processedResults.reduce((sum, r) => sum + r.tokens_used, 0);\nconst avgConfidence = processedResults.reduce((sum, r) => sum + r.confidence, 0) / processedResults.length;\nconst avgSentiment = processedResults.reduce((sum, r) => sum + r.sentiment_score, 0) / processedResults.length;\n\n// Distribuição de urgência\nconst urgencyDistribution = processedResults.reduce((dist, r) => {\n  dist[r.urgency_level] = (dist[r.urgency_level] || 0) + 1;\n  return dist;\n}, {});\n\n// Distribuição de emoções\nconst emotionDistribution = processedResults.reduce((dist, r) => {\n  dist[r.emotion] = (dist[r.emotion] || 0) + 1;\n  return dist;\n}, {});\n\nconst batchResult = {\n  batch_id: batchData.batch_id,\n  total_messages: batchData.total_messages,\n  processed_messages: processedResults.length,\n  success_count: successCount,\n  success_rate: successCount / processedResults.length,\n  \n  // Estatísticas\n  statistics: {\n    cache_hits: cacheHits,\n    cache_hit_rate: cacheHits / processedResults.length,\n    total_tokens_used: totalTokens,\n    average_confidence: avgConfidence,\n    average_sentiment: avgSentiment,\n    urgency_distribution: urgencyDistribution,\n    emotion_distribution: emotionDistribution\n  },\n  \n  // Resultados individuais\n  results: processedResults,\n  \n  // Metadados\n  batch_processed_at: new Date().toISOString(),\n  processing_time_ms: Date.now() - new Date(batchData.batch_created_at).getTime()\n};\n\nreturn batchResult;"}, "id": "consolidate-batch-results", "name": "📊 Consolidate Batch Results", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1060, 700]}, {"parameters": {"respondWith": "json", "responseBody": "={{ JSON.stringify($json) }}"}, "id": "return-batch-results", "name": "📦 Return Batch Results", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1280, 700]}], "pinData": {}, "connections": {"sentiment-webhook": {"main": [[{"node": "validate-and-prepare", "type": "main", "index": 0}]]}, "validate-and-prepare": {"main": [[{"node": "check-cache", "type": "main", "index": 0}]]}, "check-cache": {"main": [[{"node": "cache-decision", "type": "main", "index": 0}]]}, "cache-decision": {"main": [[{"node": "return-cached-result", "type": "main", "index": 0}], [{"node": "prepare-ai-prompt", "type": "main", "index": 0}]]}, "return-cached-result": {"main": [[{"node": "return-cached-response", "type": "main", "index": 0}]]}, "prepare-ai-prompt": {"main": [[{"node": "call-openai-api", "type": "main", "index": 0}]]}, "call-openai-api": {"main": [[{"node": "check-api-success", "type": "main", "index": 0}]]}, "check-api-success": {"main": [[{"node": "process-ai-response", "type": "main", "index": 0}], [{"node": "fallback-analysis", "type": "main", "index": 0}]]}, "process-ai-response": {"main": [[{"node": "cache-result", "type": "main", "index": 0}]]}, "fallback-analysis": {"main": [[{"node": "cache-result", "type": "main", "index": 0}]]}, "cache-result": {"main": [[{"node": "log-analysis-result", "type": "main", "index": 0}]]}, "log-analysis-result": {"main": [[{"node": "return-analysis-result", "type": "main", "index": 0}]]}, "batch-analysis-webhook": {"main": [[{"node": "prepare-batch-analysis", "type": "main", "index": 0}]]}, "prepare-batch-analysis": {"main": [[{"node": "split-batch-messages", "type": "main", "index": 0}]]}, "split-batch-messages": {"main": [[{"node": "process-single-message", "type": "main", "index": 0}]]}, "process-single-message": {"main": [[{"node": "consolidate-batch-results", "type": "main", "index": 0}]]}, "consolidate-batch-results": {"main": [[{"node": "return-batch-results", "type": "main", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1", "saveManualExecutions": true, "callerPolicy": "workflowsFromSameOwner", "errorWorkflow": "error-handler-workflow"}, "versionId": "2.0", "meta": {"templateCredsSetupCompleted": true, "instanceId": "escalation-ai-sentiment-analysis"}, "id": "escalation-ai-sentiment-analysis-v2", "tags": [{"createdAt": "2024-01-15T10:00:00.000Z", "updatedAt": "2024-01-15T10:00:00.000Z", "id": "escalation", "name": "escalation"}, {"createdAt": "2024-01-15T10:00:00.000Z", "updatedAt": "2024-01-15T10:00:00.000Z", "id": "ai", "name": "ai"}, {"createdAt": "2024-01-15T10:00:00.000Z", "updatedAt": "2024-01-15T10:00:00.000Z", "id": "sentiment", "name": "sentiment"}]}