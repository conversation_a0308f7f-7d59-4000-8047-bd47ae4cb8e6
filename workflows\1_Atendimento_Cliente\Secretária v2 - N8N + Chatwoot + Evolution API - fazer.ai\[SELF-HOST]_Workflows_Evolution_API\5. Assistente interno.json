{"nodes": [{"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{ $('Receber Mensagem Telegram').item.json.message.voice }}", "rightValue": "", "operator": {"type": "object", "operation": "exists", "singleValue": true}, "id": "48feeb05-d1a0-45a7-9e6b-04faef4b5175"}], "combinator": "and"}, "renameOutput": true, "outputKey": "<PERSON><PERSON><PERSON>"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "8cf1a12e-bd0a-4ac6-a33c-6748438c9c8a", "leftValue": "={{ $('Receber Mensagem Telegram').item.json.message.text }}", "rightValue": "", "operator": {"type": "string", "operation": "exists", "singleValue": true}}], "combinator": "and"}, "renameOutput": true, "outputKey": "Texto"}]}, "options": {}}, "type": "n8n-nodes-base.switch", "typeVersion": 3.2, "position": [200, 180], "id": "0801f5bc-de6f-4cda-a612-4076eb4ecbef", "name": "Tipo de mensagem"}, {"parameters": {"resource": "audio", "operation": "transcribe", "options": {"language": "pt"}}, "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.8, "position": [700, 80], "id": "85e68a62-b6aa-45e7-a4fb-0e21261f76fa", "name": "Transcrever <PERSON>", "credentials": {}}, {"parameters": {"assignments": {"assignments": [{"id": "45efc554-3f74-4922-a2cf-f64d2ea084c0", "name": "mensagem", "value": "={{ $('Receber Mensagem Telegram').item.json.message.text }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [480, 280], "id": "a6a3d2d0-539e-456e-b996-cd01fd00317c", "name": "Set mensagem texto"}, {"parameters": {"assignments": {"assignments": [{"id": "0e95db55-1b2e-4762-a547-9481029291e9", "name": "mensagem", "value": "={{ $json.text }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [920, 80], "id": "e3c90ff2-74ed-4239-b3ac-793306f12a9b", "name": "Set mensagem áudio"}, {"parameters": {"content": "## Assistente interno\n", "height": 560, "width": 2120, "color": 5}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-340, 20], "id": "ab4a776a-66a3-4962-888d-d7b491512790", "name": "Sticky Note5"}, {"parameters": {"content": "[![fazer.ai](https://framerusercontent.com/images/HqY9djLTzyutSKnuLLqBr92KbM.png?scale-down-to=256)](https://fazer.ai?utm_source=n8n&utm_campaign=sec-ep2&utm_medium=evo-5)\n\n## Esse é um template faça você mesmo do canal\n## <PERSON> Moreira\n\n### Inscreva-se no nosso canal no YouTube\n[![YouTube Lucas Moreira](https://img.shields.io/youtube/channel/subscribers/UCtmp6SxzLscu0GRTbgM8FTw?style=flat-square&logo=youtube&label=Inscreva-se&color=f00)](https://youtube.com/@eulucassmoreira?si=0lH7hwX9pukjhmPQ)\n\n### Siga nosso GitHub\n[![GitHub fazer.ai](https://img.shields.io/badge/github-%23121011.svg?style=for-the-badge&logo=github&logoColor=white&label)](https://github.com/fazer-ai)\n", "height": 440, "width": 550, "color": 7}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-340, -440], "id": "82d7bcef-817b-4eee-921f-dd29ef0d94a9", "name": "Sticky Note11"}, {"parameters": {"content": "## Quer entender como funciona?\n\n\n### Assista o vídeo, deixe um like, e se inscreva no canal para ter acesso a mais workflows como esse!\n\n[![IMAGE ALT TEXT HERE](https://i1.ytimg.com/vi_webp/cvTWGNJGAu4/maxresdefault.webp)](https://www.youtube.com/watch?v=cvTWGNJGAu4)", "height": 440, "width": 500, "color": 7}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [220, -440], "id": "1fc712b9-13d2-4715-a729-a6c51e1d5d47", "name": "Sticky Note15"}, {"parameters": {"content": "![Evolution API](https://mintlify.s3.us-west-1.amazonaws.com/evolution/logo/dark.svg)", "height": 100, "width": 280, "color": 7}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-120, -140], "id": "95a86e92-3f4e-44d0-af32-f4d9c438d3a6", "name": "Sticky Note20"}, {"parameters": {"resource": "file", "fileId": "={{ $('Receber Mensagem Telegram').item.json.message.voice.file_id }}"}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [480, 80], "id": "1abe7b2f-9422-47bf-a860-617602ea4a34", "name": "Download áudio", "webhookId": "9b28d599-c57f-4ecf-94a1-6334f7dabf6d", "credentials": {}}, {"parameters": {"updates": ["message"], "additionalFields": {}}, "type": "n8n-nodes-base.telegramTrigger", "typeVersion": 1.2, "position": [-280, 180], "id": "351b7172-837c-4bd0-8932-1452c0c0ccaa", "name": "Receber Mensagem Telegram", "webhookId": "f2b29356-d5d3-4f5d-9ef1-273001c0a820", "credentials": {}}, {"parameters": {"assignments": {"assignments": [{"id": "0a3fa735-aced-46e9-b9de-1734f9f2b537", "name": "id_conta", "value": "={{ $json.account_id }}", "type": "string"}, {"id": "2c4f57f2-8175-43e9-8d59-2796b2ebf488", "name": "telegram_chat_id", "value": "={{ $('Receber Mensagem Telegram').item.json.message.chat.id }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-20, 180], "id": "4e1de6db-d161-4881-97e7-c67a30496361", "name": "Info"}, {"parameters": {"promptType": "define", "text": "={{ $json.mensagem }}", "options": {"systemMessage": "=Agora são {{ $now.format('FFFF') }}.\n\n## PAPEL\n\nVocê é um assistente interno de reagendamento no escritório, acionado diretamente por um profissional via mensagem para gerenciar situações de remarcação de consultas, incluir lembretes na lista de compras ou agendar tarefas.\n\n## OBJETIVO GERAL\n\n1. Reagendar consultas a pedido do profissional.  \n2. Adicionar lembretes na lista de compras quando solicitado.  \n3. Agendar tarefas a pedido do profissional\n\n\n## RESUMO DE RESPONSABILIDADES\n\n1. Reagendamento de leads  \n   - Acesse o Google Calendar por meio das ferramentas de evento para identificar as consultas afetadas.\n   - Extraia o número de telefone e o ID da conversa na descrição do evento.\n   - Use a ferramenta \"Enviar_reagendamento\" para enviar mensagens de reagendamento aos pacientes.\n   - Envie a pergunta de preferência de nova data e horário.\n2. Lista de compras do escritório  \n   - Se o profissional solicitar a inclusão de um item na lista de compras, utilize a ferramenta \"Criar_tarefa\" para adicionar o lembrete.\n3. Agendamento de tarefas do escritório\n   - Se o profissional solicitar o agendamento de uma tarefa na agenda do Google Calendar, utilize a ferramenta \"Criar_tarefa\" para adicionar o lembrete.\n4. Ler e resumir lista de emails\n   - Se o profissional solicitar informação sobre os emails recebidos, use a ferramenta \"Ler_emails\" e resuma as informações\n\n## ORIENTAÇÕES DE LINGUAGEM E PROCEDIMENTO\n\n- Use uma abordagem empática, profissional e acolhedora.\n- Nunca envie mensagens para pacientes sem autorização explícita do profissional.\n- Quando listar eventos ou tarefas, seja objetivo e organizado.\n- Mantenha clareza e concisão em todas as interações.\n\n## PROFISSIONAIS E ESPECIALIDADES\n\nSegue o nome dos profissionais, suas especialidades, e o ID da agenda que deve ser usado nas ferramentas Google Calendar\n\n**MUITO IMPORTANTE!! O ID DA AGENDA INCLUI O \"@group.calendar.google.com\". NÃO OMITA AO UTILIZAR AS FERRAMENTAS**\n\n- Dr. João Paulo Ferreira - Médico - Clinico Geral (<EMAIL>)\n- Dr. Roberto Almeida - Médico - Cardiologia (<EMAIL>)\n- Dra. Ana Silva - Dentista - Clínica Geral (<EMAIL>)\n- Dra. Carla Mendes - Dentista - Odontopediatria (<EMAIL>)\n\n## IMPORTANTE\n\n- Use a ferramenta \"Refletir\" antes e depois de realizar operações complexas, para ter certeza de que deu tudo certo.\n- SEMPRE QUE ENVIAR UMA MENSAGEM PARA O PACIENTE, **USE A FERRAMENTA \"Salvar_memoria\"**. ISSO É MUITO IMPORTANTE, NÃO FAÇA ERRADO POR FAVOR.\n\n## INSTRUÇÕES FINAIS\n\n- Atenda exclusivamente às solicitações de reagendamento e inclusão de lembretes.\n- A remarcação de consultas ocorre somente quando o profissional pede, utilizando as ferramentas de evento para identificar os pacientes e a ferramenta \"Enviar_reagendamento\" para enviar a mensagem.\n- Para a lista de compras e lembretes, sempre use \"Criar_tarefa\".\n- Para a sua resposta que será enviada para o profissional que fez a soliticação, **NÃO UTILIZE FORMATAÇÃO MARKDOWN**.\n"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.8, "position": [1200, 180], "id": "eec99543-5f8c-4449-b372-1df7cc611d59", "name": "Assistente do escritório interno"}, {"parameters": {"chatId": "={{ $('Info').item.json.telegram_chat_id }}", "text": "={{ $json.output }}", "additionalFields": {}}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [1560, 180], "id": "9470cffe-7522-452a-80a8-45b08b651f1f", "name": "Responder Telegram", "webhookId": "21855174-4f7f-49f5-b8f4-a284d6ee4ddf", "credentials": {}}, {"parameters": {"modelName": "models/gemini-2.5-pro-preview-03-25", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "typeVersion": 1, "position": [680, 420], "id": "8e1b777a-7179-404f-8983-9f274108c477", "name": "Google Gemini Chat Model", "credentials": {}}, {"parameters": {"sessionIdType": "customKey", "sessionKey": "assistente_interno", "tableName": "n8n_historico_mensagens", "contextWindowLength": 10}, "type": "@n8n/n8n-nodes-langchain.memoryPostgresChat", "typeVersion": 1.3, "position": [820, 420], "id": "c0406347-cc71-48cf-b4e6-c3856ae3f3ac", "name": "Postgres Chat Memory", "credentials": {}}, {"parameters": {"descriptionType": "manual", "toolDescription": "Salva a informação de agendamento enviada, para que a secretária saiba que foi enviada.", "schema": {"__rl": true, "mode": "list", "value": "public"}, "table": {"__rl": true, "value": "n8n_historico_mensagens", "mode": "list"}, "columns": {"mappingMode": "defineBelow", "value": {"session_id": "={{ $fromAI('telefone', 'Telefone do paciente, formatado com apenas números, incluindo código do país. Ex.: \"551112345678\"', 'string') }}", "message": "={ \"type\": \"ai\", \"content\": \"{{ $fromAI('message', 'A mesma mensagem enviada para o paciente.', 'string') }}\" }"}, "matchingColumns": ["id"], "schema": [{"id": "id", "displayName": "id", "required": false, "defaultMatch": true, "display": true, "type": "number", "canBeUsedToMatch": true, "removed": true}, {"id": "session_id", "displayName": "session_id", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "message", "displayName": "message", "required": true, "defaultMatch": false, "display": true, "type": "object", "canBeUsedToMatch": true}, {"id": "created_at", "displayName": "created_at", "required": false, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": true, "removed": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.postgresTool", "typeVersion": 2.6, "position": [960, 420], "id": "633f7a40-ab1e-4a80-a0dc-b5cb197fdc51", "name": "<PERSON>var memoria", "credentials": {}}, {"parameters": {"sseEndpoint": "https://n8n.fazer.ai/mcp/a2b7a3ee-767c-4689-a8fa-765080bea169/sse"}, "type": "@n8n/n8n-nodes-langchain.mcpClientTool", "typeVersion": 1, "position": [1080, 420], "id": "ed430f0a-d070-42bf-82c8-05cba5539b4f", "name": "MCP Google Calendar"}, {"parameters": {"task": "YXdwUHJaRVoxWDdudFNGNg", "title": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Title', ``, 'string') }}", "additionalFields": {"dueDate": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Due_Date', ``, 'string') }}", "notes": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Notes', ``, 'string') }}", "status": "needsAction"}}, "type": "n8n-nodes-base.googleTasksTool", "typeVersion": 1, "position": [1360, 420], "id": "fcb1a7a0-8192-48ef-8033-4b1fd43f0f1a", "name": "<PERSON><PERSON><PERSON> tarefa", "credentials": {}}, {"parameters": {"operation": "getAll", "limit": 5, "filters": {}}, "type": "n8n-nodes-base.gmailTool", "typeVersion": 2.1, "position": [1500, 420], "id": "beb6847a-43fe-464a-a53b-e6cd366ba98e", "name": "Ler emails", "webhookId": "dde17a90-60a1-42ad-9a09-06cf2fbb3105", "credentials": {}}, {"parameters": {}, "type": "@n8n/n8n-nodes-langchain.toolThink", "typeVersion": 1, "position": [1640, 420], "id": "3f67a630-8463-4b3c-97a5-388d5b6c1743", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"parameters": {"toolDescription": "Use essa ferramenta para enviar as informações de agendamento no WhatsApp.\n\nO telefone deve ser formatado com apenas números, incluindo o código do país.\n\nExemplo: \"551112345678\"", "method": "POST", "url": "={{ $('Info').item.json.url_evolution }}/message/sendText/{{ $('Info').item.json.instancia }}", "authentication": "predefinedCredentialType", "nodeCredentialType": "evolutionApi", "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"number\": \"{telefone}\",\n  \"text\": \"{mensagem}\"\n}"}, "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "typeVersion": 1.1, "position": [1220, 420], "id": "69443fb6-6561-45b6-97fb-b3c906b3fc27", "name": "Enviar reagendamento", "credentials": {}}], "connections": {"Tipo de mensagem": {"main": [[{"node": "Download áudio", "type": "main", "index": 0}], [{"node": "Set mensagem texto", "type": "main", "index": 0}]]}, "Transcrever áudio": {"main": [[{"node": "Set mensagem áudio", "type": "main", "index": 0}]]}, "Set mensagem texto": {"main": [[{"node": "Assistente do escritório interno", "type": "main", "index": 0}]]}, "Set mensagem áudio": {"main": [[{"node": "Assistente do escritório interno", "type": "main", "index": 0}]]}, "Download áudio": {"main": [[{"node": "Transcrever <PERSON>", "type": "main", "index": 0}]]}, "Receber Mensagem Telegram": {"main": [[{"node": "Info", "type": "main", "index": 0}]]}, "Info": {"main": [[{"node": "Tipo de mensagem", "type": "main", "index": 0}]]}, "Assistente do escritório interno": {"main": [[{"node": "Responder Telegram", "type": "main", "index": 0}]]}, "Google Gemini Chat Model": {"ai_languageModel": [[{"node": "Assistente do escritório interno", "type": "ai_languageModel", "index": 0}]]}, "Postgres Chat Memory": {"ai_memory": [[{"node": "Assistente do escritório interno", "type": "ai_memory", "index": 0}]]}, "Salvar memoria": {"ai_tool": [[{"node": "Assistente do escritório interno", "type": "ai_tool", "index": 0}]]}, "MCP Google Calendar": {"ai_tool": [[{"node": "Assistente do escritório interno", "type": "ai_tool", "index": 0}]]}, "Criar tarefa": {"ai_tool": [[{"node": "Assistente do escritório interno", "type": "ai_tool", "index": 0}]]}, "Ler emails": {"ai_tool": [[{"node": "Assistente do escritório interno", "type": "ai_tool", "index": 0}]]}, "Refletir": {"ai_tool": [[{"node": "Assistente do escritório interno", "type": "ai_tool", "index": 0}]]}, "Enviar reagendamento": {"ai_tool": [[{"node": "Assistente do escritório interno", "type": "ai_tool", "index": 0}]]}}, "pinData": {}, "meta": {}}