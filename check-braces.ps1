# Script para verificar balanceamento de chaves no bloco específico
param(
    [string]$FilePath = "Start-Environment.ps1",
    [int]$StartLine = 415,
    [int]$EndLine = 470
)

$content = Get-Content $FilePath
$lines = $content[($StartLine-1)..($EndLine-1)]

$openBraces = 0
$closeBraces = 0
$lineNumber = $StartLine

foreach ($line in $lines) {
    $openCount = ($line.ToCharArray() | Where-Object { $_ -eq '{' }).Count
    $closeCount = ($line.ToCharArray() | Where-Object { $_ -eq '}' }).Count
    
    $openBraces += $openCount
    $closeBraces += $closeCount
    
    if ($openCount -gt 0 -or $closeCount -gt 0) {
        Write-Host "Linha $lineNumber`: { = $openCount, } = $closeCount (Total: { = $openBraces, } = $closeBraces)"
    }
    
    $lineNumber++
}

Write-Host "`nResumo do bloco (linhas $StartLine-$EndLine):"
Write-Host "Chaves de abertura: $openBraces"
Write-Host "Chaves de fechamento: $closeBraces"
Write-Host "Diferença: $($openBraces - $closeBraces)"

if ($openBraces -ne $closeBraces) {
    Write-Host "❌ PROBLEMA: Chaves não balanceadas!" -ForegroundColor Red
    if ($openBraces -gt $closeBraces) {
        Write-Host "Faltam $($openBraces - $closeBraces) chave(s) de fechamento }" -ForegroundColor Yellow
    } else {
        Write-Host "Há $($closeBraces - $openBraces) chave(s) de fechamento } em excesso" -ForegroundColor Yellow
    }
} else {
    Write-Host "✅ Chaves balanceadas neste bloco" -ForegroundColor Green
}