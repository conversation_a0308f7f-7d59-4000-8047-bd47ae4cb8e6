{"name": "[LOG] AI Usage Logger", "nodes": [{"parameters": {"workflowId": "={{ $env.EXECUTE_WORKFLOW_ID }}"}, "id": "start-node", "name": "Start", "type": "n8n-nodes-base.start", "typeVersion": 1, "position": [440, 440]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "INSERT INTO agent.ai_usage_logs (request_id, calling_workflow_id, calling_node_id, task_type, provider, model_used, prompt_tokens, completion_tokens, total_tokens, estimated_cost, response_time_ms) VALUES (MD5(random()::text || clock_timestamp()::text), '{{$json.body.calling_workflow_id}}', '{{$json.body.calling_node_id}}', '{{$json.body.task_type}}', '{{$json.body.provider}}', '{{$json.body.model_used}}', {{$json.body.prompt_tokens}}, {{$json.body.completion_tokens}}, {{$json.body.total_tokens}}, {{$json.body.estimated_cost}}, {{$json.body.response_time_ms}});", "options": {}}, "id": "db-insert-log", "name": "DB: Insert Log", "type": "n8n-nodes-base.postgres", "typeVersion": 3.1, "position": [640, 440], "credentials": {"postgres": {"id": "1", "name": "Postgres"}}}], "connections": {"start-node": {"main": [[{"node": "db-insert-log", "type": "main", "index": 0}]]}}, "pinData": {}}