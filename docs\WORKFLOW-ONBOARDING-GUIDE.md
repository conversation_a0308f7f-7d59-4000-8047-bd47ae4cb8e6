# Guia do Sistema de Onboarding de Workflows

## 📋 Visão Geral

O **Sistema de Onboarding de Workflows** é uma solução modular que permite configurar workflows específicos do n8n sobre uma infraestrutura já provisionada, mantendo total separação entre a camada de infraestrutura e a camada de aplicação.

## 🏗️ Arquitetura

### Separação de Responsabilidades

```
┌─────────────────────────────────────────────┐
│          Camada de Aplicação                │
│     (Workflows e Configurações)             │
│                                             │
│  • Onboard-Workflow.ps1                     │
│  • Esquemas SQL específicos                 │
│  • Configurações de workflow                │
└─────────────────────────────────────────────┘
                    ▲
                    │
                    │ Configura
                    │
┌─────────────────────────────────────────────┐
│         Camada de Infraestrutura            │
│        (Serviços Base Genéricos)            │
│                                             │
│  • Start-Environment.ps1                    │
│  • PostgreSQL, Redis, n8n, MinIO           │
│  • Bancos de dados vazios                   │
└─────────────────────────────────────────────┘
```

### Princípios Fundamentais

1. **Não-Intrusividade**: O script de infraestrutura (`Start-Environment.ps1`) permanece 100% genérico e agnóstico a workflows
2. **Modularidade**: Cada workflow é um pacote autocontido com suas próprias dependências
3. **Escalabilidade**: Novos workflows podem ser adicionados sem modificar a infraestrutura base
4. **Clareza**: Processo em duas etapas claras e distintas

## 🚀 Como Usar

### Passo 1: Iniciar a Infraestrutura Base

```powershell
.\Start-Environment.ps1
```

Este comando:
- Provisiona todos os serviços base (PostgreSQL, Redis, n8n, etc.)
- Cria bancos de dados vazios
- Gera credenciais de acesso
- Exibe o dashboard de serviços

### Passo 2: Configurar um Workflow Específico

```powershell
.\Onboard-Workflow.ps1 -WorkflowName "Enterprise Sales Machine"
```

Este comando:
- Aplica esquemas SQL específicos do workflow
- Exibe guias de configuração necessárias
- Prepara o ambiente para o workflow selecionado

### Parâmetros Disponíveis

```powershell
.\Onboard-Workflow.ps1 `
    -WorkflowName "NomeDoWorkflow" `      # Obrigatório: Nome do workflow
    -TargetDatabase "n8n_fila" `          # Opcional: Banco de dados alvo (padrão: n8n_fila)
    -PostgresContainer "postgres_aula"     # Opcional: Nome do contêiner PostgreSQL
```

## 📁 Estrutura de Diretórios

```
projeto/
├── Start-Environment.ps1           # Script de infraestrutura (não modificar!)
├── Onboard-Workflow.ps1           # Script de onboarding de workflows
├── PowerShellModules/
│   └── AutomationUtils.psm1       # Módulo de funções utilitárias
└── workflows/
    └── 1_Atendimento_Cliente/
        └── Enterprise Sales Machine/
            ├── sql/                # Esquemas SQL do workflow
            │   ├── 01-agent-schema.sql
            │   ├── 02-campaigns-schema.sql
            │   └── ...
            ├── config/             # Configurações e variáveis
            │   └── escalation-agent-config.json
            └── *.json             # Arquivos de workflow do n8n
```

## 🔧 Estrutura de um Workflow

Para que um workflow seja compatível com o sistema de onboarding, ele deve seguir esta estrutura:

```
workflows/categoria/NomeDoWorkflow/
├── sql/                    # (Opcional) Scripts SQL
│   ├── 01-schemas.sql     # Numerados para ordem de execução
│   └── 02-tables.sql
├── config/                 # (Opcional) Guias de configuração
│   ├── .env.example       # Variáveis de ambiente necessárias
│   └── config.json        # Configurações do workflow
└── *.json                 # Arquivos de workflow do n8n
```

## 📝 Exemplos de Uso

### Configurar o Enterprise Sales Machine

```powershell
# Passo 1: Iniciar infraestrutura
.\Start-Environment.ps1

# Passo 2: Configurar o workflow
.\Onboard-Workflow.ps1 -WorkflowName "Enterprise Sales Machine"
```

### Listar Workflows Disponíveis

Se você tentar configurar um workflow inexistente, o script listará todos os disponíveis:

```powershell
.\Onboard-Workflow.ps1 -WorkflowName "WorkflowInexistente"
```

### Usar um Banco de Dados Diferente

```powershell
.\Onboard-Workflow.ps1 `
    -WorkflowName "Marketing Automation" `
    -TargetDatabase "marketing_db"
```

## 🎯 Fluxo de Execução

1. **Validação**: Verifica se o workflow existe
2. **Pré-requisitos**: Confirma que PostgreSQL está rodando
3. **Esquema SQL**: 
   - Lista arquivos SQL em ordem alfabética
   - Aplica cada arquivo no banco de dados
   - Exibe progresso e erros detalhados
4. **Configuração**:
   - Procura arquivos de configuração
   - Extrai variáveis de ambiente necessárias
   - Exibe instruções de configuração manual
5. **Resumo**: Mostra próximos passos

## ⚠️ Tratamento de Erros

O sistema possui tratamento robusto de erros:

- **Workflow não encontrado**: Lista workflows disponíveis
- **PostgreSQL não rodando**: Instrui a executar `Start-Environment.ps1`
- **Erros SQL**: Pergunta se deve continuar ou abortar
- **Arquivos faltantes**: Continua com avisos apropriados

## 🔍 Saída do Script

### Durante a Execução

```
📋 Verificando pré-requisitos...
✅ Todos os pré-requisitos atendidos!

🗄️  Aplicando esquema SQL do workflow...
📑 Encontrados 32 arquivos SQL:

[1/32] Aplicando: 01-agent-schema.sql
   ✅ Aplicado com sucesso!
   📝 Avisos:
      NOTICE: table "agents" does not exist, skipping
```

### Guia de Configuração

```
╔══════════════════════════════════════════════════════════╗
║      📋 AÇÕES MANUAIS DE CONFIGURAÇÃO NECESSÁRIAS       ║
╚══════════════════════════════════════════════════════════╝

🔧 Variáveis de ambiente necessárias:
   • DB_HOST
   • DB_PORT
   • OPENAI_API_KEY
   • CHATWOOT_API_TOKEN
   • SLACK_BOT_TOKEN
```

## 🎨 Personalizações

### Adicionar Novo Workflow

1. Crie a estrutura de diretórios em `workflows/`
2. Adicione arquivos SQL numerados em `sql/`
3. Adicione configurações em `config/`
4. Execute `Onboard-Workflow.ps1` com o nome do workflow

### Customizar Comportamento

O script pode ser estendido facilmente:

```powershell
# No Onboard-Workflow.ps1, adicione nova lógica após a seção SQL
if (Test-Path (Join-Path $workflowFullPath "migrations")) {
    # Lógica para aplicar migrações
}
```

## 📊 Benefícios da Arquitetura

1. **Manutenibilidade**: Infraestrutura e workflows evoluem independentemente
2. **Reutilização**: Mesma infraestrutura suporta múltiplos workflows
3. **Testabilidade**: Workflows podem ser testados isoladamente
4. **Versionamento**: Cada workflow pode ter seu próprio ciclo de vida
5. **Colaboração**: Times podem trabalhar em workflows sem conflitos

## 🚨 Notas Importantes

- **Nunca modifique** `Start-Environment.ps1` para adicionar lógica de workflow
- **Sempre execute** na ordem: Infraestrutura → Onboarding
- **Documente** as dependências do seu workflow em `config/`
- **Teste** os scripts SQL antes de adicionar ao workflow

## 📚 Referências

- [PowerShell Documentation](https://docs.microsoft.com/powershell/)
- [n8n Workflow Documentation](https://docs.n8n.io/)
- [Docker Compose Reference](https://docs.docker.com/compose/)

---

**Versão**: 1.0  
**Autor**: Sistema de Automação  
**Licença**: MIT 