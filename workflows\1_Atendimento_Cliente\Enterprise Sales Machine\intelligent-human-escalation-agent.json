{"name": "Intelligent Human Escalation Agent v1.0", "nodes": [{"parameters": {"httpMethod": "POST", "path": "escalate-intelligent", "options": {"noResponseBody": false}, "responseMode": "responseNode"}, "id": "webhook-trigger", "name": "🚨 Webhook Trigger - Escalation Request", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [200, 300], "webhookId": "intelligent-escalation-webhook"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "contact-id-validation", "leftValue": "={{ $json.contact_id }}", "rightValue": "", "operator": {"type": "string", "operation": "exists", "singleValue": true}}, {"id": "escalation-reason-validation", "leftValue": "={{ $json.escalation_reason }}", "rightValue": 10, "operator": {"type": "string", "operation": "lengthGte"}}, {"id": "urgency-level-validation", "leftValue": "={{ $json.urgency_level }}", "rightValue": "low,medium,high,critical", "operator": {"type": "string", "operation": "contains"}}], "combinator": "and"}, "options": {}}, "id": "data-validation", "name": "✅ Data Validation", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [400, 300]}, {"parameters": {"respondWith": "json", "responseBody": "={\n  \"success\": false,\n  \"error\": \"VALIDATION_ERROR\",\n  \"message\": \"Dados de entrada inválidos\",\n  \"details\": {\n    \"contact_id\": \"{{ $json.contact_id ? 'válido' : 'obrigatório' }}\",\n    \"escalation_reason\": \"{{ ($json.escalation_reason && $json.escalation_reason.length >= 10) ? 'válido' : 'mínimo 10 caracteres' }}\",\n    \"urgency_level\": \"{{ ['low','medium','high','critical'].includes($json.urgency_level) ? 'válido' : 'deve ser: low, medium, high ou critical' }}\"\n  },\n  \"timestamp\": \"{{ new Date().toISOString() }}\"\n}", "options": {"responseCode": 400}}, "id": "validation-error-response", "name": "❌ Validation Error Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [600, 500]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "INSERT INTO intelligent_escalation.escalations (\n  organization_id,\n  external_contact_id,\n  escalation_reason,\n  urgency_level,\n  status,\n  context_data,\n  conversation_history,\n  sla_response_deadline,\n  sla_resolution_deadline\n) VALUES (\n  '{{ $('webhook-trigger').item.json.organization_id || 'f47ac10b-58cc-4372-a567-0e02b2c3d479' }}',\n  '{{ $('webhook-trigger').item.json.contact_id }}',\n  '{{ $('webhook-trigger').item.json.escalation_reason }}',\n  '{{ $('webhook-trigger').item.json.urgency_level || 'medium' }}',\n  'analyzing',\n  '{{ JSON.stringify($('webhook-trigger').item.json.context_data || {}) }}',\n  '{{ JSON.stringify($('webhook-trigger').item.json.conversation_history || []) }}',\n  NOW() + INTERVAL '{{ $('webhook-trigger').item.json.urgency_level === 'critical' ? '15' : $('webhook-trigger').item.json.urgency_level === 'high' ? '60' : $('webhook-trigger').item.json.urgency_level === 'medium' ? '240' : '1440' }} minutes',\n  NOW() + INTERVAL '{{ $('webhook-trigger').item.json.urgency_level === 'critical' ? '2' : $('webhook-trigger').item.json.urgency_level === 'high' ? '8' : $('webhook-trigger').item.json.urgency_level === 'medium' ? '24' : '72' }} hours'\n) RETURNING id, created_at;", "options": {}}, "id": "create-escalation-record", "name": "📝 Create Escalation Record", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [600, 300], "credentials": {"postgres": {"id": "postgres-main-db", "name": "PostgreSQL - Main Database"}}}, {"parameters": {"method": "POST", "url": "{{ $vars.OPENAI_API_URL || 'https://api.openai.com/v1/chat/completions' }}", "authentication": "predefinedCredentialType", "nodeCredentialType": "openAiApi", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "model", "value": "gpt-4-turbo-preview"}, {"name": "messages", "value": "=[\n  {\n    \"role\": \"system\",\n    \"content\": \"Você é um especialista em análise de contexto para escalação de atendimento. Analise o contexto fornecido e retorne um JSON com: sentiment_score (0-100), urgency_indicators (array), complexity_level (1-5), specialization_match (array), confidence_score (0-100), recommended_action (string).\"\n  },\n  {\n    \"role\": \"user\",\n    \"content\": \"Analise esta escalação:\\n\\nMotivo: {{ $('webhook-trigger').item.json.escalation_reason }}\\nUrgência declarada: {{ $('webhook-trigger').item.json.urgency_level }}\\nContexto: {{ JSON.stringify($('webhook-trigger').item.json.context_data || {}) }}\\nHistórico: {{ JSON.stringify($('webhook-trigger').item.json.conversation_history || []) }}\"\n  }\n]"}, {"name": "temperature", "value": 0.3}, {"name": "max_tokens", "value": 1000}]}, "options": {"timeout": 30000, "retry": {"enabled": true, "maxTries": 3}}}, "id": "ai-context-analysis", "name": "🤖 AI Context Analysis", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [800, 300], "credentials": {"openAiApi": {"id": "openai-main-api", "name": "OpenAI API - Main"}}}, {"parameters": {"jsCode": "// Parse AI response and extract analysis\nconst aiResponse = $input.first().json;\nconst escalationData = $('webhook-trigger').first().json;\nconst escalationRecord = $('create-escalation-record').first().json;\n\nlet aiAnalysis = {};\ntry {\n  // Try to parse AI response content\n  const content = aiResponse.choices[0].message.content;\n  aiAnalysis = JSON.parse(content);\n} catch (error) {\n  console.error('Error parsing AI response:', error);\n  // Fallback analysis\n  aiAnalysis = {\n    sentiment_score: 50,\n    urgency_indicators: ['manual_escalation'],\n    complexity_level: 3,\n    specialization_match: ['general_support'],\n    confidence_score: 60,\n    recommended_action: 'route_to_general_support'\n  };\n}\n\n// Determine specialization based on analysis\nlet recommendedSpecialization = 'general_support';\nif (aiAnalysis.specialization_match && aiAnalysis.specialization_match.length > 0) {\n  recommendedSpecialization = aiAnalysis.specialization_match[0];\n}\n\n// Override based on keywords in escalation reason\nconst reason = escalationData.escalation_reason.toLowerCase();\nif (reason.includes('billing') || reason.includes('payment') || reason.includes('invoice')) {\n  recommendedSpecialization = 'billing_support';\n} else if (reason.includes('technical') || reason.includes('bug') || reason.includes('error')) {\n  recommendedSpecialization = 'technical_support';\n} else if (reason.includes('sales') || reason.includes('purchase') || reason.includes('quote')) {\n  recommendedSpecialization = 'sales_support';\n}\n\n// Adjust urgency based on AI analysis\nlet finalUrgency = escalationData.urgency_level || 'medium';\nif (aiAnalysis.sentiment_score < 30 && aiAnalysis.urgency_indicators.length > 2) {\n  finalUrgency = 'high';\n}\nif (aiAnalysis.sentiment_score < 20) {\n  finalUrgency = 'critical';\n}\n\nreturn [{\n  escalation_id: escalationRecord.id,\n  ai_analysis: aiAnalysis,\n  recommended_specialization: recommendedSpecialization,\n  final_urgency: finalUrgency,\n  processing_timestamp: new Date().toISOString(),\n  original_data: escalationData\n}];"}, "id": "process-ai-analysis", "name": "🧠 Process AI Analysis", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1000, 300]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT \n  a.id,\n  a.name,\n  a.email,\n  a.current_escalation_count,\n  a.max_concurrent_escalations,\n  a.performance_score,\n  s.proficiency_level\nFROM intelligent_escalation.human_agents a\nJOIN intelligent_escalation.agent_specializations s ON a.id = s.agent_id\nJOIN intelligent_escalation.escalation_queues q ON s.queue_id = q.id\nWHERE q.specialization = '{{ $json.recommended_specialization }}'\n  AND a.status = 'available'\n  AND a.is_active = true\n  AND a.current_escalation_count < a.max_concurrent_escalations\nORDER BY \n  s.proficiency_level DESC,\n  a.performance_score DESC,\n  a.current_escalation_count ASC,\n  a.last_assignment_at ASC NULLS FIRST\nLIMIT 1;", "options": {}}, "id": "find-available-agent", "name": "👤 Find Available Agent", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [1200, 300], "credentials": {"postgres": {"id": "postgres-main-db", "name": "PostgreSQL - Main Database"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "agent-found", "leftValue": "={{ $('find-available-agent').item.json.id }}", "rightValue": "", "operator": {"type": "string", "operation": "exists", "singleValue": true}}], "combinator": "and"}, "options": {}}, "id": "check-agent-availability", "name": "🔍 Check Agent Availability", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [1400, 300]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "UPDATE intelligent_escalation.escalations \nSET \n  assigned_agent_id = '{{ $('find-available-agent').item.json.id }}',\n  status = 'assigned',\n  ai_analysis = '{{ JSON.stringify($('process-ai-analysis').item.json.ai_analysis) }}',\n  ai_confidence_score = {{ $('process-ai-analysis').item.json.ai_analysis.confidence_score }},\n  routing_metadata = '{\n    \"agent_selection_criteria\": {\n      \"specialization\": \"{{ $('process-ai-analysis').item.json.recommended_specialization }}\",\n      \"proficiency_level\": {{ $('find-available-agent').item.json.proficiency_level }},\n      \"performance_score\": {{ $('find-available-agent').item.json.performance_score }},\n      \"current_load\": {{ $('find-available-agent').item.json.current_escalation_count }}\n    },\n    \"routing_timestamp\": \"{{ new Date().toISOString() }}\"\n  }',\n  updated_at = NOW()\nWHERE id = '{{ $('process-ai-analysis').item.json.escalation_id }}';\n\n-- Also update agent counter\nUPDATE intelligent_escalation.human_agents\nSET \n  current_escalation_count = current_escalation_count + 1,\n  last_assignment_at = NOW()\nWHERE id = '{{ $('find-available-agent').item.json.id }}';", "options": {}}, "id": "assign-agent", "name": "✅ Assign Agent", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [1600, 200], "credentials": {"postgres": {"id": "postgres-main-db", "name": "PostgreSQL - Main Database"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "UPDATE intelligent_escalation.escalations \nSET \n  status = 'failed',\n  routing_metadata = '{\n    \"failure_reason\": \"no_available_agent\",\n    \"specialization_requested\": \"{{ $('process-ai-analysis').item.json.recommended_specialization }}\",\n    \"timestamp\": \"{{ new Date().toISOString() }}\"\n  }',\n  updated_at = NOW()\nWHERE id = '{{ $('process-ai-analysis').item.json.escalation_id }}';", "options": {}}, "id": "mark-as-failed", "name": "❌ Mark as Failed", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [1600, 400], "credentials": {"postgres": {"id": "postgres-main-db", "name": "PostgreSQL - Main Database"}}}, {"parameters": {"method": "POST", "url": "{{ $vars.CHATWOOT_API_URL }}/api/v1/accounts/{{ $vars.CHATWOOT_ACCOUNT_ID }}/conversations", "authentication": "predefinedCredentialType", "nodeCredentialType": "httpHeaderAuth", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "source_id", "value": "escalation_{{ $('process-ai-analysis').item.json.escalation_id }}"}, {"name": "inbox_id", "value": "{{ $vars.CHATWOOT_INBOX_ID }}"}, {"name": "contact_id", "value": "{{ $('webhook-trigger').item.json.contact_id }}"}, {"name": "assignee_id", "value": "{{ $('find-available-agent').item.json.id }}"}, {"name": "status", "value": "open"}, {"name": "priority", "value": "{{ $('process-ai-analysis').item.json.final_urgency === 'critical' ? 'urgent' : $('process-ai-analysis').item.json.final_urgency === 'high' ? 'high' : 'medium' }}"}, {"name": "custom_attributes", "value": "={\n  \"escalation_id\": \"{{ $('process-ai-analysis').item.json.escalation_id }}\",\n  \"ai_confidence_score\": {{ $('process-ai-analysis').item.json.ai_analysis.confidence_score }},\n  \"escalation_reason\": \"{{ $('webhook-trigger').item.json.escalation_reason }}\",\n  \"urgency_level\": \"{{ $('process-ai-analysis').item.json.final_urgency }}\",\n  \"specialization_required\": \"{{ $('process-ai-analysis').item.json.recommended_specialization }}\",\n  \"sentiment_score\": {{ $('process-ai-analysis').item.json.ai_analysis.sentiment_score }}\n}"}]}, "options": {"timeout": 15000, "retry": {"enabled": true, "maxTries": 3}}}, "id": "create-chatwoot-conversation", "name": "💬 Create Chatwoot Conversation", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1800, 200], "credentials": {"httpHeaderAuth": {"id": "chatwoot-api-auth", "name": "Chatwoot API Authentication"}}}, {"parameters": {"method": "POST", "url": "{{ $vars.SLACK_WEBHOOK_URL }}", "sendBody": true, "bodyParameters": {"parameters": [{"name": "channel", "value": "#escalation-{{ $('process-ai-analysis').item.json.recommended_specialization.replace('_', '-') }}"}, {"name": "blocks", "value": "=[\n  {\n    \"type\": \"header\",\n    \"text\": {\n      \"type\": \"plain_text\",\n      \"text\": \"🚨 Nova Escalação {{ $('process-ai-analysis').item.json.final_urgency === 'critical' ? '🔴' : $('process-ai-analysis').item.json.final_urgency === 'high' ? '🟠' : $('process-ai-analysis').item.json.final_urgency === 'medium' ? '🟡' : '🟢' }}\"\n    }\n  },\n  {\n    \"type\": \"section\",\n    \"fields\": [\n      {\"type\": \"mrkdwn\", \"text\": \"*ID:* {{ $('process-ai-analysis').item.json.escalation_id }}\"},\n      {\"type\": \"mrkdwn\", \"text\": \"*Urgência:* {{ $('process-ai-analysis').item.json.final_urgency }}\"},\n      {\"type\": \"mrkdwn\", \"text\": \"*Agente:* {{ $('find-available-agent').item.json.name }}\"},\n      {\"type\": \"mrkdwn\", \"text\": \"*Score IA:* {{ $('process-ai-analysis').item.json.ai_analysis.confidence_score }}/100\"}\n    ]\n  },\n  {\n    \"type\": \"section\",\n    \"text\": {\n      \"type\": \"mrkdwn\",\n      \"text\": \"*Motivo:* {{ $('webhook-trigger').item.json.escalation_reason }}\"\n    }\n  },\n  {\n    \"type\": \"actions\",\n    \"elements\": [\n      {\n        \"type\": \"button\",\n        \"text\": {\"type\": \"plain_text\", \"text\": \"Ver no Chatwoot\"},\n        \"url\": \"{{ $vars.CHATWOOT_BASE_URL }}/app/accounts/{{ $vars.CHATWOOT_ACCOUNT_ID }}/conversations/{{ $('create-chatwoot-conversation').item.json.id }}\",\n        \"style\": \"primary\"\n      },\n      {\n        \"type\": \"button\",\n        \"text\": {\"type\": \"plain_text\", \"text\": \"Reatribuir\"},\n        \"action_id\": \"reassign_escalation\",\n        \"value\": \"{{ $('process-ai-analysis').item.json.escalation_id }}\"\n      }\n    ]\n  }\n]"}]}, "options": {"timeout": 10000}}, "id": "send-slack-notification", "name": "📢 Send Slack Notification", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [2000, 200]}, {"parameters": {"method": "POST", "url": "{{ $vars.SLACK_WEBHOOK_URL }}", "sendBody": true, "bodyParameters": {"parameters": [{"name": "channel", "value": "#escalation-alerts"}, {"name": "blocks", "value": "=[\n  {\n    \"type\": \"header\",\n    \"text\": {\n      \"type\": \"plain_text\",\n      \"text\": \"⚠️ Escalação Falhou - Nenhum Agente Disponível\"\n    }\n  },\n  {\n    \"type\": \"section\",\n    \"fields\": [\n      {\"type\": \"mrkdwn\", \"text\": \"*ID:* {{ $('process-ai-analysis').item.json.escalation_id }}\"},\n      {\"type\": \"mrkdwn\", \"text\": \"*Especialização:* {{ $('process-ai-analysis').item.json.recommended_specialization }}\"},\n      {\"type\": \"mrkdwn\", \"text\": \"*Urgência:* {{ $('process-ai-analysis').item.json.final_urgency }}\"},\n      {\"type\": \"mrkdwn\", \"text\": \"*Timestamp:* {{ new Date().toISOString() }}\"}\n    ]\n  },\n  {\n    \"type\": \"section\",\n    \"text\": {\n      \"type\": \"mrkdwn\",\n      \"text\": \"*Motivo:* {{ $('webhook-trigger').item.json.escalation_reason }}\\n\\n*Ação Necessária:* Supervisor deve atribuir manualmente ou adicionar mais agentes à fila.\"\n    }\n  }\n]"}]}, "options": {"timeout": 10000}}, "id": "send-failure-notification", "name": "🚨 Send Failure Notification", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [2000, 400]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "INSERT INTO intelligent_escalation.escalation_metrics (\n  escalation_id,\n  metric_type,\n  metric_value,\n  metric_unit,\n  metadata\n) VALUES \n  ('{{ $('process-ai-analysis').item.json.escalation_id }}', 'processing_time_ms', {{ Date.now() - new Date($('create-escalation-record').item.json.created_at).getTime() }}, 'milliseconds', '{\"workflow_version\": \"1.0\"}'),\n  ('{{ $('process-ai-analysis').item.json.escalation_id }}', 'ai_confidence_score', {{ $('process-ai-analysis').item.json.ai_analysis.confidence_score }}, 'percentage', '{\"ai_model\": \"gpt-4-turbo-preview\"}'),\n  ('{{ $('process-ai-analysis').item.json.escalation_id }}', 'sentiment_score', {{ $('process-ai-analysis').item.json.ai_analysis.sentiment_score }}, 'percentage', '{\"analysis_timestamp\": \"{{ new Date().toISOString() }}\"}');", "options": {}}, "id": "log-metrics", "name": "📊 Log Metrics", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [2200, 300], "credentials": {"postgres": {"id": "postgres-main-db", "name": "PostgreSQL - Main Database"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "INSERT INTO intelligent_escalation.event_logs (\n  escalation_id,\n  event_type,\n  event_source,\n  event_data,\n  processing_time_ms\n) VALUES (\n  '{{ $('process-ai-analysis').item.json.escalation_id }}',\n  'escalation_completed',\n  'intelligent_escalation_workflow',\n  '{\n    \"success\": {{ $('find-available-agent').item.json.id ? 'true' : 'false' }},\n    \"agent_assigned\": \"{{ $('find-available-agent').item.json.id || null }}\",\n    \"specialization\": \"{{ $('process-ai-analysis').item.json.recommended_specialization }}\",\n    \"urgency_final\": \"{{ $('process-ai-analysis').item.json.final_urgency }}\",\n    \"ai_analysis\": {{ JSON.stringify($('process-ai-analysis').item.json.ai_analysis) }},\n    \"chatwoot_conversation_id\": \"{{ $('create-chatwoot-conversation').item.json.id || null }}\"\n  }',\n  {{ Date.now() - new Date($('create-escalation-record').item.json.created_at).getTime() }}\n);", "options": {}}, "id": "log-completion-event", "name": "📝 Log Completion Event", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [2400, 300], "credentials": {"postgres": {"id": "postgres-main-db", "name": "PostgreSQL - Main Database"}}}, {"parameters": {"respondWith": "json", "responseBody": "={\n  \"success\": true,\n  \"escalation_id\": \"{{ $('process-ai-analysis').item.json.escalation_id }}\",\n  \"status\": \"{{ $('find-available-agent').item.json.id ? 'assigned' : 'failed' }}\",\n  \"assigned_agent\": {\n    \"id\": \"{{ $('find-available-agent').item.json.id || null }}\",\n    \"name\": \"{{ $('find-available-agent').item.json.name || null }}\",\n    \"email\": \"{{ $('find-available-agent').item.json.email || null }}\"\n  },\n  \"ai_analysis\": {\n    \"confidence_score\": {{ $('process-ai-analysis').item.json.ai_analysis.confidence_score }},\n    \"sentiment_score\": {{ $('process-ai-analysis').item.json.ai_analysis.sentiment_score }},\n    \"recommended_specialization\": \"{{ $('process-ai-analysis').item.json.recommended_specialization }}\",\n    \"final_urgency\": \"{{ $('process-ai-analysis').item.json.final_urgency }}\"\n  },\n  \"integrations\": {\n    \"chatwoot_conversation_id\": \"{{ $('create-chatwoot-conversation').item.json.id || null }}\",\n    \"slack_notification_sent\": {{ $('send-slack-notification').item.json ? 'true' : 'false' }}\n  },\n  \"sla\": {\n    \"response_deadline\": \"{{ $('create-escalation-record').item.json.sla_response_deadline }}\",\n    \"resolution_deadline\": \"{{ $('create-escalation-record').item.json.sla_resolution_deadline }}\"\n  },\n  \"processing_time_ms\": {{ Date.now() - new Date($('create-escalation-record').item.json.created_at).getTime() }},\n  \"timestamp\": \"{{ new Date().toISOString() }}\"\n}", "options": {"responseCode": 200}}, "id": "success-response", "name": "✅ Success Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [2600, 300]}], "connections": {"webhook-trigger": {"main": [[{"node": "data-validation", "type": "main", "index": 0}]]}, "data-validation": {"main": [[{"node": "create-escalation-record", "type": "main", "index": 0}], [{"node": "validation-error-response", "type": "main", "index": 0}]]}, "create-escalation-record": {"main": [[{"node": "ai-context-analysis", "type": "main", "index": 0}]]}, "ai-context-analysis": {"main": [[{"node": "process-ai-analysis", "type": "main", "index": 0}]]}, "process-ai-analysis": {"main": [[{"node": "find-available-agent", "type": "main", "index": 0}]]}, "find-available-agent": {"main": [[{"node": "check-agent-availability", "type": "main", "index": 0}]]}, "check-agent-availability": {"main": [[{"node": "assign-agent", "type": "main", "index": 0}], [{"node": "mark-as-failed", "type": "main", "index": 0}]]}, "assign-agent": {"main": [[{"node": "create-chatwoot-conversation", "type": "main", "index": 0}]]}, "mark-as-failed": {"main": [[{"node": "send-failure-notification", "type": "main", "index": 0}]]}, "create-chatwoot-conversation": {"main": [[{"node": "send-slack-notification", "type": "main", "index": 0}]]}, "send-slack-notification": {"main": [[{"node": "log-metrics", "type": "main", "index": 0}]]}, "send-failure-notification": {"main": [[{"node": "log-metrics", "type": "main", "index": 0}]]}, "log-metrics": {"main": [[{"node": "log-completion-event", "type": "main", "index": 0}]]}, "log-completion-event": {"main": [[{"node": "success-response", "type": "main", "index": 0}]]}}, "pinData": {}, "settings": {"executionOrder": "v1", "saveManualExecutions": true, "callerPolicy": "workflowsFromSameOwner", "errorWorkflow": "error-handler-workflow"}, "staticData": {}, "tags": [{"createdAt": "2024-01-01T00:00:00.000Z", "updatedAt": "2024-01-01T00:00:00.000Z", "id": "intelligent-escalation", "name": "Intelligent Escalation"}, {"createdAt": "2024-01-01T00:00:00.000Z", "updatedAt": "2024-01-01T00:00:00.000Z", "id": "enterprise-grade", "name": "Enterprise Grade"}, {"createdAt": "2024-01-01T00:00:00.000Z", "updatedAt": "2024-01-01T00:00:00.000Z", "id": "ai-powered", "name": "AI Powered"}], "triggerCount": 1, "updatedAt": "2024-01-01T00:00:00.000Z", "versionId": "intelligent-escalation-v1.0"}