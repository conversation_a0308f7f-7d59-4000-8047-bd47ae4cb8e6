{"name": "[ESCALATION] 🚨 Main Workflow v3.0", "nodes": [{"parameters": {"httpMethod": "POST", "path": "escalation-main", "options": {}}, "id": "escalation-webhook-trigger", "name": "🔗 Escalation Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300], "webhookId": "escalation-main"}, {"parameters": {"jsCode": "// Enhanced Escalation Data Processing\nconst escalationData = $input.first().json.escalation_data || $input.first().json;\n\n// Validate escalation data\nif (!escalationData || typeof escalationData !== 'object') {\n  return [{ json: { error: 'Invalid escalation data', processed: false } }];\n}\n\n// Enhanced escalation validation\nconst validateEscalation = (data) => {\n  const validation = {\n    is_valid: true,\n    errors: [],\n    warnings: [],\n    completeness_score: 0\n  };\n  \n  // Required fields validation\n  const requiredFields = ['escalation_id', 'source', 'priority', 'urgency_score'];\n  const missingFields = requiredFields.filter(field => !data[field]);\n  \n  if (missingFields.length > 0) {\n    validation.errors.push(`Missing required fields: ${missingFields.join(', ')}`);\n    validation.is_valid = false;\n  }\n  \n  // Data quality checks\n  if (data.urgency_score && (data.urgency_score < 0 || data.urgency_score > 100)) {\n    validation.warnings.push('Urgency score outside expected range (0-100)');\n  }\n  \n  if (data.priority && !['low', 'medium', 'high', 'critical'].includes(data.priority)) {\n    validation.warnings.push('Priority not in expected values');\n  }\n  \n  // Completeness scoring\n  const optionalFields = ['customer_info', 'conversation_context', 'ai_analysis', 'agent_info', 'sla_info'];\n  const presentOptionalFields = optionalFields.filter(field => data[field]);\n  validation.completeness_score = (requiredFields.length + presentOptionalFields.length) / (requiredFields.length + optionalFields.length) * 100;\n  \n  return validation;\n};\n\n// Enhanced priority calculation\nconst calculateEnhancedPriority = (data) => {\n  let priorityScore = 0;\n  let priorityFactors = [];\n  \n  // Base urgency score\n  if (data.urgency_score) {\n    priorityScore += data.urgency_score * 0.4;\n    priorityFactors.push({ factor: 'urgency_score', value: data.urgency_score, weight: 0.4 });\n  }\n  \n  // AI analysis impact\n  if (data.ai_analysis) {\n    if (data.ai_analysis.sentiment === 'very_negative') {\n      priorityScore += 25;\n      priorityFactors.push({ factor: 'very_negative_sentiment', value: 25, weight: 0.25 });\n    } else if (data.ai_analysis.sentiment === 'negative') {\n      priorityScore += 15;\n      priorityFactors.push({ factor: 'negative_sentiment', value: 15, weight: 0.15 });\n    }\n    \n    if (data.ai_analysis.emotion === 'anger') {\n      priorityScore += 20;\n      priorityFactors.push({ factor: 'anger_emotion', value: 20, weight: 0.2 });\n    }\n    \n    if (data.ai_analysis.escalation_prediction) {\n      priorityScore += data.ai_analysis.escalation_confidence * 30;\n      priorityFactors.push({ factor: 'ai_escalation_prediction', value: data.ai_analysis.escalation_confidence * 30, weight: 0.3 });\n    }\n  }\n  \n  // Customer tier impact\n  if (data.customer_info?.tier) {\n    const tierMultipliers = { vip: 1.5, premium: 1.3, standard: 1.0, basic: 0.8 };\n    const multiplier = tierMultipliers[data.customer_info.tier] || 1.0;\n    priorityScore *= multiplier;\n    priorityFactors.push({ factor: 'customer_tier', value: data.customer_info.tier, weight: multiplier });\n  }\n  \n  // SLA impact\n  if (data.sla_info?.response_time_remaining_minutes) {\n    if (data.sla_info.response_time_remaining_minutes < 30) {\n      priorityScore += 30;\n      priorityFactors.push({ factor: 'sla_critical', value: 30, weight: 0.3 });\n    } else if (data.sla_info.response_time_remaining_minutes < 60) {\n      priorityScore += 15;\n      priorityFactors.push({ factor: 'sla_warning', value: 15, weight: 0.15 });\n    }\n  }\n  \n  // Time-based factors\n  if (data.temporal_context) {\n    if (!data.temporal_context.is_business_hours) {\n      priorityScore += 10;\n      priorityFactors.push({ factor: 'after_hours', value: 10, weight: 0.1 });\n    }\n    \n    if (data.temporal_context.is_weekend) {\n      priorityScore += 5;\n      priorityFactors.push({ factor: 'weekend', value: 5, weight: 0.05 });\n    }\n  }\n  \n  // Source-specific factors\n  if (data.source === 'slack' && data.slack_context?.user?.is_admin) {\n    priorityScore += 15;\n    priorityFactors.push({ factor: 'admin_user', value: 15, weight: 0.15 });\n  }\n  \n  if (data.source === 'chatwoot' && data.chatwoot_context?.conversation?.priority === 'urgent') {\n    priorityScore += 20;\n    priorityFactors.push({ factor: 'urgent_conversation', value: 20, weight: 0.2 });\n  }\n  \n  // Calculate final priority level\n  let finalPriority = 'medium';\n  if (priorityScore >= 80) {\n    finalPriority = 'critical';\n  } else if (priorityScore >= 60) {\n    finalPriority = 'high';\n  } else if (priorityScore >= 30) {\n    finalPriority = 'medium';\n  } else {\n    finalPriority = 'low';\n  }\n  \n  return {\n    enhanced_priority: finalPriority,\n    enhanced_priority_score: Math.round(priorityScore),\n    priority_factors: priorityFactors,\n    original_priority: data.priority\n  };\n};\n\n// Enhanced agent assignment logic\nconst determineAgentAssignment = (data, priorityInfo) => {\n  const assignment = {\n    assignment_type: 'auto',\n    target_agent_id: null,\n    target_team: null,\n    assignment_criteria: [],\n    estimated_assignment_time: null,\n    backup_options: []\n  };\n  \n  // Priority-based assignment\n  if (priorityInfo.enhanced_priority === 'critical') {\n    assignment.target_team = 'senior_support';\n    assignment.assignment_criteria.push('critical_priority');\n    assignment.estimated_assignment_time = '< 5 minutes';\n  } else if (priorityInfo.enhanced_priority === 'high') {\n    assignment.target_team = 'escalation_team';\n    assignment.assignment_criteria.push('high_priority');\n    assignment.estimated_assignment_time = '< 15 minutes';\n  } else {\n    assignment.target_team = 'general_support';\n    assignment.assignment_criteria.push('standard_priority');\n    assignment.estimated_assignment_time = '< 30 minutes';\n  }\n  \n  // Skill-based assignment\n  if (data.ai_analysis?.intent) {\n    const skillMapping = {\n      'technical': 'technical_team',\n      'billing': 'billing_team',\n      'sales': 'sales_team',\n      'complaint': 'escalation_team'\n    };\n    \n    if (skillMapping[data.ai_analysis.intent]) {\n      assignment.target_team = skillMapping[data.ai_analysis.intent];\n      assignment.assignment_criteria.push(`skill_based_${data.ai_analysis.intent}`);\n    }\n  }\n  \n  // Source-specific assignment\n  if (data.source === 'slack') {\n    assignment.assignment_criteria.push('slack_integration');\n    if (data.slack_context?.channel?.name?.includes('urgent')) {\n      assignment.target_team = 'escalation_team';\n    }\n  }\n  \n  if (data.source === 'chatwoot') {\n    assignment.assignment_criteria.push('chatwoot_integration');\n    if (data.chatwoot_context?.conversation?.assignee_id) {\n      assignment.target_agent_id = data.chatwoot_context.conversation.assignee_id;\n      assignment.assignment_type = 'existing_assignee';\n    }\n  }\n  \n  // Customer-based assignment\n  if (data.customer_info?.preferred_agent_id) {\n    assignment.backup_options.push({\n      type: 'preferred_agent',\n      agent_id: data.customer_info.preferred_agent_id,\n      reason: 'customer_preference'\n    });\n  }\n  \n  if (data.customer_info?.tier === 'vip') {\n    assignment.target_team = 'vip_support';\n    assignment.assignment_criteria.push('vip_customer');\n  }\n  \n  return assignment;\n};\n\n// Enhanced notification strategy\nconst determineNotificationStrategy = (data, priorityInfo, agentAssignment) => {\n  const notifications = {\n    immediate: [],\n    scheduled: [],\n    escalation_chain: [],\n    channels: []\n  };\n  \n  // Immediate notifications based on priority\n  if (priorityInfo.enhanced_priority === 'critical') {\n    notifications.immediate.push({\n      type: 'sms',\n      target: 'on_call_manager',\n      message: 'Critical escalation requires immediate attention'\n    });\n    notifications.immediate.push({\n      type: 'slack',\n      target: '#critical-alerts',\n      message: 'Critical customer escalation'\n    });\n    notifications.channels.push('sms', 'slack', 'email', 'phone');\n  } else if (priorityInfo.enhanced_priority === 'high') {\n    notifications.immediate.push({\n      type: 'slack',\n      target: '#escalations',\n      message: 'High priority escalation'\n    });\n    notifications.channels.push('slack', 'email');\n  } else {\n    notifications.channels.push('email');\n  }\n  \n  // Source-specific notifications\n  if (data.source === 'slack') {\n    notifications.immediate.push({\n      type: 'slack_thread',\n      target: data.slack_context?.channel?.id,\n      message: 'Escalation created and being processed'\n    });\n  }\n  \n  if (data.source === 'chatwoot') {\n    notifications.immediate.push({\n      type: 'chatwoot_note',\n      target: data.chatwoot_context?.conversation?.id,\n      message: 'Escalation created - agent will respond shortly'\n    });\n  }\n  \n  // Scheduled follow-ups\n  notifications.scheduled.push({\n    type: 'follow_up',\n    delay_minutes: priorityInfo.enhanced_priority === 'critical' ? 10 : 30,\n    action: 'check_assignment_status'\n  });\n  \n  // Escalation chain\n  notifications.escalation_chain = [\n    { level: 1, delay_minutes: 15, target: 'team_lead' },\n    { level: 2, delay_minutes: 30, target: 'department_manager' },\n    { level: 3, delay_minutes: 60, target: 'director' }\n  ];\n  \n  return notifications;\n};\n\n// Process the escalation\nconst validation = validateEscalation(escalationData);\nif (!validation.is_valid) {\n  return [{\n    json: {\n      error: 'Escalation validation failed',\n      validation_errors: validation.errors,\n      escalation_id: escalationData.escalation_id,\n      processed: false\n    }\n  }];\n}\n\nconst priorityInfo = calculateEnhancedPriority(escalationData);\nconst agentAssignment = determineAgentAssignment(escalationData, priorityInfo);\nconst notificationStrategy = determineNotificationStrategy(escalationData, priorityInfo, agentAssignment);\n\n// Enhanced escalation processing result\nconst processedEscalation = {\n  // Original data\n  ...escalationData,\n  \n  // Processing metadata\n  processing_metadata: {\n    processed_at: new Date().toISOString(),\n    processing_version: '3.0',\n    validation_result: validation,\n    processing_duration_ms: Date.now() - (escalationData.created_at ? new Date(escalationData.created_at).getTime() : Date.now())\n  },\n  \n  // Enhanced analysis\n  enhanced_analysis: {\n    ...priorityInfo,\n    agent_assignment: agentAssignment,\n    notification_strategy: notificationStrategy\n  },\n  \n  // Status tracking\n  status_tracking: {\n    current_status: 'processing',\n    status_history: [{\n      status: 'created',\n      timestamp: escalationData.created_at || new Date().toISOString(),\n      source: 'webhook'\n    }, {\n      status: 'processing',\n      timestamp: new Date().toISOString(),\n      source: 'main_workflow'\n    }],\n    next_status: 'agent_assignment',\n    estimated_resolution_time: new Date(Date.now() + (priorityInfo.enhanced_priority === 'critical' ? 30 : 60) * 60 * 1000).toISOString()\n  },\n  \n  // Integration sync data\n  integration_sync: {\n    sync_required: true,\n    target_systems: [],\n    sync_data: {}\n  }\n};\n\n// Determine sync requirements\nif (escalationData.source === 'slack') {\n  processedEscalation.integration_sync.target_systems.push('slack');\n  processedEscalation.integration_sync.sync_data.slack = {\n    update_thread: true,\n    notify_channel: true,\n    update_user_status: true\n  };\n}\n\nif (escalationData.source === 'chatwoot') {\n  processedEscalation.integration_sync.target_systems.push('chatwoot');\n  processedEscalation.integration_sync.sync_data.chatwoot = {\n    update_conversation: true,\n    add_labels: ['escalated', priorityInfo.enhanced_priority],\n    assign_agent: agentAssignment.target_agent_id,\n    send_message: true\n  };\n}\n\nreturn [{ json: processedEscalation }];"}, "id": "process-escalation-data", "name": "⚙️ Process Escalation Data", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [460, 300]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "critical-priority", "leftValue": "={{ $json.enhanced_analysis.enhanced_priority }}", "rightValue": "critical", "operator": {"type": "string", "operation": "equals"}}], "combinator": "and"}, "options": {}}, "id": "check-priority-level", "name": "🚨 Check Priority Level", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [680, 300]}, {"parameters": {"operation": "upsert", "schema": {"value": "agent"}, "table": {"value": "escalations"}, "columnToMatchOn": "escalation_id", "valuesToSend": {"values": {"escalation_id": "={{ $json.escalation_id }}", "source": "={{ $json.source }}", "priority": "={{ $json.enhanced_analysis.enhanced_priority }}", "urgency_score": "={{ $json.enhanced_analysis.enhanced_priority_score }}", "status": "={{ $json.status_tracking.current_status }}", "customer_info": "={{ JSON.stringify($json.customer_info) }}", "conversation_context": "={{ JSON.stringify($json.conversation_context) }}", "ai_analysis": "={{ JSON.stringify($json.ai_analysis) }}", "agent_assignment": "={{ JSON.stringify($json.enhanced_analysis.agent_assignment) }}", "notification_strategy": "={{ JSON.stringify($json.enhanced_analysis.notification_strategy) }}", "sla_info": "={{ JSON.stringify($json.sla_info) }}", "integration_sync": "={{ JSON.stringify($json.integration_sync) }}", "processing_metadata": "={{ JSON.stringify($json.processing_metadata) }}", "created_at": "={{ $json.created_at }}", "updated_at": "={{ new Date().toISOString() }}", "estimated_resolution_time": "={{ $json.status_tracking.estimated_resolution_time }}"}}, "options": {}}, "id": "save-escalation-data", "name": "💾 Save Escalation Data", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [900, 300], "credentials": {"postgres": {"id": "postgres-main", "name": "PostgreSQL Main"}}}, {"parameters": {"url": "http://localhost:5678/webhook/critical-escalation", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}, {"name": "X-Priority", "value": "critical"}, {"name": "X-Source", "value": "={{ $json.source }}"}, {"name": "X-Escalation-ID", "value": "={{ $json.escalation_id }}"}]}, "sendBody": true, "bodyParameters": {"parameters": []}, "jsonBody": "={{ JSON.stringify($json) }}", "options": {}}, "id": "trigger-critical-workflow", "name": "🚨 Trigger Critical Workflow", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [900, 200]}, {"parameters": {"url": "http://localhost:5678/webhook/standard-escalation", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}, {"name": "X-Priority", "value": "={{ $json.enhanced_analysis.enhanced_priority }}"}, {"name": "X-Source", "value": "={{ $json.source }}"}, {"name": "X-Escalation-ID", "value": "={{ $json.escalation_id }}"}]}, "sendBody": true, "bodyParameters": {"parameters": []}, "jsonBody": "={{ JSON.stringify($json) }}", "options": {}}, "id": "trigger-standard-workflow", "name": "📋 Trigger Standard Workflow", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [900, 400]}, {"parameters": {"jsCode": "// Send Immediate Notifications\nconst escalationData = $input.first().json;\nconst notifications = escalationData.enhanced_analysis.notification_strategy.immediate;\n\nconst notificationResults = [];\n\n// Process each immediate notification\nnotifications.forEach(notification => {\n  const result = {\n    type: notification.type,\n    target: notification.target,\n    message: notification.message,\n    timestamp: new Date().toISOString(),\n    status: 'pending',\n    escalation_id: escalationData.escalation_id\n  };\n  \n  // Prepare notification data based on type\n  switch (notification.type) {\n    case 'slack':\n    case 'slack_thread':\n      result.slack_data = {\n        channel: notification.target,\n        text: notification.message,\n        blocks: [{\n          type: 'section',\n          text: {\n            type: 'mrkdwn',\n            text: `*Escalation Alert*\\n${notification.message}\\n*Priority:* ${escalationData.enhanced_analysis.enhanced_priority}\\n*Source:* ${escalationData.source}\\n*ID:* ${escalationData.escalation_id}`\n          }\n        }]\n      };\n      break;\n      \n    case 'email':\n      result.email_data = {\n        to: notification.target,\n        subject: `Escalation Alert - ${escalationData.enhanced_analysis.enhanced_priority.toUpperCase()} Priority`,\n        body: `${notification.message}\\n\\nEscalation Details:\\n- ID: ${escalationData.escalation_id}\\n- Priority: ${escalationData.enhanced_analysis.enhanced_priority}\\n- Source: ${escalationData.source}\\n- Created: ${escalationData.created_at}`\n      };\n      break;\n      \n    case 'sms':\n      result.sms_data = {\n        to: notification.target,\n        message: `CRITICAL ESCALATION: ${notification.message} - ID: ${escalationData.escalation_id}`\n      };\n      break;\n      \n    case 'chatwoot_note':\n      result.chatwoot_data = {\n        conversation_id: notification.target,\n        content: notification.message,\n        message_type: 'outgoing',\n        private: false\n      };\n      break;\n  }\n  \n  notificationResults.push(result);\n});\n\n// Prepare batch notification data\nconst batchNotificationData = {\n  escalation_id: escalationData.escalation_id,\n  priority: escalationData.enhanced_analysis.enhanced_priority,\n  source: escalationData.source,\n  notifications: notificationResults,\n  batch_metadata: {\n    total_notifications: notificationResults.length,\n    created_at: new Date().toISOString(),\n    processing_version: '3.0'\n  }\n};\n\nreturn [{ json: batchNotificationData }];"}, "id": "prepare-notifications", "name": "📢 Prepare Notifications", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1120, 300]}, {"parameters": {"url": "http://localhost:5678/webhook/send-notifications", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}, {"name": "X-Notification-<PERSON><PERSON>", "value": "true"}, {"name": "X-Escalation-ID", "value": "={{ $json.escalation_id }}"}]}, "sendBody": true, "bodyParameters": {"parameters": []}, "jsonBody": "={{ JSON.stringify($json) }}", "options": {}}, "id": "send-notifications", "name": "📤 Send Notifications", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1340, 300]}, {"parameters": {"jsCode": "// Update Integration Systems\nconst escalationData = $input.first().json;\nconst syncData = escalationData.integration_sync;\n\nconst syncResults = [];\n\n// Process each target system\nsyncData.target_systems.forEach(system => {\n  const syncResult = {\n    system: system,\n    escalation_id: escalationData.escalation_id,\n    sync_timestamp: new Date().toISOString(),\n    sync_data: syncData.sync_data[system],\n    status: 'pending'\n  };\n  \n  // System-specific sync preparation\n  switch (system) {\n    case 'slack':\n      syncResult.slack_sync = {\n        webhook_url: 'https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK',\n        channel: escalationData.slack_context?.channel?.id,\n        thread_ts: escalationData.slack_context?.message?.ts,\n        update_data: {\n          text: `Escalation ${escalationData.escalation_id} is being processed`,\n          blocks: [{\n            type: 'section',\n            text: {\n              type: 'mrkdwn',\n              text: `*Escalation Update*\\n*Status:* ${escalationData.status_tracking.current_status}\\n*Priority:* ${escalationData.enhanced_analysis.enhanced_priority}\\n*Assigned Team:* ${escalationData.enhanced_analysis.agent_assignment.target_team}`\n            }\n          }]\n        }\n      };\n      break;\n      \n    case 'chatwoot':\n      syncResult.chatwoot_sync = {\n        api_url: 'https://your-chatwoot-instance.com/api/v1',\n        conversation_id: escalationData.chatwoot_context?.conversation?.id,\n        update_data: {\n          labels: syncData.sync_data.chatwoot.add_labels,\n          assignee_id: syncData.sync_data.chatwoot.assign_agent,\n          status: 'open',\n          priority: escalationData.enhanced_analysis.enhanced_priority\n        },\n        message_data: {\n          content: `Your request has been escalated and is being processed by our ${escalationData.enhanced_analysis.agent_assignment.target_team} team. Expected response time: ${escalationData.enhanced_analysis.agent_assignment.estimated_assignment_time}`,\n          message_type: 'outgoing',\n          private: false\n        }\n      };\n      break;\n  }\n  \n  syncResults.push(syncResult);\n});\n\n// Prepare batch sync data\nconst batchSyncData = {\n  escalation_id: escalationData.escalation_id,\n  sync_operations: syncResults,\n  batch_metadata: {\n    total_operations: syncResults.length,\n    created_at: new Date().toISOString(),\n    processing_version: '3.0'\n  }\n};\n\nreturn [{ json: batchSyncData }];"}, "id": "prepare-integration-sync", "name": "🔄 Prepare Integration Sync", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1120, 500]}, {"parameters": {"url": "http://localhost:5678/webhook/integration-sync", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}, {"name": "X-<PERSON><PERSON>-<PERSON><PERSON>", "value": "true"}, {"name": "X-Escalation-ID", "value": "={{ $json.escalation_id }}"}]}, "sendBody": true, "bodyParameters": {"parameters": []}, "jsonBody": "={{ JSON.stringify($json) }}", "options": {}}, "id": "execute-integration-sync", "name": "🔄 Execute Integration Sync", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1340, 500]}], "connections": {"escalation-webhook-trigger": {"main": [[{"node": "process-escalation-data", "type": "main", "index": 0}]]}, "process-escalation-data": {"main": [[{"node": "check-priority-level", "type": "main", "index": 0}]]}, "check-priority-level": {"main": [[{"node": "trigger-critical-workflow", "type": "main", "index": 0}, {"node": "save-escalation-data", "type": "main", "index": 0}], [{"node": "trigger-standard-workflow", "type": "main", "index": 0}, {"node": "save-escalation-data", "type": "main", "index": 0}]]}, "save-escalation-data": {"main": [[{"node": "prepare-notifications", "type": "main", "index": 0}, {"node": "prepare-integration-sync", "type": "main", "index": 0}]]}, "prepare-notifications": {"main": [[{"node": "send-notifications", "type": "main", "index": 0}]]}, "prepare-integration-sync": {"main": [[{"node": "execute-integration-sync", "type": "main", "index": 0}]]}}, "pinData": {}, "settings": {"executionOrder": "v1"}, "staticData": null, "tags": [{"createdAt": "2024-01-15T10:00:00.000Z", "updatedAt": "2024-01-15T10:00:00.000Z", "id": "escalation", "name": "Escalation"}, {"createdAt": "2024-01-15T10:00:00.000Z", "updatedAt": "2024-01-15T10:00:00.000Z", "id": "main", "name": "Main"}, {"createdAt": "2024-01-15T10:00:00.000Z", "updatedAt": "2024-01-15T10:00:00.000Z", "id": "workflow", "name": "Workflow"}]}