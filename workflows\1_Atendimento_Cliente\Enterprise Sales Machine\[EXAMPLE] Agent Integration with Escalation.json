{"meta": {"instanceId": "EXAMPLE_AGENT_WITH_ESCALATION"}, "name": "[EXAMPLE] Agent Integration with Escalation", "nodes": [{"parameters": {"path": "example-agent-with-escalation", "responseMode": "onReceived", "options": {}}, "id": "trigger_agent_interaction", "name": "TRIGGER: Agent Interaction", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [140, 300], "notes": "Exemplo de como integrar escalation em qualquer agente existente"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "-- Verificar se automação está pausada para este contato\nSELECT \n  cas.automation_paused,\n  cas.paused_reason,\n  cas.escalation_id,\n  he.status as escalation_status\nFROM agent.contact_automation_status cas\nLEFT JOIN agent.human_escalations he ON cas.escalation_id = he.id\nWHERE cas.contact_id = $1\nLIMIT 1;", "options": {"parameters": {"values": ["={{ $json.body.contact_id }}"]}}}, "id": "check_automation_status", "name": "🔍 Check Automation Status", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [360, 300], "credentials": {"postgres": {"id": "postgres-main", "name": "PostgreSQL Main"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "automation_is_paused", "leftValue": "={{ $json.automation_paused }}", "rightValue": "true", "operator": {"type": "boolean", "operation": "equal"}}]}}, "id": "if_automation_paused", "name": "IF: Automation Paused?", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [580, 300]}, {"parameters": {"respondWith": "json", "responseBody": "{\n  \"status\": \"paused\",\n  \"message\": \"Automação pausada - contato sendo atendido por agente humano\",\n  \"escalation_id\": {{ $('check_automation_status').item.json.escalation_id }},\n  \"escalation_status\": \"{{ $('check_automation_status').item.json.escalation_status }}\",\n  \"paused_reason\": \"{{ $('check_automation_status').item.json.paused_reason }}\"\n}", "options": {}}, "id": "return_paused_status", "name": "⏸️ Return Paused Status", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [800, 200], "notes": "Retorna quando a automação está pausada"}, {"parameters": {"workflowId": "{{ $env.DISPATCHER_WORKFLOW_ID }}", "data": "={{ {\n  body: {\n    prompt: $json.body.message || $json.body.prompt,\n    task_type: 'conversation',\n    calling_workflow_id: $workflow.id,\n    calling_node_id: 'process_with_ai',\n    contact_data: $json.body.contact_data\n  }\n} }}", "options": {}}, "id": "process_with_ai", "name": "🤖 Process with AI", "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 2, "position": [800, 400], "notes": "Processa a mensagem normalmente quando não há escalation"}, {"parameters": {"jsCode": "// Analisar resposta da IA para detectar necessidade de escalation\nconst aiResponse = $input.all()[0].json;\nconst originalMessage = $('trigger_agent_interaction').item.json.body.message;\nconst contactData = $('trigger_agent_interaction').item.json.body.contact_data;\n\n// Indicadores de escalation\nconst escalationIndicators = {\n  // Palavras-chave críticas\n  critical_keywords: ['cancelar', 'cancelamento', 'péssimo', 'horrível', 'nunca mais', 'processo judicial'],\n  \n  // Palavras técnicas\n  technical_keywords: ['erro', 'bug', 'não funciona', 'travou', 'integração', 'API', 'webhook'],\n  \n  // Oportunidades de vendas\n  sales_keywords: ['proposta', 'orçamento', 'contrato', 'negociação', 'desconto especial'],\n  \n  // Indicadores de frustração\n  frustration_keywords: ['não entendi', 'não resolve', 'j<PERSON> falei isso', 'repetindo']\n};\n\n// Função para calcular score de escalation\nfunction calculateEscalationScore(message, response) {\n  let score = 0;\n  const lowerMessage = message.toLowerCase();\n  const lowerResponse = response.toLowerCase();\n  \n  // Verificar palavras críticas\n  escalationIndicators.critical_keywords.forEach(keyword => {\n    if (lowerMessage.includes(keyword)) score += 30;\n  });\n  \n  // Verificar palavras técnicas\n  escalationIndicators.technical_keywords.forEach(keyword => {\n    if (lowerMessage.includes(keyword)) score += 20;\n  });\n  \n  // Verificar oportunidades de vendas\n  escalationIndicators.sales_keywords.forEach(keyword => {\n    if (lowerMessage.includes(keyword)) score += 25;\n  });\n  \n  // Verificar frustração\n  escalationIndicators.frustration_keywords.forEach(keyword => {\n    if (lowerMessage.includes(keyword)) score += 15;\n  });\n  \n  // IA indica incerteza\n  if (lowerResponse.includes('não tenho certeza') || \n      lowerResponse.includes('preciso verificar') ||\n      lowerResponse.includes('não posso garantir')) {\n    score += 25;\n  }\n  \n  // Cliente VIP ou alta prioridade\n  if (contactData && contactData.engagement_score > 80) {\n    score += 10;\n  }\n  \n  return score;\n}\n\n// Determinar urgência baseada no score\nfunction determineUrgency(score) {\n  if (score >= 50) return 'critical';\n  if (score >= 35) return 'high';\n  if (score >= 20) return 'medium';\n  return 'low';\n}\n\n// Calcular score e determinar necessidade de escalation\nconst escalationScore = calculateEscalationScore(originalMessage, aiResponse.choices[0].message.content);\nconst needsEscalation = escalationScore >= 20; // Threshold configurável\nconst urgencyLevel = determineUrgency(escalationScore);\n\n// Identificar razão específica do escalation\nlet escalationReason = 'Análise automática detectou necessidade de intervenção humana';\nif (escalationScore >= 50) {\n  escalationReason = 'Situação crítica detectada - requer atenção imediata';\n} else if (originalMessage.toLowerCase().includes('cancelar')) {\n  escalationReason = 'Cliente solicitou cancelamento - requer retenção especializada';\n} else if (escalationIndicators.technical_keywords.some(k => originalMessage.toLowerCase().includes(k))) {\n  escalationReason = 'Questão técnica complexa identificada';\n} else if (escalationIndicators.sales_keywords.some(k => originalMessage.toLowerCase().includes(k))) {\n  escalationReason = 'Oportunidade de vendas identificada - requer especialista';\n}\n\nreturn [{\n  json: {\n    ai_response: aiResponse,\n    escalation_analysis: {\n      needs_escalation: needsEscalation,\n      escalation_score: escalationScore,\n      urgency_level: urgencyLevel,\n      escalation_reason: escalationReason,\n      detected_indicators: {\n        critical_found: escalationIndicators.critical_keywords.filter(k => originalMessage.toLowerCase().includes(k)),\n        technical_found: escalationIndicators.technical_keywords.filter(k => originalMessage.toLowerCase().includes(k)),\n        sales_found: escalationIndicators.sales_keywords.filter(k => originalMessage.toLowerCase().includes(k)),\n        frustration_found: escalationIndicators.frustration_keywords.filter(k => originalMessage.toLowerCase().includes(k))\n      }\n    },\n    original_message: originalMessage,\n    contact_data: contactData\n  }\n}];"}, "id": "analyze_escalation_need", "name": "🧠 Analyze Escalation Need", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1020, 400]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "needs_escalation", "leftValue": "={{ $json.escalation_analysis.needs_escalation }}", "rightValue": "true", "operator": {"type": "boolean", "operation": "equal"}}]}}, "id": "if_needs_escalation", "name": "IF: Needs Escalation?", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [1240, 400]}, {"parameters": {"method": "POST", "url": "{{ $env.N8N_WEBHOOK_BASE_URL }}/webhook/escalate-to-human", "sendBody": true, "specifyBody": "json", "jsonBody": "{\n  \"contact_id\": {{ $('trigger_agent_interaction').item.json.body.contact_id }},\n  \"escalation_reason\": \"{{ $json.escalation_analysis.escalation_reason }}\",\n  \"urgency_level\": \"{{ $json.escalation_analysis.urgency_level }}\",\n  \"context_data\": {\n    \"conversation_history\": {{ JSON.stringify($('trigger_agent_interaction').item.json.body.conversation_history || []) }},\n    \"ai_response_attempted\": {{ JSON.stringify($json.ai_response.choices[0].message.content) }},\n    \"escalation_score\": {{ $json.escalation_analysis.escalation_score }},\n    \"detected_indicators\": {{ JSON.stringify($json.escalation_analysis.detected_indicators) }},\n    \"customer_message\": {{ JSON.stringify($json.original_message) }},\n    \"customer_sentiment\": {{ $json.contact_data?.sentiment_score || 0 }},\n    \"customer_profile\": {{ JSON.stringify($json.contact_data || {}) }}\n  },\n  \"agent_identity\": \"{{ $workflow.name }} - Autonomous Agent\"\n}", "options": {}}, "id": "escalate_to_human", "name": "🚨 Escalate to Human", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [1460, 300], "continueOnFail": true}, {"parameters": {"workflowId": "{{ $env.SEND_MESSAGE_WORKFLOW_ID }}", "data": "={{ {\n  body: {\n    channel: 'whatsapp',\n    recipientId: $('trigger_agent_interaction').item.json.body.contact_id,\n    messageContent: `Entendo sua situação. Para oferecer o melhor atendimento possível, vou conectar você com um de nossos especialistas que poderá ajudar de forma mais personalizada. Aguarde apenas um momento, por favor! 👨‍💼`,\n    delay_minutes: 0\n  }\n} }}", "options": {}}, "id": "send_escalation_message", "name": "📱 Send Escalation Message", "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 2, "position": [1680, 300], "continueOnFail": true}, {"parameters": {"workflowId": "{{ $env.SEND_MESSAGE_WORKFLOW_ID }}", "data": "={{ {\n  body: {\n    channel: 'whatsapp',\n    recipientId: $('trigger_agent_interaction').item.json.body.contact_id,\n    messageContent: $('analyze_escalation_need').item.json.ai_response.choices[0].message.content,\n    delay_minutes: 0\n  }\n} }}", "options": {}}, "id": "send_normal_response", "name": "📱 Send Normal Response", "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 2, "position": [1460, 500], "continueOnFail": true}, {"parameters": {"respondWith": "json", "responseBody": "{\n  \"status\": \"escalated\",\n  \"message\": \"Conversa escalada para atendimento humano\",\n  \"escalation_reason\": \"{{ $('analyze_escalation_need').item.json.escalation_analysis.escalation_reason }}\",\n  \"urgency_level\": \"{{ $('analyze_escalation_need').item.json.escalation_analysis.urgency_level }}\",\n  \"escalation_score\": {{ $('analyze_escalation_need').item.json.escalation_analysis.escalation_score }},\n  \"customer_notified\": true\n}", "options": {}}, "id": "return_escalation_status", "name": "🚨 Return Escalation Status", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1900, 300]}, {"parameters": {"respondWith": "json", "responseBody": "{\n  \"status\": \"processed\",\n  \"message\": \"Mensagem processada normalmente\",\n  \"ai_response\": {{ JSON.stringify($('analyze_escalation_need').item.json.ai_response.choices[0].message.content) }},\n  \"escalation_analysis\": {\n    \"score\": {{ $('analyze_escalation_need').item.json.escalation_analysis.escalation_score }},\n    \"threshold_met\": false,\n    \"urgency_would_be\": \"{{ $('analyze_escalation_need').item.json.escalation_analysis.urgency_level }}\"\n  }\n}", "options": {}}, "id": "return_normal_status", "name": "✅ Return Normal Status", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1680, 500]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "-- Log da interação para analytics\nINSERT INTO agent.interaction_logs (\n  contact_id,\n  workflow_name,\n  interaction_type,\n  escalation_score,\n  escalation_triggered,\n  urgency_level,\n  response_content,\n  processing_time_ms,\n  created_at\n)\nVALUES (\n  $1,\n  $2,\n  'ai_agent_interaction',\n  $3,\n  $4,\n  $5,\n  $6,\n  $7,\n  NOW()\n);", "options": {"parameters": {"values": ["={{ $('trigger_agent_interaction').item.json.body.contact_id }}", "={{ $workflow.name }}", "={{ $('analyze_escalation_need').item.json.escalation_analysis.escalation_score }}", "={{ $('analyze_escalation_need').item.json.escalation_analysis.needs_escalation }}", "={{ $('analyze_escalation_need').item.json.escalation_analysis.urgency_level }}", "={{ JSON.stringify($('analyze_escalation_need').item.json.ai_response.choices[0].message.content) }}", "={{ Math.round((Date.now() - new Date($('trigger_agent_interaction').item.json.timestamp || Date.now()).getTime())) }}"]}}}, "id": "log_interaction", "name": "📊 Log Interaction", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [1240, 600], "credentials": {"postgres": {"id": "postgres-main", "name": "PostgreSQL Main"}}, "continueOnFail": true}], "connections": {"trigger_agent_interaction": {"main": [[{"node": "check_automation_status", "type": "main", "index": 0}]]}, "check_automation_status": {"main": [[{"node": "if_automation_paused", "type": "main", "index": 0}]]}, "if_automation_paused": {"main": [[{"node": "return_paused_status", "type": "main", "index": 0}], [{"node": "process_with_ai", "type": "main", "index": 0}]]}, "process_with_ai": {"main": [[{"node": "analyze_escalation_need", "type": "main", "index": 0}]]}, "analyze_escalation_need": {"main": [[{"node": "if_needs_escalation", "type": "main", "index": 0}, {"node": "log_interaction", "type": "main", "index": 0}]]}, "if_needs_escalation": {"main": [[{"node": "escalate_to_human", "type": "main", "index": 0}], [{"node": "send_normal_response", "type": "main", "index": 0}]]}, "escalate_to_human": {"main": [[{"node": "send_escalation_message", "type": "main", "index": 0}]]}, "send_escalation_message": {"main": [[{"node": "return_escalation_status", "type": "main", "index": 0}]]}, "send_normal_response": {"main": [[{"node": "return_normal_status", "type": "main", "index": 0}]]}}, "settings": {"executionOrder": "v1"}, "staticData": null, "tags": [{"createdAt": "2024-01-15T20:00:00.000Z", "updatedAt": "2024-01-15T20:00:00.000Z", "id": "escalation-example", "name": "escalation-example"}], "triggerCount": 1, "updatedAt": "2024-01-15T20:00:00.000Z", "versionId": "1"}