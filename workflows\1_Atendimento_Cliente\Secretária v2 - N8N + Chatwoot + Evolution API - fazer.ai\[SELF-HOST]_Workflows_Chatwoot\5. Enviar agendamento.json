{"nodes": [{"parameters": {"workflowInputs": {"values": [{"name": "mensagem"}, {"name": "id_conta"}, {"name": "id_conversa"}, {"name": "url_chatwoot"}]}}, "type": "n8n-nodes-base.executeWorkflowTrigger", "typeVersion": 1.1, "position": [340, 0], "id": "ea015117-f959-45cd-8318-deafac7af4b5", "name": "When Executed by Another Workflow"}, {"parameters": {"content": "[![fazer.ai](https://framerusercontent.com/images/HqY9djLTzyutSKnuLLqBr92KbM.png?scale-down-to=256)](https://fazer.ai?utm_source=n8n&utm_campaign=sec-ep2&utm_medium=cw-5)\n\n## Esse é um template faça você mesmo do canal\n## <PERSON> Moreira\n\n### Inscreva-se no nosso canal no YouTube\n[![YouTube Lucas Moreira](https://img.shields.io/youtube/channel/subscribers/UCtmp6SxzLscu0GRTbgM8FTw?style=flat-square&logo=youtube&label=Inscreva-se&color=f00)](https://youtube.com/@eulucassmoreira?si=0lH7hwX9pukjhmPQ)\n\n### Siga nosso GitHub\n[![GitHub fazer.ai](https://img.shields.io/badge/github-%23121011.svg?style=for-the-badge&logo=github&logoColor=white&label)](https://github.com/fazer-ai)\n", "height": 440, "width": 550, "color": 7}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-20, -540], "id": "55c0b9bd-100e-4d23-b57e-11363747d777", "name": "Sticky Note10"}, {"parameters": {"content": "## Quer entender como funciona?\n\n\n### Assista o vídeo, deixe um like, e se inscreva no canal para ter acesso a mais workflows como esse!\n\n[![IMAGE ALT TEXT HERE](https://i1.ytimg.com/vi_webp/cvTWGNJGAu4/maxresdefault.webp)](https://www.youtube.com/watch?v=cvTWGNJGAu4)", "height": 440, "width": 500, "color": 7}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [540, -540], "id": "cc3aa465-d5f2-4c8c-a6c0-0f605a069eab", "name": "Sticky Note13"}, {"parameters": {"method": "POST", "url": "={{ $json.url_chatwoot }}/api/v1/accounts/{{ $json.id_conta }}/conversations/{{ $json.id_conversa }}/messages", "authentication": "predefinedCredentialType", "nodeCredentialType": "chatwootApi", "sendBody": true, "bodyParameters": {"parameters": [{"name": "content", "value": "={{ $json.mensagem }}"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [560, 0], "id": "b38ea12b-aa12-48e7-a5f1-7a8bfa4e648a", "name": "Enviar mensagem", "credentials": {}}, {"parameters": {"content": "![Chatwoot](https://app.chatwoot.com/brand-assets/logo_dark.svg)", "height": 100, "width": 280, "color": 7}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [200, -240], "id": "a8158125-e415-48bc-be9a-b73a7859f305", "name": "Sticky Note23"}, {"parameters": {"content": "## 5. Enviar agendamento", "height": 80, "width": 540, "color": 5}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-20, -640], "id": "ebe52eee-9f14-45cb-9f4e-ae25aaf25483", "name": "Sticky Note30"}], "connections": {"When Executed by Another Workflow": {"main": [[{"node": "Enviar mensagem", "type": "main", "index": 0}]]}}, "pinData": {}, "meta": {}}