{"name": "[ESCALATION] 🎯 Chatwoot Integration Manager v2.0", "nodes": [{"parameters": {"path": "chatwoot-webhook", "options": {"noResponseBody": false}}, "id": "webhook-trigger", "name": "🎯 Chatwoot Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [180, 300], "webhookId": "chatwoot-escalation-webhook"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "event-type-check", "leftValue": "={{ $json.event }}", "rightValue": "message_created", "operator": {"type": "string", "operation": "equals"}}, {"id": "message-type-check", "leftValue": "={{ $json.message_type }}", "rightValue": "incoming", "operator": {"type": "string", "operation": "equals"}}], "combineOperation": "all"}}, "id": "event-filter", "name": "🔍 Filter Events", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [400, 300]}, {"parameters": {"jsCode": "// Extrair e processar dados do Chatwoot\nconst webhookData = $input.first().json;\n\n// Dados da conversa\nconst conversation = webhookData.conversation || {};\nconst message = webhookData.message || {};\nconst contact = webhookData.contact || {};\nconst account = webhookData.account || {};\n\n// Processar dados do cliente\nconst customerData = {\n  id: contact.id,\n  name: contact.name || 'Cliente Anônimo',\n  email: contact.email,\n  phone: contact.phone_number,\n  custom_attributes: contact.custom_attributes || {},\n  created_at: contact.created_at\n};\n\n// Processar dados da conversa\nconst conversationData = {\n  id: conversation.id,\n  status: conversation.status,\n  priority: conversation.priority || 'medium',\n  assignee_id: conversation.assignee_id,\n  team_id: conversation.team_id,\n  inbox_id: conversation.inbox_id,\n  labels: conversation.labels || [],\n  custom_attributes: conversation.custom_attributes || {},\n  created_at: conversation.created_at,\n  updated_at: conversation.updated_at\n};\n\n// Processar mensagem\nconst messageData = {\n  id: message.id,\n  content: message.content,\n  message_type: message.message_type,\n  content_type: message.content_type,\n  sender: message.sender,\n  created_at: message.created_at,\n  attachments: message.attachments || []\n};\n\n// Detectar palavras-chave de escalação\nconst escalationKeywords = [\n  'urgente', 'emergência', 'problema grave', 'não funciona',\n  'cancelar', 'reembolso', 'supervisor', 'gerente',\n  'reclamação', 'insatisfeito', 'péssimo', 'horrível'\n];\n\nconst messageContent = (message.content || '').toLowerCase();\nconst hasEscalationKeywords = escalationKeywords.some(keyword => \n  messageContent.includes(keyword)\n);\n\n// Calcular score de urgência baseado em fatores\nlet urgencyScore = 0;\n\n// Palavras-chave (+30)\nif (hasEscalationKeywords) urgencyScore += 30;\n\n// Prioridade da conversa (+20)\nif (conversation.priority === 'high') urgencyScore += 20;\nelse if (conversation.priority === 'medium') urgencyScore += 10;\n\n// Status da conversa (+15)\nif (conversation.status === 'pending') urgencyScore += 15;\nelse if (conversation.status === 'snoozed') urgencyScore += 10;\n\n// Tempo sem resposta (+25)\nconst lastMessageTime = new Date(message.created_at);\nconst now = new Date();\nconst hoursSinceLastMessage = (now - lastMessageTime) / (1000 * 60 * 60);\n\nif (hoursSinceLastMessage > 24) urgencyScore += 25;\nelse if (hoursSinceLastMessage > 12) urgencyScore += 15;\nelse if (hoursSinceLastMessage > 4) urgencyScore += 10;\n\n// Número de mensagens na conversa (+10)\nconst messageCount = conversation.messages_count || 0;\nif (messageCount > 10) urgencyScore += 10;\nelse if (messageCount > 5) urgencyScore += 5;\n\n// Determinar se deve escalar\nconst shouldEscalate = urgencyScore >= 50 || hasEscalationKeywords;\n\n// Determinar categoria baseada no conteúdo\nlet category = 'general';\nif (messageContent.includes('técnico') || messageContent.includes('bug') || messageContent.includes('erro')) {\n  category = 'technical';\n} else if (messageContent.includes('pagamento') || messageContent.includes('cobrança') || messageContent.includes('fatura')) {\n  category = 'billing';\n} else if (messageContent.includes('produto') || messageContent.includes('funcionalidade')) {\n  category = 'product';\n} else if (messageContent.includes('conta') || messageContent.includes('login') || messageContent.includes('senha')) {\n  category = 'account';\n}\n\n// Preparar dados para análise de IA\nconst aiAnalysisData = {\n  message_content: message.content,\n  conversation_history: [], // Será preenchido posteriormente\n  customer_context: {\n    previous_conversations: 0,\n    customer_tier: 'standard',\n    account_age_days: 0\n  },\n  urgency_indicators: {\n    keywords_found: hasEscalationKeywords,\n    response_time_hours: hoursSinceLastMessage,\n    message_count: messageCount,\n    priority_level: conversation.priority\n  }\n};\n\n// Resultado final\nconst result = {\n  // Dados originais\n  webhook_data: webhookData,\n  \n  // Dados processados\n  customer: customerData,\n  conversation: conversationData,\n  message: messageData,\n  \n  // Análise de escalação\n  escalation_analysis: {\n    should_escalate: shouldEscalate,\n    urgency_score: urgencyScore,\n    category: category,\n    keywords_found: hasEscalationKeywords,\n    escalation_reasons: []\n  },\n  \n  // Dados para IA\n  ai_analysis_input: aiAnalysisData,\n  \n  // Metadados\n  processed_at: new Date().toISOString(),\n  source: 'chatwoot',\n  integration_version: '2.0'\n};\n\n// Adicionar razões de escalação\nif (hasEscalationKeywords) {\n  result.escalation_analysis.escalation_reasons.push('Palavras-chave de escalação detectadas');\n}\nif (urgencyScore >= 50) {\n  result.escalation_analysis.escalation_reasons.push(`Score de urgência alto: ${urgencyScore}`);\n}\nif (hoursSinceLastMessage > 24) {\n  result.escalation_analysis.escalation_reasons.push('Tempo de resposta excedido (>24h)');\n}\nif (conversation.priority === 'high') {\n  result.escalation_analysis.escalation_reasons.push('Conversa marcada como alta prioridade');\n}\n\nreturn result;"}, "id": "process-chatwoot-data", "name": "⚙️ Process Chatwoot Data", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [620, 300]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "should-escalate-check", "leftValue": "={{ $json.escalation_analysis.should_escalate }}", "rightValue": true, "operator": {"type": "boolean", "operation": "true"}}], "combineOperation": "all"}}, "id": "escalation-decision", "name": "🚨 Should Escalate?", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [840, 300]}, {"parameters": {"url": "http://localhost:5678/webhook/ai-sentiment-analysis", "options": {"timeout": 10000}, "sendBody": true, "specifyBody": "json", "jsonBody": "={{ JSON.stringify($json.ai_analysis_input) }}"}, "id": "ai-sentiment-analysis", "name": "🤖 AI Sentiment Analysis", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [1060, 200], "continueOnFail": true}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT id FROM escalation.escalations WHERE chatwoot_conversation_id = $1 AND status NOT IN ('resolved', 'closed')", "additionalFields": {"values": ["={{ $json.conversation.id }}"]}}, "id": "check-existing-escalation", "name": "🔍 Check Existing Escalation", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [1060, 300]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "no-existing-escalation", "leftValue": "={{ $json.length }}", "rightValue": 0, "operator": {"type": "number", "operation": "equals"}}], "combineOperation": "all"}}, "id": "new-escalation-check", "name": "🆕 Is New Escalation?", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [1280, 300]}, {"parameters": {"jsCode": "// Combinar dados do Chatwoot com análise de IA\nconst chatwootData = $input.first().json;\nconst aiAnalysis = $input.last().json;\n\n// Processar resultado da IA (pode ter falhado)\nlet sentimentData = {\n  sentiment_score: 0,\n  emotion: 'neutral',\n  urgency_level: 'medium',\n  confidence: 0.5,\n  ai_analysis_failed: false\n};\n\nif (aiAnalysis && !aiAnalysis.error) {\n  try {\n    const aiResult = typeof aiAnalysis === 'string' ? JSON.parse(aiAnalysis) : aiAnalysis;\n    sentimentData = {\n      sentiment_score: aiResult.sentiment_score || 0,\n      emotion: aiResult.emotion || 'neutral',\n      urgency_level: aiResult.urgency_level || 'medium',\n      confidence: aiResult.confidence || 0.5,\n      ai_analysis_failed: false\n    };\n  } catch (error) {\n    sentimentData.ai_analysis_failed = true;\n  }\n} else {\n  sentimentData.ai_analysis_failed = true;\n}\n\n// Ajustar urgência baseada na análise de IA\nlet finalUrgency = chatwootData.escalation_analysis.urgency_score;\nif (!sentimentData.ai_analysis_failed) {\n  // Adicionar peso da análise de sentimento\n  if (sentimentData.sentiment_score < -0.5) finalUrgency += 20;\n  else if (sentimentData.sentiment_score < -0.2) finalUrgency += 10;\n  \n  // Adicionar peso da urgência detectada pela IA\n  if (sentimentData.urgency_level === 'critical') finalUrgency += 30;\n  else if (sentimentData.urgency_level === 'high') finalUrgency += 20;\n  else if (sentimentData.urgency_level === 'medium') finalUrgency += 10;\n}\n\n// Determinar prioridade final\nlet priority = 'medium';\nif (finalUrgency >= 80) priority = 'critical';\nelse if (finalUrgency >= 60) priority = 'high';\nelse if (finalUrgency >= 40) priority = 'medium';\nelse priority = 'low';\n\n// Calcular SLA baseado na prioridade\nconst slaMinutes = {\n  critical: 15,\n  high: 60,\n  medium: 240,\n  low: 480\n};\n\nconst dueAt = new Date();\ndueAt.setMinutes(dueAt.getMinutes() + slaMinutes[priority]);\n\n// Preparar dados para criação da escalação\nconst escalationData = {\n  // IDs únicos\n  id: require('crypto').randomUUID(),\n  external_id: `chatwoot_${chatwootData.conversation.id}_${Date.now()}`,\n  \n  // Dados do cliente\n  customer_name: chatwootData.customer.name,\n  customer_email: chatwootData.customer.email,\n  customer_phone: chatwootData.customer.phone,\n  customer_id: chatwootData.customer.id.toString(),\n  \n  // Dados da escalação\n  title: `Escalação Chatwoot - Conversa #${chatwootData.conversation.id}`,\n  description: chatwootData.message.content,\n  category: chatwootData.escalation_analysis.category,\n  priority: priority,\n  urgency_score: finalUrgency,\n  \n  // Canal e origem\n  channel: 'chatwoot',\n  source: 'chatwoot_webhook',\n  \n  // Dados específicos do Chatwoot\n  chatwoot_conversation_id: chatwootData.conversation.id,\n  chatwoot_message_id: chatwootData.message.id,\n  chatwoot_inbox_id: chatwootData.conversation.inbox_id,\n  chatwoot_assignee_id: chatwootData.conversation.assignee_id,\n  \n  // Análise de IA\n  ai_sentiment_score: sentimentData.sentiment_score,\n  ai_emotion: sentimentData.emotion,\n  ai_urgency_level: sentimentData.urgency_level,\n  ai_confidence: sentimentData.confidence,\n  \n  // SLA\n  due_at: dueAt.toISOString(),\n  sla_minutes: slaMinutes[priority],\n  \n  // Status\n  status: 'pending',\n  \n  // Metadados\n  metadata: {\n    chatwoot_data: chatwootData.conversation,\n    escalation_reasons: chatwootData.escalation_analysis.escalation_reasons,\n    ai_analysis: sentimentData,\n    processing_details: {\n      processed_at: new Date().toISOString(),\n      integration_version: '2.0',\n      urgency_calculation: {\n        base_score: chatwootData.escalation_analysis.urgency_score,\n        ai_adjustment: finalUrgency - chatwootData.escalation_analysis.urgency_score,\n        final_score: finalUrgency\n      }\n    }\n  },\n  \n  // Timestamps\n  created_at: new Date().toISOString(),\n  updated_at: new Date().toISOString()\n};\n\nreturn escalationData;"}, "id": "prepare-escalation-data", "name": "📋 Prepare Escalation Data", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1500, 300]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "INSERT INTO escalation.escalations (\n  id, external_id, customer_name, customer_email, customer_phone, customer_id,\n  title, description, category, priority, urgency_score, channel, source,\n  chatwoot_conversation_id, chatwoot_message_id, chatwoot_inbox_id, chatwoot_assignee_id,\n  ai_sentiment_score, ai_emotion, ai_urgency_level, ai_confidence,\n  due_at, sla_minutes, status, metadata, created_at, updated_at\n) VALUES (\n  $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20, $21, $22, $23, $24, $25, $26, $27\n) RETURNING id", "additionalFields": {"values": ["={{ $json.id }}", "={{ $json.external_id }}", "={{ $json.customer_name }}", "={{ $json.customer_email }}", "={{ $json.customer_phone }}", "={{ $json.customer_id }}", "={{ $json.title }}", "={{ $json.description }}", "={{ $json.category }}", "={{ $json.priority }}", "={{ $json.urgency_score }}", "={{ $json.channel }}", "={{ $json.source }}", "={{ $json.chatwoot_conversation_id }}", "={{ $json.chatwoot_message_id }}", "={{ $json.chatwoot_inbox_id }}", "={{ $json.chatwoot_assignee_id }}", "={{ $json.ai_sentiment_score }}", "={{ $json.ai_emotion }}", "={{ $json.ai_urgency_level }}", "={{ $json.ai_confidence }}", "={{ $json.due_at }}", "={{ $json.sla_minutes }}", "={{ $json.status }}", "={{ JSON.stringify($json.metadata) }}", "={{ $json.created_at }}", "={{ $json.updated_at }}"]}}, "id": "create-escalation", "name": "💾 Create Escalation", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [1720, 300]}, {"parameters": {"url": "http://localhost:5678/webhook/escalation-created", "options": {"timeout": 5000}, "sendBody": true, "specifyBody": "json", "jsonBody": "={{ JSON.stringify({\n  escalation_id: $('create-escalation').first().json.id,\n  priority: $('prepare-escalation-data').first().json.priority,\n  category: $('prepare-escalation-data').first().json.category,\n  customer_name: $('prepare-escalation-data').first().json.customer_name,\n  source: 'chatwoot',\n  created_at: new Date().toISOString()\n}) }}"}, "id": "trigger-escalation-workflow", "name": "🚀 Trigger Escalation Workflow", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [1940, 300], "continueOnFail": true}, {"parameters": {"authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "url": "={{ $env.CHATWOOT_API_URL }}/api/v1/accounts/{{ $env.CHATWOOT_ACCOUNT_ID }}/conversations/{{ $('process-chatwoot-data').first().json.conversation.id }}/labels", "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"labels\": [\"escalated\", \"priority-{{ $('prepare-escalation-data').first().json.priority }}\"]\n}", "options": {"timeout": 5000}}, "id": "update-chatwoot-labels", "name": "🏷️ Update Chatwoot Labels", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [2160, 300], "continueOnFail": true}, {"parameters": {"authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "url": "={{ $env.CHATWOOT_API_URL }}/api/v1/accounts/{{ $env.CHATWOOT_ACCOUNT_ID }}/conversations/{{ $('process-chatwoot-data').first().json.conversation.id }}/messages", "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"content\": \"🚨 Esta conversa foi escalada para atendimento humano especializado.\\n\\n📋 **Detalhes da Escalação:**\\n- ID: {{ $('create-escalation').first().json.id }}\\n- Prioridade: {{ $('prepare-escalation-data').first().json.priority }}\\n- Categoria: {{ $('prepare-escalation-data').first().json.category }}\\n- SLA: {{ $('prepare-escalation-data').first().json.sla_minutes }} minutos\\n\\n⏰ Um agente especializado entrará em contato em breve.\",\n  \"message_type\": \"outgoing\",\n  \"private\": false\n}", "options": {"timeout": 5000}}, "id": "send-escalation-message", "name": "💬 Send Escalation Message", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [2380, 300], "continueOnFail": true}, {"parameters": {"operation": "execute<PERSON>uery", "query": "INSERT INTO agent.integration_logs (\n  integration_name, integration_type, operation, direction,\n  endpoint, http_method, request_data, response_data,\n  status_code, success, processing_time_ms, escalation_id,\n  correlation_id, metadata, created_at\n) VALUES (\n  'chatwoot', 'chat_platform', 'webhook_received', 'inbound',\n  '/webhook/chatwoot-webhook', 'POST', $1, $2, 200, true,\n  $3, $4, $5, $6, NOW()\n)", "additionalFields": {"values": ["={{ JSON.stringify($('webhook-trigger').first().json) }}", "={{ JSON.stringify({\n              escalation_created: true,\n              escalation_id: $('create-escalation').first().json.id,\n              processing_result: 'success'\n            }) }}", "={{ Date.now() - new Date($('process-chatwoot-data').first().json.processed_at).getTime() }}", "={{ $('create-escalation').first().json.id }}", "={{ 'chatwoot_' + $('process-chatwoot-data').first().json.conversation.id }}", "={{ JSON.stringify({\n              webhook_processed: true,\n              ai_analysis_success: !$('prepare-escalation-data').first().json.metadata.ai_analysis.ai_analysis_failed,\n              urgency_score: $('prepare-escalation-data').first().json.urgency_score,\n              priority: $('prepare-escalation-data').first().json.priority\n            }) }}"]}}, "id": "log-integration-success", "name": "📝 Log Integration Success", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [2600, 300]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "UPDATE escalation.escalations SET \n  chatwoot_message_id = $1,\n  updated_at = NOW()\nWHERE chatwoot_conversation_id = $2 AND status NOT IN ('resolved', 'closed')", "additionalFields": {"values": ["={{ $('process-chatwoot-data').first().json.message.id }}", "={{ $('process-chatwoot-data').first().json.conversation.id }}"]}}, "id": "update-existing-escalation", "name": "🔄 Update Existing Escalation", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [1280, 500]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "INSERT INTO agent.integration_logs (\n  integration_name, integration_type, operation, direction,\n  endpoint, http_method, request_data, response_data,\n  status_code, success, error_message, escalation_id,\n  correlation_id, metadata, created_at\n) VALUES (\n  'chatwoot', 'chat_platform', 'webhook_received', 'inbound',\n  '/webhook/chatwoot-webhook', 'POST', $1, $2, 200, false,\n  'No escalation needed', NULL, $3, $4, NOW()\n)", "additionalFields": {"values": ["={{ JSON.stringify($('webhook-trigger').first().json) }}", "={{ JSON.stringify({\n              escalation_created: false,\n              reason: 'Urgency score below threshold',\n              urgency_score: $json.escalation_analysis.urgency_score\n            }) }}", "={{ 'chatwoot_' + $json.conversation.id }}", "={{ JSON.stringify({\n              webhook_processed: true,\n              escalation_needed: false,\n              urgency_score: $json.escalation_analysis.urgency_score,\n              threshold: 50\n            }) }}"]}}, "id": "log-no-escalation", "name": "📝 Log No Escalation", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [840, 500]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "INSERT INTO agent.integration_logs (\n  integration_name, integration_type, operation, direction,\n  endpoint, http_method, request_data, response_data,\n  status_code, success, error_message, correlation_id,\n  metadata, created_at\n) VALUES (\n  'chatwoot', 'chat_platform', 'webhook_received', 'inbound',\n  '/webhook/chatwoot-webhook', 'POST', $1, $2, 200, false,\n  'Event filtered out', $3, $4, NOW()\n)", "additionalFields": {"values": ["={{ JSON.stringify($('webhook-trigger').first().json) }}", "={{ JSON.stringify({\n              event_type: $('webhook-trigger').first().json.event,\n              message_type: $('webhook-trigger').first().json.message_type,\n              filter_reason: 'Not a relevant event for escalation'\n            }) }}", "={{ 'chatwoot_' + ($('webhook-trigger').first().json.conversation?.id || 'unknown') }}", "={{ JSON.stringify({\n              webhook_received: true,\n              event_filtered: true,\n              event_type: $('webhook-trigger').first().json.event\n            }) }}"]}}, "id": "log-filtered-event", "name": "📝 Log Filtered Event", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [400, 500]}, {"parameters": {"respondWith": "json", "responseBody": "={\n  \"status\": \"success\",\n  \"message\": \"Escalation created successfully\",\n  \"escalation_id\": \"{{ $('create-escalation').first().json.id }}\",\n  \"priority\": \"{{ $('prepare-escalation-data').first().json.priority }}\",\n  \"sla_minutes\": {{ $('prepare-escalation-data').first().json.sla_minutes }},\n  \"processed_at\": \"{{ new Date().toISOString() }}\"\n}"}, "id": "success-response", "name": "✅ Success Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [2820, 300]}, {"parameters": {"respondWith": "json", "responseBody": "={\n  \"status\": \"success\",\n  \"message\": \"Existing escalation updated\",\n  \"action\": \"updated\",\n  \"processed_at\": \"{{ new Date().toISOString() }}\"\n}"}, "id": "update-response", "name": "🔄 Update Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1500, 500]}, {"parameters": {"respondWith": "json", "responseBody": "={\n  \"status\": \"success\",\n  \"message\": \"No escalation needed\",\n  \"urgency_score\": {{ $json.escalation_analysis.urgency_score }},\n  \"threshold\": 50,\n  \"processed_at\": \"{{ new Date().toISOString() }}\"\n}"}, "id": "no-escalation-response", "name": "ℹ️ No Escalation Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1060, 500]}, {"parameters": {"respondWith": "json", "responseBody": "={\n  \"status\": \"success\",\n  \"message\": \"Event processed but filtered\",\n  \"event_type\": \"{{ $('webhook-trigger').first().json.event }}\",\n  \"filter_reason\": \"Not relevant for escalation\",\n  \"processed_at\": \"{{ new Date().toISOString() }}\"\n}"}, "id": "filtered-response", "name": "🔍 Filtered Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [620, 500]}], "pinData": {}, "connections": {"webhook-trigger": {"main": [[{"node": "event-filter", "type": "main", "index": 0}]]}, "event-filter": {"main": [[{"node": "process-chatwoot-data", "type": "main", "index": 0}], [{"node": "log-filtered-event", "type": "main", "index": 0}]]}, "process-chatwoot-data": {"main": [[{"node": "escalation-decision", "type": "main", "index": 0}]]}, "escalation-decision": {"main": [[{"node": "ai-sentiment-analysis", "type": "main", "index": 0}, {"node": "check-existing-escalation", "type": "main", "index": 0}], [{"node": "log-no-escalation", "type": "main", "index": 0}]]}, "ai-sentiment-analysis": {"main": [[{"node": "prepare-escalation-data", "type": "main", "index": 1}]]}, "check-existing-escalation": {"main": [[{"node": "new-escalation-check", "type": "main", "index": 0}]]}, "new-escalation-check": {"main": [[{"node": "prepare-escalation-data", "type": "main", "index": 0}], [{"node": "update-existing-escalation", "type": "main", "index": 0}]]}, "prepare-escalation-data": {"main": [[{"node": "create-escalation", "type": "main", "index": 0}]]}, "create-escalation": {"main": [[{"node": "trigger-escalation-workflow", "type": "main", "index": 0}]]}, "trigger-escalation-workflow": {"main": [[{"node": "update-chatwoot-labels", "type": "main", "index": 0}]]}, "update-chatwoot-labels": {"main": [[{"node": "send-escalation-message", "type": "main", "index": 0}]]}, "send-escalation-message": {"main": [[{"node": "log-integration-success", "type": "main", "index": 0}]]}, "log-integration-success": {"main": [[{"node": "success-response", "type": "main", "index": 0}]]}, "update-existing-escalation": {"main": [[{"node": "update-response", "type": "main", "index": 0}]]}, "log-no-escalation": {"main": [[{"node": "no-escalation-response", "type": "main", "index": 0}]]}, "log-filtered-event": {"main": [[{"node": "filtered-response", "type": "main", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1", "saveManualExecutions": true, "callerPolicy": "workflowsFromSameOwner", "errorWorkflow": "error-handler-workflow"}, "versionId": "2.0", "meta": {"templateCredsSetupCompleted": true, "instanceId": "escalation-chatwoot-integration"}, "id": "escalation-chatwoot-integration-v2", "tags": [{"createdAt": "2024-01-15T10:00:00.000Z", "updatedAt": "2024-01-15T10:00:00.000Z", "id": "escalation", "name": "escalation"}, {"createdAt": "2024-01-15T10:00:00.000Z", "updatedAt": "2024-01-15T10:00:00.000Z", "id": "chatwoot", "name": "chatwoot"}, {"createdAt": "2024-01-15T10:00:00.000Z", "updatedAt": "2024-01-15T10:00:00.000Z", "id": "integration", "name": "integration"}]}