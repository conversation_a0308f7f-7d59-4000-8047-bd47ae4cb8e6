{"name": "[MCP Server] Google Calendar", "nodes": [{"parameters": {"path": "google-calendar"}, "type": "@n8n/n8n-nodes-langchain.mcpTrigger", "typeVersion": 1, "position": [20, 0], "id": "19413332-0640-4c17-bf15-9e44dcb294e3", "name": "[MCP Server] Google Calendar", "webhookId": "aaa3c565-aba0-4e2d-8db8-33502e1317d6"}, {"parameters": {"calendar": {"__rl": true, "value": "<EMAIL>", "mode": "list", "cachedResultName": "<EMAIL>"}, "start": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Start', ``, 'string') }}", "end": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('End', ``, 'string') }}", "additionalFields": {"description": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Description', ``, 'string') }}", "summary": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Summary', ``, 'string') }}"}}, "type": "n8n-nodes-base.googleCalendarTool", "typeVersion": 1.3, "position": [-80, 220], "id": "aec98903-2345-46dd-9fca-ff5f256f74ea", "name": "Criar evento", "credentials": {}}, {"parameters": {"operation": "delete", "calendar": {"__rl": true, "value": "<EMAIL>", "mode": "list", "cachedResultName": "<EMAIL>"}, "eventId": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Event_ID', ``, 'string') }}", "options": {}}, "type": "n8n-nodes-base.googleCalendarTool", "typeVersion": 1.3, "position": [120, 220], "id": "a9251074-bad1-4dea-98da-f0a3c51aeb9d", "name": "Deletar evento", "credentials": {}}, {"parameters": {"operation": "get", "calendar": {"__rl": true, "value": "<EMAIL>", "mode": "list", "cachedResultName": "<EMAIL>"}, "eventId": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Event_ID', ``, 'string') }}", "options": {}}, "type": "n8n-nodes-base.googleCalendarTool", "typeVersion": 1.3, "position": [320, 220], "id": "ab65b159-0ec5-4df3-aeb4-2f42fe8a6a1e", "name": "Buscar evento", "credentials": {}}, {"parameters": {"operation": "getAll", "calendar": {"__rl": true, "value": "<EMAIL>", "mode": "list", "cachedResultName": "<EMAIL>"}, "limit": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Limit', ``, 'number') }}", "timeMin": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('After', ``, 'string') }}", "timeMax": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Before', ``, 'string') }}", "options": {}}, "type": "n8n-nodes-base.googleCalendarTool", "typeVersion": 1.3, "position": [520, 220], "id": "28ce1152-5696-44cf-bbb1-eb8449c09d96", "name": "Buscar todos os eventos", "credentials": {}}, {"parameters": {"operation": "update", "calendar": {"__rl": true, "value": "<EMAIL>", "mode": "list", "cachedResultName": "<EMAIL>"}, "eventId": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Event_ID', ``, 'string') }}", "updateFields": {"description": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Description', ``, 'string') }}", "end": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('End', ``, 'string') }}", "start": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Start', ``, 'string') }}", "summary": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Summary', ``, 'string') }}"}}, "type": "n8n-nodes-base.googleCalendarTool", "typeVersion": 1.3, "position": [720, 220], "id": "add8b8b8-942c-4ad7-85f4-3372f8d44a5b", "name": "Atualizar evento", "credentials": {}}], "pinData": {}, "connections": {"Criar evento": {"ai_tool": [[{"node": "[MCP Server] Google Calendar", "type": "ai_tool", "index": 0}]]}, "Deletar evento": {"ai_tool": [[{"node": "[MCP Server] Google Calendar", "type": "ai_tool", "index": 0}]]}, "Buscar evento": {"ai_tool": [[{"node": "[MCP Server] Google Calendar", "type": "ai_tool", "index": 0}]]}, "Buscar todos os eventos": {"ai_tool": [[{"node": "[MCP Server] Google Calendar", "type": "ai_tool", "index": 0}]]}, "Atualizar evento": {"ai_tool": [[{"node": "[MCP Server] Google Calendar", "type": "ai_tool", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1"}, "versionId": "b0987bd7-2598-4e1c-9c16-e960ced3e5be", "meta": {"templateCredsSetupCompleted": true, "instanceId": "0b49d177f34e66e4cb02bb7fd4f3a4af9f44ac0cbad00917db36a2465834ec4e"}, "id": "YOeWmF0lo9N5MAsw", "tags": [{"createdAt": "2025-04-12T19:23:58.255Z", "updatedAt": "2025-04-12T19:23:58.255Z", "id": "J2mRJhMwsDXFG7L0", "name": "mcp-server"}, {"createdAt": "2025-04-12T19:24:26.346Z", "updatedAt": "2025-04-12T19:24:26.346Z", "id": "nzjmNiBtIYWd12Cd", "name": "credencial-lucas"}, {"createdAt": "2025-04-12T22:46:24.076Z", "updatedAt": "2025-04-12T22:46:24.076Z", "id": "6mN2rPIbFGcvOjzP", "name": "videoyoutube"}]}