#!/bin/bash
# SCRIPT DE INICIALIZAÇÃO DO POSTGRESQL - ENTERPRISE ECOSYSTEM
# VERSÃO: 4.0 - TEMPLATE CENTRALIZADO
# 
# Este script é executado automaticamente quando o container PostgreSQL inicia pela primeira vez
# Ele cria todos os bancos de dados e usuários necessários para o ecossistema Enterprise
# 
# FUNÇÃO:
# - Criar usuários com permissões específicas
# - Criar bancos de dados para cada serviço
# - Configurar permissões e privilégios
# - Verificar integridade das criações
# 
# SERVIÇOS SUPORTADOS:
# - N8N (Automação de Workflows)
# - Evolution API (WhatsApp API)
# - Chatwoot (Atendimento ao Cliente)
# - Metabase (Business Intelligence)
# - Grafana (Dashboards e Monitoramento)

set -e

# Função para log com timestamp
log_message() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
}

log_message "🚀 INICIANDO CONFIGURAÇÃO DO POSTGRESQL - ENTERPRISE ECOSYSTEM"
log_message "📊 Criando bancos e usuários para todos os serviços..."

# =============================================================================
# CONFIGURAÇÃO N8N - AUTOMAÇÃO DE WORKFLOWS
# =============================================================================
log_message "⚙️  Configurando N8N Database..."

# Criar usuário N8N
psql -v ON_ERROR_STOP=1 --username "$POSTGRES_USER" --dbname "$POSTGRES_DB" <<-EOSQL
    -- Criar usuário N8N se não existir
    DO \$\$
    BEGIN
        IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = 'n8n_user') THEN
            CREATE USER n8n_user WITH PASSWORD '$N8N_DB_PASSWORD';
            RAISE NOTICE 'Usuário n8n_user criado com sucesso';
        ELSE
            RAISE NOTICE 'Usuário n8n_user já existe';
        END IF;
    END
    \$\$;

    -- Criar database N8N se não existir
    DO \$\$
    BEGIN
        IF NOT EXISTS (SELECT FROM pg_database WHERE datname = 'n8n_db') THEN
            CREATE DATABASE n8n_db OWNER n8n_user;
            RAISE NOTICE 'Database n8n_db criado com sucesso';
        ELSE
            RAISE NOTICE 'Database n8n_db já existe';
        END IF;
    END
    \$\$;

    -- Conceder privilégios
    GRANT ALL PRIVILEGES ON DATABASE n8n_db TO n8n_user;
    GRANT CONNECT ON DATABASE n8n_db TO n8n_user;
EOSQL

log_message "✅ N8N Database configurado com sucesso"

# =============================================================================
# CONFIGURAÇÃO EVOLUTION API - WHATSAPP INTEGRATION
# =============================================================================
log_message "📱 Configurando Evolution API Database..."

psql -v ON_ERROR_STOP=1 --username "$POSTGRES_USER" --dbname "$POSTGRES_DB" <<-EOSQL
    -- Criar usuário Evolution API
    DO \$\$
    BEGIN
        IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = 'evolution_user') THEN
            CREATE USER evolution_user WITH PASSWORD '$EVOLUTION_DB_PASSWORD';
            RAISE NOTICE 'Usuário evolution_user criado com sucesso';
        ELSE
            RAISE NOTICE 'Usuário evolution_user já existe';
        END IF;
    END
    \$\$;

    -- Criar database Evolution API
    DO \$\$
    BEGIN
        IF NOT EXISTS (SELECT FROM pg_database WHERE datname = 'evolution_db') THEN
            CREATE DATABASE evolution_db OWNER evolution_user;
            RAISE NOTICE 'Database evolution_db criado com sucesso';
        ELSE
            RAISE NOTICE 'Database evolution_db já existe';
        END IF;
    END
    \$\$;

    -- Conceder privilégios
    GRANT ALL PRIVILEGES ON DATABASE evolution_db TO evolution_user;
    GRANT CONNECT ON DATABASE evolution_db TO evolution_user;
EOSQL

log_message "✅ Evolution API Database configurado com sucesso"

# =============================================================================
# CONFIGURAÇÃO CHATWOOT - CUSTOMER SERVICE PLATFORM
# =============================================================================
log_message "💬 Configurando Chatwoot Database..."

psql -v ON_ERROR_STOP=1 --username "$POSTGRES_USER" --dbname "$POSTGRES_DB" <<-EOSQL
    -- Criar usuário Chatwoot
    DO \$\$
    BEGIN
        IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = 'chatwoot_user') THEN
            CREATE USER chatwoot_user WITH PASSWORD '$CHATWOOT_DB_PASSWORD';
            RAISE NOTICE 'Usuário chatwoot_user criado com sucesso';
        ELSE
            RAISE NOTICE 'Usuário chatwoot_user já existe';
        END IF;
    END
    \$\$;

    -- Criar database Chatwoot
    DO \$\$
    BEGIN
        IF NOT EXISTS (SELECT FROM pg_database WHERE datname = 'chatwoot_db') THEN
            CREATE DATABASE chatwoot_db OWNER chatwoot_user;
            RAISE NOTICE 'Database chatwoot_db criado com sucesso';
        ELSE
            RAISE NOTICE 'Database chatwoot_db já existe';
        END IF;
    END
    \$\$;

    -- Conceder privilégios
    GRANT ALL PRIVILEGES ON DATABASE chatwoot_db TO chatwoot_user;
    GRANT CONNECT ON DATABASE chatwoot_db TO chatwoot_user;
EOSQL

log_message "✅ Chatwoot Database configurado com sucesso"

# =============================================================================
# CONFIGURAÇÃO METABASE - BUSINESS INTELLIGENCE
# =============================================================================
log_message "📊 Configurando Metabase Database..."

psql -v ON_ERROR_STOP=1 --username "$POSTGRES_USER" --dbname "$POSTGRES_DB" <<-EOSQL
    -- Criar usuário Metabase
    DO \$\$
    BEGIN
        IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = 'metabase_user') THEN
            CREATE USER metabase_user WITH PASSWORD '$METABASE_DB_PASSWORD';
            RAISE NOTICE 'Usuário metabase_user criado com sucesso';
        ELSE
            RAISE NOTICE 'Usuário metabase_user já existe';
        END IF;
    END
    \$\$;

    -- Criar database Metabase
    DO \$\$
    BEGIN
        IF NOT EXISTS (SELECT FROM pg_database WHERE datname = 'metabase_db') THEN
            CREATE DATABASE metabase_db OWNER metabase_user;
            RAISE NOTICE 'Database metabase_db criado com sucesso';
        ELSE
            RAISE NOTICE 'Database metabase_db já existe';
        END IF;
    END
    \$\$;

    -- Conceder privilégios
    GRANT ALL PRIVILEGES ON DATABASE metabase_db TO metabase_user;
    GRANT CONNECT ON DATABASE metabase_db TO metabase_user;
EOSQL

log_message "✅ Metabase Database configurado com sucesso"

# =============================================================================
# CONFIGURAÇÃO GRAFANA - MONITORING AND VISUALIZATION
# =============================================================================
log_message "📈 Configurando Grafana Database..."

psql -v ON_ERROR_STOP=1 --username "$POSTGRES_USER" --dbname "$POSTGRES_DB" <<-EOSQL
    -- Criar usuário Grafana
    DO \$\$
    BEGIN
        IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = 'grafana_user') THEN
            CREATE USER grafana_user WITH PASSWORD '$GRAFANA_DB_PASSWORD';
            RAISE NOTICE 'Usuário grafana_user criado com sucesso';
        ELSE
            RAISE NOTICE 'Usuário grafana_user já existe';
        END IF;
    END
    \$\$;

    -- Criar database Grafana
    DO \$\$
    BEGIN
        IF NOT EXISTS (SELECT FROM pg_database WHERE datname = 'grafana_db') THEN
            CREATE DATABASE grafana_db OWNER grafana_user;
            RAISE NOTICE 'Database grafana_db criado com sucesso';
        ELSE
            RAISE NOTICE 'Database grafana_db já existe';
        END IF;
    END
    \$\$;

    -- Conceder privilégios
    GRANT ALL PRIVILEGES ON DATABASE grafana_db TO grafana_user;
    GRANT CONNECT ON DATABASE grafana_db TO grafana_user;
EOSQL

log_message "✅ Grafana Database configurado com sucesso"

# =============================================================================
# VERIFICAÇÃO DE INTEGRIDADE E RELATÓRIO FINAL
# =============================================================================
log_message "🔍 Verificando integridade das configurações..."

# Verificar databases criados
psql -v ON_ERROR_STOP=1 --username "$POSTGRES_USER" --dbname "$POSTGRES_DB" <<-EOSQL
    \echo '📋 RELATÓRIO DE DATABASES CRIADOS:'
    SELECT datname as "Database", 
           datowner::regrole as "Owner"
    FROM pg_database 
    WHERE datname IN ('n8n_db', 'evolution_db', 'chatwoot_db', 'metabase_db', 'grafana_db')
    ORDER BY datname;
    
    \echo '👥 RELATÓRIO DE USUÁRIOS CRIADOS:'
    SELECT rolname as "Username",
           rolcanlogin as "Can Login",
           rolconnlimit as "Connection Limit"
    FROM pg_roles 
    WHERE rolname IN ('n8n_user', 'evolution_user', 'chatwoot_user', 'metabase_user', 'grafana_user')
    ORDER BY rolname;
EOSQL

log_message "🎉 CONFIGURAÇÃO POSTGRESQL CONCLUÍDA COM SUCESSO!"
log_message "📊 Todos os bancos de dados e usuários foram criados"
log_message "🚀 Sistema pronto para receber as aplicações"
log_message "⚡ Enterprise Ecosystem PostgreSQL inicializado com sucesso!"

exit 0 