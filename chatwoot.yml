#version: "3.8"
services:
  chatwoot-rails:
    image: chatwoot/chatwoot:v4.0.0
    container_name: chatwoot-rails-1
    ports:
      - "3000:3000"
    networks:
      - app_network
    environment:
      - POSTGRES_HOST=postgres
      - POSTGRES_USERNAME=postgres
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_DATABASE=chatwoot
      - REDIS_URL=redis://redis:6379
      - SECRET_KEY_BASE=${SECRET_KEY_BASE}
    command: bundle exec rails s -p 3000 -b 0.0.0.0
    depends_on:
      - postgres
      - redis
  chatwoot-sidekiq:
    image: chatwoot/chatwoot:v4.0.0
    container_name: chatwoot-sidekiq-1
    networks:
      - app_network
    environment:
      - POSTGRES_HOST=postgres
      - POSTGRES_USERNAME=postgres
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_DATABASE=chatwoot
      - REDIS_URL=redis://redis:6379
      - SECRET_KEY_BASE=${SECRET_KEY_BASE}
    command: bundle exec sidekiq -C config/sidekiq.yml
    depends_on:
      chatwoot-rails:
        # Garante que o sidekiq só inicie após o serviço principal estar saudável.
        condition: service_healthy
networks:
  app_network:
    external: true