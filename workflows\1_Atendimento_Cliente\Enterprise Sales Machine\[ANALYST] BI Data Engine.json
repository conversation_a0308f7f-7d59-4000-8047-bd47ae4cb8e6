{"name": "[ANALYST] BI Data Engine v1.1 - Cost-Aware", "nodes": [{"parameters": {"path": "run-bi-analysis", "responseMode": "onReceived", "options": {}}, "id": "trigger_webhook", "name": "TRIGGER: BI Analysis Request", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [400, 300], "notes": "Ex: .../run-bi-analysis?report=leads_by_source"}, {"parameters": {"routing": {"rules": {"values": [{"value1": "={{$json.query.report}}", "value2": "leads_by_source"}]}, "fallback": "unknown_report"}}, "id": "switch_report_type", "name": "SWITCH: Report Type", "type": "n8n-nodes-base.switch", "typeVersion": 2, "position": [640, 300]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT \n  t.tag as source_tag,\n  COUNT(c.id) as lead_count\nFROM agent.contatos c, jsonb_array_elements_text(c.tags) as t(tag)\nWHERE t.tag LIKE 'lead_from_%'\nGROUP BY t.tag\nORDER BY lead_count DESC;", "options": {}}, "id": "db_get_leads_by_source", "name": "DB: Get Leads by Source", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [860, 200], "credentials": {"postgres": {"id": "YOUR_POSTGRES_CREDENTIAL_ID"}}}, {"parameters": {"workflowId": "={{ $env.DISPATCHER_WORKFLOW_ID }}", "data": "={{ { body: { \n    prompt: `Você é um Analista de BI especializado em visualização de dados. Sua tarefa é transformar os seguintes dados brutos em um diagrama de barras horizontais usando a sintaxe Mermaid. Mostre apenas os 5 principais resultados e agrupe o restante em uma categoria 'Outros'.\\n\\n**DADOS BRUTOS (JSON):**\\n${JSON.stringify($items)}\\n\\n**TAREFA:**\\nGere APENAS o código do diagrama Mermaid. Não inclua a palavra 'mermaid' ou os acentos de código (```). Comece diretamente com 'graph TD'.`,\n    task_type: 'complex_analysis',\n    calling_workflow_id: $workflow.id,\n    calling_node_id: 'call_dispatcher_for_chart'\n} } }}", "options": {}}, "id": "call_dispatcher_for_chart", "name": "Call Dispatcher for Chart", "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 2, "position": [1080, 200]}, {"parameters": {"content": "={{$json.choices[0].message.content}}", "options": {}}, "id": "send_mermaid_diagram", "name": "Respond: Mermaid Diagram", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1300, 200], "notes": "Responde ao webhook com o código Mermaid, que será renderizado em certas UIs (como a do n8n)."}, {"parameters": {"message": "Erro: Tipo de relatório desconhecido.", "options": {}}, "id": "respond_error", "name": "Respond: <PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [860, 400]}], "connections": {"trigger_webhook": {"main": [[{"node": "switch_report_type"}]]}, "switch_report_type": {"main": [[{"node": "db_get_leads_by_source"}], [{"node": "respond_error"}]]}, "db_get_leads_by_source": {"main": [[{"node": "call_dispatcher_for_chart"}]]}, "call_dispatcher_for_chart": {"main": [[{"node": "send_mermaid_diagram"}]]}}}